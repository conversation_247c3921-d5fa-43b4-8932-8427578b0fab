#!/usr/bin/env python3
"""
Enhanced Xero OAuth Setup Script with Guided Configuration and Validation
"""

import os
import sys
import json
import webbrowser
import requests
from typing import Dict, List, Tuple, Optional
from dotenv import load_dotenv, set_key
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from mcx3d_finance.auth.xero_oauth import XeroAuthManager
from mcx3d_finance.core.config import get_xero_config, settings
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class XeroOAuthSetupWizard:
    """Enhanced setup wizard for Xero OAuth configuration."""
    
    def __init__(self):
        self.env_file = Path('.env.development')
        self.config = {}
        self.issues = []
        self.warnings = []
        
    def print_header(self):
        """Print setup wizard header."""
        print("🚀 MCX3D Financials - Xero OAuth Setup Wizard")
        print("=" * 60)
        print("This wizard will guide you through setting up Xero OAuth integration")
        print("with enhanced security, validation, and developer experience.\n")
        
    def check_prerequisites(self) -> bool:
        """Check system prerequisites."""
        print("🔍 Checking Prerequisites...")
        
        prerequisites = [
            ("Python version >= 3.8", sys.version_info >= (3, 8)),
            ("Environment file exists", self.env_file.exists()),
            ("Redis connection", self._test_redis_connection()),
            ("Database connection", self._test_database_connection()),
        ]
        
        all_passed = True
        for check, passed in prerequisites:
            status = "✅" if passed else "❌"
            print(f"   {status} {check}")
            if not passed:
                all_passed = False
        
        if not all_passed:
            print("\n❌ Prerequisites failed. Please resolve the issues above before continuing.")
            return False
            
        print("✅ All prerequisites passed!\n")
        return True
    
    def _test_redis_connection(self) -> bool:
        """Test Redis connectivity."""
        try:
            from mcx3d_finance.utils.redis_client import get_redis_client
            redis_client = get_redis_client()
            redis_client.ping()
            return True
        except Exception:
            return False
    
    def _test_database_connection(self) -> bool:
        """Test database connectivity."""
        try:
            from mcx3d_finance.db.session import SessionLocal
            from sqlalchemy import text
            db = SessionLocal()
            db.execute(text("SELECT 1"))
            db.close()
            return True
        except Exception:
            return False
    
    def collect_xero_credentials(self) -> Dict[str, str]:
        """Interactively collect Xero API credentials."""
        print("🔑 Xero API Credentials Setup")
        print("-" * 40)
        print("You need to create a Xero app at: https://developer.xero.com/app/manage")
        print("App Type: Web App")
        print("Redirect URI: http://localhost:8000/api/auth/xero/callback")
        print("Required Scopes: Accounting Transactions, Contacts, Reports, Settings, Offline Access\n")
        
        credentials = {}
        
        while True:
            credentials['client_id'] = input("Enter your Xero Client ID: ").strip()
            if credentials['client_id'] and len(credentials['client_id']) > 10:
                break
            print("❌ Invalid Client ID. Please ensure it's the correct value from your Xero app.")
        
        while True:
            credentials['client_secret'] = input("Enter your Xero Client Secret: ").strip()
            if credentials['client_secret'] and len(credentials['client_secret']) > 20:
                break
            print("❌ Invalid Client Secret. Please ensure it's the correct value from your Xero app.")
        
        # Optional webhook key
        webhook_key = input("Enter your Xero Webhook Key (optional, press Enter to skip): ").strip()
        if webhook_key:
            credentials['webhook_key'] = webhook_key
        
        return credentials
    
    def validate_xero_app_config(self, credentials: Dict[str, str]) -> Tuple[bool, List[str]]:
        """Validate Xero app configuration by testing API connectivity."""
        print("\n🔬 Validating Xero App Configuration...")
        
        validation_errors = []
        
        # Test 1: Validate Client ID format
        if not credentials['client_id'].replace('-', '').replace('_', '').isalnum():
            validation_errors.append("Client ID format appears invalid")
        
        # Test 2: Validate Client Secret format  
        if len(credentials['client_secret']) < 20:
            validation_errors.append("Client Secret appears too short")
        
        # Test 3: Test OAuth URL generation (this validates client_id without requiring full auth)
        try:
            from requests_oauthlib import OAuth2Session
            oauth = OAuth2Session(
                client_id=credentials['client_id'],
                redirect_uri="http://localhost:8000/api/auth/xero/callback",
                scope=['accounting.transactions', 'accounting.contacts', 'accounting.reports.read', 'accounting.settings', 'offline_access']
            )
            auth_url, state = oauth.authorization_url(
                "https://login.xero.com/identity/connect/authorize",
                code_challenge="test_challenge",
                code_challenge_method="S256"
            )
            print("   ✅ OAuth URL generation successful")
        except Exception as e:
            validation_errors.append(f"OAuth URL generation failed: {e}")
        
        if validation_errors:
            print("   ❌ Validation failed:")
            for error in validation_errors:
                print(f"      - {error}")
            return False, validation_errors
        
        print("   ✅ Xero app configuration appears valid")
        return True, []
    
    def update_environment_file(self, credentials: Dict[str, str]) -> bool:
        """Update .env file with new credentials."""
        print(f"\n💾 Updating {self.env_file}...")
        
        try:
            # Ensure .env file exists
            if not self.env_file.exists():
                self.env_file.touch()
            
            # Update environment variables
            env_updates = {
                'XERO_CLIENT_ID': credentials['client_id'],
                'XERO_CLIENT_SECRET': credentials['client_secret'], 
                'XERO_REDIRECT_URI': 'http://localhost:8000/api/auth/xero/callback',
                'XERO_SCOPES': 'accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access',
            }
            
            if 'webhook_key' in credentials:
                env_updates['XERO_WEBHOOK_KEY'] = credentials['webhook_key']
            
            for key, value in env_updates.items():
                set_key(str(self.env_file), key, value)
                print(f"   ✅ Set {key}")
            
            print(f"✅ Environment file updated successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to update environment file: {e}")
            return False
    
    def test_full_configuration(self) -> bool:
        """Test the complete OAuth configuration."""
        print("\n🧪 Testing Complete OAuth Configuration...")
        
        try:
            # Reload environment
            load_dotenv(self.env_file)
            
            # Test configuration loading
            config = get_xero_config()
            print("   ✅ Configuration loading successful")
            
            # Test XeroAuthManager initialization
            auth_manager = XeroAuthManager()
            print("   ✅ XeroAuthManager initialization successful")
            
            # Test auth URL generation
            auth_url, state = auth_manager.generate_auth_url()
            if auth_url and state:
                print("   ✅ OAuth URL generation successful")
                print(f"   📋 Generated state token: {state[:20]}...")
                return True
            else:
                print("   ❌ OAuth URL generation failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Configuration test failed: {e}")
            return False
    
    def create_health_check_endpoint(self) -> None:
        """Create OAuth health check endpoint."""
        print("\n🏥 Creating OAuth Health Check Endpoint...")
        
        health_check_code = '''from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging
from ..auth.xero_oauth import XeroAuthManager
from ..core.config import get_xero_config

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/health/oauth")
async def oauth_health_check() -> Dict[str, Any]:
    """
    OAuth health check endpoint for monitoring and troubleshooting.
    """
    try:
        health_status = {
            "status": "healthy",
            "checks": {},
            "warnings": []
        }
        
        # Check 1: Configuration validation
        try:
            config = get_xero_config()
            health_status["checks"]["configuration"] = "✅ Valid"
        except Exception as e:
            health_status["checks"]["configuration"] = f"❌ {str(e)}"
            health_status["status"] = "unhealthy"
        
        # Check 2: XeroAuthManager initialization
        try:
            auth_manager = XeroAuthManager()
            health_status["checks"]["auth_manager"] = "✅ Initialized"
        except Exception as e:
            health_status["checks"]["auth_manager"] = f"❌ {str(e)}"
            health_status["status"] = "unhealthy"
        
        # Check 3: State storage connectivity
        try:
            auth_manager = XeroAuthManager()
            test_key = "health_check_test"
            auth_manager.state_storage.set(test_key, "test_value", 60)
            value = auth_manager.state_storage.get(test_key)
            auth_manager.state_storage.delete(test_key)
            
            if value == "test_value":
                health_status["checks"]["state_storage"] = "✅ Redis connected"
            else:
                health_status["checks"]["state_storage"] = "⚠️ Using in-memory fallback"
                health_status["warnings"].append("Redis unavailable, using in-memory state storage")
        except Exception as e:
            health_status["checks"]["state_storage"] = f"❌ {str(e)}"
            health_status["status"] = "unhealthy"
        
        # Check 4: OAuth URL generation
        try:
            auth_manager = XeroAuthManager()
            auth_url, state = auth_manager.generate_auth_url()
            if auth_url and state:
                health_status["checks"]["oauth_generation"] = "✅ Working"
            else:
                health_status["checks"]["oauth_generation"] = "❌ Failed to generate URL"
                health_status["status"] = "unhealthy"
        except Exception as e:
            health_status["checks"]["oauth_generation"] = f"❌ {str(e)}"
            health_status["status"] = "unhealthy"
        
        return health_status
        
    except Exception as e:
        logger.error(f"OAuth health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
'''
        
        # Write health check to API routes
        health_file = Path('mcx3d_finance/api/oauth_health_routes.py')
        try:
            with open(health_file, 'w') as f:
                f.write(health_check_code)
            print(f"   ✅ Created {health_file}")
            print("   📋 Add to your main router: include_router(oauth_health_routes.router)")
        except Exception as e:
            print(f"   ⚠️ Could not create health check file: {e}")
    
    def run_oauth_test(self) -> None:
        """Run the complete OAuth flow test."""
        print("\n🔄 Running OAuth Flow Test...")
        
        try:
            auth_manager = XeroAuthManager()
            auth_url, state = auth_manager.generate_auth_url()
            
            print(f"🌐 Opening authorization URL: {auth_url[:50]}...")
            print(f"📋 State token: {state}")
            
            # Open browser
            try:
                webbrowser.open(auth_url)
                print("✅ Browser opened successfully")
            except Exception as e:
                print(f"⚠️ Could not open browser: {e}")
                print("Please copy the URL above and open it manually.")
            
            print("\n📖 Complete these steps:")
            print("1. ✅ Log in to your Xero account")
            print("2. 🏢 Select the organization to connect")
            print("3. ✋ Click 'Authorize' to grant access")
            print("4. 🔄 You'll be redirected to the callback URL")
            print("5. 💾 Organization data will be saved to database")
            
        except Exception as e:
            print(f"❌ OAuth test failed: {e}")
    
    def run(self) -> None:
        """Run the complete setup wizard."""
        self.print_header()
        
        # Step 1: Prerequisites
        if not self.check_prerequisites():
            sys.exit(1)
        
        # Step 2: Collect credentials
        credentials = self.collect_xero_credentials()
        
        # Step 3: Validate credentials
        valid, errors = self.validate_xero_app_config(credentials)
        if not valid:
            print("❌ Credential validation failed. Please check your Xero app configuration.")
            sys.exit(1)
        
        # Step 4: Update environment
        if not self.update_environment_file(credentials):
            sys.exit(1)
        
        # Step 5: Test configuration
        if not self.test_full_configuration():
            print("❌ Configuration test failed. Check the errors above.")
            sys.exit(1)
        
        # Step 6: Create health check
        self.create_health_check_endpoint()
        
        # Step 7: Run OAuth test
        print("\n" + "="*60)
        print("✅ Setup completed successfully!")
        print("🎯 Next steps:")
        print("   1. Start your application: docker-compose up")
        print("   2. Test health endpoint: GET /api/health/oauth")
        print("   3. Run OAuth flow test below:")
        print("="*60)
        
        run_test = input("\nWould you like to run the OAuth flow test now? (y/N): ").strip().lower()
        if run_test in ['y', 'yes']:
            self.run_oauth_test()


def main():
    """Main entry point."""
    try:
        wizard = XeroOAuthSetupWizard()
        wizard.run()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        logger.exception("Setup wizard failed")
        sys.exit(1)


if __name__ == "__main__":
    main()