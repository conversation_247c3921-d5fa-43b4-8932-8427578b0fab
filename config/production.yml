# Production Environment Configuration
# MCX3D Financials - Production Settings
# CRITICAL: All sensitive values MUST be set via environment variables
# This file contains only non-sensitive production defaults

# Environment identification
environment: production

# Database configuration for production
database:
  # DATABASE_URL must be set in environment variables
  # Use connection pooling for production performance
  pool_size: 20
  pool_pre_ping: true
  pool_recycle: 3600
  timeout: 60
  echo_sql: false  # Never log SQL in production
  
  # Production database optimizations
  connect_args:
    sslmode: require
    connect_timeout: 30

# Redis configuration for production
redis:
  # REDIS_URL must be set in environment variables
  # Production Redis with authentication and SSL
  db: 0
  socket_timeout: 5
  socket_connect_timeout: 5
  retry_on_timeout: true
  health_check_interval: 30

# Xero OAuth configuration
xero:
  # XERO_CLIENT_ID and XERO_CLIENT_SECRET must be set in environment
  # Use production Xero application
  redirect_uri: "https://api.mcx3d.com/api/auth/xero/callback"
  scopes: "accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access"
  
  # Production-specific Xero settings
  rate_limit_calls_per_minute: 60
  timeout_seconds: 30
  retry_attempts: 3

# Security configuration for production
security:
  # SECRET_KEY and ENCRYP<PERSON>ON_KEY must be set in environment variables
  # Production security settings - no compromises
  access_token_expire_minutes: 15  # Short-lived tokens
  refresh_token_expire_days: 30
  max_login_attempts: 3  # Strict limit
  lockout_duration_minutes: 30
  session_idle_timeout_minutes: 30
  
  # Enhanced security features
  enable_audit_encryption: true
  enable_field_encryption: true
  enable_key_rotation: true
  key_rotation_days: 90
  
  # MFA enforcement
  enforce_mfa_for_admin: true
  mfa_code_validity_minutes: 2

# Rate limiting - strict production limits
rate_limiting:
  api_default: 100          # General API calls per minute
  api_authenticated: 500    # Authenticated users get higher limits
  auth_login: 3             # Login attempts per 5 minutes
  auth_register: 1          # Registration attempts per hour
  password_reset: 2         # Password reset per hour
  report_generation: 5      # Report generation per 5 minutes
  data_export: 2            # Data export per hour
  
  # IP-based rate limiting
  enable_ip_rate_limiting: true
  suspicious_activity_threshold: 50

# Logging configuration for production
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  enable_sql_logging: false  # Never enable in production
  log_file: "/var/log/mcx3d_finance/production.log"
  max_file_size_mb: 100
  backup_count: 10
  
  # Structured logging for production monitoring
  enable_json_logging: true
  include_request_id: true
  sanitize_sensitive_data: true

# Reporting configuration for production
reporting:
  output_dir: "/var/reports/production"
  default_format: "pdf"
  enable_charts: true
  chart_dpi: 300
  max_file_size_mb: 100
  
  # Production report optimization
  enable_compression: true
  compression_level: 6
  concurrent_generation_limit: 3

# Performance settings for production
performance:
  memory_limit_mb: 2048
  timeout_seconds: 300
  parallel_workers: 8
  enable_caching: true
  
  # Production optimizations
  enable_response_compression: true
  enable_query_optimization: true
  connection_pool_size: 20

# CORS settings for production - restrictive
cors:
  origins:
    - "https://app.mcx3d.com"
    - "https://dashboard.mcx3d.com" 
  allow_credentials: true
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Content-Type", "Authorization", "X-Request-ID", "X-API-Version"]
  max_age: 3600

# Monitoring - comprehensive production monitoring
monitoring:
  enable_metrics: true
  enable_tracing: true
  enable_profiling: false  # Disable in production for performance
  enable_alerting: true
  enable_business_intelligence: true
  
  # Health check configuration
  health_check_interval_seconds: 60
  health_check_timeout_seconds: 30
  
  # Metrics configuration
  metrics_retention_days: 90
  detailed_metrics_enabled: true

# SSL/TLS configuration - enforce HTTPS
ssl:
  enforce_https: true
  hsts_max_age: 31536000
  hsts_include_subdomains: true
  hsts_preload: true
  
  # Certificate settings
  tls_version: "1.2"
  cipher_suites: "ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS"

# Data retention - compliance and legal requirements
data_retention:
  # Financial data retention (7 years)
  financial_data_days: 2555
  audit_logs_days: 2555
  user_activity_logs_days: 1095
  session_data_days: 90
  temp_files_hours: 24
  
  # Automated cleanup
  enable_automated_cleanup: true
  cleanup_schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM

# Backup configuration - critical for production
backup:
  enable_automated_backups: true
  backup_schedule: "0 1 * * *"  # Daily at 1 AM
  full_backup_schedule: "0 1 * * 0"  # Weekly full backup
  retention_policy: "30d"
  encryption_enabled: true
  
  # Backup verification
  enable_backup_verification: true
  verify_backup_integrity: true

# Disaster recovery
disaster_recovery:
  enable_point_in_time_recovery: true
  recovery_point_objective_minutes: 60  # Max 1 hour data loss
  recovery_time_objective_hours: 4      # Max 4 hours downtime
  
  # Multi-region backup
  enable_cross_region_backup: true
  backup_regions: ["us-west-2", "us-east-1"]

# Compliance and auditing
compliance:
  enable_gdpr_compliance: true
  enable_sox_compliance: true
  enable_pci_compliance: false  # Enable if processing payments
  
  # Audit requirements
  enable_comprehensive_auditing: true
  audit_all_data_access: true
  audit_configuration_changes: true
  
# Error handling and alerting
error_handling:
  enable_error_tracking: true
  enable_performance_monitoring: true
  alert_on_error_rate_threshold: 0.01  # Alert if error rate > 1%
  alert_on_response_time_threshold: 2000  # Alert if response time > 2s
  
# Production-specific security measures
security_hardening:
  disable_debug_mode: true
  disable_development_features: true
  enable_request_validation: true
  enable_response_validation: true
  enable_input_sanitization: true
  
  # API security
  enable_api_versioning: true
  require_api_key_for_sensitive_operations: true
  enable_request_signing: false  # Enable if needed for high-security operations