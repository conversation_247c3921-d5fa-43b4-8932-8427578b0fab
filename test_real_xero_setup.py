#!/usr/bin/env python3
"""
Test script demonstrating how to set up and use real Xero data for report generation.

This script shows the complete process:
1. Configure real Xero credentials
2. Complete OAuth authentication
3. Generate reports using real Xero trial balance data
"""

import os
import sys
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demonstrate_real_xero_setup():
    """Demonstrate the complete setup process for real Xero data."""
    
    print("🚀 MCX3D Financials - Real Xero Data Setup Guide")
    print("=" * 60)
    
    # Step 1: Check current configuration
    print("\n📋 Step 1: Current Configuration Status")
    print("-" * 40)
    
    try:
        from mcx3d_finance.core.config import get_xero_config
        config = get_xero_config()
        
        # Check if real credentials are loaded
        if config['client_id'].startswith('475EC4'):
            print("✅ REAL Xero credentials detected!")
            print(f"   Client ID: {config['client_id'][:12]}...")
        else:
            print("⚠️  Development credentials currently loaded")
            print(f"   Client ID: {config['client_id'][:12]}...")
            
        print(f"   Redirect URI: {config['redirect_uri']}")
        print(f"   Scopes: {config['scopes']}")
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    # Step 2: Check organizations
    print("\n🏢 Step 2: Available Organizations")
    print("-" * 40)
    
    try:
        from mcx3d_finance.db.session import get_db
        from mcx3d_finance.db.models import Organization
        
        db = next(get_db())
        orgs = db.query(Organization).all()
        
        for org in orgs:
            auth_status = "🔗 Connected" if org.xero_token else "❌ Not Connected"
            tenant_type = "Real" if org.xero_tenant_id and not org.xero_tenant_id.startswith(('demo-', 'test-', 'mcx3d-sample')) else "Test"
            
            print(f"   ID {org.id}: {org.name}")
            print(f"         Status: {auth_status}")
            print(f"         Type: {tenant_type} Data")
            print(f"         Tenant: {org.xero_tenant_id or 'None'}")
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        db.close()
    
    # Step 3: Demonstrate OAuth flow (if needed)
    print("\n🔐 Step 3: OAuth Authentication Process")
    print("-" * 40)
    
    real_orgs = [org for org in orgs if org.xero_token and org.xero_tenant_id 
                 and not org.xero_tenant_id.startswith(('demo-', 'test-', 'mcx3d-sample'))]
    
    if real_orgs:
        print("✅ Real Xero organizations found:")
        for org in real_orgs:
            print(f"   - {org.name} (ID: {org.id})")
    else:
        print("📝 OAuth authentication required:")
        print("   1. Ensure real Xero credentials are in .env:")
        print("      XERO_CLIENT_ID=475EC4359EA4461DBDB16C4282A67410")
        print("      XERO_CLIENT_SECRET=o7MuRbbBHjqgX...")
        print("   2. Run OAuth flow:")
        print("      python scripts/development/xero_auth_helper.py")
        print("   3. Complete authorization in browser")
        print("   4. Organization will be created/updated with real token")
    
    # Step 4: Demonstrate report generation
    print("\n📊 Step 4: Report Generation with Real Data")
    print("-" * 40)
    
    if real_orgs:
        test_org = real_orgs[0]
        print(f"Using organization: {test_org.name} (ID: {test_org.id})")
        
        try:
            # Import the report generator  
            from mcx3d_finance.reporting.comprehensive_report import ComprehensiveReportGenerator
            
            print("🔄 Testing Xero trial balance retrieval...")
            
            # Create generator with Xero trial balance enabled
            generator = ComprehensiveReportGenerator(
                organization_id=test_org.id, 
                year=2024, 
                use_xero_trial_balance=True
            )
            
            # This would actually fetch real data from Xero
            print("✅ Report generator configured for real Xero data")
            print("   - Will use Xero trial balance as primary data source")
            print("   - Falls back to local data if Xero unavailable")
            print("   - Generates UK FRS 102 compliant reports")
            
        except Exception as e:
            print(f"⚠️  Report generation test failed: {e}")
    else:
        print("⏳ No real Xero organizations available for testing")
        print("   Complete OAuth authentication first")
    
    # Step 5: Show the data flow
    print("\n🔄 Step 5: Data Flow with Real Xero Integration")
    print("-" * 40)
    
    print("Real Data Flow:")
    print("1. 📡 Fetch trial balance from Xero API")
    print("2. 🔄 Transform to UK FRS 102 format")
    print("3. 📊 Generate financial statements")
    print("4. 📈 Calculate financial ratios")
    print("5. 📄 Produce comprehensive reports")
    print()
    print("Fallback Flow (if Xero unavailable):")
    print("1. 📚 Use local transaction data")
    print("2. 🧮 Calculate balances from transactions")
    print("3. 📊 Generate financial statements")
    print("4. 📈 Calculate financial ratios")
    print("5. 📄 Produce comprehensive reports")
    
    return True

def test_xero_data_retrieval(organization_id: int = None):
    """Test actual Xero data retrieval (if authenticated)."""
    
    if not organization_id:
        print("❌ No organization ID provided for testing")
        return
        
    print(f"\n🧪 Testing Xero Data Retrieval for Organization {organization_id}")
    print("-" * 50)
    
    try:
        from mcx3d_finance.integrations.xero_client import XeroClient
        
        # Create Xero client
        xero_client = XeroClient(organization_id)
        
        if not xero_client.api_client:
            print("❌ Xero client not authenticated")
            print("   Complete OAuth flow first")
            return
            
        print("✅ Xero client authenticated")
        
        # Test trial balance retrieval
        print("🔄 Testing trial balance retrieval...")
        trial_balance = xero_client.get_trial_balance(datetime(2024, 12, 31))
        
        if trial_balance and trial_balance.get('accounts'):
            print(f"✅ Trial balance retrieved: {len(trial_balance['accounts'])} accounts")
            
            # Show sample accounts
            print("📋 Sample accounts:")
            for account in trial_balance['accounts'][:3]:
                print(f"   - {account.get('account_name', 'Unknown')}: £{account.get('ytd_balance', 0):,.2f}")
                
        else:
            print("⚠️  Trial balance empty or unavailable")
            
    except Exception as e:
        print(f"❌ Xero data retrieval failed: {e}")
        print("   This is expected if OAuth authentication is not complete")

if __name__ == "__main__":
    print("Running Real Xero Setup Demonstration...")
    
    # Run the demonstration
    success = demonstrate_real_xero_setup()
    
    if success:
        print("\n🎉 Setup Guide Complete!")
        print("Ready to switch to real Xero data once OAuth is complete.")
    else:
        print("\n❌ Setup issues found. Please resolve before proceeding.")
        
    # Test with organization 5 (our real data org)
    test_xero_data_retrieval(5)