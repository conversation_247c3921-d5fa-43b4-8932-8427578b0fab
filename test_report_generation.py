#!/usr/bin/env python3
"""Test script to generate financial reports and verify content"""
import sys
sys.path.append('/app')

from datetime import datetime
from mcx3d_finance.reporting.comprehensive_report import ComprehensiveReportGenerator
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, Transaction
import json


def test_report_generation():
    """Test comprehensive report generation and check for zero values"""
    db = SessionLocal()
    
    try:
        # Get Demo Company Inc (ID: 1)
        org = db.query(Organization).filter(Organization.id == 1).first()
        if not org:
            print("❌ Organization not found!")
            return False
            
        print(f"✓ Found organization: {org.name}")
        
        # Check transaction count
        transaction_count = db.query(Transaction).filter(
            Transaction.organization_id == 1
        ).count()
        print(f"✓ Transaction count: {transaction_count}")
        
        # Generate comprehensive report for 2024
        print("\n📊 Generating comprehensive report for 2024...")
        generator = ComprehensiveReportGenerator(
            organization_id=1, 
            year=2024,
            use_xero_trial_balance=False  # Use database data
        )
        
        report = generator.generate()
        
        # Check income statement
        income_stmt = report.get('income_statement', {})
        turnover = income_stmt.get('turnover', 0)
        print(f"\n💰 Income Statement:")
        print(f"   - Turnover: £{turnover:,.2f}")
        print(f"   - Gross Profit: £{income_stmt.get('gross_profit', 0):,.2f}")
        print(f"   - Operating Profit: £{income_stmt.get('operating_profit', 0):,.2f}")
        
        # Check balance sheet
        balance_sheet = report.get('balance_sheet', {})
        total_assets = balance_sheet.get('total_assets', 0)
        print(f"\n📋 Balance Sheet:")
        print(f"   - Total Assets: £{total_assets:,.2f}")
        print(f"   - Total Liabilities: £{balance_sheet.get('total_liabilities', 0):,.2f}")
        print(f"   - Net Assets: £{balance_sheet.get('net_assets', 0):,.2f}")
        
        # Check cash flow
        cash_flow = report.get('cash_flow_statement', {})
        print(f"\n💸 Cash Flow:")
        print(f"   - Net Cash from Operations: £{cash_flow.get('net_cash_from_operations', 0):,.2f}")
        print(f"   - Net Cash Movement: £{cash_flow.get('net_cash_movement', 0):,.2f}")
        
        # Save report to file
        output_file = '/tmp/test_comprehensive_report_2024.json'
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        print(f"\n✅ Report saved to: {output_file}")
        
        # Check for zero values
        has_non_zero = (
            turnover != 0 or 
            total_assets != 0 or 
            cash_flow.get('net_cash_from_operations', 0) != 0
        )
        
        if has_non_zero:
            print("\n✅ SUCCESS: Report contains non-zero values!")
            return True
        else:
            print("\n❌ ISSUE: All financial values are zero!")
            return False
            
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


if __name__ == "__main__":
    success = test_report_generation()
    sys.exit(0 if success else 1)