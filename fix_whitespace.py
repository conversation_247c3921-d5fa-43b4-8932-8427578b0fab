#!/usr/bin/env python3
"""
Fix whitespace issues in Python files.
"""
import os
import re

def fix_whitespace_issues(file_path):
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Remove trailing whitespace
    lines = content.split('\n')
    fixed_lines = [line.rstrip() for line in lines]
    
    # Ensure file ends with newline
    if fixed_lines and fixed_lines[-1]:
        fixed_lines.append('')
    
    # Remove empty lines that contain only whitespace
    for i, line in enumerate(fixed_lines):
        if line.strip() == '':
            fixed_lines[i] = ''
    
    new_content = '\n'.join(fixed_lines)
    
    if new_content != content:
        with open(file_path, 'w') as f:
            f.write(new_content)
        print(f'Fixed whitespace in {file_path}')

def main():
    # Find all Python files in core directory
    for root, dirs, files in os.walk('mcx3d_finance/core'):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                fix_whitespace_issues(file_path)

if __name__ == '__main__':
    main()