fastapi
uvicorn
slowapi
click
tabulate
sqlalchemy
psycopg2-binary
redis
celery
pandas
numpy
pydantic
pydantic-settings
reportlab
openpyxl
plotly
kaleido
xero-python
requests-oauthlib
pytest
pytest-cov
pytest-asyncio
pytest-benchmark
pytest-mock
pytest-xdist
httpx
PyPDF2
memory-profiler
psutil
python-dotenv
requests
alembic

# Security dependencies
python-jose[cryptography]
passlib[bcrypt]
cryptography
pyotp
bleach
email_validator

# Development tools
black
flake8

# Configuration
PyYAML

# Testing
pytest-asyncio
pytest-mock
httpx
fakeredis[json]
freezegun

# Form data handling
python-multipart

# OAuth2 handling
requests-oauthlib

# CLI tools
rich
typer

# Production Monitoring and Observability
structlog==23.2.0
prometheus-client==0.19.0
python-json-logger==2.0.7
opentelemetry-api==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0
opentelemetry-instrumentation-redis==0.42b0
opentelemetry-instrumentation-celery==0.42b0
grafana-client==3.5.0
aiohttp==3.9.1
