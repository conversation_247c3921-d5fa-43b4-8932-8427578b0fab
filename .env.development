# MCX3D Financials - Development Environment Configuration
# This file is used for Docker development environment

# Environment Configuration
ENVIRONMENT=development
DEBUG=True
LOG_LEVEL=DEBUG
APP_NAME=MCX3D Finance
APP_VERSION=2.0.0

# Database Configuration - DEVELOPMENT (matches docker-compose.yml)
DATABASE_URL=**********************************/mcx3d_db

# Redis Configuration - DEVELOPMENT (matches docker-compose.yml)
REDIS_URL=redis://redis:6379/0

# SECURITY KEYS - Development Only (DO NOT USE IN PRODUCTION)
SECRET_KEY=C7VKA43ZDuTxUKLf#S2RNdcFJ^embDF-9kp_y5n3I2M-*DZCHOw@sy0mf2hm1IVe
ENCRYPTION_KEY=dev_encryption_key_base64_encoded_for_development

# AI API Keys
GEMINI_API_KEY=AIzaSyAXcdQ-__S1dxeME1ejnECedeJBISLKlr8

# JWT Configuration
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Xero OAuth Configuration - DEVELOPMENT
XERO_CLIENT_ID=475EC4359EA4461DBDB16C4282A67410
XERO_CLIENT_SECRET=xE_9bPNWR7qQDb1K-0CwT1KZbqD-sF3O8cg7IuOS2uX9s-9j
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback
XERO_SCOPES=accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access

# Session Management - Relaxed for development
MAX_SESSIONS_PER_USER=10
SESSION_IDLE_TIMEOUT_MINUTES=120

# Account Security - Relaxed for development
MAX_LOGIN_ATTEMPTS=10
LOCKOUT_DURATION_MINUTES=5

# Rate Limiting - Relaxed for development
RATE_LIMIT_DEFAULT=1000
RATE_LIMIT_AUTH=50
RATE_LIMIT_PER_MINUTE=1000
RATE_LIMIT_PER_HOUR=10000

# CORS Configuration - Development Origins
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:8000
CORS_MAX_AGE=3600

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Data Protection & Encryption - Disabled for development
ENABLE_FIELD_ENCRYPTION=false
ENABLE_AUDIT_ENCRYPTION=false

# Performance Configuration - Development
MEMORY_LIMIT_MB=1024
TIMEOUT_SECONDS=120
PARALLEL_WORKERS=2
ENABLE_CACHING=false

# Monitoring and Alerting - Disabled for development
ENABLE_METRICS=false
ENABLE_ALERTING=false
HEALTH_CHECK_INTERVAL_SECONDS=60

# Email Configuration - Development (optional)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=
EMAIL_FROM=<EMAIL>

# SSL/TLS Configuration - Disabled for development
FORCE_HTTPS=false
HSTS_MAX_AGE=0
SECURE_COOKIES=false