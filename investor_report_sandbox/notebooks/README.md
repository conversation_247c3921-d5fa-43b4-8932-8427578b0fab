# Investor Presentation Notebooks

This directory contains Jupyter notebooks for investor presentations and financial analysis.

## 📓 Available Notebooks

### modular_cx_investor_analysis_consolidated.ipynb

A comprehensive investor analysis notebook featuring:
- Historical performance from 2020-2025
- Quarterly and annual revenue analysis
- Customer concentration with April 2025 cutoff
- Unit economics and SaaS metrics
- Financial position and cash flow analysis
- Investment requirements and projections

## 🔌 Data Integration

All notebooks use the `NotebookDataBridge` to access calculated metrics:

```python
from analysis_unified.notebook_data_bridge import NotebookDataBridge
data_bridge = NotebookDataBridge()
```

### Key Methods:
- `get_historical_revenue_timeline()` - Revenue from bank transactions
- `get_quarterly_metrics()` - Quarterly P&L aggregation
- `get_annual_metrics()` - Annual performance with growth rates
- `get_customer_timeline()` - Customer analysis with churn cutoff

## 📊 Data Sources

- **Revenue**: Bank transactions (all sources) + P&L statement
- **Expenses**: Bank transactions categorized by type
- **Customers**: Transaction history with April 2025 cutoff
- **Cash Position**: Balance sheet reconciliation

## 🚀 Running the Notebooks

```bash
# Start Jupyter
jupyter notebook modular_cx_investor_analysis_consolidated.ipynb
```

## ⚠️ Important Notes

1. All calculations come from the unified calculation engine
2. No hardcoded values - everything is data-driven
3. April 2025 cutoff applied to customer analysis
4. Bank transactions used for historical timeline (includes unbilled revenue)

## 📈 Key Visualizations

- Annual revenue evolution
- Quarterly performance trends
- Customer concentration analysis
- Unit economics comparison
- Cash runway projection
- Financial health assessment

---

For technical details, see `/analysis_unified/notebook_data_bridge.py`