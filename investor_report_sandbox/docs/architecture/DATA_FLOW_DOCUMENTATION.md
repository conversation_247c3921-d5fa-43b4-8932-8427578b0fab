# Modular CX Investor Report Sandbox - Data Flow Documentation

## Overview

This document describes the complete data flow architecture of the Modular CX investor report sandbox, which ensures 100% data-driven financial calculations with no hardcoded values or placeholders.

## Architecture Principles

### 1. Single Source of Truth
- All financial calculations flow through `analysis_unified/core/calculations.py`
- No duplicate calculation logic exists anywhere in the codebase
- All consumers (notebooks, reports, visualizations) use the same calculated metrics

### 2. Data-Driven Calculations
- All metrics are calculated from actual Xero JSON data files
- No hardcoded values or arbitrary defaults
- When data is missing, calculations return 0 or handle gracefully

### 3. Configuration-Based Rules
- Business rules defined in `calculation_config.yaml`
- All defaults set to `null` to force calculation from data
- Validation ranges ensure metric sanity

## Data Flow Pipeline

```
┌─────────────────┐
│  JSON Source    │
│     Files       │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│   DataLoader    │ ← Loads and validates JSON files
│ (data_loader.py)│
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Data Validation │ ← NEW: Pre-calculation validation layer
│  (validator.py) │   • Checks data sufficiency
│                 │   • Validates minimum requirements
│                 │   • Ensures data quality
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│FinancialCalc   │ ← Central calculation engine
│(calculations.py)│   • Dynamic time period calculation
│                 │   • Error on insufficient data
│                 │   • Configuration-based rules
└────────┬────────┘
         │
         ├────────────────────┬────────────────────┬─────────────────┐
         ▼                    ▼                    ▼                 ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐
│   Analyzers     │  │  Data Bridge    │  │   Validator     │  │Visualizations│
│(revenue,expense,│  │(notebook_data_  │  │(post-calc      │  │(graphs)      │
│ unit_economics) │  │   bridge.py)    │  │ validation)     │  │              │
└─────────────────┘  └────────┬────────┘  └─────────────────┘  └──────────────┘
                              │
                              ▼
                     ┌─────────────────┐
                     │ Jupyter         │
                     │ Notebooks       │
                     └────────┬────────┘
                              │
                              ▼
                     ┌─────────────────┐
                     │  PDF Report     │ ← Single source of truth for reporting
                     │(pdf_generator)  │   • Historical analysis (2020-2025)
                     │                 │   • Quarterly/annual breakdowns
                     │                 │   • Cohort analysis
                     │                 │   • Risk mitigation
                     │                 │   • Dynamic narratives
                     └─────────────────┘
                              ↑
                     [ARCHIVED 2025-07-28]
                     Hardcoded markdown reports
                     (moved to output/reports/archive_2025_07_28/)
```

## Component Details

### 1. JSON Source Files (data/)
- **invoices_latest.json**: Customer invoices for revenue calculations
- **profit_loss_latest.json**: P&L statement from Xero
- **balance_sheet_latest.json**: Balance sheet for validation
- **bank_transactions_latest.json**: Cash flow data
- **contacts_latest.json**: Customer information
- **accounts_latest.json**: Chart of accounts
- **trial_balance_latest.json**: Trial balance
- **organization_latest.json**: Organization details

### 2. DataLoader (analysis_unified/core/data_loader.py)
- Loads all JSON files from the data directory
- Validates data structure and completeness
- Provides unified data object to calculator

### 3. FinancialCalculator (analysis_unified/core/calculations.py)

#### Revenue Metrics (_calculate_revenue_metrics)
```python
# From actual invoice data:
- total_revenue: Sum of all PAID invoices
- revenue_by_customer: Revenue grouped by customer
- revenue_by_currency: Multi-currency support
- customer_count: Unique customers with paid invoices
- concentration_risk: Top customer revenue percentage
```

#### Expense Metrics (_calculate_expense_metrics)
```python
# From P&L data:
- total_expenses: Sum of all operating expenses
- expense_categories: Breakdown by category
- largest_expense: Highest expense category
- salary_percentage: Salaries as % of total expenses
```

#### Unit Economics (_calculate_unit_economics)
```python
# CAC Calculation:
- Marketing expenses + 30% of salaries
- Divided by number of customers
- NO arbitrary minimums

# LTV Calculation:
- Average monthly revenue per customer
- Multiplied by gross margin (from P&L)
- Divided by churn rate (calculated from data)
- If churn is 0, capped at 24 months

# Key Ratios:
- ltv_cac_ratio: LTV / CAC
- payback_months: CAC / (Monthly Revenue × Gross Margin)
```

#### Churn Calculation (_calculate_churn_from_data)
```python
# From customer retention patterns:
- Tracks customers active each month
- Calculates who churned (active → inactive)
- Returns average monthly churn rate
- Capped at 30% maximum
- Returns 0 if insufficient data (<3 months)
```

#### Growth Metrics (_calculate_growth_metrics)
```python
# From historical revenue:
- yoy_growth: Year-over-year growth from invoices
- mom_growth: Month-over-month growth
- customer_growth: Change in customer count
- All based on actual historical data
```

### 4. Data Bridge (analysis_unified/notebook_data_bridge.py)
- Provides clean API for Jupyter notebooks
- Prevents hardcoding in notebooks
- Forces use of calculated metrics
- Core Methods:
  - `get_financial_position()`: Current financial summary
  - `get_revenue_analysis()`: Revenue metrics and trends
  - `get_unit_economics()`: CAC, LTV, ratios
  - `get_growth_metrics()`: Growth rates and projections
- Historical Analysis Methods (NEW 2025-07-28):
  - `get_historical_revenue_timeline()`: Revenue by month/quarter/year
  - `get_quarterly_metrics()`: Quarterly performance analysis
  - `get_annual_metrics()`: Annual metrics with YoY growth
  - `get_customer_timeline()`: Active vs churned customer analysis
  - `get_cohort_analysis()`: Customer cohorts by acquisition month
  - `get_retention_curves()`: Retention curves by cohort
  - `get_historical_unit_economics()`: Unit economics evolution

### 5. Validators (analysis_unified/core/validator.py)

#### Multi-Tier Validation:
1. **Data Completeness**: Checks for required fields
2. **Revenue Validation**: Cross-checks invoices vs P&L
3. **Balance Sheet**: Assets = Liabilities + Equity
4. **Unit Economics**: LTV/CAC ratio sanity (0.5-10.0)
5. **Cash Position**: Validates across sources
6. **Temporal Consistency**: Date validation

### 6. Configuration (analysis_unified/config/calculation_config.yaml)
```yaml
defaults:
  customer:
    monthly_churn_rate: null  # Must calculate from data
  financial:
    gross_margin: null        # Must calculate from P&L
    minimum_cac: null         # No minimum - use actual
  growth:
    default_yoy_growth: null  # Must calculate from history

validation:
  ranges:
    ltv_cac_ratio:
      min: 0.5
      max: 10.0
      healthy_min: 3.0
    monthly_churn:
      max: 0.3  # 30% cap
    min_months_for_churn_calc: 3
```

### 7. Testing (tests/test_financial_calculations.py)
- Comprehensive unit tests for all calculations
- Uses actual Xero data structures
- Tests edge cases (empty data, zero division)
- Validates metric ranges
- Ensures no hardcoded values

## Key Calculation Formulas

### Financial Metrics
- **MRR**: Monthly Recurring Revenue = Total Revenue / 12
- **ARR**: Annual Recurring Revenue = MRR × 12
- **Gross Margin**: (Revenue - COGS) / Revenue × 100
- **Net Margin**: Net Profit / Revenue × 100
- **Rule of 40**: Growth Rate + Operating Margin

### Unit Economics
- **CAC**: (Marketing + 0.3 × Salaries) / New Customers
- **LTV**: (Revenue per Customer × Gross Margin) / Churn Rate
- **Payback**: CAC / (Monthly Revenue × Gross Margin)
- **NRR**: (Starting MRR + Expansion - Churn) / Starting MRR

### Growth Rates
- **YoY**: ((Current Year - Prior Year) / Prior Year) × 100
- **MoM**: ((Current Month - Prior Month) / Prior Month) × 100

## Usage Examples

### Command Line
```bash
# Full analysis
python analyze.py --full

# Specific analyses
python analyze.py --revenue --unit-economics

# Comprehensive investor report (NEW 2025-07-28)
python analyze.py --investor-report
```

### Programmatic Access
```python
from analysis_unified.financial_analyzer import UnifiedFinancialAnalyzer

analyzer = UnifiedFinancialAnalyzer()
results = analyzer.run_analysis()
```

### Jupyter Notebooks
```python
from analysis_unified.notebook_data_bridge import get_financial_data

data = get_financial_data()
revenue = data['revenue_analysis']
unit_economics = data['unit_economics']
```

## Data Validation Layer (Enhanced 2025-07-28)

### Pre-Calculation Validation
The system now includes a comprehensive validation layer that runs before calculations:

1. **Data Sufficiency Check** (`_validate_data_sufficiency`)
   - Verifies minimum 3 months of data available
   - Counts unique customers (minimum 5 required)
   - Checks for critical data files (P&L, invoices)
   - **Action**: Raises clear errors if requirements not met

2. **Dynamic Time Period Calculation** (`_calculate_data_period_months`)
   - Scans all invoices and bank transactions for date range
   - Calculates actual months of data (not assumed)
   - **Action**: Used for accurate monthly averages

3. **Configuration Integration**
   - Reads from `calculation_config.yaml`
   - Applies business rules (churn cutoff, active window)
   - **Action**: Makes calculations configurable without code changes

### Error Propagation
```
Data Issue → Clear Error Message → User Action Required
                    ↓
         "Insufficient data period: 2.1 months (minimum 3 required)"
         "Cannot calculate LTV: Gross margin not available from P&L data"
         "Insufficient data: Only 3 customers found (minimum 5 required)"
```

## Data Quality Assurance

### Validation Rules (Updated)
1. **Data Period**: Minimum 3 months required (no longer assumed 12)
2. **Customer Count**: Minimum 5 for statistical relevance
3. **Customer Concentration**: Warning if >50% from one customer
4. **Churn Rate**: Calculated, not defaulted (April 2025 cutoff)
5. **LTV/CAC**: Warning if <3.0
6. **Gross Margin**: Must come from P&L data (no 80% default)

### Error Handling (Enhanced)
- **No Silent Defaults**: System raises errors instead
- **Clear Messages**: Actionable error descriptions
- **Fail Fast**: Validation before calculation
- **Configuration-Based**: Business rules externalized

## Maintenance Guidelines

### Adding New Metrics
1. Add calculation method to `FinancialCalculator`
2. Update configuration if new rules needed
3. Add validation in `Validator`
4. Create unit tests
5. Update data bridge if needed

### Modifying Calculations
1. Never hardcode values - always calculate from data
2. Update tests to reflect new logic
3. Document formula in config
4. Ensure backward compatibility

### Data Updates
1. Place new JSON files in `data/` directory
2. Run validation: `python analyze.py --validate`
3. Review warnings before proceeding
4. Generate new reports

## Troubleshooting

### Common Issues

1. **Zero Metrics**
   - Check JSON files exist in data/
   - Verify invoice status is PAID
   - Check date parsing in logs

2. **High Churn Rate**
   - Need minimum 3 months data
   - Check customer ID consistency
   - Review calculation cap (30%)

3. **Missing P&L Data**
   - Verify profit_loss_latest.json structure
   - Check report type is "ProfitAndLoss"
   - Review section titles match

4. **Validation Failures**
   - Check financial_analysis.log
   - Review validation report in output
   - Verify data completeness

## Security Considerations

1. **No Hardcoded Credentials**: All auth via environment
2. **Data Sanitization**: Customer names cleaned
3. **No Sensitive Data in Logs**: Only aggregates logged
4. **Output Directory Permissions**: Restricted access

## Reporting Consolidation (2025-07-28)

### PDF Report as Single Source of Truth
The PDF generator (`analysis_unified/reports/pdf_generator.py`) is now the authoritative source for all investor reporting:

1. **Comprehensive Sections**:
   - Executive Summary (dynamic based on metrics)
   - Financial Overview (current period)
   - Revenue Analysis (with customer breakdown)
   - Expense Analysis (with efficiency metrics)
   - Unit Economics (CAC, LTV, cohort analysis)
   - Cash Flow & Runway
   - Historical Performance (NEW - 2020-2025 trends)
   - Quarterly Analysis (NEW - quarter-by-quarter breakdown)
   - Cohort Analysis (NEW - customer retention by cohort)
   - Risk Mitigation (NEW - strategic risk assessment)
   - Financial Projections
   - Appendix: Data Sources (NEW - transparency)

2. **Dynamic Narratives**:
   - Executive summary adapts based on actual performance
   - Highlights key strengths and areas for improvement
   - No hardcoded text - all generated from metrics

3. **Historical Context**:
   - Multi-year revenue and expense trends
   - Quarterly performance comparisons
   - Customer acquisition and retention patterns
   - Unit economics evolution over time

### Archived Reports
The following hardcoded markdown reports have been archived to `output/reports/archive_2025_07_28/`:
- `executive_summary_one_pager.md` - Contained incorrect LTV/CAC ratio of 4.8:1
- `financial_reports_2020_2025.md` - Had hardcoded revenue figures
- `company_narrative_2020_2025.md` - Static narrative not based on data

### Enhanced Visualizations
The visualization suite now includes 8 comprehensive charts:
1. `revenue_forecast.png` - Historical and projected revenue
2. `expense_breakdown.png` - Category breakdown with efficiency metrics
3. `cash_flow_waterfall.png` - Cash flow visualization with runway
4. `unit_economics_dashboard.png` - CAC, LTV, and cohort metrics
5. `customer_concentration.png` - Customer revenue distribution
6. `cash_runway_analysis.png` - Runway projection scenarios
7. `scenario_comparison.png` - Multiple growth scenarios
8. `cash_efficiency_dashboard.png` - Efficiency metrics
9. `financial_health_dashboard.png` (NEW) - Overall health score
10. `margin_benchmarks.png` (NEW) - Company vs industry margins

## Future Enhancements

1. **Real-time Xero Integration**: Direct API instead of JSON
2. **Predictive Analytics**: ML-based projections
3. **Automated Alerts**: Threshold monitoring
4. **Multi-Company Support**: Consolidated reporting
5. **API Endpoints**: RESTful access to metrics
6. **Interactive Dashboards**: Web-based reporting interface
7. **Automated Report Distribution**: Email/Slack integration

---

Last Updated: July 2025
Version: 4.0 (Reporting Consolidation)