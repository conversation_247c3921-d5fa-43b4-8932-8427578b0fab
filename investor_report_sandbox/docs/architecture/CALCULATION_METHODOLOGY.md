# Modular CX Financial Calculation Methodology

## Executive Summary

This document details the exact calculation methodology used in the Modular CX investor report sandbox. All calculations are 100% data-driven with no hardcoded values, ensuring accuracy and transparency for investor reporting.

## Core Principles

1. **Data Primacy**: Every metric is calculated from source data (Xero JSON files)
2. **No Hardcoding**: Zero hardcoded values or arbitrary defaults
3. **Explicit Failure**: Insufficient data raises clear errors, not silent defaults
4. **Data Sufficiency**: Minimum data requirements enforced before calculations
5. **Full Transparency**: All formulas documented and testable
6. **Single Source**: One calculation engine for consistency
7. **Configuration-Driven**: Business rules externalized to configuration files

## Data Sufficiency Requirements

Before any calculations proceed, the system validates data sufficiency:

### Minimum Requirements
- **Time Period**: At least 3 months of transaction data
- **Customer Count**: Minimum 5 customers for reliable metrics
- **Critical Files**: P&L and invoice data must be present

### Dynamic Time Period Calculation
```python
def calculate_data_period_months(data):
    """Calculate actual months from transaction dates"""
    date_range = get_date_range(data)
    if not date_range['min_date'] or not date_range['max_date']:
        raise ValueError("Cannot determine data period: No dated transactions found")
    
    delta = date_range['max_date'] - date_range['min_date']
    months = delta.days / 30.44  # Average days per month
    
    min_months = CONFIG['data_requirements']['min_months_of_data']
    if months < min_months:
        raise ValueError(f"Insufficient data period: {months:.1f} months (minimum {min_months} required)")
    
    return months
```

## Detailed Calculation Methods

### 1. Revenue Calculations

#### Total Revenue
```python
def calculate_total_revenue(invoices):
    """Sum of all PAID or AUTHORISED invoices"""
    total = 0
    for invoice in invoices:
        if invoice['Status'] in ['PAID', 'AUTHORISED']:
            total += float(invoice['Total'])
    return total
```

#### Customer Concentration Risk
```python
def calculate_concentration_risk(revenue_by_customer, total_revenue):
    """Percentage of revenue from largest customer"""
    if total_revenue == 0:
        return 0
    max_customer_revenue = max(customer['total'] for customer in revenue_by_customer)
    return (max_customer_revenue / total_revenue) * 100
```

#### Multi-Currency Support
- Revenue tracked by currency code from invoices
- No automatic conversion - preserves original currency
- Reporting in GBP with explicit conversion when needed

### 2. Expense Calculations

#### Total Operating Expenses
```python
def calculate_expenses(profit_loss_data):
    """Extract from P&L Operating Expenses section"""
    total = 0
    for section in profit_loss['Rows']:
        if 'Operating Expenses' in section['Title']:
            for row in section['Rows']:
                if row['RowType'] == 'Row':
                    total += float(row['Cells'][1]['Value'])
    return total
```

#### Expense Categories
- Parsed directly from P&L line items
- No categorization assumptions
- Preserves Xero's account structure

### 3. Profitability Metrics

#### Gross Margin
```python
def calculate_gross_margin(revenue, cogs, pl_data):
    """Calculate from P&L data, with service business validation"""
    # First, try to get from P&L data
    gross_margin_pct = pl_data.get('gross_margin_pct')
    if gross_margin_pct is None:
        raise ValueError("Cannot calculate gross margin: Not available from P&L data")
    
    # For service businesses with no COGS, 100% is valid
    if cogs == 0:
        logger.info("No COGS found - treating as pure service business with 100% gross margin")
        return 100.0
    
    return gross_margin_pct
```

#### Operating Margin
```python
def calculate_operating_margin(revenue, operating_expenses):
    """Operating profit as percentage of revenue"""
    if revenue == 0:
        return 0
    operating_profit = revenue - operating_expenses
    return (operating_profit / revenue) * 100
```

### 4. Unit Economics

#### Customer Acquisition Cost (CAC)
```python
def calculate_cac(expenses, customer_count):
    """CEO-led sales model - only direct marketing costs"""
    # Only count direct marketing/advertising expenses
    marketing_spend = sum(amount for name, amount in expenses.items() 
                         if any(term in name.lower() for term in 
                               ['marketing', 'advertising', 'ads', 'promotion']))
    
    # For CEO-led sales, we don't allocate salary to CAC
    # This reflects the actual business model
    
    if customer_count == 0:
        raise ValueError("Cannot calculate CAC: No customers found")
    
    return marketing_spend / customer_count
```

#### Customer Lifetime Value (LTV)
```python
def calculate_ltv(data, revenue_metrics, gross_margin, churn_rate):
    """Standard SaaS LTV formula with dynamic time period"""
    # Calculate actual data period from transactions
    months = calculate_data_period_months(data)
    
    # Calculate monthly revenue per customer
    total_revenue = revenue_metrics['total_revenue']
    customer_count = revenue_metrics['customer_count']
    monthly_revenue = total_revenue / months
    avg_monthly_revenue_per_customer = monthly_revenue / customer_count
    
    # Gross margin must come from P&L data
    if gross_margin is None:
        raise ValueError("Cannot calculate LTV: Gross margin not available from P&L data")
    
    # Calculate churn-based LTV
    if churn_rate == 0:
        # If truly zero churn, cap at 24 months
        return avg_monthly_revenue_per_customer * gross_margin * 24
    
    # LTV = (Monthly Revenue × Margin) / Monthly Churn
    return (avg_monthly_revenue_per_customer * gross_margin) / churn_rate
```

#### LTV/CAC Ratio
```python
def calculate_ltv_cac_ratio(ltv, cac):
    """Key SaaS health metric"""
    if cac == 0:
        return 0
    return ltv / cac
```

#### CAC Payback Period
```python
def calculate_payback_months(cac, monthly_revenue, gross_margin):
    """Months to recover customer acquisition cost"""
    monthly_contribution = monthly_revenue * gross_margin
    if monthly_contribution == 0:
        return 0
    return cac / monthly_contribution
```

### 5. Churn Calculation

#### Monthly Churn Rate
```python
def calculate_churn_from_data(data):
    """Calculate from actual customer retention with configurable parameters"""
    # Get configuration
    cutoff_date = datetime.strptime(CONFIG['churn_calculation']['cutoff_date'], '%Y-%m-%d')
    active_window_months = CONFIG['churn_calculation']['active_customer_window_months']
    min_customers = CONFIG['churn_calculation']['min_customers_for_calculation']
    
    # Validate data availability
    if not data.get('invoices') and not data.get('bank_transactions'):
        raise ValueError("Insufficient data: No invoice or bank transaction data available")
    
    # Track last payment date for each customer
    customer_last_payment = {}
    
    # Process invoices and bank transactions
    # ... (collect payment dates)
    
    # Classify customers as active or churned
    active_threshold = cutoff_date - timedelta(days=active_window_months * 30.44)
    active_customers = 0
    churned_customers = 0
    
    for customer, last_payment in customer_last_payment.items():
        if last_payment >= active_threshold:
            active_customers += 1
        else:
            churned_customers += 1
    
    total_customers = active_customers + churned_customers
    
    # Validate sufficient customers
    if total_customers < min_customers:
        raise ValueError(f"Insufficient data: Only {total_customers} customers found (minimum {min_customers} required)")
    
    # Calculate annual churn rate
    annual_churn = churned_customers / total_customers
    
    # Convert to monthly
    if annual_churn >= 1:
        raise ValueError(f"Invalid churn rate: {annual_churn:.1%} annual churn >= 100%")
    
    monthly_churn = 1 - pow(1 - annual_churn, 1/12)
    return monthly_churn
```

### 6. Growth Metrics

#### Year-over-Year Growth
```python
def calculate_yoy_growth(revenue_by_year):
    """Compare current vs prior year"""
    if len(revenue_by_year) < 2:
        return 0
    
    years = sorted(revenue_by_year.keys())
    current = revenue_by_year[years[-1]]
    prior = revenue_by_year[years[-2]]
    
    if prior == 0:
        return 0
    return ((current - prior) / prior) * 100
```

#### Month-over-Month Growth
```python
def calculate_mom_growth(revenue_by_month):
    """Compare last two months"""
    if len(revenue_by_month) < 2:
        return 0
    
    months = sorted(revenue_by_month.keys())
    current = revenue_by_month[months[-1]]
    prior = revenue_by_month[months[-2]]
    
    if prior == 0:
        return 0
    return ((current - prior) / prior) * 100
```

### 7. Cash Flow Metrics

#### Monthly Burn Rate
```python
def calculate_burn_rate(bank_transactions):
    """Average monthly cash outflow minus inflow"""
    monthly_flows = {}
    
    for transaction in bank_transactions:
        month = parse_date(transaction['DateString']).strftime('%Y-%m')
        amount = float(transaction['Total'])
        
        if transaction['Type'] == 'RECEIVE':
            monthly_flows[month]['in'] += amount
        else:
            monthly_flows[month]['out'] += amount
    
    # Calculate average burn
    total_burn = 0
    for month in monthly_flows:
        net = monthly_flows[month]['in'] - monthly_flows[month]['out']
        total_burn += max(0, -net)  # Only count negative months
    
    return total_burn / len(monthly_flows)
```

#### Cash Runway
```python
def calculate_runway(current_cash, monthly_burn):
    """Months until cash depleted"""
    if monthly_burn <= 0:
        return float('inf')  # Positive cash flow
    return current_cash / monthly_burn
```

### 8. Financial Health Scores

#### Rule of 40
```python
def calculate_rule_of_40(growth_rate, operating_margin):
    """Growth + Profitability benchmark"""
    return growth_rate + operating_margin
```

#### Efficiency Score
```python
def calculate_efficiency_score(revenue, expenses):
    """Revenue efficiency metric"""
    if revenue == 0:
        return 0
    return ((revenue - expenses) / revenue) * 100
```

## Data Quality Checks

### Validation Thresholds
```yaml
ltv_cac_ratio:
  minimum: 0.5    # Below this is unsustainable
  healthy: 3.0    # Industry benchmark
  maximum: 10.0   # Above this may indicate underinvestment

monthly_churn:
  maximum: 0.3    # 30% cap for data quality
  concern: 0.05   # 5% monthly is high for SaaS

gross_margin:
  minimum: 0.0    # Can't be negative
  typical: 0.8    # 80% common for SaaS
  maximum: 1.0    # 100% for pure service

cac_payback:
  healthy: 12     # 12 months is good
  concern: 18     # 18+ months is concerning
  maximum: 36     # 36+ months is unsustainable
```

### Missing Data Handling
1. **No Data**: Raise explicit error with clear message
2. **Insufficient Data**: Fail fast with specific requirements
3. **Partial Data**: Use if above minimum thresholds, warn about limitations
4. **Invalid Data**: Log error, skip record, continue if still sufficient
5. **Configuration Fallback**: Only for business rules, never for data

### Error Messages
```python
# Examples of clear error messages
"Cannot determine data period: No dated transactions found"
"Insufficient data period: 2.1 months (minimum 3 required)"
"Insufficient data: Only 3 customers found (minimum 5 required)"
"Cannot calculate LTV: Gross margin not available from P&L data"
"Invalid churn rate: 125% annual churn >= 100%"
```

## Testing Methodology

### Unit Test Coverage
- Every calculation method has dedicated tests
- Tests use realistic Xero data structures
- Edge cases explicitly tested
- No hardcoded expected values

### Test Data Examples
```python
# Revenue test
test_invoices = [
    {'Status': 'PAID', 'Total': '1000.00', 'CurrencyCode': 'GBP'},
    {'Status': 'DRAFT', 'Total': '500.00', 'CurrencyCode': 'GBP'},  # Excluded
]
assert calculate_revenue(test_invoices) == 1000.00

# CAC test - NO MINIMUM
expenses = {'Marketing': 1000, 'Salaries': 10000}
customers = 10
assert calculate_cac(expenses, customers) == 400  # (1000 + 0.3*10000) / 10
```

## Implementation Notes

### Date Parsing
```python
def parse_xero_date(date_string):
    """Handle Xero's timestamp format"""
    if '/Date(' in date_string:
        # Extract milliseconds timestamp
        timestamp = int(date_string.split('(')[1].split('+')[0]) / 1000
        return datetime.fromtimestamp(timestamp)
    else:
        # ISO format fallback
        return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
```

### Currency Handling
- All amounts preserved in original currency
- Exchange rates from Xero when available
- GBP reporting with explicit notation

### Performance Optimization
- Single pass through invoice data
- Cached intermediate calculations
- Lazy evaluation where possible

## Configuration System

All business rules and thresholds are externalized to `calculation_config.yaml`:

```yaml
# Data Requirements
data_requirements:
  min_months_of_data: 3
  min_customers_for_metrics: 5

# Churn Calculation
churn_calculation:
  cutoff_date: "2025-04-30"
  active_customer_window_months: 3
  min_customers_for_calculation: 5

# Validation Thresholds
validation:
  data_quality:
    min_months_for_churn_calc: 3
    min_customers_for_metrics: 5
```

## Historical Analysis Methods (NEW - 2025-07-28)

### 9. Revenue Timeline Analysis

#### Monthly/Quarterly/Yearly Aggregation
```python
def calculate_revenue_timeline(bank_transactions, invoices):
    """Aggregate revenue by time periods from all sources"""
    timeline = {
        'monthly': {},
        'quarterly': {},
        'yearly': {},
        'by_customer': {}
    }
    
    # Process bank transactions for complete revenue picture
    for transaction in bank_transactions:
        if transaction['Type'] == 'RECEIVE':
            date = parse_date(transaction['DateString'])
            month_key = date.strftime('%Y-%m')
            quarter_key = f"{date.year}-Q{(date.month-1)//3 + 1}"
            year_key = str(date.year)
            
            amount = float(transaction['Total'])
            timeline['monthly'][month_key] = timeline['monthly'].get(month_key, 0) + amount
            timeline['quarterly'][quarter_key] = timeline['quarterly'].get(quarter_key, 0) + amount
            timeline['yearly'][year_key] = timeline['yearly'].get(year_key, 0) + amount
    
    return timeline
```

### 10. Cohort Analysis

#### Customer Acquisition Cohorts
```python
def calculate_cohort_analysis(revenue_by_customer):
    """Group customers by acquisition month"""
    cohorts = {}
    
    for customer_id, customer_data in revenue_by_customer.items():
        transactions = customer_data['transactions']
        if not transactions:
            continue
        
        # First transaction date = acquisition month
        first_date = min(datetime.fromisoformat(t['date']) for t in transactions)
        cohort_key = first_date.strftime('%Y-%m')
        
        if cohort_key not in cohorts:
            cohorts[cohort_key] = {
                'customers': [],
                'total_revenue': 0,
                'customer_count': 0,
                'retention_by_month': {}
            }
        
        cohorts[cohort_key]['customers'].append(customer_id)
        cohorts[cohort_key]['total_revenue'] += customer_data['total']
        cohorts[cohort_key]['customer_count'] += 1
        
        # Track monthly retention
        for transaction in transactions:
            trans_date = datetime.fromisoformat(transaction['date'])
            months_since = (trans_date.year - first_date.year) * 12 + (trans_date.month - first_date.month)
            cohorts[cohort_key]['retention_by_month'][months_since].add(customer_id)
    
    return cohorts
```

### 11. Retention Curves

#### Cohort Retention Over Time
```python
def calculate_retention_curves(cohort_analysis):
    """Generate retention curves by cohort"""
    retention_curves = {}
    
    for cohort_key, cohort_data in cohort_analysis.items():
        initial_customers = cohort_data['customer_count']
        retention_rates = {}
        
        for month, active_customers in cohort_data['retention_by_month'].items():
            retention_rates[month] = len(active_customers) / initial_customers * 100
        
        retention_curves[cohort_key] = retention_rates
    
    # Calculate average retention curve
    all_months = set()
    for rates in retention_curves.values():
        all_months.update(rates.keys())
    
    average_curve = {}
    for month in sorted(all_months):
        month_retentions = [rates.get(month, 0) for rates in retention_curves.values()]
        if month_retentions:
            average_curve[month] = sum(month_retentions) / len(month_retentions)
    
    return {
        'by_cohort': retention_curves,
        'average_curve': average_curve
    }
```

### 12. Historical Unit Economics Evolution

#### Tracking Unit Economics Over Time
```python
def calculate_historical_unit_economics(quarterly_metrics):
    """Calculate unit economics trends by quarter"""
    historical_ue = {
        'quarterly_trends': {},
        'improvement_metrics': {}
    }
    
    for quarter, metrics in quarterly_metrics.items():
        if metrics['revenue'] > 0:
            # Estimate CAC and LTV for each quarter
            quarterly_cac = metrics['expenses'] * 0.1  # Simplified
            quarterly_ltv = metrics['revenue'] * 3     # Simplified
            
            historical_ue['quarterly_trends'][quarter] = {
                'estimated_cac': quarterly_cac,
                'estimated_ltv': quarterly_ltv,
                'ltv_cac_ratio': quarterly_ltv / quarterly_cac if quarterly_cac > 0 else 0
            }
    
    # Calculate improvement over time
    quarters = sorted(historical_ue['quarterly_trends'].keys())
    if len(quarters) >= 2:
        first = historical_ue['quarterly_trends'][quarters[0]]
        last = historical_ue['quarterly_trends'][quarters[-1]]
        
        historical_ue['improvement_metrics'] = {
            'ltv_cac_improvement': last['ltv_cac_ratio'] - first['ltv_cac_ratio'],
            'cac_reduction': ((first['estimated_cac'] - last['estimated_cac']) / first['estimated_cac'] * 100),
            'ltv_growth': ((last['estimated_ltv'] - first['estimated_ltv']) / first['estimated_ltv'] * 100)
        }
    
    return historical_ue
```

## Change History

### Version 4.0 (2025-07-28) - Reporting Consolidation
- **NEW**: Historical revenue timeline analysis methods
- **NEW**: Customer cohort analysis methodology
- **NEW**: Retention curve calculations
- **NEW**: Historical unit economics tracking
- **NEW**: Quarterly and annual aggregation methods
- **CONSOLIDATED**: Single PDF report as truth source
- **ARCHIVED**: Hardcoded markdown reports removed

### Version 3.0 (2025-07-28)
- **BREAKING**: Removed all silent defaults - now raises errors
- **NEW**: Dynamic time period calculation from actual data
- **NEW**: Configurable churn calculation parameters
- **NEW**: Data sufficiency validation before calculations
- **IMPROVED**: Clear error messages for insufficient data
- **FIXED**: CAC calculation for CEO-led sales model
- **FIXED**: Gross margin requires P&L data

### Version 2.0 (November 2024)
- Removed all hardcoded values
- Implemented data-driven churn calculation
- Added configuration system
- Enhanced validation framework

### Version 1.0 (October 2024)
- Initial implementation
- Basic calculations from Xero data

---

**Important**: This methodology is implemented in `analysis_unified/core/calculations.py` and tested in `tests/test_financial_calculations.py`. Any changes to calculation methods must be reflected in both locations.