# Data Integrity Requirements

## Overview

This document outlines the data integrity requirements for the MCX3D investor report system. The system enforces strict data quality standards to ensure all calculations are truly data-driven with no silent defaults.

## Minimum Data Requirements

### 1. Time Period Requirements
- **Minimum**: 3 months of transaction data
- **Validation**: System calculates actual data period from transaction dates
- **Error Example**: `"Insufficient data period: 2.1 months (minimum 3 required)"`

### 2. Customer Count Requirements
- **Minimum**: 5 customers with payment history
- **Purpose**: Ensures statistical relevance for churn and unit economics
- **Error Example**: `"Insufficient data: Only 3 customers found (minimum 5 required)"`

### 3. Critical Data Files
Required Xero JSON files:
- `profit_loss_latest.json` - For revenue and gross margin
- `invoices_latest.json` - For customer-level analysis
- `bank_transactions_latest.json` - For cash flow and timeline
- `balance_sheet_latest.json` - For financial position

Optional but recommended:
- `contacts_latest.json` - For customer details
- `accounts_latest.json` - For account classification
- `trial_balance_latest.json` - For validation
- `organization_latest.json` - For company details

## Configuration Parameters

All configurable business rules are in `config/calculation_config.yaml`:

```yaml
# Data Requirements
data_requirements:
  min_months_of_data: 3
  min_customers_for_metrics: 5

# Churn Calculation Settings
churn_calculation:
  cutoff_date: "2025-04-30"  # When to measure active vs churned
  active_customer_window_months: 3  # How recent for "active"
  min_customers_for_calculation: 5  # Statistical minimum
```

## Error Handling Strategy

### 1. Fail Fast Principle
- No silent defaults or fallbacks
- Clear, actionable error messages
- Validation before calculation

### 2. Error Message Format
```python
# Pattern: "Cannot [action]: [specific problem]"
"Cannot determine data period: No dated transactions found"
"Cannot calculate LTV: Gross margin not available from P&L data"
"Insufficient data to calculate churn rate: No invoice or bank transaction data available"
```

### 3. Validation Layers
1. **Data Loading**: File existence and JSON validity
2. **Data Sufficiency**: Minimum requirements met
3. **Calculation Prerequisites**: Required fields present
4. **Business Logic**: Values within reasonable bounds

## Pre-Flight Checklist

Before running analysis, verify:

### Data Completeness
- [ ] At least 3 months of historical data
- [ ] 5 or more customers in the system
- [ ] P&L statement with income/expense data
- [ ] Bank transactions or invoices for timeline

### Data Quality
- [ ] Dates are parseable and consistent
- [ ] Customer IDs are consistent across files
- [ ] Currency codes are present
- [ ] Transaction types are classified

### Business Configuration
- [ ] Churn cutoff date is appropriate
- [ ] Active window matches business model
- [ ] Minimum thresholds are reasonable

## Common Issues and Solutions

### Issue: "Cannot determine data period"
**Cause**: No dated transactions in invoice or bank data
**Solution**: Ensure DateString or Date fields are populated

### Issue: "Insufficient customers for churn"
**Cause**: Less than 5 unique customers found
**Solution**: 
- Check invoice Status (must be PAID/AUTHORISED)
- Verify Contact IDs are consistent
- Include bank transaction contacts

### Issue: "Gross margin not available"
**Cause**: P&L data missing or improperly structured
**Solution**: Ensure P&L has standard Xero report structure

### Issue: "Invalid churn rate >= 100%"
**Cause**: All customers classified as churned
**Solution**: Check cutoff date and active window settings

## Validation Process

The system performs validation in this order:

1. **Load Data** (`DataLoader`)
   - Verify file existence
   - Parse JSON successfully
   - Log any missing files

2. **Check Sufficiency** (`FinancialValidator`)
   - Count unique customers
   - Calculate data period
   - Verify critical fields

3. **Calculate Metrics** (`FinancialCalculator`)
   - Use validated data only
   - Raise errors on missing prerequisites
   - Log all assumptions

4. **Generate Report** (`UnifiedFinancialAnalyzer`)
   - Only proceed if validation passes
   - Include validation summary
   - Note any limitations

## Best Practices

### For Developers
1. Always check return values from data methods
2. Use try/except blocks around calculations
3. Log both successes and failures
4. Test with minimal data scenarios

### For Users
1. Run validation before full analysis
2. Address errors in order reported
3. Don't modify configuration without understanding impact
4. Keep Xero data exports current

### For Data Quality
1. Regular exports from Xero (monthly minimum)
2. Consistent customer naming/IDs
3. Complete transaction categorization
4. Accurate invoice status updates

## Monitoring and Alerts

The system logs all validation issues:
- **ERROR**: Data insufficient for calculations
- **WARNING**: Data quality concerns but calculations proceed
- **INFO**: Successful validations and assumptions made

Example log output:
```
ERROR: Insufficient data period: 2.1 months (minimum 3 required)
WARNING: Limited customer data: 6 customers (recommend 10+ for better accuracy)
INFO: Data period calculated: 12.3 months from 2024-01-01 to 2025-01-15
INFO: No COGS found in P&L - treating as pure service business with 100% gross margin
```

## Future Enhancements

Planned improvements to data integrity:
1. Automated data quality scoring
2. Historical data requirement adjustments
3. Industry-specific validation rules
4. Real-time Xero sync validation
5. Predictive data sufficiency warnings

---

**Remember**: The system is designed to fail explicitly rather than produce incorrect results. This protects the integrity of investor communications and ensures all metrics are genuinely data-driven.