# MCX3D Investor Report Calculation Audit

**Date**: 2025-07-28  
**Auditor**: Claude Code Analysis  
**Status**: 🚨 **CRITICAL ISSUES FOUND**

## Executive Summary

The MCX3D investor report contains multiple critical data integrity issues that make it **unsuitable for investor presentation** in its current state. The system claims to be "100% data-driven" but our audit reveals significant hardcoded values and calculation errors.

## Critical Issues

### 1. Revenue Discrepancy (CRITICAL) ❌

**Finding**: $10,696 unexplained difference between data sources
- Invoice Revenue: $73,026
- P&L Revenue: $83,722  
- Variance: 14.6%

**Impact**: Undermines financial credibility
**Root Cause**: Likely timing differences or unbilled revenue
**Recommendation**: Reconcile and document the difference

### 2. Balance Sheet Imbalance (CRITICAL) ❌

**Finding**: Balance sheet doesn't balance by $21,707.58
- Assets: $21,792.58
- Liabilities + Equity: $43,500.16
- Difference: $21,707.58

**Impact**: Fundamental accounting error
**Root Cause**: Data extraction or calculation error
**Recommendation**: Immediate investigation required

### 3. Hardcoded Values (HIGH) ❌

Despite claims of being "100% data-driven", we found:

| Metric | Hardcoded Value | Should Be |
|--------|-----------------|-----------|
| Gross Margin | 80% | 100% (service business) or calculated from P&L |
| Monthly Churn | 5% | Calculated from customer retention data |
| YoY Growth | 20% | Calculated from historical revenue |
| MoM Growth | 5% | Calculated from monthly data |
| Customer Growth | 10% | Calculated from customer counts |
| S&M Salary Allocation | 30% | Based on actual role allocations |

### 4. Inconsistent Calculations (HIGH) ❌

**Burn Rate Confusion**:
- Cash flow shows: $3,767.56/month
- Financial ratios show: $550.79/month
- Difference: $3,216.77 (585% variance!)

**Cash Runway**: Shows 0 months despite negative cash flow

## Detailed Findings

### Revenue Analysis

```
Source Comparison:
- Invoices (PAID/AUTHORISED): $73,026
- P&L Statement Income: $83,722
- Difference: $10,696 (14.6%)

Customer Analysis:
- Total Customers: 15
- Top Customer: UPOD MEDICAL LTD ($14,500 - 19.86%)
- Concentration Risk: Acceptable (<20%)

Currency Mix:
- GBP: $54,140 (74.1%)
- USD: $18,886 (25.9%)
```

**Possible Explanations**:
1. Accrued but unbilled revenue
2. Timing differences (cash vs accrual)
3. Deferred revenue adjustments
4. Currency conversion differences

### Expense Analysis

```
Total Operating Expenses: $79,635.51
- Salaries: $66,536.91 (83.6%) ← Extremely high
- Marketing: $524.26 (0.7%) ← Suspiciously low
- Subscriptions: $5,504.05 (6.9%)
- Other: $6,570.29 (8.8%)
```

**Red Flags**:
- Marketing spend too low for growing SaaS
- Salary percentage indicates potential inefficiency
- No clear R&D allocation

### Unit Economics Audit

#### CAC (Customer Acquisition Cost)
```
Calculation:
Marketing Spend: $524.26
+ 30% of Salaries: $19,961.07 ← ARBITRARY ASSUMPTION
= Total S&M: $20,485.33
÷ Customers: 15
= CAC: $1,365.69
```

**Issues**:
- 30% salary allocation is arbitrary
- No distinction between new vs existing customers
- Marketing spend seems unrealistically low

#### LTV (Lifetime Value)
```
Calculation:
Avg Monthly Revenue/Customer: $405.70
× Gross Margin: 80% ← HARDCODED, SHOULD BE 100%
÷ Monthly Churn: 5% ← HARDCODED DEFAULT
= LTV: $6,491.20
```

**Issues**:
- Gross margin not calculated from actual P&L
- Churn rate appears to be default value
- No evidence of actual retention analysis

#### LTV/CAC Ratio: 4.75
- Looks healthy but based on flawed inputs
- Cannot be trusted for investor decisions

### Growth Metrics Audit

All growth metrics show suspicious round numbers:
- YoY Growth: **Exactly 20%**
- MoM Growth: **Exactly 5%**  
- Customer Growth: **Exactly 10%**

**Probability of natural occurrence**: <0.1%
**Conclusion**: These are hardcoded defaults, not calculated values

### Cash Flow Analysis

```
Total Cash In: $267,922.38
Total Cash Out: $358,343.91
Net Cash Flow: -$90,421.53

Monthly Analysis:
- Method 1 (avg_monthly_burn): $3,767.56
- Method 2 (monthly_burn_rate): $550.79
- Variance: 585%!
```

**Cash Runway**: 0 months (calculation error or no cash balance)

## Code Review Findings

### calculations.py Analysis

The code structure appears sound:
- ✅ Proper calculation methods exist
- ✅ Logic for data-driven calculations present
- ❌ But output suggests defaults are being used instead

### Specific Issues:

1. **Churn Calculation** (lines 492-546):
   - Code exists to calculate from retention
   - Requires 3+ months of data
   - Output shows exactly 5% → insufficient data or override

2. **Gross Margin** (lines 209-216):
   - Code correctly identifies service business → 100%
   - Output shows 80% → override or misconfiguration

3. **Growth Calculation** (lines 398-467):
   - Proper historical analysis code exists
   - Output shows round numbers → defaults being used

## Validation Requirements

Before sharing with investors, the following MUST be addressed:

### P1 - Critical (Block investor sharing)
- [ ] Reconcile revenue discrepancy
- [ ] Fix balance sheet imbalance
- [ ] Replace ALL hardcoded values with calculations

### P2 - High (Fix before sharing)
- [ ] Clarify burn rate calculation
- [ ] Calculate actual churn from data
- [ ] Verify gross margin calculation
- [ ] Calculate real growth rates

### P3 - Medium (Document/explain)
- [ ] Document CAC assumptions
- [ ] Explain high salary percentage
- [ ] Add cash balance for runway calc

## Recommendations

1. **Immediate Actions**:
   - DO NOT share current report with investors
   - Run validation script to identify all issues
   - Fix critical calculation errors

2. **Data Integrity**:
   - Implement automated validation checks
   - Add unit tests for all calculations
   - Document all assumptions clearly

3. **Process Improvements**:
   - Separate "calculated" vs "assumed" metrics
   - Add data quality indicators
   - Implement calculation audit trail

## Conclusion

The current investor report has significant data integrity issues that could damage investor confidence if shared. The discrepancy between the "100% data-driven" claim and the reality of hardcoded values is particularly concerning.

**Recommendation**: **DO NOT SHARE** until all critical issues are resolved.

---

*This audit was performed on 2025-07-28. All findings should be verified and corrected before any external distribution.*