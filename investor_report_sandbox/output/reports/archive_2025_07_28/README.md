# Archived Reports - 2025-07-28

This directory contains reports that have been archived due to containing hardcoded values that do not reflect the actual calculated metrics from our data-driven system.

## Archived Files

### Valuation Documents (Archived from docs/)
1. **MODULAR_CX_VALUATION_STRATEGY.md**
   - Contains incorrect LTV/CAC ratio of 4.8:1 (actual: 131:1)
   - Hardcoded valuations not based on current data
   - Strategic planning document with outdated metrics

2. **VALUATION_EXECUTIVE_BRIEF.md**
   - Contains incorrect LTV/CAC ratio of 4.8:1 (actual: 131:1)
   - Hardcoded metrics throughout
   - Misleading negotiation guidance based on wrong data

3. **VALUATION_SCENARIO_MODEL.md**
   - Contains incorrect LTV/CAC ratio of 4.8:1 (actual: 131:1)
   - Financial projections based on incorrect base metrics
   - Could mislead investors with wrong unit economics

### Previously Archived Markdown Reports
These files were already noted as archived but may exist elsewhere:
- `executive_summary_one_pager.md` - Incorrect LTV/CAC ratio
- `financial_reports_2020_2025.md` - Hardcoded revenue figures
- `company_narrative_2020_2025.md` - Static narrative

## Why These Were Archived

1. **Incorrect Metrics**: All files contained the wrong LTV/CAC ratio of 4.8:1 when the actual calculated value is 131:1
2. **Hardcoded Values**: Values were not dynamically calculated from actual data
3. **Misleading Information**: Could confuse or mislead investors with incorrect unit economics
4. **Not Data-Driven**: Violated our principle of 100% data-driven calculations

## Current Authoritative Source

The single source of truth for all reporting is now:
- **PDF Report**: `output/reports/modular_cx_financial_report.pdf`
- **Generated by**: `analysis_unified/reports/pdf_generator.py`
- **Features**: 
  - 100% data-driven calculations
  - Historical analysis (2020-2025)
  - Dynamic narratives based on actual metrics
  - Comprehensive 12-section report

## Recommendation

Do not use any files in this archive directory for investor communications. All reports should be generated fresh using:

```bash
python analyze.py --investor-report
```

This ensures all metrics are calculated from the latest Xero data with no hardcoded values.