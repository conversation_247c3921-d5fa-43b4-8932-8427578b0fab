#!/usr/bin/env python3
"""
Unified Financial Analysis CLI
Single entry point for all financial analysis operations

Usage:
    python analyze.py --full               # Run complete analysis
    python analyze.py --revenue            # Revenue analysis only
    python analyze.py --expenses           # Expense analysis only
    python analyze.py --unit-economics     # Unit economics analysis
    python analyze.py --projections        # Financial projections
    python analyze.py --validate           # Validate data only
"""

import argparse
import sys
import json
from pathlib import Path
from datetime import datetime

# Add the analysis_unified directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from analysis_unified.financial_analyzer import UnifiedFinancialAnalyzer

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description='Modular CX Unified Financial Analysis Tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python analyze.py --full                    # Run complete analysis
  python analyze.py --revenue --output-dir results  # Revenue analysis with custom output
  python analyze.py --validate                # Check data quality only
  python analyze.py --list-analyses           # Show available analysis types
        """
    )
    
    # Analysis options
    analysis_group = parser.add_mutually_exclusive_group(required=True)
    analysis_group.add_argument('--full', action='store_true',
                               help='Run complete financial analysis')
    analysis_group.add_argument('--revenue', action='store_true',
                               help='Run revenue analysis only')
    analysis_group.add_argument('--expenses', action='store_true',
                               help='Run expense analysis only')
    analysis_group.add_argument('--cashflow', action='store_true',
                               help='Run cash flow analysis only')
    analysis_group.add_argument('--unit-economics', action='store_true',
                               help='Run unit economics analysis only')
    analysis_group.add_argument('--projections', action='store_true',
                               help='Run financial projections only')
    analysis_group.add_argument('--validate', action='store_true',
                               help='Validate data quality only')
    analysis_group.add_argument('--investor-report', action='store_true',
                               help='Generate comprehensive investor report with all historical data')
    analysis_group.add_argument('--list-analyses', action='store_true',
                               help='List all available analysis types')
    
    # Configuration options
    parser.add_argument('--data-dir', type=str, default='data',
                       help='Directory containing financial data files (default: data)')
    parser.add_argument('--output-dir', type=str, default='output',
                       help='Directory for analysis output (default: output)')
    parser.add_argument('--format', choices=['json', 'summary', 'detailed'], default='summary',
                       help='Output format (default: summary)')
    parser.add_argument('--no-pdf', action='store_true',
                       help='Skip PDF report generation')
    parser.add_argument('--no-charts', action='store_true',
                       help='Skip chart generation')
    parser.add_argument('--quiet', action='store_true',
                       help='Minimal output')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    # Handle list-analyses
    if args.list_analyses:
        print("\nAvailable Analysis Types:")
        print("-" * 40)
        print("  --full           Complete financial analysis")
        print("  --revenue        Revenue patterns and customer analysis")
        print("  --expenses       Expense categories and burn rate")
        print("  --cashflow       Cash flow statements and runway")
        print("  --unit-economics LTV, CAC, and unit metrics")
        print("  --projections    Growth scenarios and forecasts")
        print("  --validate       Data quality validation only")
        print("  --investor-report Comprehensive CFO-level investor report")
        print()
        return 0
    
    # Initialize analyzer
    try:
        analyzer = UnifiedFinancialAnalyzer(args.data_dir, args.output_dir)
    except Exception as e:
        print(f"Error initializing analyzer: {e}")
        return 1
    
    # Determine analysis type
    if args.validate:
        result = run_validation(analyzer, args)
    elif args.full:
        result = run_full_analysis(analyzer, args)
    elif args.investor_report:
        result = run_investor_report(analyzer, args)
    else:
        # Single analysis type
        analysis_type = get_analysis_type(args)
        result = run_specific_analysis(analyzer, analysis_type, args)
    
    # Display results
    display_results(result, args)
    
    return 0 if result.get('status') == 'success' else 1

def get_analysis_type(args):
    """Determine which analysis type was requested"""
    if args.revenue:
        return 'revenue'
    elif args.expenses:
        return 'expenses'
    elif args.cashflow:
        return 'cashflow'
    elif args.unit_economics:
        return 'unit-economics'
    elif args.projections:
        return 'projections'
    return None

def run_validation(analyzer, args):
    """Run data validation only"""
    if not args.quiet:
        print("Running data validation...")
    
    # Load data
    data = analyzer.data_loader.load_all_data()
    validation_report = analyzer.data_loader.get_validation_report()
    
    # Run validator
    analyzer.calculations = analyzer.calculator.calculate_all_metrics(data)
    full_validation = analyzer.validator.validate_all(data, analyzer.calculations)
    
    return {
        'status': 'success' if full_validation['status'] != 'FAIL' else 'error',
        'validation': full_validation,
        'data_loading': validation_report
    }

def run_full_analysis(analyzer, args):
    """Run complete analysis"""
    if not args.quiet:
        print("Running complete financial analysis...")
        print(f"Data directory: {args.data_dir}")
        print(f"Output directory: {args.output_dir}")
        print("-" * 50)
    
    # Configure analyzer based on args
    if args.no_pdf:
        analyzer.skip_pdf = True
    if args.no_charts:
        analyzer.skip_charts = True
    
    # Run analysis
    result = analyzer.run_full_analysis()
    
    return result

def run_specific_analysis(analyzer, analysis_type, args):
    """Run specific analysis type"""
    if not args.quiet:
        print(f"Running {analysis_type} analysis...")
    
    result = analyzer.run_specific_analysis(analysis_type)
    
    return result

def run_investor_report(analyzer, args):
    """Run comprehensive investor report generation"""
    if not args.quiet:
        print("Generating comprehensive investor report...")
        print("This includes:")
        print("  • Historical performance analysis (2020-2025)")
        print("  • Quarterly and annual breakdowns")
        print("  • Customer cohort analysis")
        print("  • Unit economics evolution")
        print("  • Risk analysis and mitigation")
        print("  • Financial projections")
        print()
    
    # Force full analysis with all visualizations
    original_no_pdf = args.no_pdf
    original_no_charts = args.no_charts
    
    # Ensure we generate everything for investor report
    args.no_pdf = False
    args.no_charts = False
    
    # Run full analysis which includes everything
    result = analyzer.run_full_analysis()
    
    # Restore original settings
    args.no_pdf = original_no_pdf
    args.no_charts = original_no_charts
    
    # Add investor report specific flag to result
    if result.get('status') == 'success':
        result['report_type'] = 'investor_report'
        result['report_description'] = 'Comprehensive CFO-level investor report with historical analysis'
        
        if not args.quiet:
            print("\n✅ Investor report generated successfully!")
            print(f"📄 PDF Report: {result.get('outputs', {}).get('pdf_report', 'N/A')}")
            print(f"📊 Visualizations: {result.get('outputs', {}).get('visualizations', 'N/A')}")
            print(f"📋 Full Analysis: {result.get('outputs', {}).get('main_results', 'N/A')}")
    
    return result

def display_results(result, args):
    """Display results based on format preference"""
    if args.format == 'json':
        print(json.dumps(result, indent=2, default=str))
    elif args.format == 'detailed':
        display_detailed_results(result, args)
    else:  # summary
        display_summary_results(result, args)

def display_summary_results(result, args):
    """Display summary of results"""
    if args.quiet:
        return
    
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE")
    print("="*60)
    
    if result.get('status') == 'success':
        print(f"✅ Status: SUCCESS")
        
        # Show runtime
        if 'runtime_seconds' in result:
            print(f"⏱️  Runtime: {result['runtime_seconds']:.1f} seconds")
        
        # Show summary metrics
        if 'summary' in result:
            print("\n📊 Key Metrics:")
            for key, value in result['summary'].items():
                print(f"  • {key.replace('_', ' ').title()}: {value}")
        
        # Show output locations
        if 'outputs' in result:
            print("\n📁 Output Files:")
            for output_type, path in result['outputs'].items():
                print(f"  • {output_type}: {path}")
        
        # Show validation status
        if 'validation' in result:
            validation = result['validation']
            print(f"\n✓ Validation: {validation.get('status', 'Unknown')}")
            if validation.get('warnings'):
                print(f"  ⚠️  {len(validation['warnings'])} warnings")
            if validation.get('errors'):
                print(f"  ❌ {len(validation['errors'])} errors")
    else:
        print(f"❌ Status: ERROR")
        if 'errors' in result:
            print("\nErrors:")
            for error in result['errors']:
                print(f"  • {error}")

def display_detailed_results(result, args):
    """Display detailed results"""
    display_summary_results(result, args)
    
    # Add validation details
    if 'validation' in result and not args.quiet:
        validation = result['validation']
        if validation.get('errors'):
            print("\n❌ Validation Errors:")
            for error in validation['errors']:
                print(f"  • {error}")
        
        if validation.get('warnings'):
            print("\n⚠️  Validation Warnings:")
            for warning in validation['warnings']:
                print(f"  • {warning}")

if __name__ == "__main__":
    sys.exit(main())