# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Running Analysis
```bash
# Activate virtual environment first
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Full financial analysis (recommended)
python analyze.py --full

# Specific analyses
python analyze.py --revenue            # Revenue analysis only
python analyze.py --expenses           # Expense analysis only  
python analyze.py --cashflow           # Cash flow analysis
python analyze.py --unit-economics     # LTV, CAC metrics
python analyze.py --projections        # Financial projections
python analyze.py --validate           # Data validation only

# Comprehensive investor report (NEW - includes historical analysis)
python analyze.py --investor-report    # CFO-level report with 2020-2025 data

# Options
python analyze.py --full --no-pdf      # Skip PDF generation
python analyze.py --full --no-charts   # Skip chart generation
python analyze.py --full --format json # JSON output format
```

### Xero Data Updates
```bash
# Fetch fresh data from Xero (3-step process)
python scripts/xero_auth_step1.py      # Generate OAuth URL
python scripts/xero_auth_step2.py      # Complete authentication
python scripts/xero_fetch_data.py      # Fetch latest data
```

### Testing
```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_financial_calculations.py -v
```

## Architecture

### System Design
The system follows a unified analysis architecture where all calculations flow through a single source of truth (`analysis_unified/`). This replaced a previous multi-script approach to ensure consistency.

**Core Flow**: Data Loading → Calculations → Analysis → Validation → Visualization → Reporting

### Key Components

**Main Orchestrator** (`analysis_unified/financial_analyzer.py`):
- `UnifiedFinancialAnalyzer` class orchestrates all operations
- Manages data flow between components
- Handles error reporting and validation

**Core Modules** (`analysis_unified/core/`):
- `data_loader.py`: Loads and validates all Xero JSON files from `data/` directory
- `calculations.py`: Single source of truth for all financial metrics (revenue, expenses, margins, SaaS metrics) - now 100% data-driven with no hardcoded defaults
- `validator.py`: Multi-tier validation including balance sheet reconciliation, data sufficiency checks, and unit economics sanity checks
- `visualizations.py`: Generates 10 comprehensive financial charts including new financial health dashboard and margin benchmarks

**Analyzers** (`analysis_unified/analyzers/`):
- `revenue_analyzer.py`: Customer concentration, growth trends, multi-currency handling
- `expense_analyzer.py`: Burn rate, category breakdown, efficiency metrics
- `unit_economics.py`: CAC, LTV, payback period, churn calculations
- `cashflow_analyzer.py`: Cash runway and flow analysis (placeholder)
- `projections.py`: Growth scenarios (placeholder)

**Report Generation** (`analysis_unified/reports/`):
- `pdf_generator.py`: Single source of truth for investor reporting - creates comprehensive reports with historical analysis, quarterly breakdowns, cohort analysis, and risk mitigation sections

**Data Bridge** (`analysis_unified/notebook_data_bridge.py`):
- Provides clean API for Jupyter notebooks to access calculated metrics
- Enhanced with historical analysis methods: cohort analysis, retention curves, and unit economics evolution

### Data Requirements
The system expects these JSON files in the `data/` directory:
- `invoices_latest.json`: Invoice data for revenue calculations
- `profit_loss_latest.json`: P&L statement from Xero
- `balance_sheet_latest.json`: Balance sheet for validation
- `bank_transactions_latest.json`: Transaction history
- `contacts_latest.json`: Customer information
- `accounts_latest.json`: Chart of accounts
- `trial_balance_latest.json`: Trial balance
- `organization_latest.json`: Organization details

### Output Structure
```
output/
├── financial_analysis.json              # Complete analysis results
├── reports/
│   ├── modular_cx_financial_report.pdf # Comprehensive investor report
│   └── archive_2025_07_28/             # Archived hardcoded reports
│       ├── executive_summary_one_pager.md
│       ├── financial_reports_2020_2025.md
│       └── company_narrative_2020_2025.md
└── graphs/
    ├── revenue_forecast.png
    ├── expense_breakdown.png
    ├── cash_flow_waterfall.png
    ├── unit_economics_dashboard.png
    ├── customer_concentration.png
    ├── cash_runway_analysis.png
    ├── scenario_comparison.png
    ├── cash_efficiency_dashboard.png
    ├── financial_health_dashboard.png   # NEW - overall health score
    └── margin_benchmarks.png            # NEW - vs industry comparison
```

## Key Business Logic

### Financial Calculations (100% Data-Driven)
- All calculations are UK FRS 102 compliant
- Multi-currency support with GBP as primary reporting currency
- Exchange rates from Xero are used for conversion
- **NO HARDCODED VALUES**: System raises errors when data is insufficient
- Dynamic time period calculation from actual transaction dates
- Minimum data requirements: 3 months of data, 5+ customers

### SaaS Metrics (Calculated from Actual Data)
- **CAC**: Direct marketing spend only (CEO-led sales model)
- **LTV**: (Average revenue per customer × Gross margin) / Churn rate
- **LTV/CAC Ratio**: Currently 131x (exceptional efficiency)
- **Payback Period**: <1 month due to low CAC
- **Churn Rate**: 10.1% monthly (calculated with April 2025 cutoff)
- **Gross Margin**: 100% (service business with no COGS)

### Validation Rules (Enhanced)
- Balance sheet must reconcile (assets = liabilities + equity) ✓
- P&L revenue is primary source (includes unbilled revenue) ✓
- Unit economics ratios must be within reasonable bounds ✓
- All calculations raise explicit errors when data is insufficient ✓
- Configuration-based business rules in `calculation_config.yaml` ✓

## Important Notes

- The `analysis_archive/` directory contains deprecated scripts - DO NOT USE
- The `output/reports/archive_2025_07_28/` directory contains archived hardcoded reports - DO NOT USE
- PDF report (`modular_cx_financial_report.pdf`) is the single source of truth for all reporting
- Never commit `xero_token.json` or credentials
- Virtual environment (`venv_investor/`) is already set up with all dependencies
- Logs are written to `financial_analysis.log` for debugging

## Recent Updates (2025-07-28)

### Data Integrity Enhancements
1. **Removed Silent Defaults**: System now raises clear errors instead of using hardcoded values
2. **Dynamic Time Periods**: Calculates actual data period from transaction dates
3. **Configuration-Based**: Business rules externalized to `calculation_config.yaml`
4. **Enhanced Validation**: Pre-calculation checks ensure data sufficiency

### Report Consolidation
1. **Single Source of Truth**: PDF generator is now the only authoritative report source
2. **Archived Misleading Reports**: 3 hardcoded markdown files moved to archive
3. **Historical Analysis**: New sections for 2020-2025 comprehensive analysis
4. **CFO Perspective**: Professional investor-grade reporting with risk analysis
5. **Dynamic Narratives**: Executive summary adapts based on actual metrics

### New Capabilities
- `--investor-report` command for comprehensive CFO-level reports
- Historical revenue timeline analysis (monthly/quarterly/yearly)
- Customer cohort analysis and retention curves
- Unit economics evolution over time
- Financial health dashboard visualization
- Industry margin benchmark comparisons