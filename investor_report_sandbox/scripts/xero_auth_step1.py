#!/usr/bin/env python3
"""
Step 1: Generate Xero Authorization URL
"""

import os
import json
import secrets
import base64
import hashlib
import webbrowser
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Load environment
env_path = Path(__file__).parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

CLIENT_ID = os.getenv("XERO_CLIENT_ID")
CLIENT_SECRET = os.getenv("XERO_CLIENT_SECRET")
REDIRECT_URI = os.getenv("XERO_REDIRECT_URI", "http://localhost:8000/api/auth/xero/callback")

if not all([CLIENT_ID, CLIENT_SECRET]):
    print("❌ Missing XERO_CLIENT_ID or XERO_CLIENT_SECRET")
    exit(1)

# Generate PKCE parameters
code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
code_challenge = base64.urlsafe_b64encode(
    hashlib.sha256(code_verifier.encode('utf-8')).digest()
).decode('utf-8').rstrip('=')

state = secrets.token_urlsafe(32)

# Save verifier and state for later use
session_data = {
    'code_verifier': code_verifier,
    'state': state,
    'created_at': datetime.now().isoformat()
}

session_file = Path(__file__).parent.parent / 'xero_session.json'
with open(session_file, 'w') as f:
    json.dump(session_data, f, indent=2)

# Generate auth URL
import urllib.parse
params = {
    'response_type': 'code',
    'client_id': CLIENT_ID,
    'redirect_uri': REDIRECT_URI,
    'scope': 'accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access',
    'state': state,
    'code_challenge': code_challenge,
    'code_challenge_method': 'S256'
}

auth_url = "https://login.xero.com/identity/connect/authorize?" + urllib.parse.urlencode(params)

print("🚀 MCX3D Financials - Xero Authorization Step 1")
print("=" * 60)
print(f"Redirect URI: {REDIRECT_URI}")
print("\n✅ Authorization URL generated!")
print("\n📌 Instructions:")
print("1. Opening browser to Xero authorization page...")
print("2. Log in to Xero and select your organization")
print("3. Click 'Allow access' to authorize")
print("4. You'll be redirected to an error page (this is normal)")
print("5. Copy the ENTIRE URL from your browser's address bar")
print("6. Run step2_process_callback.py with that URL")
print("\n🔗 Authorization URL:")
print(auth_url)

try:
    webbrowser.open(auth_url)
    print("\n✅ Browser opened successfully")
except:
    print("\n⚠️ Could not open browser automatically")
    print("Please copy the URL above and open it manually")

print("\n💾 Session data saved to xero_session.json")
print("Next step: Run python scripts/step2_process_callback.py")