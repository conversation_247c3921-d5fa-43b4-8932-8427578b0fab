#!/usr/bin/env python3
"""
Security Audit Script for Investor Report Sandbox
Checks for sensitive data exposure and security best practices
"""

import os
import json
import re
from pathlib import Path
from datetime import datetime

class SecurityAuditor:
    def __init__(self):
        self.base_path = Path(__file__).parent.parent
        self.issues = {
            'critical': [],
            'high': [],
            'medium': [],
            'low': []
        }
        self.sensitive_patterns = {
            'api_key': r'(?i)(api[_-]?key|apikey)\s*[:=]\s*["\']?([a-zA-Z0-9]{20,})',
            'secret': r'(?i)(secret|password|pwd)\s*[:=]\s*["\']?([^\s"\']+)',
            'token': r'(?i)(token|bearer)\s*[:=]\s*["\']?([a-zA-Z0-9\-._~+/]{20,})',
            'client_id': r'(?i)(client[_-]?id)\s*[:=]\s*["\']?([a-zA-Z0-9]{20,})',
            'tenant_id': r'(?i)(tenant[_-]?id)\s*[:=]\s*["\']?([a-f0-9\-]{36})',
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'ip_address': r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            'credit_card': r'\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13})\b'
        }
        
    def audit_all(self):
        """Run comprehensive security audit"""
        print("🔒 Security Audit Starting...")
        print("="*60)
        
        # Check file permissions
        self._check_file_permissions()
        
        # Check for sensitive data in code
        self._scan_code_files()
        
        # Check data files
        self._scan_data_files()
        
        # Check git configuration
        self._check_git_security()
        
        # Check dependencies
        self._check_dependencies()
        
        # Generate report
        self._generate_report()
        
    def _check_file_permissions(self):
        """Check file permissions for sensitive files"""
        sensitive_files = [
            '.env',
            'xero_token.json',
            'xero_session.json',
            '.env.development',
            '.env.production'
        ]
        
        for filename in sensitive_files:
            filepath = self.base_path / filename
            if filepath.exists():
                # Check permissions
                stat = os.stat(filepath)
                mode = oct(stat.st_mode)[-3:]
                
                if mode != '600':  # Should be readable only by owner
                    self.issues['high'].append(
                        f"File {filename} has insecure permissions: {mode} (should be 600)"
                    )
                    
                # Check if tracked by git
                git_check = os.popen(f'git ls-files {filepath}').read()
                if git_check:
                    self.issues['critical'].append(
                        f"Sensitive file {filename} is tracked by git!"
                    )
    
    def _scan_code_files(self):
        """Scan Python files for hardcoded secrets"""
        code_files = list(self.base_path.rglob('*.py'))
        
        for filepath in code_files:
            if 'venv' in str(filepath) or '__pycache__' in str(filepath):
                continue
                
            try:
                with open(filepath, 'r') as f:
                    content = f.read()
                    
                # Check for sensitive patterns
                for pattern_name, pattern in self.sensitive_patterns.items():
                    matches = re.findall(pattern, content)
                    if matches:
                        # Filter out false positives
                        for match in matches:
                            if pattern_name == 'tenant_id' and 'hardcoded' in str(filepath):
                                # Known issue in xero_fetch_data.py
                                self.issues['medium'].append(
                                    f"Hardcoded {pattern_name} in {filepath.relative_to(self.base_path)}"
                                )
                            elif self._is_real_secret(match, pattern_name):
                                self.issues['critical'].append(
                                    f"Potential {pattern_name} exposed in {filepath.relative_to(self.base_path)}"
                                )
                                
            except Exception as e:
                self.issues['low'].append(f"Error scanning {filepath}: {str(e)}")
    
    def _scan_data_files(self):
        """Check data files for sensitive information"""
        data_dir = self.base_path / 'data'
        if not data_dir.exists():
            return
            
        # Check if data files are gitignored
        gitignore_path = self.base_path / '.gitignore'
        gitignored = False
        
        if gitignore_path.exists():
            with open(gitignore_path, 'r') as f:
                gitignore_content = f.read()
                if 'data/*' in gitignore_content or 'data/' in gitignore_content:
                    gitignored = True
        
        if not gitignored:
            self.issues['high'].append("Data directory is not properly gitignored")
        
        # Check for PII in JSON files
        json_files = list(data_dir.glob('*.json'))
        for filepath in json_files:
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                    
                # Check for customer PII
                if 'contacts' in str(filepath).lower():
                    self.issues['medium'].append(
                        f"Customer contact data found in {filepath.name} - ensure proper access controls"
                    )
                    
            except Exception:
                pass
    
    def _check_git_security(self):
        """Check git configuration for security issues"""
        # Check for secrets in git history
        sensitive_files = ['xero_token.json', '.env', 'credentials.json']
        
        for filename in sensitive_files:
            # Check if file ever existed in git history
            git_log = os.popen(f'git log --all --full-history -- "*{filename}"').read()
            if git_log:
                self.issues['high'].append(
                    f"Sensitive file {filename} found in git history - consider rewriting history"
                )
    
    def _check_dependencies(self):
        """Check for known vulnerabilities in dependencies"""
        requirements_file = self.base_path / 'requirements.txt'
        
        if requirements_file.exists():
            # Check for outdated packages
            vulnerable_packages = {
                'requests': '2.31.0',  # Example: minimum secure version
                'urllib3': '2.0.0',
                'cryptography': '41.0.0'
            }
            
            with open(requirements_file, 'r') as f:
                requirements = f.read()
                
            for package, min_version in vulnerable_packages.items():
                if package in requirements:
                    # Simple check - in production use proper version parsing
                    self.issues['low'].append(
                        f"Check {package} version - should be >= {min_version}"
                    )
    
    def _is_real_secret(self, match, pattern_name):
        """Filter out false positives"""
        # Common false positives
        if isinstance(match, tuple):
            value = match[1] if len(match) > 1 else match[0]
        else:
            value = match
            
        false_positives = [
            'example', 'test', 'demo', 'placeholder',
            'your_', 'my_', 'xxx', '...'
        ]
        
        for fp in false_positives:
            if fp in value.lower():
                return False
                
        # Check if it looks like a real secret
        if pattern_name in ['api_key', 'token', 'secret']:
            if len(value) < 10:  # Too short to be real
                return False
                
        return True
    
    def _generate_report(self):
        """Generate security audit report"""
        total_issues = sum(len(issues) for issues in self.issues.values())
        
        print(f"\n📊 Security Audit Summary")
        print("-"*40)
        print(f"Total Issues Found: {total_issues}")
        print(f"  Critical: {len(self.issues['critical'])}")
        print(f"  High: {len(self.issues['high'])}")
        print(f"  Medium: {len(self.issues['medium'])}")
        print(f"  Low: {len(self.issues['low'])}")
        
        # Print issues by severity
        for severity, issues in self.issues.items():
            if issues:
                print(f"\n{severity.upper()} Issues:")
                for issue in issues:
                    icon = {'critical': '🚨', 'high': '❌', 'medium': '⚠️', 'low': 'ℹ️'}[severity]
                    print(f"  {icon} {issue}")
        
        # Recommendations
        print("\n💡 Recommendations:")
        print("  1. Never commit sensitive files (.env, tokens, credentials)")
        print("  2. Use environment variables for all secrets")
        print("  3. Regularly rotate API keys and tokens")
        print("  4. Enable 2FA on all service accounts")
        print("  5. Review and update dependencies regularly")
        print("  6. Use secret scanning in CI/CD pipeline")
        
        # Save report
        report = {
            'audit_date': datetime.now().isoformat(),
            'total_issues': total_issues,
            'issues': self.issues,
            'recommendations': [
                'Never commit sensitive files',
                'Use environment variables',
                'Rotate credentials regularly',
                'Enable 2FA',
                'Update dependencies',
                'Implement CI/CD security scanning'
            ]
        }
        
        report_path = self.base_path / 'security_audit_report.json'
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
            
        print(f"\n✅ Full report saved to: {report_path}")
        
        # Return status
        if self.issues['critical'] or self.issues['high']:
            print("\n🚨 CRITICAL/HIGH SEVERITY ISSUES FOUND - ADDRESS IMMEDIATELY!")
            return False
        elif self.issues['medium']:
            print("\n⚠️  Medium severity issues found - address soon")
            return True
        else:
            print("\n✅ No major security issues found!")
            return True

def main():
    """Run security audit"""
    auditor = SecurityAuditor()
    success = auditor.audit_all()
    
    # Exit with appropriate code
    exit(0 if success else 1)

if __name__ == "__main__":
    main()