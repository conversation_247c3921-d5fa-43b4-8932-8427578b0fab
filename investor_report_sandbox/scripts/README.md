# Xero Integration Scripts

This directory contains the working Xero OAuth 2.0 authentication and data fetching scripts for the Modular CX company.

## Prerequisites

Ensure the following environment variables are set in the parent directory's `.env` file:
- `XERO_CLIENT_ID`
- `XERO_CLIENT_SECRET`
- `XERO_REDIRECT_URI` (default: `http://localhost:8000/api/auth/xero/callback`)
- `DATABASE_URL` (should point to localhost, not Docker hostname)

## Authentication Flow

The authentication process uses OAuth 2.0 with PKCE (Proof Key for Code Exchange) for enhanced security.

### Step 1: Generate Authorization URL
```bash
python scripts/xero_auth_step1.py
```
This script:
- Generates PKCE parameters (code verifier and challenge)
- Creates a secure state parameter
- Saves session data to `xero_session.json`
- Opens your browser to the Xero authorization page
- Outputs the authorization URL if browser doesn't open

### Step 2: Process Callback
After authorizing in Xero:
1. You'll be redirected to an error page (this is normal since we're not running a callback server)
2. Copy the ENTIRE URL from your browser's address bar
3. Run:
```bash
python scripts/xero_auth_step2.py "paste-the-callback-url-here"
```

This script:
- Validates the state parameter for security
- Exchanges the authorization code for an access token
- Saves the token to `xero_token.json`
- Displays the connected organization information

### Step 3: Fetch Data
```bash
python scripts/xero_fetch_data.py
```
This script:
- Uses the saved token from authentication
- Fetches comprehensive data from Xero:
  - Organization details
  - Chart of accounts
  - Contacts (customers and suppliers)
  - Invoices (last 2 years)
  - Bank transactions
  - Trial balance
  - Profit & Loss report
  - Balance sheet
- Saves all data to the `../data/` directory with timestamps

## Important Notes

1. **Stop Docker containers** before authentication to avoid port conflicts:
   ```bash
   docker-compose down
   ```

2. **Token expiry**: Access tokens expire after 30 minutes. Re-run the authentication steps if expired.

3. **Data files**: All fetched data is saved in JSON format in the `data/` directory with both timestamped and "latest" versions.

## Troubleshooting

- **"Invalid or expired authorization state"**: The main application may be intercepting callbacks. Stop Docker containers first.
- **Token expired**: Re-run steps 1 and 2 to get a fresh token.
- **Database connection errors**: Ensure DATABASE_URL uses localhost, not Docker hostname.

## Files Generated

- `xero_session.json`: Temporary session data (deleted after successful auth)
- `xero_token.json`: OAuth access token and refresh token
- `data/*_latest.json`: Latest version of each data type
- `data/*_YYYYMMDD_HHMMSS.json`: Timestamped versions of data
- `data/fetch_summary_*.json`: Summary of each data fetch operation