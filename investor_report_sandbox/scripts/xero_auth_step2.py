#!/usr/bin/env python3
"""
Step 2: Process Xero Callback and Get Token
"""

import os
import sys
import json
import requests
from datetime import datetime, timedelta
from pathlib import Path
from urllib.parse import urlparse, parse_qs
from dotenv import load_dotenv

# Load environment
env_path = Path(__file__).parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

CLIENT_ID = os.getenv("XERO_CLIENT_ID")
CLIENT_SECRET = os.getenv("XERO_CLIENT_SECRET")
REDIRECT_URI = os.getenv("XERO_REDIRECT_URI", "http://localhost:8000/api/auth/xero/callback")

if not all([CLIENT_ID, CLIENT_SECRET]):
    print("❌ Missing XERO_CLIENT_ID or XERO_CLIENT_SECRET")
    exit(1)

print("🚀 MCX3D Financials - Xero Authorization Step 2")
print("=" * 60)

# Get callback URL from command line or prompt
if len(sys.argv) > 1:
    callback_url = sys.argv[1]
else:
    print("Paste the ENTIRE callback URL from your browser:")
    print("(It should start with http://localhost:8000/api/auth/)")
    callback_url = input("\nCallback URL: ").strip()

if not callback_url:
    print("❌ No URL provided")
    exit(1)

# Parse callback URL
parsed = urlparse(callback_url)
params = parse_qs(parsed.query)

if 'error' in params:
    print(f"❌ OAuth error: {params.get('error', ['Unknown'])[0]}")
    if 'error_description' in params:
        print(f"   Description: {params['error_description'][0]}")
    exit(1)
    
if 'code' not in params:
    print("❌ No authorization code in callback URL")
    print("Make sure you copied the entire URL including all parameters")
    exit(1)
    
code = params['code'][0]
state = params.get('state', [''])[0]

print(f"✅ Found authorization code: {code[:10]}...")
print(f"✅ Found state: {state[:10]}...")

# Load session data
session_file = Path(__file__).parent.parent / 'xero_session.json'
if not session_file.exists():
    print("❌ Session file not found. Please run step1_generate_auth_url.py first.")
    exit(1)
    
with open(session_file, 'r') as f:
    session_data = json.load(f)
    
# Validate state
if state != session_data['state']:
    print("❌ State mismatch - this might be an old or invalid callback URL")
    print("Please run step1_generate_auth_url.py again to get a fresh URL")
    exit(1)
    
print("✅ State validated successfully")

# Exchange code for token
code_verifier = session_data['code_verifier']

data = {
    'grant_type': 'authorization_code',
    'client_id': CLIENT_ID,
    'client_secret': CLIENT_SECRET,
    'code': code,
    'redirect_uri': REDIRECT_URI,
    'code_verifier': code_verifier
}

print("\n📡 Exchanging authorization code for access token...")
response = requests.post('https://identity.xero.com/connect/token', data=data)

if response.status_code == 200:
    token = response.json()
    token['expires_at'] = (datetime.now() + timedelta(seconds=token.get('expires_in', 1800))).isoformat()
    
    # Save token
    token_file = Path(__file__).parent.parent / 'xero_token.json'
    with open(token_file, 'w') as f:
        json.dump(token, f, indent=2)
        
    print("✅ Token obtained and saved successfully!")
    print(f"   Access token: {token['access_token'][:20]}...")
    print(f"   Expires in: {token.get('expires_in', 1800)} seconds")
    print(f"   Token saved to: {token_file}")
    
    # Get tenant info
    print("\n📡 Getting tenant information...")
    headers = {
        'Authorization': f"Bearer {token['access_token']}",
        'Accept': 'application/json'
    }
    
    tenant_response = requests.get('https://api.xero.com/connections', headers=headers)
    
    if tenant_response.status_code == 200:
        tenants = tenant_response.json()
        if tenants:
            print("\n✅ Connected organizations:")
            for tenant in tenants:
                print(f"   - {tenant['tenantName']} (ID: {tenant['tenantId']})")
                if 'modular' in tenant['tenantName'].lower():
                    print("     ^ This looks like Modular CX!")
    
    # Clean up session file
    session_file.unlink()
    print("\n🎉 Authentication complete! You can now run fetch_xero_data.py")
    
else:
    print(f"❌ Token exchange failed: {response.status_code}")
    print(f"   Response: {response.text}")
    print("\nTroubleshooting:")
    print("1. Make sure you used the callback URL immediately after authorization")
    print("2. Authorization codes expire quickly (usually within minutes)")
    print("3. Each code can only be used once")
    print("4. Try running step1_generate_auth_url.py again for a fresh authorization")