#!/usr/bin/env python3
"""
Fetch Modular CX Data from Xero
Uses the saved token from manual authentication
"""

import os
import json
import requests
from datetime import datetime, timedelta
from pathlib import Path

def load_token():
    """Load the saved token"""
    token_file = Path(__file__).parent.parent / 'xero_token.json'
    
    if not token_file.exists():
        print("❌ No token file found. Please run the authentication steps first.")
        return None
        
    with open(token_file, 'r') as f:
        token = json.load(f)
        
    # Check if expired
    expires_at = datetime.fromisoformat(token.get('expires_at', ''))
    if expires_at <= datetime.now():
        print("❌ Token expired. Please run the authentication steps again.")
        return None
        
    print(f"✅ Valid token loaded (expires in {int((expires_at - datetime.now()).total_seconds() / 60)} minutes)")
    return token

def fetch_xero_data(access_token, tenant_id):
    """Fetch all data from Xero"""
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Accept': 'application/json',
        'Xero-tenant-id': tenant_id
    }
    
    data = {
        'organization': {
            'tenant_id': tenant_id,
            'name': 'Modular CX',
            'fetch_date': datetime.now().isoformat()
        }
    }
    
    # Get organization details
    print("\n📊 Fetching organization details...")
    response = requests.get('https://api.xero.com/api.xro/2.0/Organisation', headers=headers)
    if response.status_code == 200:
        org_data = response.json().get('Organisations', [])
        if org_data:
            data['organization'].update(org_data[0])
            print(f"✅ Organization: {org_data[0].get('Name')}")
            print(f"   Country: {org_data[0].get('CountryCode')}")
            print(f"   Base Currency: {org_data[0].get('BaseCurrency')}")
    
    # Get accounts
    print("\n📊 Fetching chart of accounts...")
    response = requests.get('https://api.xero.com/api.xro/2.0/Accounts', headers=headers)
    if response.status_code == 200:
        accounts = response.json().get('Accounts', [])
        data['accounts'] = accounts
        print(f"✅ Fetched {len(accounts)} accounts")
        
        # Show some account categories
        account_types = {}
        for acc in accounts:
            acc_type = acc.get('Type', 'Unknown')
            account_types[acc_type] = account_types.get(acc_type, 0) + 1
        
        for acc_type, count in sorted(account_types.items()):
            print(f"   - {acc_type}: {count}")
    
    # Get contacts
    print("\n📊 Fetching contacts...")
    response = requests.get('https://api.xero.com/api.xro/2.0/Contacts', headers=headers)
    if response.status_code == 200:
        contacts = response.json().get('Contacts', [])
        data['contacts'] = contacts
        print(f"✅ Fetched {len(contacts)} contacts")
        
        # Count customers vs suppliers
        customers = sum(1 for c in contacts if c.get('IsCustomer'))
        suppliers = sum(1 for c in contacts if c.get('IsSupplier'))
        print(f"   - Customers: {customers}")
        print(f"   - Suppliers: {suppliers}")
    
    # Get invoices (last 2 years)
    print("\n📊 Fetching invoices (last 2 years)...")
    from_date = datetime.now() - timedelta(days=730)
    where = f"Date >= DateTime({from_date.year},{from_date.month:02d},{from_date.day:02d})"
    
    # Need to add page parameter for large datasets
    page = 1
    all_invoices = []
    
    while True:
        response = requests.get(
            f'https://api.xero.com/api.xro/2.0/Invoices?where={where}&page={page}', 
            headers=headers
        )
        if response.status_code == 200:
            invoice_data = response.json()
            invoices = invoice_data.get('Invoices', [])
            if not invoices:
                break
            all_invoices.extend(invoices)
            print(f"   - Page {page}: {len(invoices)} invoices")
            page += 1
            
            # Safety limit
            if page > 10:
                break
        else:
            print(f"⚠️ Error fetching invoices page {page}: {response.status_code}")
            break
    
    data['invoices'] = all_invoices
    print(f"✅ Total invoices fetched: {len(all_invoices)}")
    
    if all_invoices:
        # Analyze invoice types
        invoice_types = {}
        total_amount = 0
        for inv in all_invoices:
            inv_type = inv.get('Type', 'Unknown')
            invoice_types[inv_type] = invoice_types.get(inv_type, 0) + 1
            total_amount += float(inv.get('Total', 0))
        
        for inv_type, count in sorted(invoice_types.items()):
            print(f"   - {inv_type}: {count}")
        print(f"   - Total Amount: ${total_amount:,.2f}")
    
    # Get bank transactions (last 2 years)
    print("\n📊 Fetching bank transactions...")
    response = requests.get(
        f'https://api.xero.com/api.xro/2.0/BankTransactions?where={where}', 
        headers=headers
    )
    if response.status_code == 200:
        transactions = response.json().get('BankTransactions', [])
        data['bank_transactions'] = transactions
        print(f"✅ Fetched {len(transactions)} bank transactions")
        
        if transactions:
            # Analyze transaction types
            trans_types = {}
            for trans in transactions:
                trans_type = trans.get('Type', 'Unknown')
                trans_types[trans_type] = trans_types.get(trans_type, 0) + 1
            
            for trans_type, count in sorted(trans_types.items()):
                print(f"   - {trans_type}: {count}")
    
    # Get trial balance
    print("\n📊 Fetching trial balance...")
    response = requests.get('https://api.xero.com/api.xro/2.0/Reports/TrialBalance', headers=headers)
    if response.status_code == 200:
        trial_balance = response.json()
        data['trial_balance'] = trial_balance
        print("✅ Fetched trial balance")
    
    # Get profit & loss (last 12 months)
    print("\n📊 Fetching profit & loss report...")
    response = requests.get(
        'https://api.xero.com/api.xro/2.0/Reports/ProfitAndLoss?fromDate=' + 
        (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d') + 
        '&toDate=' + datetime.now().strftime('%Y-%m-%d'),
        headers=headers
    )
    if response.status_code == 200:
        profit_loss = response.json()
        data['profit_loss'] = profit_loss
        print("✅ Fetched profit & loss report")
    
    # Get balance sheet
    print("\n📊 Fetching balance sheet...")
    response = requests.get(
        'https://api.xero.com/api.xro/2.0/Reports/BalanceSheet?date=' + 
        datetime.now().strftime('%Y-%m-%d'),
        headers=headers
    )
    if response.status_code == 200:
        balance_sheet = response.json()
        data['balance_sheet'] = balance_sheet
        print("✅ Fetched balance sheet")
    
    return data

def save_data(data):
    """Save data to JSON files"""
    output_dir = Path(__file__).parent.parent / 'data'
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"\n💾 Saving data to: {output_dir}")
    
    # Save individual data files
    for key, value in data.items():
        if value:
            # Save timestamped version
            file_path = output_dir / f"{key}_{timestamp}.json"
            with open(file_path, 'w') as f:
                json.dump(value, f, indent=2, default=str)
            
            # Save latest version
            latest_path = output_dir / f"{key}_latest.json"
            with open(latest_path, 'w') as f:
                json.dump(value, f, indent=2, default=str)
            
            if isinstance(value, list):
                print(f"✅ Saved {len(value)} {key} records")
            else:
                print(f"✅ Saved {key} data")
    
    # Save summary
    summary = {
        'fetch_timestamp': timestamp,
        'organization': data['organization'],
        'data_summary': {
            'accounts': len(data.get('accounts', [])),
            'contacts': len(data.get('contacts', [])),
            'invoices': len(data.get('invoices', [])),
            'bank_transactions': len(data.get('bank_transactions', [])),
            'reports': ['trial_balance', 'profit_loss', 'balance_sheet']
        }
    }
    
    summary_file = output_dir / f"fetch_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📋 Summary saved to: {summary_file}")

def main():
    print("🚀 MCX3D Financials - Modular CX Data Fetcher")
    print("=" * 60)
    
    # Load token
    token = load_token()
    if not token:
        return
    
    # Hardcode Modular CX tenant ID (we know it from the authentication)
    tenant_id = "22f8acc9-3fea-4bae-9b97-91f90b532eea"
    
    print(f"\n📡 Fetching data for Modular CX (Tenant ID: {tenant_id})")
    
    # Fetch data
    data = fetch_xero_data(token['access_token'], tenant_id)
    
    # Save data
    save_data(data)
    
    print("\n✅ Data fetching completed successfully!")
    print("\n📂 Check the data/ directory for:")
    print("   - accounts_latest.json - Chart of accounts")
    print("   - contacts_latest.json - Customers and suppliers")
    print("   - invoices_latest.json - Sales and purchase invoices")
    print("   - bank_transactions_latest.json - Bank transactions")
    print("   - trial_balance_latest.json - Trial balance report")
    print("   - profit_loss_latest.json - P&L report")
    print("   - balance_sheet_latest.json - Balance sheet")
    print("   - fetch_summary_*.json - Summary of the data fetch")

if __name__ == "__main__":
    main()