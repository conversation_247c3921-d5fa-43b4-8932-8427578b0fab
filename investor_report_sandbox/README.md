# MCX3D LTD Investor Report Sandbox

A comprehensive financial analysis system for MCX3D LTD's investor reporting, featuring 100% data-driven calculations from Xero accounting data.

**Last Updated**: 2025-07-28 - Report Consolidation & Data Integrity Enhancements

## 🎯 Purpose

This sandbox provides accurate, auditable financial analysis for MCX3D LTD's fundraising activities. All metrics are calculated from actual accounting data with no hardcoded values or silent defaults. The system now features comprehensive historical analysis (2020-2025) and professional CFO-level reporting.

## 📊 Key Features

- **100% Data-Driven**: All calculations from Xero data with explicit error handling (no silent defaults)
- **Single Source of Truth**: PDF report generator is the authoritative output (archived misleading markdown reports)
- **Historical Analysis**: Complete 2020-2025 revenue timeline, quarterly breakdowns, and cohort analysis
- **Dynamic Calculations**: Time periods calculated from actual transaction dates, not assumed
- **Enhanced Validation**: Pre-calculation data sufficiency checks (minimum 3 months, 5+ customers)
- **Configuration-Based**: Business rules externalized to `calculation_config.yaml`
- **Professional Reporting**: 12-section PDF with historical analysis, risk mitigation, and dynamic narratives
- **Comprehensive Visualizations**: 10 professional charts including financial health dashboard

## 🏗️ Architecture

```
investor_report_sandbox/
├── analysis_unified/        # Core calculation engine
│   ├── core/               # Base calculations and data processing
│   ├── analyzers/          # Specialized analysis modules
│   ├── reports/            # PDF report generation
│   └── notebook_data_bridge.py  # Jupyter notebook integration
├── data/                   # Xero JSON data files (gitignored)
├── notebooks/              # Jupyter presentations
├── output/                 # Generated reports and visualizations
├── docs/                   # Documentation
├── tests/                  # Test suite
└── scripts/                # Utility scripts
```

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Activate virtual environment
source venv_investor/bin/activate  # On Windows: venv_investor\Scripts\activate
```

### 2. Run Analysis
```bash
# Full analysis with all reports
python analyze.py --full

# Comprehensive investor report (NEW - recommended for fundraising)
python analyze.py --investor-report

# Specific analyses
python analyze.py --revenue         # Revenue analysis only
python analyze.py --expenses        # Expense analysis only
python analyze.py --unit-economics  # SaaS metrics
python analyze.py --validate        # Data validation only
```

### 3. Jupyter Notebook
```bash
# Start Jupyter
jupyter notebook notebooks/modular_cx_investor_analysis_consolidated.ipynb
```

## 📈 Key Metrics (Validated & Data-Driven)

- **Revenue**: £83,722 (P&L total, includes unbilled revenue like Stone Algo)
- **Churn Rate**: 10.1% monthly / 72.2% annual (calculated with April 2025 cutoff)
- **CAC**: £35 (direct marketing spend only - CEO-led sales model)
- **Gross Margin**: 100% (service business with no COGS)
- **LTV/CAC Ratio**: 131x (exceptional capital efficiency)
- **Monthly Burn**: £3,768 (minimal due to lean operations)
- **Balance Sheet**: Balanced ✓ (Assets = Liabilities + Equity)

## 🔄 Data Flow (Enhanced)

1. **Xero Data** → JSON files in `data/`
2. **Data Loader** → Validates and loads all JSON files
3. **Pre-Validation** → Checks data sufficiency (3+ months, 5+ customers)
4. **Calculator** → 100% data-driven calculations (no defaults)
5. **Bank Transactions** → Historical revenue timeline (2020-2025)
6. **P&L Statement** → Primary revenue source (includes unbilled)
7. **Enhanced Bridge** → Historical analysis methods for notebooks
8. **PDF Generator** → Single source of truth with 12 sections
9. **Output** → Comprehensive reports, 10 visualizations, analysis JSON

## 📝 Business Decisions Applied

1. **Revenue Source**: P&L statement as primary (includes unbilled revenue)
2. **Balance Sheet Classification**: 
   - Wise bank accounts → Assets (not equity)
   - Directors' loan → Equity (not liabilities)
3. **Gross Margin**: 100% for service business (no COGS in P&L)
4. **CAC Model**: Direct marketing spend only (CEO-led sales)
5. **Churn Calculation**: 
   - April 2025 cutoff date (configurable)
   - 3-month active window
   - Last payment date = service end
6. **Time Periods**: Calculated dynamically from transaction dates
7. **Minimum Data**: 3 months of data, 5+ customers required

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/ -v

# Run with coverage
python -m pytest tests/ --cov=analysis_unified --cov-report=html
```

## 📚 Documentation

- `docs/architecture/` - System design and data flow
- `docs/implementation/` - Implementation details and fixes
- `docs/validation/` - Validation results and audit trails

## 🔐 Security Notes

- Never commit `xero_token.json` or credentials
- All sensitive data in `data/` is gitignored
- Use environment variables for API keys

## 🤝 Contributing

1. All calculations must be data-driven (no hardcoding)
2. Use the unified calculation engine
3. Maintain comprehensive test coverage
4. Document all business logic decisions

## 📞 Support

For questions about the financial analysis system:
- Technical: Check `docs/architecture/`
- Business Logic: See `docs/implementation/`
- Validation: Review `docs/validation/`

## 🆕 Recent Updates (2025-07-28)

### Data Integrity Enhancements
- ✅ **No Silent Defaults**: System raises errors when data insufficient
- ✅ **Dynamic Time Periods**: Calculated from actual transaction dates
- ✅ **Configuration-Based**: Business rules in `calculation_config.yaml`
- ✅ **Enhanced Validation**: Pre-calculation data sufficiency checks

### Report Consolidation
- ✅ **Single Source of Truth**: PDF generator is authoritative output
- ✅ **Archived Reports**: Misleading markdown files moved to archive
- ✅ **Historical Analysis**: New sections for 2020-2025 comprehensive view
- ✅ **Dynamic Narratives**: Executive summary adapts to actual metrics

### New Capabilities
- 📊 **Financial Health Dashboard**: Overall health score visualization
- 📊 **Margin Benchmarks**: Company vs industry comparison
- 📈 **Cohort Analysis**: Customer retention by acquisition month
- 📈 **Retention Curves**: Visual retention patterns over time
- 📈 **Historical Unit Economics**: Track evolution of key metrics
- 🎯 **Investor Report Command**: `--investor-report` for CFO-level reports

## 📁 Output Structure

```
output/
├── financial_analysis.json              # Complete analysis data
├── reports/
│   ├── modular_cx_financial_report.pdf # Comprehensive PDF report
│   └── archive_2025_07_28/             # Archived hardcoded reports
└── graphs/                             # 10 professional visualizations
```

---

Built with accuracy and transparency for MCX3D LTD's fundraising success.