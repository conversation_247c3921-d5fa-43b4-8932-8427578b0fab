aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiosignal==1.4.0
amqp==5.3.1
annotated-types==0.7.0
anyio==4.9.0
appnope==0.1.4
argon2-cffi==25.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.5
asyncio-throttle==1.0.2
attrs==25.3.0
babel==2.17.0
beautifulsoup4==4.13.4
billiard==4.2.1
bleach==6.2.0
celery==5.5.3
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==*******
click-repl==0.3.0
comm==0.2.3
contourpy==1.3.3
coverage==7.10.0
cycler==0.12.1
debugpy==1.8.15
decorator==5.2.1
defusedxml==0.7.1
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.116.1
fastjsonschema==2.21.1
fonttools==4.59.0
fqdn==1.5.1
frozenlist==1.7.0
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
idna==3.10
iniconfig==2.1.0
ipykernel==6.30.0
ipython==9.4.0
ipython_pygments_lexers==1.1.1
ipywidgets==8.1.7
isoduration==20.11.0
jedi==0.19.2
Jinja2==3.1.6
joblib==1.5.1
json5==0.12.0
jsonpointer==3.0.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.12.0
jupyter-lsp==2.2.6
jupyter_client==8.6.3
jupyter_core==5.8.1
jupyter_server==2.16.0
jupyter_server_terminals==0.5.3
jupyterlab==4.4.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.15
kiwisolver==1.4.8
kombu==5.5.4
lark==1.2.2
Levenshtein==0.27.1
MarkupSafe==3.0.2
matplotlib==3.10.3
matplotlib-inline==0.1.7
mcx3d_finance @ file:///Users/<USER>/Documents/GitHub/mcx3d_financials/v2
mistune==3.1.3
multidict==6.6.3
narwhals==1.48.1
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
notebook==7.4.4
notebook_shim==0.2.4
numpy==2.3.2
oauthlib==3.3.1
openpyxl==3.1.5
overrides==7.7.0
packaging==25.0
pandas==2.3.1
pandocfilters==1.5.1
parso==0.8.4
pexpect==4.9.0
pillow==11.3.0
platformdirs==4.3.8
plotly==6.2.0
pluggy==1.6.0
prometheus_client==0.22.1
prompt_toolkit==3.0.51
propcache==0.3.2
psutil==7.0.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
Pygments==2.19.2
pyparsing==3.2.3
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov==6.2.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-json-logger==3.3.0
pytz==2025.2
PyYAML==6.0.2
pyzmq==27.0.0
RapidFuzz==3.13.0
redis==6.2.0
referencing==0.36.2
reportlab==4.4.3
requests==2.32.4
requests-oauthlib==2.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rfc3987-syntax==1.1.0
rpds-py==0.26.0
scikit-learn==1.7.1
scipy==1.16.0
seaborn==0.13.2
Send2Trash==1.8.3
setuptools==80.9.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette==0.47.2
structlog==25.4.0
terminado==0.18.1
threadpoolctl==3.6.0
tinycss2==1.4.0
tornado==6.5.1
traitlets==5.14.3
types-python-dateutil==2.9.0.20250708
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
uri-template==1.3.0
urllib3==2.5.0
uvicorn==0.35.0
uvloop==0.21.0
vine==5.1.0
watchfiles==1.1.0
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
widgetsnbextension==4.0.14
xero-python==6.1.0
yarl==1.20.1
