#!/usr/bin/env python3
"""
MCX3D Investor Report Validation Scratchpad
Created: 2025-07-28
Purpose: Independent validation of all financial calculations to ensure data integrity

CRITICAL ISSUES TO VALIDATE:
1. Revenue discrepancy: Invoice ($73,026) vs P&L ($83,722) - $10,696 difference
2. Balance sheet doesn't balance: Assets ($21,792.58) ≠ L+E ($43,500.16)
3. Hardcoded values being used instead of data-driven calculations
4. Suspicious round numbers in growth metrics
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List, Tuple
from collections import defaultdict

# Configuration
DATA_DIR = "data"
OUTPUT_DIR = "analysis_output"

class InvestorReportValidator:
    """Independent validator for MCX3D financial calculations"""
    
    def __init__(self):
        self.validation_results = {
            "timestamp": datetime.now().isoformat(),
            "issues": [],
            "warnings": [],
            "passed": [],
            "calculations": {}
        }
        self.data = {}
        
    def load_all_data(self):
        """Load all JSON data files"""
        print("Loading data files...")
        data_files = {
            'invoices': 'invoices_latest.json',
            'profit_loss': 'profit_loss_latest.json',
            'balance_sheet': 'balance_sheet_latest.json',
            'bank_transactions': 'bank_transactions_latest.json',
            'contacts': 'contacts_latest.json',
            'accounts': 'accounts_latest.json',
            'trial_balance': 'trial_balance_latest.json',
            'organization': 'organization_latest.json'
        }
        
        for key, filename in data_files.items():
            filepath = os.path.join(DATA_DIR, filename)
            try:
                with open(filepath, 'r') as f:
                    self.data[key] = json.load(f)
                print(f"✓ Loaded {filename}")
            except Exception as e:
                self.add_issue(f"CRITICAL", f"Failed to load {filename}: {str(e)}")
                
    def add_issue(self, severity: str, message: str, details: Dict = None):
        """Add an issue to validation results"""
        issue = {
            "severity": severity,
            "message": message,
            "details": details or {}
        }
        self.validation_results["issues"].append(issue)
        print(f"❌ {severity}: {message}")
        
    def add_warning(self, message: str, details: Dict = None):
        """Add a warning to validation results"""
        warning = {
            "message": message,
            "details": details or {}
        }
        self.validation_results["warnings"].append(warning)
        print(f"⚠️  WARNING: {message}")
        
    def add_passed(self, test: str, details: Dict = None):
        """Add a passed test to validation results"""
        passed = {
            "test": test,
            "details": details or {}
        }
        self.validation_results["passed"].append(passed)
        print(f"✅ PASSED: {test}")
        
    def validate_revenue(self):
        """Validate revenue calculations and reconcile discrepancies"""
        print("\n" + "="*50)
        print("VALIDATING REVENUE CALCULATIONS")
        print("="*50)
        
        # Calculate invoice revenue
        invoice_revenue = 0
        invoice_count = 0
        customer_revenue = defaultdict(lambda: {"total": 0, "count": 0, "name": ""})
        
        if 'invoices' in self.data:
            # Handle both list and dict formats
            invoices_data = self.data['invoices']
            if isinstance(invoices_data, list):
                invoices = invoices_data
            else:
                invoices = invoices_data.get('Invoices', [])
            
            for invoice in invoices:
                if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                    amount = float(invoice.get('Total', 0))
                    invoice_revenue += amount
                    invoice_count += 1
                    
                    # Track by customer
                    contact = invoice.get('Contact', {})
                    contact_id = contact.get('ContactID', 'Unknown')
                    contact_name = contact.get('Name', 'Unknown')
                    customer_revenue[contact_id]["total"] += amount
                    customer_revenue[contact_id]["count"] += 1
                    customer_revenue[contact_id]["name"] = contact_name
        
        # Get P&L revenue
        pl_revenue = 0
        if 'profit_loss' in self.data:
            for report in self.data['profit_loss'].get('Reports', []):
                if report.get('ReportType') == 'ProfitAndLoss':
                    for section in report.get('Rows', []):
                        if section.get('Title') == 'Income':
                            for row in section.get('Rows', []):
                                if row.get('RowType') == 'SummaryRow' and 'Total Income' in row.get('Cells', [{}])[0].get('Value', ''):
                                    pl_revenue = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
        
        # Compare and analyze
        revenue_diff = pl_revenue - invoice_revenue
        revenue_diff_pct = (revenue_diff / invoice_revenue * 100) if invoice_revenue > 0 else 0
        
        self.validation_results["calculations"]["revenue"] = {
            "invoice_revenue": invoice_revenue,
            "pl_revenue": pl_revenue,
            "difference": revenue_diff,
            "difference_pct": revenue_diff_pct,
            "invoice_count": invoice_count,
            "customer_count": len(customer_revenue)
        }
        
        print(f"\nRevenue Analysis:")
        print(f"  Invoice Revenue: ${invoice_revenue:,.2f}")
        print(f"  P&L Revenue: ${pl_revenue:,.2f}")
        print(f"  Difference: ${revenue_diff:,.2f} ({revenue_diff_pct:.1f}%)")
        
        # System shows invoice revenue as $73,026 and P&L as $83,722
        if abs(invoice_revenue - 73026) > 1:
            self.add_issue("HIGH", f"Invoice revenue calculation mismatch: Got ${invoice_revenue:,.2f}, expected $73,026")
        
        if abs(pl_revenue - 83722) > 1:
            self.add_issue("HIGH", f"P&L revenue extraction mismatch: Got ${pl_revenue:,.2f}, expected $83,722")
        
        if abs(revenue_diff_pct) > 10:
            self.add_issue("CRITICAL", f"Revenue variance between sources: {revenue_diff_pct:.1f}%", {
                "invoice_revenue": invoice_revenue,
                "pl_revenue": pl_revenue,
                "difference": revenue_diff
            })
        else:
            self.add_passed("Revenue variance within acceptable range (<10%)")
            
        # Analyze potential causes of discrepancy
        print("\nPotential causes of revenue discrepancy:")
        print("  1. Timing differences (accrual vs cash basis)")
        print("  2. Unbilled revenue in P&L")
        print("  3. Deferred revenue adjustments")
        print("  4. Currency conversion differences")
        
    def validate_balance_sheet(self):
        """Validate balance sheet equation"""
        print("\n" + "="*50)
        print("VALIDATING BALANCE SHEET")
        print("="*50)
        
        assets = 0
        liabilities = 0
        equity = 0
        
        if 'balance_sheet' in self.data:
            for report in self.data['balance_sheet'].get('Reports', []):
                if report.get('ReportType') == 'BalanceSheet':
                    for section in report.get('Rows', []):
                        title = section.get('Title', '')
                        
                        # Process each row in the section
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                                account_name = row['Cells'][0].get('Value', '')
                                value = float(row['Cells'][1].get('Value', 0))
                                
                                # Categorize based on section title or account name
                                if 'Assets' in title or 'Asset' in account_name:
                                    assets += value
                                elif 'Liabilities' in title or 'Liability' in account_name:
                                    liabilities += value
                                elif 'Equity' in title:
                                    equity += value
                                    
                            elif row.get('RowType') == 'SummaryRow':
                                # Check for section totals
                                if 'Total Assets' in row.get('Cells', [{}])[0].get('Value', ''):
                                    assets = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
                                elif 'Total Liabilities' in row.get('Cells', [{}])[0].get('Value', ''):
                                    liabilities = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
                                elif 'Total Equity' in row.get('Cells', [{}])[0].get('Value', ''):
                                    equity = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
        
        # Check balance sheet equation
        balance_diff = assets - (liabilities + equity)
        
        self.validation_results["calculations"]["balance_sheet"] = {
            "assets": assets,
            "liabilities": liabilities,
            "equity": equity,
            "difference": balance_diff,
            "balanced": abs(balance_diff) < 0.01
        }
        
        print(f"\nBalance Sheet Analysis:")
        print(f"  Assets: ${assets:,.2f}")
        print(f"  Liabilities: ${liabilities:,.2f}")
        print(f"  Equity: ${equity:,.2f}")
        print(f"  L + E: ${liabilities + equity:,.2f}")
        print(f"  Difference: ${balance_diff:,.2f}")
        
        # System shows Assets=$21,792.58, L+E=$43,500.16, Diff=$21,707.58
        if abs(balance_diff) > 0.01:
            self.add_issue("CRITICAL", f"Balance sheet doesn't balance: Difference=${balance_diff:,.2f}", {
                "assets": assets,
                "liabilities": liabilities,
                "equity": equity
            })
        else:
            self.add_passed("Balance sheet equation holds (A = L + E)")
            
    def validate_unit_economics(self):
        """Validate unit economics calculations"""
        print("\n" + "="*50)
        print("VALIDATING UNIT ECONOMICS")
        print("="*50)
        
        # Load system output for comparison
        system_output_path = os.path.join(OUTPUT_DIR, "financial_analysis.json")
        try:
            with open(system_output_path, 'r') as f:
                system_output = json.load(f)
        except:
            self.add_issue("HIGH", "Cannot load system output for comparison")
            return
            
        unit_econ = system_output.get('calculations', {}).get('unit_economics', {})
        
        # Validate gross margin
        print("\n1. Gross Margin Validation:")
        gross_margin = unit_econ.get('gross_margin', 0)
        if gross_margin == 0.8:  # 80%
            self.add_issue("HIGH", "Gross margin appears hardcoded at 80%", {
                "value": gross_margin,
                "expected": "Should be 100% for service business or calculated from P&L"
            })
        
        # Validate churn rate
        print("\n2. Churn Rate Validation:")
        monthly_churn = unit_econ.get('monthly_churn', 0)
        if monthly_churn == 0.05:  # Exactly 5%
            self.add_issue("HIGH", "Monthly churn appears hardcoded at 5%", {
                "value": monthly_churn,
                "note": "Suspiciously exact value - should be calculated from retention data"
            })
            
        # Validate CAC calculation
        print("\n3. CAC Calculation Validation:")
        total_sm_spend = unit_econ.get('total_sm_spend', 0)
        new_customers = unit_econ.get('new_customers', 0)
        avg_cac = unit_econ.get('avg_cac', 0)
        
        # Check if 30% salary allocation is being used
        expenses = system_output.get('calculations', {}).get('expenses', {})
        salaries = expenses.get('expense_categories', {}).get('Salaries', 0)
        marketing = expenses.get('expense_categories', {}).get('Advertising & Marketing', 0)
        
        expected_sm_spend = marketing + (salaries * 0.3)
        
        if abs(total_sm_spend - expected_sm_spend) < 1:
            self.add_warning("CAC uses arbitrary 30% salary allocation to S&M", {
                "marketing_spend": marketing,
                "salary_allocation": salaries * 0.3,
                "total": total_sm_spend
            })
            
        # Validate growth metrics
        print("\n4. Growth Metrics Validation:")
        growth = system_output.get('calculations', {}).get('growth', {})
        yoy = growth.get('yoy_growth', 0)
        mom = growth.get('mom_growth', 0)
        customer_growth = growth.get('customer_growth', 0)
        
        if yoy == 20 and mom == 5 and customer_growth == 10:
            self.add_issue("HIGH", "Growth metrics appear to be hardcoded defaults", {
                "yoy_growth": yoy,
                "mom_growth": mom,
                "customer_growth": customer_growth,
                "note": "All perfectly round numbers - unlikely to be calculated"
            })
            
    def validate_cash_flow(self):
        """Validate cash flow calculations"""
        print("\n" + "="*50)
        print("VALIDATING CASH FLOW")
        print("="*50)
        
        # Load system output
        system_output_path = os.path.join(OUTPUT_DIR, "financial_analysis.json")
        try:
            with open(system_output_path, 'r') as f:
                system_output = json.load(f)
        except:
            return
            
        cash_flow = system_output.get('calculations', {}).get('cash_flow', {})
        ratios = system_output.get('calculations', {}).get('financial_ratios', {})
        
        # Check burn rate inconsistency
        avg_monthly_burn = cash_flow.get('avg_monthly_burn', 0)
        monthly_burn_rate = ratios.get('monthly_burn_rate', 0)
        
        print(f"\nBurn Rate Analysis:")
        print(f"  Cash flow avg_monthly_burn: ${avg_monthly_burn:,.2f}")
        print(f"  Financial ratios monthly_burn_rate: ${monthly_burn_rate:,.2f}")
        
        if abs(avg_monthly_burn - monthly_burn_rate) > 100:
            self.add_issue("HIGH", "Inconsistent burn rate calculations", {
                "cash_flow_burn": avg_monthly_burn,
                "ratios_burn": monthly_burn_rate,
                "difference": abs(avg_monthly_burn - monthly_burn_rate)
            })
            
        # Check runway
        runway = cash_flow.get('cash_runway_months', 0)
        if runway == 0:
            self.add_warning("Cash runway shows 0 months - verify cash balance data", {
                "net_cash_flow": cash_flow.get('net_cash_flow', 0),
                "monthly_burn": avg_monthly_burn
            })
            
    def generate_report(self):
        """Generate final validation report"""
        print("\n" + "="*70)
        print("VALIDATION SUMMARY")
        print("="*70)
        
        print(f"\n✅ Passed Tests: {len(self.validation_results['passed'])}")
        print(f"⚠️  Warnings: {len(self.validation_results['warnings'])}")
        print(f"❌ Issues Found: {len(self.validation_results['issues'])}")
        
        # Count by severity
        severity_counts = defaultdict(int)
        for issue in self.validation_results['issues']:
            severity_counts[issue['severity']] += 1
            
        print("\nIssues by Severity:")
        for severity, count in sorted(severity_counts.items()):
            print(f"  {severity}: {count}")
            
        # Save detailed report
        report_path = "validation_results.json"
        with open(report_path, 'w') as f:
            json.dump(self.validation_results, f, indent=2)
        print(f"\nDetailed report saved to: {report_path}")
        
        # Overall assessment
        critical_count = severity_counts.get('CRITICAL', 0)
        high_count = severity_counts.get('HIGH', 0)
        
        print("\n" + "="*70)
        print("FINAL ASSESSMENT")
        print("="*70)
        
        if critical_count > 0:
            print("❌ FAIL: Critical issues found - DO NOT share with investors")
        elif high_count > 2:
            print("❌ FAIL: Multiple high-severity issues - requires fixes before sharing")
        elif len(self.validation_results['warnings']) > 5:
            print("⚠️  CONDITIONAL PASS: Many warnings - review and fix before sharing")
        else:
            print("✅ PASS: Report appears suitable for investor review")
            
        return self.validation_results


def main():
    """Run all validations"""
    print("MCX3D Investor Report Validation")
    print("================================")
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    validator = InvestorReportValidator()
    
    # Load all data
    validator.load_all_data()
    
    # Run validations
    validator.validate_revenue()
    validator.validate_balance_sheet()
    validator.validate_unit_economics()
    validator.validate_cash_flow()
    
    # Generate report
    results = validator.generate_report()
    
    print(f"\nCompleted: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return results


if __name__ == "__main__":
    main()