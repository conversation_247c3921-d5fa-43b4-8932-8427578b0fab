#!/usr/bin/env python3
"""
Data Reconciliation Script for MCX3D Financial Report
Purpose: Deep dive into data discrepancies and reconcile different sources
"""

import json
import os
from datetime import datetime
from collections import defaultdict

class DataReconciler:
    """Reconcile financial data across different sources"""
    
    def __init__(self):
        self.data = {}
        self.reconciliation_report = {
            "timestamp": datetime.now().isoformat(),
            "revenue_reconciliation": {},
            "balance_sheet_reconciliation": {},
            "unit_economics_reconciliation": {},
            "recommendations": []
        }
        
    def load_data(self):
        """Load all necessary data files"""
        files = ['invoices_latest.json', 'profit_loss_latest.json', 
                 'balance_sheet_latest.json', 'trial_balance_latest.json']
        
        for filename in files:
            filepath = os.path.join('data', filename)
            try:
                with open(filepath, 'r') as f:
                    key = filename.replace('_latest.json', '')
                    self.data[key] = json.load(f)
                    print(f"✓ Loaded {filename}")
            except Exception as e:
                print(f"❌ Failed to load {filename}: {e}")
                
    def reconcile_revenue(self):
        """Deep dive into revenue discrepancy"""
        print("\n" + "="*60)
        print("REVENUE RECONCILIATION ANALYSIS")
        print("="*60)
        
        # Calculate invoice revenue by period
        invoice_revenue_by_month = defaultdict(float)
        invoice_revenue_by_status = defaultdict(float)
        
        invoices = self.data.get('invoices', [])
        for invoice in invoices:
            amount = float(invoice.get('Total', 0))
            status = invoice.get('Status', 'Unknown')
            date_str = invoice.get('DateString', invoice.get('Date', ''))
            
            # Track by status
            invoice_revenue_by_status[status] += amount
            
            # Parse date for monthly breakdown
            if date_str and status in ['PAID', 'AUTHORISED']:
                try:
                    # Handle different date formats
                    if '/Date(' in date_str:
                        timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
                        date = datetime.fromtimestamp(timestamp)
                    else:
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    
                    month_key = date.strftime('%Y-%m')
                    invoice_revenue_by_month[month_key] += amount
                except:
                    pass
        
        # Get P&L revenue details
        pl_revenue = 0
        pl_revenue_items = []
        
        if 'profit_loss' in self.data:
            for report in self.data['profit_loss'].get('Reports', []):
                if report.get('ReportType') == 'ProfitAndLoss':
                    # Get report period
                    report_date = report.get('ReportDate', 'Unknown')
                    
                    for section in report.get('Rows', []):
                        if section.get('Title') == 'Income':
                            for row in section.get('Rows', []):
                                if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                                    item_name = row['Cells'][0].get('Value', 'Unknown')
                                    item_value = float(row['Cells'][1].get('Value', 0))
                                    if item_value > 0:
                                        pl_revenue_items.append({
                                            'name': item_name,
                                            'amount': item_value
                                        })
                                elif row.get('RowType') == 'SummaryRow' and 'Total Income' in row.get('Cells', [{}])[0].get('Value', ''):
                                    pl_revenue = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
        
        # Analysis
        paid_revenue = invoice_revenue_by_status.get('PAID', 0)
        authorised_revenue = invoice_revenue_by_status.get('AUTHORISED', 0)
        invoice_total = paid_revenue + authorised_revenue
        
        print("\nInvoice Revenue Breakdown:")
        print(f"  PAID invoices: ${paid_revenue:,.2f}")
        print(f"  AUTHORISED invoices: ${authorised_revenue:,.2f}")
        print(f"  Total invoice revenue: ${invoice_total:,.2f}")
        
        print("\nInvoice Revenue by Status:")
        for status, amount in sorted(invoice_revenue_by_status.items()):
            print(f"  {status}: ${amount:,.2f}")
            
        print("\nP&L Revenue Items:")
        for item in pl_revenue_items:
            print(f"  {item['name']}: ${item['amount']:,.2f}")
        print(f"  Total P&L revenue: ${pl_revenue:,.2f}")
        
        print("\nRevenue by Month (from invoices):")
        for month in sorted(invoice_revenue_by_month.keys()):
            print(f"  {month}: ${invoice_revenue_by_month[month]:,.2f}")
            
        # Store reconciliation data
        self.reconciliation_report['revenue_reconciliation'] = {
            'invoice_total': invoice_total,
            'pl_total': pl_revenue,
            'difference': pl_revenue - invoice_total,
            'difference_pct': ((pl_revenue - invoice_total) / invoice_total * 100) if invoice_total > 0 else 0,
            'invoice_by_status': dict(invoice_revenue_by_status),
            'pl_items': pl_revenue_items,
            'monthly_breakdown': dict(invoice_revenue_by_month)
        }
        
        # Recommendations
        if abs(pl_revenue - invoice_total) > 1000:
            self.reconciliation_report['recommendations'].append({
                'area': 'Revenue',
                'issue': f'${pl_revenue - invoice_total:,.2f} discrepancy between invoices and P&L',
                'action': 'Review unbilled revenue, accruals, and timing differences'
            })
            
    def reconcile_balance_sheet(self):
        """Deep dive into balance sheet imbalance"""
        print("\n" + "="*60)
        print("BALANCE SHEET RECONCILIATION")
        print("="*60)
        
        # Detailed extraction
        assets_detail = defaultdict(float)
        liabilities_detail = defaultdict(float)
        equity_detail = defaultdict(float)
        
        if 'balance_sheet' in self.data:
            for report in self.data['balance_sheet'].get('Reports', []):
                if report.get('ReportType') == 'BalanceSheet':
                    for section in report.get('Rows', []):
                        section_title = section.get('Title', '')
                        
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                                account_name = row['Cells'][0].get('Value', '')
                                value = float(row['Cells'][1].get('Value', 0))
                                
                                # Categorize based on account name and section
                                if 'asset' in account_name.lower() or 'asset' in section_title.lower():
                                    assets_detail[account_name] = value
                                elif 'liabilit' in account_name.lower() or 'liabilit' in section_title.lower():
                                    liabilities_detail[account_name] = value
                                elif 'equity' in account_name.lower() or 'equity' in section_title.lower():
                                    equity_detail[account_name] = value
                                else:
                                    # Try to categorize by common patterns
                                    if any(term in account_name.lower() for term in ['bank', 'cash', 'receivable', 'prepaid']):
                                        assets_detail[account_name] = value
                                    elif any(term in account_name.lower() for term in ['payable', 'loan', 'debt']):
                                        liabilities_detail[account_name] = value
                                    else:
                                        equity_detail[account_name] = value
        
        # Calculate totals
        total_assets = sum(assets_detail.values())
        total_liabilities = sum(liabilities_detail.values())
        total_equity = sum(equity_detail.values())
        
        print("\nAssets Detail:")
        for account, value in sorted(assets_detail.items()):
            print(f"  {account}: ${value:,.2f}")
        print(f"  Total Assets: ${total_assets:,.2f}")
        
        print("\nLiabilities Detail:")
        for account, value in sorted(liabilities_detail.items()):
            print(f"  {account}: ${value:,.2f}")
        print(f"  Total Liabilities: ${total_liabilities:,.2f}")
        
        print("\nEquity Detail:")
        for account, value in sorted(equity_detail.items()):
            print(f"  {account}: ${value:,.2f}")
        print(f"  Total Equity: ${total_equity:,.2f}")
        
        print("\nBalance Sheet Equation Check:")
        print(f"  Assets: ${total_assets:,.2f}")
        print(f"  Liabilities + Equity: ${total_liabilities + total_equity:,.2f}")
        print(f"  Difference: ${total_assets - (total_liabilities + total_equity):,.2f}")
        
        # Cross-check with trial balance
        if 'trial_balance' in self.data:
            print("\nTrial Balance Cross-Check:")
            tb_debits = 0
            tb_credits = 0
            
            for report in self.data['trial_balance'].get('Reports', []):
                if report.get('ReportType') == 'TrialBalance':
                    for section in report.get('Rows', []):
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 3:
                                account = row['Cells'][0].get('Value', '')
                                debit_val = row['Cells'][1].get('Value', 0)
                                credit_val = row['Cells'][2].get('Value', 0)
                                
                                # Handle empty strings
                                debit = float(debit_val) if debit_val and debit_val != '' else 0
                                credit = float(credit_val) if credit_val and credit_val != '' else 0
                                tb_debits += debit
                                tb_credits += credit
                                
            print(f"  Total Debits: ${tb_debits:,.2f}")
            print(f"  Total Credits: ${tb_credits:,.2f}")
            print(f"  Difference: ${tb_debits - tb_credits:,.2f}")
        
        # Store reconciliation
        self.reconciliation_report['balance_sheet_reconciliation'] = {
            'assets': dict(assets_detail),
            'liabilities': dict(liabilities_detail),
            'equity': dict(equity_detail),
            'total_assets': total_assets,
            'total_liabilities': total_liabilities,
            'total_equity': total_equity,
            'imbalance': total_assets - (total_liabilities + total_equity)
        }
        
        if abs(total_assets - (total_liabilities + total_equity)) > 0.01:
            self.reconciliation_report['recommendations'].append({
                'area': 'Balance Sheet',
                'issue': f'Balance sheet out of balance by ${abs(total_assets - (total_liabilities + total_equity)):,.2f}',
                'action': 'Review account classifications and data extraction logic'
            })
            
    def analyze_hardcoded_values(self):
        """Check for evidence of hardcoded values"""
        print("\n" + "="*60)
        print("HARDCODED VALUES ANALYSIS")
        print("="*60)
        
        # Load system output
        try:
            with open('analysis_output/financial_analysis.json', 'r') as f:
                system_output = json.load(f)
        except:
            print("Cannot load system output")
            return
            
        # Check for suspicious values
        suspicious_values = []
        
        # Check unit economics
        ue = system_output.get('calculations', {}).get('unit_economics', {})
        if ue.get('monthly_churn') == 0.05:
            suspicious_values.append(('Monthly Churn', '5%', 'Exactly matches common default'))
        if ue.get('gross_margin') == 0.8:
            suspicious_values.append(('Gross Margin', '80%', 'Round number, should be 100% for service'))
            
        # Check growth metrics
        growth = system_output.get('calculations', {}).get('growth', {})
        if growth.get('yoy_growth') == 20:
            suspicious_values.append(('YoY Growth', '20%', 'Suspiciously round number'))
        if growth.get('mom_growth') == 5:
            suspicious_values.append(('MoM Growth', '5%', 'Suspiciously round number'))
        if growth.get('customer_growth') == 10:
            suspicious_values.append(('Customer Growth', '10%', 'Suspiciously round number'))
            
        print("\nSuspicious Values Found:")
        for metric, value, reason in suspicious_values:
            print(f"  {metric}: {value} - {reason}")
            
        # Check if churn can be calculated from data
        print("\n\nChecking if churn can be calculated from invoice data...")
        months_with_customers = set()
        
        invoices = self.data.get('invoices', [])
        for invoice in invoices:
            if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                date_str = invoice.get('DateString', invoice.get('Date', ''))
                if date_str:
                    try:
                        if '/Date(' in date_str:
                            timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
                            date = datetime.fromtimestamp(timestamp)
                        else:
                            date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        months_with_customers.add(date.strftime('%Y-%m'))
                    except:
                        pass
                        
        print(f"  Months with customer activity: {len(months_with_customers)}")
        print(f"  Date range: {min(months_with_customers) if months_with_customers else 'N/A'} to {max(months_with_customers) if months_with_customers else 'N/A'}")
        
        if len(months_with_customers) < 3:
            print("  ⚠️  Insufficient data for churn calculation (need 3+ months)")
            self.reconciliation_report['recommendations'].append({
                'area': 'Unit Economics',
                'issue': 'Using default 5% churn due to insufficient data',
                'action': 'Need 3+ months of customer data for accurate churn calculation'
            })
        
        self.reconciliation_report['unit_economics_reconciliation'] = {
            'suspicious_values': suspicious_values,
            'months_of_data': len(months_with_customers)
        }
            
    def generate_report(self):
        """Generate final reconciliation report"""
        print("\n" + "="*60)
        print("RECONCILIATION SUMMARY")
        print("="*60)
        
        # Save report
        with open('reconciliation_report.json', 'w') as f:
            json.dump(self.reconciliation_report, f, indent=2)
            
        print(f"\nRecommendations ({len(self.reconciliation_report['recommendations'])}):")
        for i, rec in enumerate(self.reconciliation_report['recommendations'], 1):
            print(f"\n{i}. {rec['area']}")
            print(f"   Issue: {rec['issue']}")
            print(f"   Action: {rec['action']}")
            
        print("\nDetailed report saved to: reconciliation_report.json")


def main():
    """Run reconciliation analysis"""
    print("MCX3D Data Reconciliation Analysis")
    print("==================================")
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    reconciler = DataReconciler()
    reconciler.load_data()
    reconciler.reconcile_revenue()
    reconciler.reconcile_balance_sheet()
    reconciler.analyze_hardcoded_values()
    reconciler.generate_report()
    
    print(f"\nCompleted: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()