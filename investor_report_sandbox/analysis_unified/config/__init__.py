"""Configuration module for financial calculations."""

import os
import yaml
from typing import Dict, Any

def load_config() -> Dict[str, Any]:
    """Load calculation configuration from YAML file."""
    config_path = os.path.join(os.path.dirname(__file__), 'calculation_config.yaml')
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    return config

# Load config on module import
CONFIG = load_config()