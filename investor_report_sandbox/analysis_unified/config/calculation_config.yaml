# Modular CX Financial Calculation Configuration
# This file defines all business rules and assumptions used in financial calculations
# All values here should be used ONLY when actual data is not available

# Company Information
company:
  name: "MCX3D LTD"
  registration_number: "13325322"
  vat_number: "*********"
  incorporated_date: "2020-12-17"
  reporting_currency: "GBP"

# Calculation Defaults (USE ONLY WHEN DATA NOT AVAILABLE)
defaults:
  # Customer Metrics
  customer:
    # Only use if customer retention data is insufficient
    monthly_churn_rate: null  # Should be calculated from actual retention data
    annual_churn_rate: null   # Should be calculated from actual retention data
    
  # Financial Metrics  
  financial:
    # Gross margin should ALWAYS be calculated from P&L data
    gross_margin: null  # (Revenue - COGS) / Revenue from actual data
    
    # CAC should be calculated from actual S&M expenses
    minimum_cac: null  # No minimum - use actual calculation
    
    # LTV period should be based on actual customer lifetime data
    ltv_months_cap: null  # No cap - use actual customer lifetime
    
  # Growth Metrics
  growth:
    # Growth rates must be calculated from historical data
    default_yoy_growth: null  # Calculate from actual revenue history
    default_mom_growth: null  # Calculate from actual monthly data
    
# Calculation Methods
calculation_methods:
  # Revenue Metrics
  revenue:
    # MRR: Sum of all recurring revenue in current month
    # ARR: MRR * 12
    # ARPU: MRR / Active Customers
    
  # Unit Economics
  unit_economics:
    # CAC: (Sales Expenses + Marketing Expenses) / New Customers Acquired
    # LTV: ARPU * Customer Lifetime (in months) * Gross Margin
    # LTV/CAC Ratio: LTV / CAC
    # Payback Period: CAC / (ARPU * Gross Margin)
    
  # Churn Calculation
  churn:
    # Monthly Churn: (Customers Lost in Month) / (Customers at Start of Month)
    # Annual Churn: 1 - (1 - Monthly Churn)^12
    # Revenue Churn: (MRR Lost - MRR Expansion) / MRR at Start
    
  # Growth Rates
  growth:
    # YoY Growth: ((Current Year Revenue - Prior Year Revenue) / Prior Year Revenue) * 100
    # MoM Growth: ((Current Month Revenue - Prior Month Revenue) / Prior Month Revenue) * 100
    # CAGR: ((Ending Value / Beginning Value)^(1 / Years)) - 1
    
# Validation Rules
validation:
  # Acceptable ranges for sanity checks
  ranges:
    ltv_cac_ratio:
      min: 0.5
      max: 10.0
      healthy_min: 3.0
    
    gross_margin:
      min: 0.0
      max: 1.0
      saas_typical_min: 0.7
      saas_typical_max: 0.95
      
    monthly_churn:
      min: 0.0
      max: 0.3  # 30% monthly churn is extremely high
      healthy_max: 0.05  # 5% is concerning
      
    cac_payback_months:
      min: 1
      max: 36
      healthy_max: 18
      
  # Data quality checks
  data_quality:
    min_months_for_churn_calc: 3  # Need at least 3 months of data
    min_customers_for_metrics: 5   # Need meaningful customer base
    max_customer_concentration: 0.5 # No single customer > 50% of revenue
    
# Time Periods
time_periods:
  fiscal_year_start: "08-01"  # August 1st
  fiscal_year_end: "07-31"    # July 31st

# Churn Calculation Settings
churn_calculation:
  cutoff_date: "2025-04-30"  # Cutoff date for customer activity analysis
  active_customer_window_months: 3  # Months to look back for active customers
  min_customers_for_calculation: 5  # Minimum customers required for reliable churn calculation
  
# Data Requirements
data_requirements:
  min_months_of_data: 3  # Minimum months of data for reliable calculations
  min_customers_for_metrics: 5  # Minimum customers for meaningful metrics
  
# Currency Conversion (if needed)
currency:
  base: "GBP"
  # Exchange rates should be pulled from Xero data when available
  
# Data Sources
data_sources:
  invoices: "invoices_latest.json"
  profit_loss: "profit_loss_latest.json"
  balance_sheet: "balance_sheet_latest.json"
  bank_transactions: "bank_transactions_latest.json"
  contacts: "contacts_latest.json"
  accounts: "accounts_latest.json"
  trial_balance: "trial_balance_latest.json"
  organization: "organization_latest.json"

# Feature Flags
features:
  use_weighted_average_cac: true  # Weight CAC by customer value
  include_expansion_revenue: true  # Include upsells in growth metrics
  exclude_one_time_revenue: true  # Focus on recurring revenue only
  
# Notes
# - This configuration should be loaded by all calculation modules
# - Any hardcoded values in code should reference this config
# - All calculations should prefer actual data over these defaults
# - Defaults marked as 'null' MUST be calculated from data