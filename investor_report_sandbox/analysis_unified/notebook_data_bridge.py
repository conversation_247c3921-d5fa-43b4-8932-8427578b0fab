"""
Notebook Data Bridge
====================
This module provides a clean interface for Jupyter notebooks to access 
calculated financial metrics from the single source of truth.

It ensures all notebooks use the same data pipeline and calculations,
preventing hardcoded values and maintaining consistency.
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional, List

# Add path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_loader import DataLoader
from core.calculations import FinancialCalculator
from core.validator import FinancialValidator
from config import CONFIG

class NotebookDataBridge:
    """Bridge between data processing pipeline and Jupyter notebooks"""
    
    def __init__(self, data_path: Optional[str] = None):
        """
        Initialize the data bridge
        
        Args:
            data_path: Optional path to data directory. If not provided,
                      uses the default from config
        """
        self.data_path = data_path or os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            'data'
        )
        self.data_loader = DataLoader(self.data_path)
        self.calculator = FinancialCalculator()
        self.validator = FinancialValidator()
        
        self._data = None
        self._metrics = None
        self._validation_results = None
        self._last_refresh = None
        
    def refresh_data(self) -> Dict[str, Any]:
        """
        Load fresh data from JSON files and recalculate all metrics
        
        Returns:
            Dictionary containing all calculated metrics
        """
        print("Loading data from source files...")
        self._data = self.data_loader.load_all_data()
        
        print("Calculating financial metrics...")
        self._metrics = self.calculator.calculate_all_metrics(self._data)
        
        print("Validating data integrity...")
        self._validation_results = self.validator.validate_all(self._data, self._metrics)
        
        self._last_refresh = datetime.now()
        
        print(f"Data refreshed at {self._last_refresh}")
        return self._metrics
    
    def get_metrics(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get all calculated metrics
        
        Args:
            force_refresh: If True, forces a data refresh even if data exists
            
        Returns:
            Dictionary containing all financial metrics
        """
        if force_refresh or self._metrics is None:
            self.refresh_data()
        
        return self._metrics
    
    def get_revenue_metrics(self) -> Dict[str, Any]:
        """Get revenue-specific metrics"""
        metrics = self.get_metrics()
        return metrics.get('revenue', {})
    
    def get_expense_metrics(self) -> Dict[str, Any]:
        """Get expense-specific metrics"""
        metrics = self.get_metrics()
        return metrics.get('expenses', {})
    
    def get_unit_economics(self) -> Dict[str, Any]:
        """Get unit economics metrics"""
        metrics = self.get_metrics()
        return metrics.get('unit_economics', {})
    
    def get_cash_flow_metrics(self) -> Dict[str, Any]:
        """Get cash flow metrics"""
        metrics = self.get_metrics()
        return metrics.get('cash_flow', {})
    
    def get_growth_metrics(self) -> Dict[str, Any]:
        """Get growth metrics"""
        metrics = self.get_metrics()
        return metrics.get('growth', {})
    
    def get_financial_position(self) -> Dict[str, Any]:
        """Get current financial position summary"""
        metrics = self.get_metrics()
        
        # Extract key financial position data
        revenue = metrics.get('revenue', {})
        expenses = metrics.get('expenses', {})
        cash_flow = metrics.get('cash_flow', {})
        pl = metrics.get('profit_loss', {})
        ratios = metrics.get('financial_ratios', {})
        
        # Get balance sheet data
        balance_sheet_data = self._data.get('balance_sheet', {}) if self._data else {}
        cash_position = self._extract_cash_position(balance_sheet_data)
        
        return {
            'total_revenue': revenue.get('total_revenue', 0),
            'total_expenses': expenses.get('total_expenses', 0),
            'net_profit': pl.get('net_profit', 0),
            'gross_margin_pct': pl.get('gross_margin_pct', 0),
            'net_margin_pct': pl.get('net_margin_pct', 0),
            'cash_position': cash_position,
            'monthly_burn': ratios.get('monthly_burn_rate', 0),
            'runway_months': cash_position / ratios.get('monthly_burn_rate', 1) if ratios.get('monthly_burn_rate', 0) > 0 else 0,
            'customer_count': revenue.get('customer_count', 0),
            'mrr': ratios.get('monthly_revenue', 0),
            'arr': ratios.get('monthly_revenue', 0) * 12
        }
    
    def get_valuation_metrics(self) -> Dict[str, Any]:
        """Get metrics specifically for valuation analysis"""
        position = self.get_financial_position()
        unit_econ = self.get_unit_economics()
        growth = self.get_growth_metrics()
        
        return {
            'revenue_annual': position['total_revenue'],
            'revenue_monthly': position['mrr'],
            'arr': position['arr'],
            'expenses_annual': position['total_expenses'],
            'expenses_monthly': position['total_expenses'] / 12,
            'net_profit': position['net_profit'],
            'gross_margin': position['gross_margin_pct'],
            'net_margin': position['net_margin_pct'],
            'total_cash': position['cash_position'],
            'monthly_burn': position['monthly_burn'],
            'runway_months': position['runway_months'],
            'customer_count': position['customer_count'],
            'ltv': unit_econ.get('avg_ltv', 0),
            'cac': unit_econ.get('avg_cac', 0),
            'ltv_cac_ratio': unit_econ.get('ltv_cac_ratio', 0),
            'payback_months': unit_econ.get('payback_months', 0),
            'yoy_growth': growth.get('yoy_growth', 0),
            'mom_growth': growth.get('mom_growth', 0),
            'customer_growth': growth.get('customer_growth', 0)
        }
    
    def get_validation_report(self) -> Dict[str, Any]:
        """Get data validation results"""
        if self._validation_results is None:
            self.refresh_data()
        
        return self._validation_results
    
    def export_for_notebook(self) -> Dict[str, Any]:
        """
        Export all data in a format optimized for notebook usage
        
        Returns:
            Dictionary with all metrics organized for easy notebook access
        """
        return {
            'metadata': {
                'last_refresh': self._last_refresh.isoformat() if self._last_refresh else None,
                'data_path': self.data_path,
                'company': CONFIG['company']
            },
            'financial_position': self.get_financial_position(),
            'revenue': self.get_revenue_metrics(),
            'expenses': self.get_expense_metrics(),
            'unit_economics': self.get_unit_economics(),
            'cash_flow': self.get_cash_flow_metrics(),
            'growth': self.get_growth_metrics(),
            'valuation': self.get_valuation_metrics(),
            'validation': self.get_validation_report()
        }
    
    def _extract_cash_position(self, balance_sheet_data: Dict) -> float:
        """Extract total cash position from balance sheet"""
        total_cash = 0
        
        for report in balance_sheet_data.get('Reports', []):
            if report.get('ReportType') == 'BalanceSheet':
                for section in report.get('Rows', []):
                    if section.get('Title') == 'Bank':
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row':
                                # Get the current period value (usually second cell)
                                cells = row.get('Cells', [])
                                if len(cells) >= 2:
                                    try:
                                        amount = float(cells[1].get('Value', 0))
                                        total_cash += amount
                                    except:
                                        continue
        
        return total_cash
    
    def get_historical_revenue_timeline(self) -> Dict[str, Any]:
        """Get revenue timeline from bank transactions (includes all revenue sources)"""
        metrics = self.get_metrics()
        
        # Get revenue timeline from bank transactions
        revenue_timeline = metrics.get('revenue_timeline', {})
        
        # Also include ledger revenue for completeness
        ledger_revenue = metrics.get('ledger_revenue', {})
        
        return {
            'monthly': revenue_timeline.get('monthly', {}),
            'quarterly': revenue_timeline.get('quarterly', {}),
            'yearly': revenue_timeline.get('yearly', {}),
            'by_customer': revenue_timeline.get('by_customer', {}),
            'total_from_transactions': revenue_timeline.get('total', 0),
            'ledger_accounts': ledger_revenue,
            'p_and_l_total': metrics.get('revenue', {}).get('total_revenue', 0)
        }
    
    def get_quarterly_metrics(self) -> Dict[str, Any]:
        """Aggregate all metrics by quarter"""
        revenue_timeline = self.get_historical_revenue_timeline()
        quarterly_revenue = revenue_timeline.get('quarterly', {})
        
        # Process expenses by quarter if available
        if self._data and 'bank_transactions' in self._data:
            quarterly_expenses = self._process_expenses_by_quarter(self._data['bank_transactions'])
        else:
            quarterly_expenses = {}
        
        # Combine into quarterly metrics
        quarters = sorted(set(list(quarterly_revenue.keys()) + list(quarterly_expenses.keys())))
        quarterly_metrics = {}
        
        for quarter in quarters:
            revenue = quarterly_revenue.get(quarter, 0)
            expenses = quarterly_expenses.get(quarter, 0)
            
            quarterly_metrics[quarter] = {
                'revenue': revenue,
                'expenses': expenses,
                'net_income': revenue - expenses,
                'margin': ((revenue - expenses) / revenue * 100) if revenue > 0 else 0
            }
        
        return quarterly_metrics
    
    def get_annual_metrics(self) -> Dict[str, Any]:
        """Aggregate all metrics by year"""
        revenue_timeline = self.get_historical_revenue_timeline()
        yearly_revenue = revenue_timeline.get('yearly', {})
        
        # Process expenses by year if available
        if self._data and 'bank_transactions' in self._data:
            yearly_expenses = self._process_expenses_by_year(self._data['bank_transactions'])
        else:
            yearly_expenses = {}
        
        # Combine into annual metrics
        years = sorted(set(list(yearly_revenue.keys()) + list(yearly_expenses.keys())))
        annual_metrics = {}
        
        for year in years:
            revenue = yearly_revenue.get(year, 0)
            expenses = yearly_expenses.get(year, 0)
            
            annual_metrics[year] = {
                'revenue': revenue,
                'expenses': expenses,
                'net_income': revenue - expenses,
                'margin': ((revenue - expenses) / revenue * 100) if revenue > 0 else 0,
                'growth_rate': 0  # Will be calculated below
            }
        
        # Calculate year-over-year growth rates
        for i, year in enumerate(years[1:], 1):
            prior_year = years[i-1]
            if annual_metrics[prior_year]['revenue'] > 0:
                growth = ((annual_metrics[year]['revenue'] - annual_metrics[prior_year]['revenue']) / 
                         annual_metrics[prior_year]['revenue'] * 100)
                annual_metrics[year]['growth_rate'] = growth
        
        return annual_metrics
    
    def get_customer_timeline(self, cutoff_date: str = '2025-04-30') -> Dict[str, Any]:
        """Track customer lifecycle with configurable cutoff date"""
        from datetime import datetime
        
        cutoff = datetime.fromisoformat(cutoff_date)
        revenue_timeline = self.get_historical_revenue_timeline()
        customers = revenue_timeline.get('by_customer', {})
        
        customer_analysis = {
            'active_customers': {},
            'churned_customers': {},
            'total_active': 0,
            'total_churned': 0,
            'retention_rate': 0,
            'churn_rate': 0
        }
        
        for customer_id, customer_data in customers.items():
            transactions = customer_data.get('transactions', [])
            if not transactions:
                continue
            
            # Sort transactions by date
            sorted_txns = sorted(transactions, key=lambda x: x['date'])
            
            # Get first and last transaction dates
            first_date = datetime.fromisoformat(sorted_txns[0]['date'])
            last_date = datetime.fromisoformat(sorted_txns[-1]['date'])
            
            # Determine if customer is active based on cutoff
            if last_date >= cutoff:
                customer_analysis['active_customers'][customer_id] = {
                    'name': customer_data['name'],
                    'total_revenue': customer_data['total'],
                    'first_transaction': first_date.isoformat(),
                    'last_transaction': last_date.isoformat(),
                    'lifetime_days': (last_date - first_date).days
                }
                customer_analysis['total_active'] += 1
            else:
                customer_analysis['churned_customers'][customer_id] = {
                    'name': customer_data['name'],
                    'total_revenue': customer_data['total'],
                    'first_transaction': first_date.isoformat(),
                    'last_transaction': last_date.isoformat(),
                    'lifetime_days': (last_date - first_date).days,
                    'days_since_last_transaction': (cutoff - last_date).days
                }
                customer_analysis['total_churned'] += 1
        
        # Calculate retention and churn rates
        total_customers = customer_analysis['total_active'] + customer_analysis['total_churned']
        if total_customers > 0:
            customer_analysis['retention_rate'] = customer_analysis['total_active'] / total_customers * 100
            customer_analysis['churn_rate'] = customer_analysis['total_churned'] / total_customers * 100
        
        return customer_analysis
    
    def get_cohort_analysis(self) -> Dict[str, Any]:
        """Analyze customer cohorts by acquisition month"""
        revenue_timeline = self.get_historical_revenue_timeline()
        revenue_by_customer = revenue_timeline.get('by_customer', {})
        
        cohorts = {}
        
        for customer_id, customer_data in revenue_by_customer.items():
            transactions = customer_data.get('transactions', [])
            if not transactions:
                continue
            
            # Get first transaction date (acquisition month)
            first_date = min(datetime.fromisoformat(t['date']) for t in transactions)
            cohort_key = f"{first_date.year}-{first_date.month:02d}"
            
            if cohort_key not in cohorts:
                cohorts[cohort_key] = {
                    'customers': [],
                    'total_revenue': 0,
                    'customer_count': 0,
                    'avg_revenue_per_customer': 0,
                    'retention_by_month': {}
                }
            
            cohorts[cohort_key]['customers'].append(customer_id)
            cohorts[cohort_key]['total_revenue'] += customer_data['total']
            cohorts[cohort_key]['customer_count'] += 1
            
            # Track monthly activity for retention
            for transaction in transactions:
                transaction_date = datetime.fromisoformat(transaction['date'])
                months_since_acquisition = (transaction_date.year - first_date.year) * 12 + (transaction_date.month - first_date.month)
                
                if months_since_acquisition not in cohorts[cohort_key]['retention_by_month']:
                    cohorts[cohort_key]['retention_by_month'][months_since_acquisition] = set()
                cohorts[cohort_key]['retention_by_month'][months_since_acquisition].add(customer_id)
        
        # Calculate average revenue and retention rates
        for cohort_key, cohort_data in cohorts.items():
            if cohort_data['customer_count'] > 0:
                cohort_data['avg_revenue_per_customer'] = cohort_data['total_revenue'] / cohort_data['customer_count']
                
                # Calculate retention rates
                cohort_data['retention_rates'] = {}
                initial_customers = cohort_data['customer_count']
                for month, active_customers in cohort_data['retention_by_month'].items():
                    cohort_data['retention_rates'][month] = len(active_customers) / initial_customers * 100
        
        return cohorts
    
    def get_retention_curves(self) -> Dict[str, Any]:
        """Generate retention curves by cohort"""
        cohort_analysis = self.get_cohort_analysis()
        
        retention_curves = {
            'by_cohort': {},
            'average_curve': {},
            'best_cohort': None,
            'worst_cohort': None
        }
        
        all_retention_data = []
        
        for cohort_key, cohort_data in cohort_analysis.items():
            retention_rates = cohort_data.get('retention_rates', {})
            if retention_rates:
                retention_curves['by_cohort'][cohort_key] = retention_rates
                all_retention_data.append((cohort_key, retention_rates))
        
        # Calculate average retention curve
        if all_retention_data:
            max_months = max(max(rates.keys()) for _, rates in all_retention_data if rates)
            
            for month in range(max_months + 1):
                month_retentions = []
                for _, rates in all_retention_data:
                    if month in rates:
                        month_retentions.append(rates[month])
                
                if month_retentions:
                    retention_curves['average_curve'][month] = sum(month_retentions) / len(month_retentions)
            
            # Find best and worst cohorts by 3-month retention
            cohorts_by_3m_retention = []
            for cohort_key, rates in all_retention_data:
                if 3 in rates:
                    cohorts_by_3m_retention.append((cohort_key, rates[3]))
            
            if cohorts_by_3m_retention:
                cohorts_by_3m_retention.sort(key=lambda x: x[1], reverse=True)
                retention_curves['best_cohort'] = cohorts_by_3m_retention[0][0]
                retention_curves['worst_cohort'] = cohorts_by_3m_retention[-1][0]
        
        return retention_curves
    
    def get_historical_unit_economics(self) -> Dict[str, Any]:
        """Calculate unit economics trends over time"""
        metrics = self.get_metrics()
        revenue_timeline = self.get_historical_revenue_timeline()
        
        # Get current unit economics
        current_ue = self.get_unit_economics()
        
        # Calculate historical trends
        historical_ue = {
            'current': current_ue,
            'quarterly_trends': {},
            'annual_trends': {},
            'improvement_over_time': {}
        }
        
        # Get quarterly metrics
        quarterly_metrics = self.get_quarterly_metrics()
        
        if quarterly_metrics:
            for quarter, q_metrics in quarterly_metrics.items():
                if q_metrics['revenue'] > 0:
                    # Simple approximations for quarterly UE
                    quarterly_cac = q_metrics.get('expenses', 0) * 0.1  # Assume 10% of expenses for CAC
                    quarterly_ltv = q_metrics['revenue'] * 3  # Simple 3x multiplier
                    
                    historical_ue['quarterly_trends'][quarter] = {
                        'estimated_cac': quarterly_cac,
                        'estimated_ltv': quarterly_ltv,
                        'ltv_cac_ratio': quarterly_ltv / quarterly_cac if quarterly_cac > 0 else 0
                    }
        
        # Track improvement metrics
        if historical_ue['quarterly_trends']:
            quarters = sorted(historical_ue['quarterly_trends'].keys())
            if len(quarters) >= 2:
                first_q = historical_ue['quarterly_trends'][quarters[0]]
                last_q = historical_ue['quarterly_trends'][quarters[-1]]
                
                historical_ue['improvement_over_time'] = {
                    'ltv_cac_improvement': last_q['ltv_cac_ratio'] - first_q['ltv_cac_ratio'],
                    'cac_reduction': (first_q['estimated_cac'] - last_q['estimated_cac']) / first_q['estimated_cac'] * 100 if first_q['estimated_cac'] > 0 else 0,
                    'ltv_growth': (last_q['estimated_ltv'] - first_q['estimated_ltv']) / first_q['estimated_ltv'] * 100 if first_q['estimated_ltv'] > 0 else 0
                }
        
        return historical_ue
    
    def _process_expenses_by_quarter(self, bank_transactions: List[Dict]) -> Dict[str, float]:
        """Process bank transactions to get expenses by quarter"""
        quarterly_expenses = {}
        
        for transaction in bank_transactions:
            if transaction.get('Type') == 'SPEND':
                amount = float(transaction.get('Total', 0))
                date_str = transaction.get('DateString', transaction.get('Date', ''))
                
                try:
                    # Parse date
                    if '/Date(' in date_str:
                        timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
                        date = datetime.fromtimestamp(timestamp)
                    else:
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    
                    quarter_key = f"{date.year}-Q{(date.month-1)//3 + 1}"
                    quarterly_expenses[quarter_key] = quarterly_expenses.get(quarter_key, 0) + amount
                    
                except Exception:
                    continue
        
        return quarterly_expenses
    
    def _process_expenses_by_year(self, bank_transactions: List[Dict]) -> Dict[str, float]:
        """Process bank transactions to get expenses by year"""
        yearly_expenses = {}
        
        for transaction in bank_transactions:
            if transaction.get('Type') == 'SPEND':
                amount = float(transaction.get('Total', 0))
                date_str = transaction.get('DateString', transaction.get('Date', ''))
                
                try:
                    # Parse date
                    if '/Date(' in date_str:
                        timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
                        date = datetime.fromtimestamp(timestamp)
                    else:
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    
                    year_key = str(date.year)
                    yearly_expenses[year_key] = yearly_expenses.get(year_key, 0) + amount
                    
                except Exception:
                    continue
        
        return yearly_expenses
    
    def print_summary(self):
        """Print a summary of key metrics"""
        position = self.get_financial_position()
        unit_econ = self.get_unit_economics()
        
        print("\n" + "="*50)
        print("FINANCIAL METRICS SUMMARY")
        print("="*50)
        print(f"Revenue (Annual): £{position['total_revenue']:,.2f}")
        print(f"MRR: £{position['mrr']:,.2f}")
        print(f"Customers: {position['customer_count']}")
        print(f"Cash Position: £{position['cash_position']:,.2f}")
        print(f"Monthly Burn: £{position['monthly_burn']:,.2f}")
        print(f"Runway: {position['runway_months']:.1f} months")
        print(f"LTV/CAC Ratio: {unit_econ.get('ltv_cac_ratio', 0):.1f}")
        print(f"CAC Payback: {unit_econ.get('payback_months', 0):.1f} months")
        print("="*50 + "\n")


# Convenience function for notebooks
def get_financial_data(force_refresh: bool = False) -> Dict[str, Any]:
    """
    Quick function to get all financial data in notebooks
    
    Usage in notebook:
        from analysis_unified.notebook_data_bridge import get_financial_data
        data = get_financial_data()
    """
    bridge = NotebookDataBridge()
    return bridge.export_for_notebook()