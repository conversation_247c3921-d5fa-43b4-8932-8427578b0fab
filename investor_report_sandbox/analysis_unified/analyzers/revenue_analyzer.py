"""
Revenue Analysis Module
Deep analysis of revenue patterns, growth, and customer metrics
"""

import pandas as pd
from datetime import datetime
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class RevenueAnalyzer:
    """Specialized revenue analysis"""
    
    def analyze(self, data: Dict, base_calculations: Dict) -> Dict:
        """Perform comprehensive revenue analysis"""
        logger.info("Performing revenue analysis...")
        
        revenue_metrics = base_calculations.get('revenue', {})
        
        analysis = {
            'summary': self._create_summary(revenue_metrics),
            'customer_analysis': self._analyze_customers(revenue_metrics),
            'growth_analysis': self._analyze_growth(data, revenue_metrics),
            'currency_analysis': self._analyze_currencies(revenue_metrics),
            'recommendations': self._generate_recommendations(revenue_metrics)
        }
        
        return analysis
    
    def _create_summary(self, metrics: Dict) -> Dict:
        """Create revenue summary"""
        return {
            'total_revenue': metrics.get('total_revenue', 0),
            'customer_count': metrics.get('customer_count', 0),
            'average_revenue_per_customer': (
                metrics.get('total_revenue', 0) / metrics.get('customer_count', 1)
                if metrics.get('customer_count', 0) > 0 else 0
            ),
            'concentration_risk': metrics.get('concentration_risk', 0),
            'top_customer': metrics.get('top_customer', {})
        }
    
    def _analyze_customers(self, metrics: Dict) -> Dict:
        """Analyze customer distribution and quality"""
        customers = metrics.get('revenue_by_customer', {})
        
        if not customers:
            return {}
        
        # Calculate customer segments
        revenues = [c['total'] for c in customers.values()]
        
        # Pareto analysis
        sorted_revenues = sorted(revenues, reverse=True)
        total_revenue = sum(revenues)
        
        # Find 80/20 split
        cumsum = 0
        pareto_count = 0
        for i, rev in enumerate(sorted_revenues):
            cumsum += rev
            if cumsum >= total_revenue * 0.8:
                pareto_count = i + 1
                break
        
        return {
            'total_customers': len(customers),
            'revenue_distribution': {
                'min': min(revenues) if revenues else 0,
                'max': max(revenues) if revenues else 0,
                'median': sorted_revenues[len(sorted_revenues)//2] if revenues else 0,
                'average': sum(revenues) / len(revenues) if revenues else 0
            },
            'pareto_analysis': {
                'customers_for_80_percent_revenue': pareto_count,
                'percentage_of_customers': (pareto_count / len(customers)) * 100 if customers else 0
            },
            'customer_quality_score': self._calculate_customer_quality_score(customers)
        }
    
    def _analyze_growth(self, data: Dict, metrics: Dict) -> Dict:
        """Analyze revenue growth patterns"""
        # This would need historical data
        # For now, provide structure for future implementation
        return {
            'growth_rate': 'Requires historical data',
            'growth_trend': 'Requires time series analysis',
            'seasonality': 'Requires 24+ months of data'
        }
    
    def _analyze_currencies(self, metrics: Dict) -> Dict:
        """Analyze multi-currency revenue"""
        currencies = metrics.get('revenue_by_currency', {})
        total = metrics.get('total_revenue', 0)
        
        currency_breakdown = {}
        for currency, amount in currencies.items():
            currency_breakdown[currency] = {
                'amount': amount,
                'percentage': (amount / total * 100) if total > 0 else 0
            }
        
        return {
            'currencies_used': len(currencies),
            'primary_currency': max(currencies.items(), key=lambda x: x[1])[0] if currencies else 'Unknown',
            'breakdown': currency_breakdown
        }
    
    def _calculate_customer_quality_score(self, customers: Dict) -> float:
        """Calculate overall customer quality score (0-100)"""
        if not customers:
            return 0
        
        scores = []
        
        # Revenue size score
        revenues = [c['total'] for c in customers.values()]
        avg_revenue = sum(revenues) / len(revenues)
        
        for customer in customers.values():
            score = 0
            
            # Revenue contribution (40 points)
            if customer['total'] > avg_revenue * 2:
                score += 40
            elif customer['total'] > avg_revenue:
                score += 30
            else:
                score += 20
            
            # Invoice frequency (30 points) - proxy for engagement
            if customer['invoice_count'] > 12:  # Monthly
                score += 30
            elif customer['invoice_count'] > 4:  # Quarterly
                score += 20
            else:
                score += 10
            
            # Add base score for being active (30 points)
            score += 30
            
            scores.append(score)
        
        return sum(scores) / len(scores)
    
    def _generate_recommendations(self, metrics: Dict) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Concentration risk
        concentration = metrics.get('concentration_risk', 0)
        if concentration > 80:
            recommendations.append({
                'priority': 'CRITICAL',
                'issue': 'Customer Concentration Risk',
                'recommendation': f'Top customer represents {concentration:.1f}% of revenue. Urgent diversification needed.',
                'action': 'Launch customer acquisition campaign targeting 20+ new customers'
            })
        elif concentration > 50:
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'High Customer Concentration',
                'recommendation': f'Top customer represents {concentration:.1f}% of revenue.',
                'action': 'Develop customer diversification strategy'
            })
        
        # Customer count
        if metrics.get('customer_count', 0) < 10:
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'Low Customer Count',
                'recommendation': 'Only {} active customers'.format(metrics.get('customer_count', 0)),
                'action': 'Accelerate sales and marketing efforts'
            })
        
        # Revenue per customer
        avg_revenue = metrics.get('total_revenue', 0) / max(metrics.get('customer_count', 1), 1)
        if avg_revenue < 5000:
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': 'Low Average Revenue per Customer',
                'recommendation': f'Average revenue per customer is ${avg_revenue:,.0f}',
                'action': 'Focus on upselling and increasing customer value'
            })
        
        return recommendations