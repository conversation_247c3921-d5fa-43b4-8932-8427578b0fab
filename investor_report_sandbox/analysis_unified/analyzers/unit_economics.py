"""
Unit Economics Analyzer
Calculates and analyzes LTV, CAC, and other unit economics metrics
"""

from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class UnitEconomicsAnalyzer:
    """Specialized unit economics analysis with corrected formulas"""
    
    def analyze(self, data: Dict, base_calculations: Dict) -> Dict:
        """Perform unit economics analysis"""
        logger.info("Performing unit economics analysis...")
        
        ue_metrics = base_calculations.get('unit_economics', {})
        revenue_metrics = base_calculations.get('revenue', {})
        
        analysis = {
            'metrics': self._enhance_metrics(ue_metrics),
            'health_check': self._check_unit_health(ue_metrics),
            'cohort_analysis': self._analyze_cohorts(data, revenue_metrics),
            'benchmarks': self._compare_to_benchmarks(ue_metrics),
            'recommendations': self._generate_recommendations(ue_metrics)
        }
        
        return analysis
    
    def _enhance_metrics(self, metrics: Dict) -> Dict:
        """Enhance base unit economics metrics"""
        enhanced = metrics.copy()
        
        # Add derived metrics
        if metrics.get('avg_cac', 0) > 0:
            enhanced['cac_recovery_rate'] = 1 / metrics.get('payback_months', 12) if metrics.get('payback_months', 0) > 0 else 0
        
        # Add health indicators
        ltv_cac = metrics.get('ltv_cac_ratio', 0)
        enhanced['health_status'] = self._get_health_status(ltv_cac)
        
        return enhanced
    
    def _check_unit_health(self, metrics: Dict) -> Dict:
        """Comprehensive unit economics health check"""
        health_scores = {}
        
        # LTV/CAC health
        ltv_cac = metrics.get('ltv_cac_ratio', 0)
        if ltv_cac >= 3:
            health_scores['ltv_cac'] = {'score': 100, 'status': 'Excellent'}
        elif ltv_cac >= 2:
            health_scores['ltv_cac'] = {'score': 70, 'status': 'Good'}
        elif ltv_cac >= 1:
            health_scores['ltv_cac'] = {'score': 40, 'status': 'Poor'}
        else:
            health_scores['ltv_cac'] = {'score': 0, 'status': 'Critical'}
        
        # Payback period health
        payback = metrics.get('payback_months', 999)
        if payback <= 6:
            health_scores['payback'] = {'score': 100, 'status': 'Excellent'}
        elif payback <= 12:
            health_scores['payback'] = {'score': 70, 'status': 'Good'}
        elif payback <= 18:
            health_scores['payback'] = {'score': 40, 'status': 'Fair'}
        else:
            health_scores['payback'] = {'score': 0, 'status': 'Poor'}
        
        # NRR health
        nrr = metrics.get('avg_nrr', 100)
        if nrr >= 120:
            health_scores['nrr'] = {'score': 100, 'status': 'Excellent'}
        elif nrr >= 100:
            health_scores['nrr'] = {'score': 70, 'status': 'Good'}
        elif nrr >= 80:
            health_scores['nrr'] = {'score': 40, 'status': 'Fair'}
        else:
            health_scores['nrr'] = {'score': 0, 'status': 'Poor'}
        
        # Overall score
        overall_score = sum(h['score'] for h in health_scores.values()) / len(health_scores)
        
        return {
            'overall_score': overall_score,
            'overall_status': self._get_overall_status(overall_score),
            'component_scores': health_scores
        }
    
    def _analyze_cohorts(self, data: Dict, revenue_metrics: Dict) -> Dict:
        """Analyze customer cohorts"""
        # Simplified cohort analysis
        customers = revenue_metrics.get('revenue_by_customer', {})
        
        if not customers:
            return {}
        
        # Group customers by value tiers
        tiers = {
            'enterprise': {'threshold': 50000, 'customers': []},
            'mid_market': {'threshold': 10000, 'customers': []},
            'small': {'threshold': 0, 'customers': []}
        }
        
        for customer_id, customer_data in customers.items():
            revenue = customer_data['total']
            if revenue >= tiers['enterprise']['threshold']:
                tiers['enterprise']['customers'].append(customer_data)
            elif revenue >= tiers['mid_market']['threshold']:
                tiers['mid_market']['customers'].append(customer_data)
            else:
                tiers['small']['customers'].append(customer_data)
        
        # Calculate tier metrics
        tier_analysis = {}
        total_revenue = revenue_metrics.get('total_revenue', 0)
        
        for tier_name, tier_data in tiers.items():
            tier_customers = tier_data['customers']
            tier_revenue = sum(c['total'] for c in tier_customers)
            
            tier_analysis[tier_name] = {
                'customer_count': len(tier_customers),
                'total_revenue': tier_revenue,
                'revenue_percentage': (tier_revenue / total_revenue * 100) if total_revenue > 0 else 0,
                'average_revenue': tier_revenue / len(tier_customers) if tier_customers else 0
            }
        
        return tier_analysis
    
    def _compare_to_benchmarks(self, metrics: Dict) -> Dict:
        """Compare metrics to industry benchmarks"""
        benchmarks = {
            'ltv_cac_ratio': {
                'excellent': 3.0,
                'good': 2.0,
                'minimum': 1.0,
                'actual': metrics.get('ltv_cac_ratio', 0)
            },
            'payback_months': {
                'excellent': 6,
                'good': 12,
                'maximum': 18,
                'actual': metrics.get('payback_months', 999)
            },
            'monthly_churn': {
                'excellent': 0.02,  # 2%
                'good': 0.05,       # 5%
                'maximum': 0.10,    # 10%
                'actual': metrics.get('monthly_churn', 0)  # Use 0 if not calculated from data
            },
            'nrr': {
                'excellent': 120,
                'good': 100,
                'minimum': 80,
                'actual': metrics.get('avg_nrr', 100)
            }
        }
        
        # Calculate performance vs benchmarks
        for metric, values in benchmarks.items():
            if metric in ['ltv_cac_ratio', 'nrr']:  # Higher is better
                if values['actual'] >= values['excellent']:
                    values['performance'] = 'Exceeds Best-in-Class'
                elif values['actual'] >= values['good']:
                    values['performance'] = 'Above Average'
                elif values['actual'] >= values.get('minimum', 0):
                    values['performance'] = 'Below Average'
                else:
                    values['performance'] = 'Needs Improvement'
            else:  # Lower is better (payback, churn)
                if values['actual'] <= values['excellent']:
                    values['performance'] = 'Exceeds Best-in-Class'
                elif values['actual'] <= values['good']:
                    values['performance'] = 'Above Average'
                elif values['actual'] <= values.get('maximum', float('inf')):
                    values['performance'] = 'Below Average'
                else:
                    values['performance'] = 'Needs Improvement'
        
        return benchmarks
    
    def _get_health_status(self, ltv_cac_ratio: float) -> str:
        """Get health status based on LTV/CAC ratio"""
        if ltv_cac_ratio >= 3:
            return "Excellent"
        elif ltv_cac_ratio >= 2:
            return "Good"
        elif ltv_cac_ratio >= 1:
            return "Fair"
        else:
            return "Poor"
    
    def _get_overall_status(self, score: float) -> str:
        """Get overall status based on score"""
        if score >= 80:
            return "Excellent"
        elif score >= 60:
            return "Good"
        elif score >= 40:
            return "Fair"
        else:
            return "Poor"
    
    def _generate_recommendations(self, metrics: Dict) -> List[Dict]:
        """Generate unit economics recommendations"""
        recommendations = []
        
        # LTV/CAC recommendations
        ltv_cac = metrics.get('ltv_cac_ratio', 0)
        if ltv_cac < 1:
            recommendations.append({
                'priority': 'CRITICAL',
                'metric': 'LTV/CAC Ratio',
                'issue': f'Ratio of {ltv_cac:.2f} is below breakeven',
                'recommendation': 'Unit economics are unsustainable',
                'actions': [
                    'Immediately reduce CAC through more efficient channels',
                    'Increase pricing or upsell to improve LTV',
                    'Improve retention to extend customer lifetime'
                ]
            })
        elif ltv_cac < 3:
            recommendations.append({
                'priority': 'HIGH',
                'metric': 'LTV/CAC Ratio',
                'issue': f'Ratio of {ltv_cac:.2f} is below optimal',
                'recommendation': 'Improve unit economics for sustainable growth',
                'actions': [
                    'Optimize marketing spend efficiency',
                    'Focus on retention and expansion revenue',
                    'Consider pricing optimization'
                ]
            })
        
        # Payback period recommendations
        payback = metrics.get('payback_months', 999)
        if payback > 12:
            recommendations.append({
                'priority': 'MEDIUM',
                'metric': 'CAC Payback Period',
                'issue': f'Payback period of {payback:.1f} months is too long',
                'recommendation': 'Accelerate cash flow recovery',
                'actions': [
                    'Front-load customer value delivery',
                    'Implement annual prepayment incentives',
                    'Reduce time to value for new customers'
                ]
            })
        
        # Churn recommendations
        churn = metrics.get('monthly_churn', 0)
        if churn > 0.05:  # 5%
            recommendations.append({
                'priority': 'HIGH',
                'metric': 'Monthly Churn Rate',
                'issue': f'Churn rate of {churn*100:.1f}% is high',
                'recommendation': 'Reduce churn to improve unit economics',
                'actions': [
                    'Implement customer success program',
                    'Analyze churn reasons and address root causes',
                    'Improve product stickiness and value delivery'
                ]
            })
        
        return recommendations