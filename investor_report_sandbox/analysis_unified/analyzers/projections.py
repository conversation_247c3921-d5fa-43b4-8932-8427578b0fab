"""Financial Projections Analyzer - Creates data-driven financial projections"""

from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class ProjectionAnalyzer:
    def analyze(self, data: Dict[str, Any], calculations: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate financial projections based on actual historical data
        
        Args:
            data: Raw financial data
            calculations: Calculated metrics from FinancialCalculator
            
        Returns:
            Financial projections with multiple scenarios
        """
        try:
            # Extract key metrics for projections
            revenue_metrics = calculations.get('revenue', {})
            expense_metrics = calculations.get('expenses', {})
            growth_metrics = calculations.get('growth', {})
            unit_economics = calculations.get('unit_economics', {})
            
            # Get base values
            current_revenue = revenue_metrics.get('total_revenue', 0)
            current_expenses = expense_metrics.get('total_expenses', 0)
            
            # Get growth rates and validate them
            mom_growth = growth_metrics.get('mom_growth', 0) / 100  # Convert to decimal
            yoy_growth = growth_metrics.get('yoy_growth', 0) / 100  # Convert to decimal
            ltv_cac_ratio = unit_economics.get('ltv_cac_ratio', 0)
            
            # Calculate a more reliable growth rate
            historical_growth = self._calculate_reliable_growth_rate(mom_growth, yoy_growth, ltv_cac_ratio)
            
            # Generate growth scenarios based on actual performance
            scenarios = self._generate_scenarios(historical_growth, ltv_cac_ratio)
            
            # Project key metrics
            projections = {
                'summary': self._generate_summary(scenarios, current_revenue),
                'scenarios': {},
                'key_assumptions': self._document_assumptions(historical_growth, ltv_cac_ratio),
                'milestones': self._identify_milestones(current_revenue, scenarios)
            }
            
            # Calculate projections for each scenario
            for scenario_name, growth_rate in scenarios.items():
                projections['scenarios'][scenario_name] = self._project_scenario(
                    current_revenue,
                    current_expenses,
                    growth_rate,
                    unit_economics
                )
            
            return projections
            
        except Exception as e:
            logger.error(f"Error in projections analysis: {str(e)}")
            return {
                'summary': 'Error generating projections',
                'scenarios': {},
                'error': str(e)
            }
    
    def _calculate_reliable_growth_rate(self, mom_growth: float, yoy_growth: float, ltv_cac_ratio: float) -> float:
        """Calculate a reliable growth rate from potentially volatile data"""
        # Handle conflicting growth signals
        if yoy_growth < -0.5 and mom_growth > 0.2:
            # Large YoY decline but recent positive growth - be conservative
            logger.warning(f"Conflicting growth signals: YoY {yoy_growth*100:.1f}%, MoM {mom_growth*100:.1f}%")
            return 0.05  # 5% monthly conservative estimate
        
        # Cap extreme values
        if abs(mom_growth) > 0.3:  # More than 30% monthly is likely an anomaly
            logger.warning(f"Extreme MoM growth detected: {mom_growth*100:.1f}%, capping at 20%")
            if mom_growth > 0:
                return min(mom_growth, 0.20)  # Cap at 20% monthly
            else:
                return max(mom_growth, -0.10)  # Floor at -10% monthly
        
        # If we have reasonable MoM growth, use it
        if -0.3 <= mom_growth <= 0.3:
            return mom_growth
        
        # Otherwise, use unit economics as a proxy
        if ltv_cac_ratio > 4:
            return 0.10  # 10% monthly for excellent unit economics
        elif ltv_cac_ratio > 3:
            return 0.07  # 7% monthly for good unit economics
        else:
            return 0.05  # 5% monthly conservative
    
    def _generate_scenarios(self, historical_growth: float, ltv_cac_ratio: float) -> Dict[str, float]:
        """Generate growth scenarios based on actual performance"""
        # Ensure base growth is reasonable
        base_growth = max(min(historical_growth, 0.20), -0.10)  # Between -10% and 20%
        
        # For negative or zero growth, use unit economics
        if base_growth <= 0:
            if ltv_cac_ratio > 4:
                base_growth = 0.10  # 10% monthly
            elif ltv_cac_ratio > 3:
                base_growth = 0.07  # 7% monthly
            else:
                base_growth = 0.05  # 5% monthly
        
        return {
            'conservative': max(base_growth * 0.5, 0.02),  # At least 2% monthly
            'base': base_growth,
            'optimistic': min(base_growth * 1.5, 0.25)  # Cap at 25% monthly
        }
    
    def _project_scenario(self, current_revenue: float, current_expenses: float, 
                         growth_rate: float, unit_economics: Dict) -> Dict[str, Any]:
        """Project financials for a specific scenario"""
        projections = {
            'growth_rate': growth_rate * 100,  # Convert to percentage
            'monthly': [],
            'annual': []
        }
        
        # Monthly projections for 24 months
        revenue = current_revenue / 12  # Monthly revenue
        expenses = current_expenses / 12  # Monthly expenses
        
        for month in range(24):
            revenue *= (1 + growth_rate)
            # Assume expenses grow at 70% of revenue growth (operational leverage)
            expenses *= (1 + growth_rate * 0.7)
            
            projections['monthly'].append({
                'month': month + 1,
                'revenue': revenue,
                'expenses': expenses,
                'net_income': revenue - expenses,
                'margin': (revenue - expenses) / revenue if revenue > 0 else 0
            })
        
        # Annual projections for 3 years
        annual_revenue = current_revenue
        annual_expenses = current_expenses
        
        for year in range(3):
            # Compound annual growth
            annual_growth = (1 + growth_rate) ** 12 - 1
            annual_revenue *= (1 + annual_growth)
            annual_expenses *= (1 + annual_growth * 0.7)
            
            projections['annual'].append({
                'year': year + 1,
                'revenue': annual_revenue,
                'expenses': annual_expenses,
                'net_income': annual_revenue - annual_expenses,
                'margin': (annual_revenue - annual_expenses) / annual_revenue if annual_revenue > 0 else 0
            })
        
        # Key metrics
        projections['key_metrics'] = {
            'breakeven_month': self._find_breakeven(projections['monthly']),
            'year_3_revenue': projections['annual'][2]['revenue'],
            'year_3_margin': projections['annual'][2]['margin'] * 100,
            'total_funding_needed': self._calculate_funding_need(projections['monthly'])
        }
        
        return projections
    
    def _find_breakeven(self, monthly_projections: List[Dict]) -> int:
        """Find the month when net income becomes positive"""
        for projection in monthly_projections:
            if projection['net_income'] > 0:
                return projection['month']
        return 999  # Not achieved in projection period
    
    def _calculate_funding_need(self, monthly_projections: List[Dict]) -> float:
        """Calculate cumulative funding needed until breakeven"""
        cumulative_loss = 0
        max_loss = 0
        
        for projection in monthly_projections:
            if projection['net_income'] < 0:
                cumulative_loss += abs(projection['net_income'])
                max_loss = max(max_loss, cumulative_loss)
            else:
                break
        
        # Add 6 months buffer
        return max_loss * 1.5
    
    def _generate_summary(self, scenarios: Dict[str, float], current_revenue: float) -> str:
        """Generate executive summary of projections"""
        base_growth = scenarios['base'] * 100
        
        # Calculate year 1 revenue at base case
        year_1_revenue = current_revenue * ((1 + scenarios['base']) ** 12)
        
        return (f"Financial projections based on {base_growth:.0f}% monthly growth "
                f"(historical trend). Base case projects £{year_1_revenue:,.0f} "
                f"ARR in 12 months, with scenarios ranging from "
                f"{scenarios['conservative']*100:.0f}% to {scenarios['optimistic']*100:.0f}% "
                f"monthly growth.")
    
    def _document_assumptions(self, historical_growth: float, ltv_cac_ratio: float) -> Dict[str, Any]:
        """Document key assumptions used in projections"""
        return {
            'revenue_growth': {
                'basis': 'historical' if historical_growth > 0 else 'unit_economics',
                'historical_rate': f"{historical_growth * 100:.0f}%",
                'ltv_cac_ratio': ltv_cac_ratio
            },
            'expense_growth': {
                'assumption': 'Grows at 70% of revenue growth rate',
                'rationale': 'Operational leverage as business scales'
            },
            'other': {
                'churn': 'Assumed constant based on current rate',
                'pricing': 'No price increases assumed',
                'market': 'Sufficient demand to support growth'
            }
        }
    
    def _identify_milestones(self, current_revenue: float, scenarios: Dict[str, float]) -> List[Dict[str, Any]]:
        """Identify key business milestones"""
        milestones = []
        
        # Revenue milestones
        revenue_targets = [100000, 250000, 500000, 1000000]  # £100K, £250K, £500K, £1M
        
        for target in revenue_targets:
            if current_revenue < target:
                # Calculate months to reach target at base growth
                months_to_target = self._months_to_revenue(current_revenue, target, scenarios['base'])
                milestones.append({
                    'milestone': f'£{target/1000:.0f}K ARR',
                    'months_to_achieve': months_to_target,
                    'scenario': 'base'
                })
        
        return milestones
    
    def _months_to_revenue(self, current: float, target: float, growth_rate: float) -> int:
        """Calculate months to reach a revenue target"""
        if growth_rate <= 0 or current >= target:
            return 0
        
        monthly_current = current / 12
        monthly_target = target / 12
        months = 0
        
        while monthly_current < monthly_target and months < 60:
            months += 1
            monthly_current *= (1 + growth_rate)
        
        return months