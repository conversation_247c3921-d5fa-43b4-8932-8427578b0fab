"""
Expense Analysis Module
Analyzes spending patterns, burn rate, and cost optimization opportunities
"""

from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class ExpenseAnalyzer:
    """Specialized expense analysis"""
    
    def analyze(self, data: Dict, base_calculations: Dict) -> Dict:
        """Perform comprehensive expense analysis"""
        logger.info("Performing expense analysis...")
        
        expense_metrics = base_calculations.get('expenses', {})
        revenue_metrics = base_calculations.get('revenue', {})
        
        analysis = {
            'summary': self._create_summary(expense_metrics),
            'category_analysis': self._analyze_categories(expense_metrics),
            'efficiency_metrics': self._calculate_efficiency(expense_metrics, revenue_metrics),
            'burn_analysis': self._analyze_burn_rate(expense_metrics, base_calculations),
            'optimization_opportunities': self._identify_optimizations(expense_metrics, revenue_metrics),
            'recommendations': self._generate_recommendations(expense_metrics, revenue_metrics)
        }
        
        return analysis
    
    def _create_summary(self, metrics: Dict) -> Dict:
        """Create expense summary"""
        return {
            'total_expenses': metrics.get('total_expenses', 0),
            'largest_expense': metrics.get('largest_expense', {}),
            'expense_categories': len(metrics.get('expense_categories', {})),
            'salary_percentage': metrics.get('salary_percentage', 0)
        }
    
    def _analyze_categories(self, metrics: Dict) -> Dict:
        """Analyze expense categories"""
        categories = metrics.get('expense_categories', {})
        total = metrics.get('total_expenses', 0)
        
        if not categories or total == 0:
            return {}
        
        # Categorize expenses
        categorized = {
            'people': {},
            'marketing': {},
            'technology': {},
            'operations': {},
            'other': {}
        }
        
        for expense_name, amount in categories.items():
            lower_name = expense_name.lower()
            percentage = (amount / total) * 100
            
            if any(term in lower_name for term in ['salary', 'salaries', 'payroll', 'compensation']):
                categorized['people'][expense_name] = {'amount': amount, 'percentage': percentage}
            elif any(term in lower_name for term in ['marketing', 'advertising', 'promotion']):
                categorized['marketing'][expense_name] = {'amount': amount, 'percentage': percentage}
            elif any(term in lower_name for term in ['software', 'hosting', 'technology', 'it', 'subscription']):
                categorized['technology'][expense_name] = {'amount': amount, 'percentage': percentage}
            elif any(term in lower_name for term in ['travel', 'office', 'general', 'bank']):
                categorized['operations'][expense_name] = {'amount': amount, 'percentage': percentage}
            else:
                categorized['other'][expense_name] = {'amount': amount, 'percentage': percentage}
        
        # Calculate category totals
        category_totals = {}
        for category, expenses in categorized.items():
            total_amount = sum(e['amount'] for e in expenses.values())
            category_totals[category] = {
                'total': total_amount,
                'percentage': (total_amount / total) * 100 if total > 0 else 0,
                'count': len(expenses)
            }
        
        return {
            'categorized_expenses': categorized,
            'category_totals': category_totals,
            'largest_category': max(category_totals.items(), key=lambda x: x[1]['total'])[0] if category_totals else None
        }
    
    def _calculate_efficiency(self, expense_metrics: Dict, revenue_metrics: Dict) -> Dict:
        """Calculate expense efficiency metrics"""
        total_expenses = expense_metrics.get('total_expenses', 0)
        total_revenue = revenue_metrics.get('total_revenue', 0)
        customer_count = revenue_metrics.get('customer_count', 1)
        
        return {
            'expense_ratio': (total_expenses / total_revenue * 100) if total_revenue > 0 else 0,
            'expense_per_customer': total_expenses / customer_count if customer_count > 0 else 0,
            'revenue_per_expense_dollar': total_revenue / total_expenses if total_expenses > 0 else 0,
            'efficiency_score': self._calculate_efficiency_score(expense_metrics, revenue_metrics)
        }
    
    def _analyze_burn_rate(self, expense_metrics: Dict, calculations: Dict) -> Dict:
        """Analyze burn rate and runway"""
        monthly_expenses = expense_metrics.get('total_expenses', 0) / 12  # Assuming annual
        monthly_revenue = calculations.get('revenue', {}).get('total_revenue', 0) / 12
        monthly_burn = monthly_expenses - monthly_revenue
        
        return {
            'monthly_expenses': monthly_expenses,
            'monthly_revenue': monthly_revenue,
            'monthly_burn_rate': max(0, monthly_burn),
            'is_cash_flow_positive': monthly_burn <= 0,
            'months_to_profitability': self._estimate_profitability_timeline(monthly_burn, calculations)
        }
    
    def _identify_optimizations(self, expense_metrics: Dict, revenue_metrics: Dict) -> List[Dict]:
        """Identify cost optimization opportunities"""
        optimizations = []
        categories = expense_metrics.get('expense_categories', {})
        total_expenses = expense_metrics.get('total_expenses', 0)
        total_revenue = revenue_metrics.get('total_revenue', 0)
        
        # Check for high expense ratios
        for expense_name, amount in categories.items():
            percentage = (amount / total_expenses * 100) if total_expenses > 0 else 0
            
            # High individual expenses
            if percentage > 20 and 'salaries' not in expense_name.lower():
                optimizations.append({
                    'category': expense_name,
                    'current_amount': amount,
                    'percentage_of_expenses': percentage,
                    'potential_savings': amount * 0.2,  # 20% reduction potential
                    'recommendation': f'Review {expense_name} for optimization opportunities'
                })
        
        # Technology optimization
        tech_expenses = sum(amount for name, amount in categories.items() 
                          if any(term in name.lower() for term in ['software', 'subscription', 'hosting']))
        if tech_expenses > total_revenue * 0.1:  # More than 10% of revenue
            optimizations.append({
                'category': 'Technology Stack',
                'current_amount': tech_expenses,
                'percentage_of_revenue': (tech_expenses / total_revenue * 100) if total_revenue > 0 else 0,
                'potential_savings': tech_expenses * 0.3,
                'recommendation': 'Consolidate technology stack and negotiate better rates'
            })
        
        return optimizations
    
    def _calculate_efficiency_score(self, expense_metrics: Dict, revenue_metrics: Dict) -> float:
        """Calculate overall expense efficiency score (0-100)"""
        score = 50  # Base score
        
        total_expenses = expense_metrics.get('total_expenses', 0)
        total_revenue = revenue_metrics.get('total_revenue', 0)
        
        if total_revenue > 0:
            expense_ratio = total_expenses / total_revenue
            
            # Better ratio = higher score
            if expense_ratio < 0.5:  # Expenses less than 50% of revenue
                score += 30
            elif expense_ratio < 0.8:
                score += 20
            elif expense_ratio < 1.0:
                score += 10
            else:
                score -= 10
        
        # Salary percentage check
        salary_pct = expense_metrics.get('salary_percentage', 0)
        if 40 <= salary_pct <= 60:  # Healthy range for service business
            score += 20
        elif salary_pct > 80:
            score -= 10
        
        return max(0, min(100, score))
    
    def _estimate_profitability_timeline(self, monthly_burn: float, calculations: Dict) -> str:
        """Estimate time to profitability"""
        if monthly_burn <= 0:
            return "Already profitable"
        
        # Use actual growth rate from data if available
        growth_metrics = calculations.get('growth', {})
        growth_rate = growth_metrics.get('mom_growth', 0) / 100  # Convert percentage to decimal
        
        # If no historical growth, use conservative estimate based on current metrics
        if growth_rate <= 0:
            # Base growth on unit economics quality
            unit_economics = calculations.get('unit_economics', {})
            ltv_cac = unit_economics.get('ltv_cac_ratio', 0)
            if ltv_cac > 4:
                growth_rate = 0.15  # 15% monthly for excellent unit economics
            elif ltv_cac > 3:
                growth_rate = 0.10  # 10% monthly for good unit economics
            else:
                growth_rate = 0.05  # 5% monthly conservative estimate
        
        months = 0
        current_monthly_revenue = calculations.get('revenue', {}).get('total_revenue', 0) / 12
        current_monthly_expenses = calculations.get('expenses', {}).get('total_expenses', 0) / 12
        
        while months < 36:
            months += 1
            current_monthly_revenue *= (1 + growth_rate)
            net_burn = current_monthly_expenses - current_monthly_revenue
            
            if net_burn <= 0:
                return f"{months} months"
        
        return ">36 months"
    
    def _generate_recommendations(self, expense_metrics: Dict, revenue_metrics: Dict) -> List[Dict]:
        """Generate expense recommendations"""
        recommendations = []
        
        total_expenses = expense_metrics.get('total_expenses', 0)
        total_revenue = revenue_metrics.get('total_revenue', 0)
        
        # Burn rate check
        if total_expenses > total_revenue:
            burn_rate = (total_expenses - total_revenue) / 12
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'Negative Cash Flow',
                'recommendation': f'Monthly burn rate of ${burn_rate:,.0f}',
                'action': 'Implement cost reduction plan or accelerate revenue growth'
            })
        
        # Salary percentage check
        salary_pct = expense_metrics.get('salary_percentage', 0)
        if salary_pct > 70:
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': 'High Salary Concentration',
                'recommendation': f'Salaries represent {salary_pct:.1f}% of expenses',
                'action': 'Review staffing efficiency and consider automation'
            })
        
        # Expense ratio check
        if total_revenue > 0:
            expense_ratio = (total_expenses / total_revenue) * 100
            if expense_ratio > 120:
                recommendations.append({
                    'priority': 'CRITICAL',
                    'issue': 'Unsustainable Expense Ratio',
                    'recommendation': f'Expenses are {expense_ratio:.1f}% of revenue',
                    'action': 'Immediate cost reduction required'
                })
        
        return recommendations