"""Cash Flow Analyzer - Analyzes cash position, burn rate, and runway"""

from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class CashFlowAnalyzer:
    def analyze(self, data: Dict[str, Any], calculations: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze cash flow patterns and runway
        
        Args:
            data: Raw financial data
            calculations: Calculated metrics from FinancialCalculator
            
        Returns:
            Cash flow analysis including runway, burn rate, and recommendations
        """
        try:
            # Extract cash flow metrics from calculations
            cash_flow_metrics = calculations.get('cash_flow', {})
            revenue_metrics = calculations.get('revenue', {})
            expense_metrics = calculations.get('expenses', {})
            
            # Get key metrics
            total_cash = cash_flow_metrics.get('ending_cash_balance', 0)
            monthly_burn = expense_metrics.get('monthly_burn', 0)
            monthly_revenue = revenue_metrics.get('total_revenue', 0) / 12
            net_cash_flow = monthly_revenue - monthly_burn
            runway_months = cash_flow_metrics.get('runway_months', 0)
            
            # Analyze cash flow trends
            analysis = {
                'summary': self._generate_summary(total_cash, runway_months, net_cash_flow),
                'metrics': {
                    'total_cash': total_cash,
                    'monthly_burn': monthly_burn,
                    'monthly_revenue': monthly_revenue,
                    'net_cash_flow': net_cash_flow,
                    'runway_months': runway_months,
                    'cash_efficiency': self._calculate_cash_efficiency(monthly_revenue, monthly_burn),
                    'burn_multiple': monthly_burn / monthly_revenue if monthly_revenue > 0 else float('inf')
                },
                'trends': self._analyze_trends(cash_flow_metrics),
                'health_score': self._calculate_health_score(runway_months, net_cash_flow, total_cash),
                'recommendations': self._generate_recommendations(runway_months, net_cash_flow, monthly_burn)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in cash flow analysis: {str(e)}")
            return {
                'summary': 'Error analyzing cash flow',
                'metrics': {},
                'error': str(e)
            }
    
    def _generate_summary(self, total_cash: float, runway_months: float, net_cash_flow: float) -> str:
        """Generate executive summary of cash position"""
        if runway_months < 3:
            status = "CRITICAL - Immediate funding required"
        elif runway_months < 6:
            status = "WARNING - Near-term funding needed"
        elif runway_months < 12:
            status = "CAUTION - Plan fundraising soon"
        else:
            status = "HEALTHY - Adequate runway"
        
        if net_cash_flow > 0:
            trend = "cash flow positive"
        elif net_cash_flow > -5000:
            trend = "approaching break-even"
        else:
            trend = "burning cash"
        
        return f"{status}. £{total_cash:,.0f} cash with {runway_months:.1f} months runway, {trend}."
    
    def _calculate_cash_efficiency(self, revenue: float, burn: float) -> float:
        """Calculate cash efficiency ratio"""
        if burn <= 0:
            return 1.0  # Profitable
        return revenue / burn if burn > 0 else 0
    
    def _analyze_trends(self, cash_flow_metrics: Dict) -> Dict[str, Any]:
        """Analyze cash flow trends"""
        # In a full implementation, this would analyze historical trends
        return {
            'direction': 'improving' if cash_flow_metrics.get('net_cash_flow', 0) > -5000 else 'declining',
            'volatility': 'low',  # Would calculate from historical data
            'seasonality': 'none detected'  # Would analyze from monthly patterns
        }
    
    def _calculate_health_score(self, runway_months: float, net_cash_flow: float, total_cash: float) -> int:
        """Calculate cash flow health score (0-100)"""
        score = 50  # Base score
        
        # Runway component (40 points max)
        if runway_months >= 12:
            score += 40
        elif runway_months >= 6:
            score += 20
        elif runway_months >= 3:
            score += 10
        else:
            score -= 20
        
        # Cash flow component (30 points max)
        if net_cash_flow > 0:
            score += 30
        elif net_cash_flow > -5000:
            score += 15
        elif net_cash_flow > -10000:
            score += 5
        else:
            score -= 10
        
        # Cash balance component (30 points max)
        if total_cash > 100000:
            score += 30
        elif total_cash > 50000:
            score += 20
        elif total_cash > 20000:
            score += 10
        else:
            score -= 10
        
        return max(0, min(100, score))
    
    def _generate_recommendations(self, runway_months: float, net_cash_flow: float, monthly_burn: float) -> List[Dict[str, str]]:
        """Generate actionable recommendations"""
        recommendations = []
        
        if runway_months < 6:
            recommendations.append({
                'priority': 'CRITICAL',
                'action': 'Initiate fundraising immediately',
                'impact': f'Extend runway beyond {runway_months:.1f} months',
                'timeline': 'Immediate'
            })
        
        if net_cash_flow < 0 and monthly_burn > 10000:
            recommendations.append({
                'priority': 'HIGH',
                'action': f'Reduce monthly burn from £{monthly_burn:,.0f}',
                'impact': f'Save £{monthly_burn * 0.2:,.0f} per month',
                'timeline': '1-2 months'
            })
        
        if net_cash_flow < -10000:
            recommendations.append({
                'priority': 'HIGH',
                'action': 'Accelerate revenue growth initiatives',
                'impact': 'Achieve cash flow breakeven',
                'timeline': '3-6 months'
            })
        
        if runway_months > 12 and net_cash_flow > -5000:
            recommendations.append({
                'priority': 'MEDIUM',
                'action': 'Plan strategic growth investments',
                'impact': 'Accelerate path to profitability',
                'timeline': '2-3 months'
            })
        
        return recommendations