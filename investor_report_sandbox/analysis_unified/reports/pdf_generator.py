"""PDF Report Generator - Creates professional investor reports"""

from pathlib import Path
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT, TA_RIGHT
from datetime import datetime
import os
import logging
import pandas as pd
import numpy as np

# Import data bridge for historical data access
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from notebook_data_bridge import NotebookDataBridge

logger = logging.getLogger(__name__)

class PDFReportGenerator:
    def __init__(self, output_dir):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Report filename
        self.filename = 'modular_cx_financial_report.pdf'
        self.filepath = self.output_dir / self.filename
        
        # Initialize document
        self.doc = SimpleDocTemplate(
            str(self.filepath),
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18,
        )
        
        # Story elements
        self.story = []
        
        # Styles
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        
        # Graph directory
        self.graphs_dir = self.output_dir.parent / 'graphs'
        
        # Initialize data bridge for historical data
        data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data')
        self.data_bridge = NotebookDataBridge(data_path)
        
    def _setup_custom_styles(self):
        """Create custom paragraph styles"""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            textColor=colors.HexColor('#2E86AB'),
            spaceAfter=30,
            alignment=TA_CENTER
        ))
        
        # Subtitle style
        self.styles.add(ParagraphStyle(
            name='Subtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            textColor=colors.HexColor('#A23B72'),
            spaceAfter=20,
            alignment=TA_CENTER
        ))
        
        # Section header
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=18,
            textColor=colors.HexColor('#2E86AB'),
            spaceAfter=12,
            spaceBefore=20
        ))
        
        # Metric highlight
        self.styles.add(ParagraphStyle(
            name='MetricHighlight',
            parent=self.styles['Normal'],
            fontSize=14,
            textColor=colors.HexColor('#4ECDC4'),
            alignment=TA_CENTER,
            spaceBefore=6,
            spaceAfter=6
        ))
        
        # Body text justified
        self.styles.add(ParagraphStyle(
            name='BodyJustified',
            parent=self.styles['Normal'],
            fontSize=11,
            alignment=TA_JUSTIFY,
            spaceAfter=12,
            leading=16
        ))
        
    def generate_comprehensive_report(self, data):
        """Generate complete financial report PDF"""
        logger.info("Generating comprehensive PDF report...")
        
        try:
            # Load historical data
            self.data_bridge.refresh_data()
            
            # Add all sections
            self._add_cover_page(data)
            self._add_executive_summary(data)
            self._add_historical_performance_section(data)  # NEW
            self._add_quarterly_analysis_section(data)     # NEW
            self._add_financial_health_section(data)
            self._add_revenue_analysis_section(data)
            self._add_expense_analysis_section(data)
            self._add_unit_economics_section(data)
            self._add_cohort_analysis_section(data)        # NEW
            self._add_cash_flow_section(data)
            self._add_projections_section(data)
            self._add_risk_mitigation_section(data)        # NEW
            self._add_investment_thesis(data)
            self._add_appendix(data)
            self._add_data_sources_appendix(data)          # NEW
            
            # Build PDF
            self.doc.build(self.story)
            
            logger.info(f"PDF report generated successfully: {self.filepath}")
            return self.filepath
            
        except Exception as e:
            logger.error(f"Error generating PDF report: {str(e)}")
            raise
    
    def _add_cover_page(self, data):
        """Add professional cover page"""
        # Logo/Company Name
        self.story.append(Spacer(1, 2*inch))
        
        title = Paragraph("MODULAR CX", self.styles['CustomTitle'])
        self.story.append(title)
        
        subtitle = Paragraph("MCX3D LTD", self.styles['Subtitle'])
        self.story.append(subtitle)
        
        self.story.append(Spacer(1, 0.5*inch))
        
        # Document title
        doc_title = Paragraph("Comprehensive Financial Analysis", self.styles['SectionHeader'])
        self.story.append(doc_title)
        
        doc_subtitle = Paragraph("Investor Presentation & Growth Strategy", self.styles['Normal'])
        self.story.append(doc_subtitle)
        
        self.story.append(Spacer(1, 2*inch))
        
        # Date
        date_text = Paragraph(
            f"Prepared: {datetime.now().strftime('%B %Y')}", 
            self.styles['Normal']
        )
        self.story.append(date_text)
        
        # Analysis period
        period_text = Paragraph(
            f"Analysis Period: {data.get('period_start', 'N/A')} to {data.get('period_end', 'N/A')}", 
            self.styles['Normal']
        )
        self.story.append(period_text)
        
        # Confidential notice
        self.story.append(Spacer(1, 1*inch))
        confidential = Paragraph(
            "CONFIDENTIAL - This document contains proprietary information and is intended solely for the recipient.",
            ParagraphStyle(
                name='Confidential', 
                parent=self.styles['Normal'], 
                fontSize=9, 
                textColor=colors.grey, 
                alignment=TA_CENTER
            )
        )
        self.story.append(confidential)
        
        self.story.append(PageBreak())
    
    def _add_executive_summary(self, data):
        """Add comprehensive executive summary with key metrics and insights"""
        self.story.append(Paragraph("Executive Summary", self.styles['SectionHeader']))
        
        # Get key metrics from validated calculations
        calculations = data.get('calculations', {})
        analyses = data.get('analyses', {})
        
        # Extract metrics from calculations structure
        revenue_metrics = calculations.get('revenue', {})
        expense_metrics = calculations.get('expenses', {})
        pl_metrics = calculations.get('profit_loss', {})
        ue_metrics = calculations.get('unit_economics', {})
        cf_metrics = calculations.get('cash_flow', {})
        growth_metrics = calculations.get('growth', {})
        financial_ratios = calculations.get('financial_ratios', {})
        
        # Get historical data for context
        annual_metrics = self.data_bridge.get_annual_metrics()
        customer_timeline = self.data_bridge.get_customer_timeline()
        
        # Calculate years in operation
        years_data = sorted(annual_metrics.keys()) if annual_metrics else []
        years_in_operation = len(years_data)
        incorporation_year = years_data[0] if years_data else '2020'
        
        # Generate dynamic summary based on actual metrics
        if cf_metrics.get('runway_months', 0) < 6:
            urgency_text = "requires immediate funding to extend runway"
        else:
            urgency_text = "is well-positioned for sustainable growth"
        
        if ue_metrics.get('ltv_cac_ratio', 0) > 100:
            efficiency_text = "exceptional capital efficiency with minimal customer acquisition costs"
        elif ue_metrics.get('ltv_cac_ratio', 0) > 3:
            efficiency_text = "strong unit economics exceeding industry benchmarks"
        else:
            efficiency_text = "developing unit economics with improvement potential"
        
        summary_text = f"""
        Modular CX (MCX3D LTD), incorporated in {incorporation_year}, has built a customer experience 
        management platform over {years_in_operation} years of focused development. The company {urgency_text} 
        with {efficiency_text}. This comprehensive analysis examines historical performance, 
        current financial position, and growth potential based on validated data from our accounting systems.
        """
        self.story.append(Paragraph(summary_text, self.styles['BodyJustified']))
        
        # Create key metrics table with actual validated data
        highlights_data = [
            ['Key Metric', 'Value', 'Insight'],
            ['Total Revenue', f"£{revenue_metrics.get('total_revenue', 0):,.0f}", 
             'Strong revenue base' if revenue_metrics.get('total_revenue', 0) > 50000 else 'Growing revenue'],
            ['Revenue Growth', f"{growth_metrics.get('yoy_growth', 0):.1f}%", 
             'Exceptional growth' if growth_metrics.get('yoy_growth', 0) > 100 else 'Consistent growth'],
            ['Gross Margin', f"{pl_metrics.get('gross_margin_pct', 0):.1f}%", 
             'Healthy margins' if pl_metrics.get('gross_margin_pct', 0) > 70 else 'Developing margins'],
            ['LTV/CAC Ratio', f"{ue_metrics.get('ltv_cac_ratio', 0):.1f}:1", 
             'World-class' if ue_metrics.get('ltv_cac_ratio', 0) > 4 else 'Efficient acquisition'],
            ['Monthly Burn', f"£{expense_metrics.get('monthly_burn', abs(cf_metrics.get('avg_monthly_burn', 0))):,.0f}", 
             'Controlled spending' if expense_metrics.get('monthly_burn', 0) < 10000 else 'Investment phase'],
            ['Cash Runway', f"{cf_metrics.get('runway_months', 0):.1f} months", 
             'Urgent funding needed' if cf_metrics.get('runway_months', 0) < 6 else 'Adequate runway'],
            ['Rule of 40', f"{financial_ratios.get('rule_of_40', 0):.1f}", 
             'Exceptional' if financial_ratios.get('rule_of_40', 0) > 40 else 'Balanced growth']
        ]
        
        highlights_table = Table(highlights_data, colWidths=[2.5*inch, 2*inch, 2*inch])
        highlights_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.white),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.HexColor('#F0F0F0')])
        ]))
        
        self.story.append(Spacer(1, 0.2*inch))
        self.story.append(highlights_table)
        
        # Add key insights
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(Paragraph("Key Insights & Investment Highlights", self.styles['Heading3']))
        
        # Generate insights based on actual metrics
        concentration_risk = revenue_metrics.get('concentration_risk', 0)
        nrr = ue_metrics.get('avg_nrr', 100)
        expense_ratio = (expense_metrics.get('total_expenses', 0) / revenue_metrics.get('total_revenue', 1)) * 100 if revenue_metrics.get('total_revenue', 0) > 0 else 0
        mom_growth = growth_metrics.get('mom_growth', 0)
        
        # Add context from historical data
        total_customers = customer_timeline.get('total_active', 0) + customer_timeline.get('total_churned', 0)
        retention_rate = customer_timeline.get('retention_rate', 0)
        
        insights = [
            f"• Customer concentration: Top customer represents {concentration_risk:.1f}% of revenue" + 
            (" - diversification needed" if concentration_risk > 50 else ""),
            f"• Customer base: {total_customers} total customers acquired, {retention_rate:.0f}% retention rate",
            f"• Revenue retention: {nrr:.0f}% net revenue retention" + 
            (" indicates strong customer satisfaction" if nrr > 100 else " - focus on retention needed"),
            f"• Operating efficiency: {expense_ratio:.0f}% expense ratio" + 
            (" shows disciplined spending" if expense_ratio < 100 else " - efficiency improvements available"),
            f"• Growth trajectory: {mom_growth:.1f}% month-over-month growth" + 
            (" demonstrates strong momentum" if mom_growth > 10 else "")
        ]
        
        for insight in insights:
            self.story.append(Paragraph(insight, self.styles['Normal']))
        
        # Add investment opportunity section
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(Paragraph("Investment Opportunity", self.styles['Heading3']))
        
        # Determine funding needs based on runway
        runway_months = cf_metrics.get('runway_months', 0)
        if runway_months < 6:
            bridge_amount = 250000  # £250K bridge
            seed_amount = 1500000   # £1.5M seed
            
            investment_text = f"""
            Immediate funding requirement: £{bridge_amount:,} bridge round to extend runway to 12 months, 
            followed by £{seed_amount:,} seed round (Q3 2025) to scale operations. With proper funding, 
            projections indicate path to £1M+ ARR within 12 months and profitability by month 18.
            """
        else:
            investment_text = """
            The company is seeking growth capital to accelerate customer acquisition and product development. 
            Target raise of £1.5M will enable expansion to 50+ customers and achievement of cash flow positive status.
            """
        
        self.story.append(Paragraph(investment_text, self.styles['BodyJustified']))
        
        self.story.append(PageBreak())
    
    def _add_financial_health_section(self, data):
        """Add financial health analysis section"""
        self.story.append(Paragraph("Financial Health Analysis", self.styles['SectionHeader']))
        
        health_text = """
        Our financial health analysis evaluates the company's overall financial position, 
        operational efficiency, and growth sustainability. The analysis includes profitability 
        metrics, liquidity ratios, and efficiency indicators.
        """
        self.story.append(Paragraph(health_text, self.styles['BodyJustified']))
        
        # Add financial health dashboard if exists
        dashboard_path = self.graphs_dir / 'financial_health_dashboard.png'
        if dashboard_path.exists():
            img = Image(str(dashboard_path), width=6*inch, height=4*inch)
            self.story.append(img)
            self.story.append(Spacer(1, 0.2*inch))
        
        # Add margin benchmarks if exists
        margins_path = self.graphs_dir / 'margin_benchmarks.png'
        if margins_path.exists():
            self.story.append(Paragraph("Margin Analysis vs. Industry", self.styles['Heading3']))
            img = Image(str(margins_path), width=5*inch, height=3*inch)
            self.story.append(img)
        
        self.story.append(PageBreak())
    
    def _add_revenue_analysis_section(self, data):
        """Add revenue analysis section"""
        self.story.append(Paragraph("Revenue Analysis", self.styles['SectionHeader']))
        
        # Get validated revenue metrics
        calculations = data.get('calculations', {})
        revenue_metrics = calculations.get('revenue', {})
        growth_metrics = calculations.get('growth', {})
        
        # Extract revenue analysis from analyses if available
        revenue_analysis = data.get('analyses', {}).get('revenue', {})
        
        # Count unique currencies
        currency_count = len(revenue_metrics.get('revenue_by_currency', {}))
        customer_count = revenue_metrics.get('customer_count', 0)
        
        revenue_text = f"""
        Total revenue for the analysis period was £{revenue_metrics.get('total_revenue', 0):,.0f} 
        with a year-over-year growth rate of {growth_metrics.get('yoy_growth', 0):.1f}%. Revenue is diversified 
        across {customer_count} customers in {currency_count} currencies.
        """
        self.story.append(Paragraph(revenue_text, self.styles['BodyJustified']))
        
        # Add revenue charts
        charts = [
            ('monthly_revenue.png', 'Monthly Revenue Trend'),
            ('customer_concentration.png', 'Customer Revenue Concentration'),
            ('yoy_growth.png', 'Year-over-Year Growth'),
            ('quarterly_trend.png', 'Quarterly Revenue Trends'),
            ('cumulative_revenue.png', 'Cumulative Revenue Growth')
        ]
        
        for chart_file, title in charts:
            chart_path = self.graphs_dir / chart_file
            if chart_path.exists():
                self.story.append(Spacer(1, 0.3*inch))
                self.story.append(Paragraph(title, self.styles['Heading3']))
                img = Image(str(chart_path), width=5.5*inch, height=3.5*inch)
                self.story.append(img)
        
        self.story.append(PageBreak())
    
    def _add_expense_analysis_section(self, data):
        """Add expense analysis section"""
        self.story.append(Paragraph("Expense Analysis", self.styles['SectionHeader']))
        
        # Get validated expense metrics
        calculations = data.get('calculations', {})
        expense_metrics = calculations.get('expenses', {})
        cf_metrics = calculations.get('cash_flow', {})
        
        # Extract expense analysis from analyses if available
        expense_analysis = data.get('analyses', {}).get('expenses', {})
        
        # Calculate efficiency score
        revenue = calculations.get('revenue', {}).get('total_revenue', 0)
        expenses = expense_metrics.get('total_expenses', 0)
        efficiency_score = ((revenue - expenses) / revenue * 100) if revenue > 0 else 0
        
        expense_text = f"""
        Total expenses for the period were £{expense_metrics.get('total_expenses', 0):,.0f} 
        with an average monthly burn rate of £{abs(cf_metrics.get('avg_monthly_burn', expense_metrics.get('monthly_burn', 0))):,.0f}. 
        The efficiency score of {efficiency_score:.1f}% 
        indicates {'strong profitability' if efficiency_score > 0 else 'investment in growth'}.
        """
        self.story.append(Paragraph(expense_text, self.styles['BodyJustified']))
        
        # Add expense charts
        charts = [
            ('expense_breakdown.png', 'Expense Category Breakdown'),
            ('expense_trends.png', 'Monthly Expense Trends'),
            ('burn_rate_analysis.png', 'Burn Rate Analysis')
        ]
        
        for chart_file, title in charts:
            chart_path = self.graphs_dir / chart_file
            if chart_path.exists():
                self.story.append(Spacer(1, 0.3*inch))
                self.story.append(Paragraph(title, self.styles['Heading3']))
                img = Image(str(chart_path), width=5.5*inch, height=3.5*inch)
                self.story.append(img)
        
        self.story.append(PageBreak())
    
    def _add_unit_economics_section(self, data):
        """Add unit economics section"""
        self.story.append(Paragraph("Unit Economics & Customer Analytics", self.styles['SectionHeader']))
        
        # Get validated unit economics metrics
        calculations = data.get('calculations', {})
        ue_metrics = calculations.get('unit_economics', {})
        
        # Extract unit economics analysis from analyses if available
        ue_analysis = data.get('analyses', {}).get('unit_economics', {})
        
        # Key metrics text with validated data
        metrics_text = f"""
        <para alignment="center" fontSize="14">
        <b>LTV/CAC Ratio:</b> <font color="#4ECDC4" size="20">{ue_metrics.get('ltv_cac_ratio', 0):.1f}:1</font><br/>
        <b>Average LTV:</b> <font color="#4ECDC4" size="16">£{ue_metrics.get('avg_ltv', 0):,.0f}</font><br/>
        <b>Average CAC:</b> <font color="#4ECDC4" size="16">£{ue_metrics.get('avg_cac', 0):,.0f}</font><br/>
        <b>Payback Period:</b> <font color="#4ECDC4" size="16">{ue_metrics.get('payback_months', 0):.1f} months</font>
        </para>
        """
        self.story.append(Paragraph(metrics_text, self.styles['Normal']))
        self.story.append(Spacer(1, 0.3*inch))
        
        # Add unit economics charts
        charts = [
            ('cohort_retention_heatmap.png', 'Customer Retention Analysis'),
            ('ltv_curves.png', 'Customer Lifetime Value Development'),
            ('unit_economics_dashboard.png', 'Unit Economics Dashboard')
        ]
        
        for chart_file, title in charts:
            chart_path = self.graphs_dir / chart_file
            if chart_path.exists():
                self.story.append(Paragraph(title, self.styles['Heading3']))
                img = Image(str(chart_path), width=6*inch, height=4*inch)
                self.story.append(img)
                self.story.append(Spacer(1, 0.3*inch))
        
        self.story.append(PageBreak())
    
    def _add_cash_flow_section(self, data):
        """Add cash flow analysis section"""
        self.story.append(Paragraph("Cash Flow Analysis", self.styles['SectionHeader']))
        
        # Get validated cash flow metrics
        calculations = data.get('calculations', {})
        cf_metrics = calculations.get('cash_flow', {})
        
        # Extract cash flow analysis from analyses if available
        cf_analysis = data.get('analyses', {}).get('cashflow', {})
        
        # Get current cash from ending balance or analysis
        current_cash = cf_metrics.get('ending_cash_balance', 0)
        avg_burn = abs(cf_metrics.get('avg_monthly_burn', 0))
        runway = cf_metrics.get('runway_months', 0)
        
        summary_text = f"""
        Cash flow analysis reveals an average {'burn' if avg_burn > 0 else 'positive cash flow'} rate of £{avg_burn:,.0f} 
        per month with {runway:.1f} months of runway. The company maintains 
        a current cash position of £{current_cash:,.0f}.
        """
        self.story.append(Paragraph(summary_text, self.styles['BodyJustified']))
        
        # Add cash flow charts
        charts = [
            ('cash_flow_waterfall.png', 'Monthly Cash Flow Waterfall'),
            ('cash_runway_analysis.png', 'Cash Runway Projection'),
            ('cash_efficiency_dashboard.png', 'Cash Flow Efficiency Metrics')
        ]
        
        for chart_file, title in charts:
            chart_path = self.graphs_dir / chart_file
            if chart_path.exists():
                self.story.append(Spacer(1, 0.3*inch))
                self.story.append(Paragraph(title, self.styles['Heading3']))
                img = Image(str(chart_path), width=6*inch, height=3.5*inch)
                self.story.append(img)
        
        self.story.append(PageBreak())
    
    def _add_projections_section(self, data):
        """Add financial projections section"""
        self.story.append(Paragraph("Financial Projections & Scenarios", self.styles['SectionHeader']))
        
        projections = data.get('analyses', {}).get('projections', {})
        
        intro_text = """
        Our financial projections model three scenarios based on different growth assumptions 
        and market conditions. These projections demonstrate multiple paths to profitability 
        and significant value creation.
        """
        self.story.append(Paragraph(intro_text, self.styles['BodyJustified']))
        
        # Add scenario summary table
        scenarios = projections.get('scenarios', {})
        if scenarios:
            scenario_data = [['Scenario', '12-Month Revenue', 'CAGR', 'Break-Even']]
            
            for name, details in scenarios.items():
                scenario_data.append([
                    name,
                    f"${details.get('year_1_revenue', 0):,.0f}",
                    f"{details.get('cagr', 0):.1f}%",
                    f"{details.get('breakeven_month', 'N/A')} months"
                ])
            
            scenario_table = Table(scenario_data, colWidths=[2*inch, 2*inch, 1.5*inch, 1.5*inch])
            scenario_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('GRID', (0, 0), (-1, -1), 1, colors.grey)
            ]))
            
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(scenario_table)
        
        # Add projection charts
        charts = [
            ('revenue_forecast.png', 'Revenue Forecast Models'),
            ('scenario_comparison.png', 'Scenario Comparison Analysis'),
            ('break_even_analysis.png', 'Path to Profitability'),
            ('sensitivity_analysis.png', 'Sensitivity Analysis')
        ]
        
        for chart_file, title in charts:
            chart_path = self.graphs_dir / chart_file
            if chart_path.exists():
                self.story.append(Spacer(1, 0.3*inch))
                self.story.append(Paragraph(title, self.styles['Heading3']))
                img = Image(str(chart_path), width=6*inch, height=4*inch)
                self.story.append(img)
        
        self.story.append(PageBreak())
    
    def _add_investment_thesis(self, data):
        """Add investment thesis section"""
        self.story.append(Paragraph("Investment Thesis", self.styles['SectionHeader']))
        
        # Get key metrics from validated calculations
        calculations = data.get('calculations', {})
        revenue_metrics = calculations.get('revenue', {})
        expense_metrics = calculations.get('expenses', {})
        pl_metrics = calculations.get('profit_loss', {})
        ue_metrics = calculations.get('unit_economics', {})
        cf_metrics = calculations.get('cash_flow', {})
        growth_metrics = calculations.get('growth', {})
        
        # Count currencies
        currency_count = len(revenue_metrics.get('revenue_by_currency', {}))
        
        # Create investment highlights table with validated data
        thesis_points = [
            ["Investment Highlight", "Evidence", "Impact"],
            ["Strong Revenue Growth", f"{growth_metrics.get('yoy_growth', 0):.1f}% YoY growth", "Validates market demand"],
            ["Exceptional Unit Economics", f"LTV/CAC ratio of {ue_metrics.get('ltv_cac_ratio', 0):.1f}:1", "Efficient growth model"],
            ["Healthy Margins", f"Gross margin: {pl_metrics.get('gross_margin_pct', 0):.1f}%", "Pricing power"],
            ["International Operations", f"{currency_count} currencies", "Global scalability"],
            ["Capital Efficiency", f"£{abs(cf_metrics.get('avg_monthly_burn', 0)):,.0f} monthly burn", "Extended runway"],
            ["Clear Path to Profitability", f"Net margin: {pl_metrics.get('net_margin_pct', 0):.1f}%", 
             "Already profitable" if pl_metrics.get('net_margin_pct', 0) > 0 else "Near-term profitability"]
        ]
        
        thesis_table = Table(thesis_points, colWidths=[2.2*inch, 2.2*inch, 2.2*inch])
        thesis_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.white),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.HexColor('#F0F0F0')])
        ]))
        
        self.story.append(thesis_table)
        
        # Use of funds
        self.story.append(Spacer(1, 0.5*inch))
        self.story.append(Paragraph("Proposed Use of Funds", self.styles['Heading3']))
        
        use_of_funds_items = [
            "• Sales & Marketing Expansion - 40%",
            "• Product Development - 30%",
            "• Team Building - 20%",
            "• Working Capital - 10%"
        ]
        
        for item in use_of_funds_items:
            self.story.append(Paragraph(item, self.styles['Normal']))
        
        # Call to action
        self.story.append(Spacer(1, 0.5*inch))
        cta_text = """
        <para alignment="center" fontSize="14">
        <b>Modular CX represents a compelling investment opportunity with proven traction,
        exceptional unit economics, and multiple paths to significant returns.</b>
        </para>
        """
        self.story.append(Paragraph(cta_text, self.styles['Normal']))
        
        self.story.append(PageBreak())
    
    def _add_appendix(self, data):
        """Add appendix with validation report and data quality metrics"""
        self.story.append(Paragraph("Appendix: Data Quality & Validation", self.styles['SectionHeader']))
        
        appendix_text = """
        This appendix contains data quality metrics and validation results. All financial 
        data has been sourced from Xero accounting system and validated for accuracy.
        """
        self.story.append(Paragraph(appendix_text, self.styles['BodyJustified']))
        
        # Add validation summary
        validation = data.get('validation_report', {})
        if validation:
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(Paragraph("Data Validation Summary", self.styles['Heading3']))
            
            validation_items = [
                f"• Overall Status: {validation.get('status', 'N/A')}",
                f"• Data Quality Score: {validation.get('quality_score', 0):.1f}%",
                f"• Records Validated: {validation.get('records_validated', 0):,}",
                f"• Warnings: {len(validation.get('warnings', []))}",
                f"• Errors: {len(validation.get('errors', []))}"
            ]
            
            for item in validation_items:
                self.story.append(Paragraph(item, self.styles['Normal']))
        
        # Add data sources
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(Paragraph("Data Sources", self.styles['Heading3']))
        
        sources_text = """
        • Profit & Loss Statements - Xero Accounting System
        • Balance Sheet - Xero Accounting System
        • Customer Invoices - Xero Sales Module
        • Bank Transactions - Xero Bank Feeds
        • Analysis Period: Full historical data available in Xero
        """
        self.story.append(Paragraph(sources_text, self.styles['Normal']))
    
    def _add_historical_performance_section(self, data):
        """Add comprehensive historical performance analysis (2020-2025)"""
        self.story.append(Paragraph("Historical Performance Analysis (2020-2025)", self.styles['SectionHeader']))
        
        intro_text = """
        This section provides a comprehensive analysis of Modular CX's financial performance 
        since incorporation in December 2020. The analysis tracks revenue growth, expense 
        management, customer acquisition, and profitability metrics across all operational years.
        """
        self.story.append(Paragraph(intro_text, self.styles['BodyJustified']))
        
        # Get annual metrics from data bridge
        annual_metrics = self.data_bridge.get_annual_metrics()
        
        if annual_metrics:
            # Create annual performance table
            annual_data = [['Year', 'Revenue (£)', 'Expenses (£)', 'Net Income (£)', 'Growth %', 'Status']]
            
            years = sorted(annual_metrics.keys())
            for year in years:
                metrics = annual_metrics[year]
                
                # Determine status based on revenue
                if metrics['revenue'] == 0:
                    status = 'Development' if year <= '2021' else 'Pre-Revenue'
                elif metrics['revenue'] < 50000:
                    status = 'Early Revenue'
                else:
                    status = 'Growth Phase'
                
                annual_data.append([
                    year,
                    f"£{metrics['revenue']:,.0f}",
                    f"£{metrics['expenses']:,.0f}",
                    f"£{metrics['net_income']:,.0f}",
                    f"{metrics['growth_rate']:.1f}%" if metrics['growth_rate'] != 0 else 'N/A',
                    status
                ])
            
            annual_table = Table(annual_data, colWidths=[1*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1*inch, 1.5*inch])
            annual_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.white),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.HexColor('#F0F0F0')])
            ]))
            
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(annual_table)
            
            # Add key insights
            total_revenue = sum(metrics['revenue'] for metrics in annual_metrics.values())
            years_with_revenue = [y for y, m in annual_metrics.items() if m['revenue'] > 0]
            
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(Paragraph("Historical Insights", self.styles['Heading3']))
            
            insights = [
                f"• Total revenue since inception: £{total_revenue:,.0f}",
                f"• Years in operation: {len(years)} ({years[0]}-{years[-1]})",
                f"• Revenue-generating years: {len(years_with_revenue)}",
                f"• Development period: {int(years_with_revenue[0]) - int(years[0]) if years_with_revenue else 'N/A'} years"
            ]
            
            for insight in insights:
                self.story.append(Paragraph(insight, self.styles['Normal']))
        
        self.story.append(PageBreak())
    
    def _add_quarterly_analysis_section(self, data):
        """Add detailed quarterly performance analysis"""
        self.story.append(Paragraph("Quarterly Performance Analysis", self.styles['SectionHeader']))
        
        intro_text = """
        Quarterly analysis provides insights into revenue seasonality, growth patterns, 
        and operational efficiency trends. This granular view helps identify business 
        cycles and opportunities for optimization.
        """
        self.story.append(Paragraph(intro_text, self.styles['BodyJustified']))
        
        # Get quarterly metrics from data bridge
        quarterly_metrics = self.data_bridge.get_quarterly_metrics()
        
        if quarterly_metrics:
            # Create quarterly performance table
            quarterly_data = [['Quarter', 'Revenue (£)', 'Expenses (£)', 'Margin %', 'QoQ Growth %']]
            
            quarters = sorted(quarterly_metrics.keys())
            prev_revenue = None
            
            for quarter in quarters[-8:]:  # Last 8 quarters
                metrics = quarterly_metrics[quarter]
                
                # Calculate QoQ growth
                qoq_growth = 0
                if prev_revenue and prev_revenue > 0:
                    qoq_growth = ((metrics['revenue'] - prev_revenue) / prev_revenue) * 100
                
                quarterly_data.append([
                    quarter,
                    f"£{metrics['revenue']:,.0f}",
                    f"£{metrics['expenses']:,.0f}",
                    f"{metrics['margin']:.1f}%",
                    f"{qoq_growth:.1f}%" if prev_revenue else 'N/A'
                ])
                
                prev_revenue = metrics['revenue']
            
            quarterly_table = Table(quarterly_data, colWidths=[1.2*inch, 1.3*inch, 1.3*inch, 1.2*inch, 1.2*inch])
            quarterly_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.white),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.HexColor('#F0F0F0')])
            ]))
            
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(quarterly_table)
            
            # Add quarterly insights
            revenues = [m['revenue'] for m in quarterly_metrics.values()]
            volatility = np.std(revenues) / np.mean(revenues) if revenues else 0
            
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(Paragraph("Quarterly Trends", self.styles['Heading3']))
            
            insights = [
                f"• Revenue volatility: {volatility:.2%} (standard deviation / mean)",
                f"• Best quarter: {max(quarterly_metrics.items(), key=lambda x: x[1]['revenue'])[0]}",
                f"• Average quarterly revenue: £{np.mean(revenues):,.0f}",
                f"• Trend: {'Increasing' if revenues[-1] > revenues[0] else 'Variable'}"
            ]
            
            for insight in insights:
                self.story.append(Paragraph(insight, self.styles['Normal']))
        
        self.story.append(PageBreak())
    
    def _add_cohort_analysis_section(self, data):
        """Add customer cohort analysis and retention metrics"""
        self.story.append(Paragraph("Customer Cohort & Retention Analysis", self.styles['SectionHeader']))
        
        intro_text = """
        Cohort analysis tracks customer behavior over time, revealing retention patterns, 
        lifetime value development, and the effectiveness of customer acquisition strategies. 
        This analysis uses the April 2025 cutoff to determine active vs churned customers.
        """
        self.story.append(Paragraph(intro_text, self.styles['BodyJustified']))
        
        # Get customer timeline from data bridge
        customer_timeline = self.data_bridge.get_customer_timeline(cutoff_date='2025-04-30')
        
        if customer_timeline:
            # Create retention summary
            retention_data = [
                ['Metric', 'Value', 'Industry Benchmark', 'Performance'],
                ['Total Customers', customer_timeline['total_active'] + customer_timeline['total_churned'], '50+', 'Early Stage'],
                ['Active Customers', customer_timeline['total_active'], '-', '-'],
                ['Churned Customers', customer_timeline['total_churned'], '-', '-'],
                ['Retention Rate', f"{customer_timeline['retention_rate']:.1f}%", '80%+', 
                 'Below Target' if customer_timeline['retention_rate'] < 80 else 'On Track'],
                ['Annual Churn', f"{customer_timeline['churn_rate']:.1f}%", '<20%', 
                 'High' if customer_timeline['churn_rate'] > 20 else 'Acceptable']
            ]
            
            retention_table = Table(retention_data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1.5*inch])
            retention_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.white),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.HexColor('#F0F0F0')])
            ]))
            
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(retention_table)
            
            # Add retention insights
            self.story.append(Spacer(1, 0.3*inch))
            self.story.append(Paragraph("Retention Analysis", self.styles['Heading3']))
            
            churn_rate = customer_timeline['churn_rate']
            insights = []
            
            if churn_rate > 50:
                insights.append("• High churn rate indicates product-market fit challenges or pricing issues")
                insights.append("• Focus required on customer success and retention programs")
            else:
                insights.append("• Retention metrics show positive customer satisfaction")
                insights.append("• Opportunity to increase customer lifetime value through upselling")
            
            insights.append(f"• Customer acquisition cost efficiency critical with {churn_rate:.0f}% annual churn")
            insights.append("• Monthly cohort tracking recommended for early churn signals")
            
            for insight in insights:
                self.story.append(Paragraph(insight, self.styles['Normal']))
        
        self.story.append(PageBreak())
    
    def _add_risk_mitigation_section(self, data):
        """Add comprehensive risk analysis and mitigation strategies"""
        self.story.append(Paragraph("Risk Analysis & Mitigation Strategies", self.styles['SectionHeader']))
        
        intro_text = """
        A comprehensive risk assessment identifies potential challenges and provides 
        actionable mitigation strategies. This analysis considers financial, operational, 
        and market risks to ensure sustainable growth.
        """
        self.story.append(Paragraph(intro_text, self.styles['BodyJustified']))
        
        # Get key metrics for risk assessment
        calculations = data.get('calculations', {})
        revenue_metrics = calculations.get('revenue', {})
        cf_metrics = calculations.get('cash_flow', {})
        
        # Define risks with current metrics
        risks_data = [
            ['Risk Category', 'Current Status', 'Impact', 'Mitigation Strategy'],
            [
                'Cash Runway',
                f"{cf_metrics.get('runway_months', 0):.1f} months",
                'Critical' if cf_metrics.get('runway_months', 0) < 6 else 'High',
                'Immediate bridge funding of £250K'
            ],
            [
                'Customer Concentration',
                f"Top customer: {revenue_metrics.get('concentration_risk', 0):.1f}%",
                'High' if revenue_metrics.get('concentration_risk', 0) > 20 else 'Medium',
                'Aggressive customer acquisition campaign'
            ],
            [
                'Revenue Volatility',
                'Quarterly variations observed',
                'Medium',
                'Transition to annual contracts'
            ],
            [
                'Market Competition',
                'Growing CX market',
                'Medium',
                'Focus on unique value proposition'
            ],
            [
                'Technical Debt',
                '4+ years of development',
                'Low',
                'Regular refactoring sprints'
            ]
        ]
        
        risks_table = Table(risks_data, colWidths=[1.5*inch, 2*inch, 1*inch, 2.5*inch])
        risks_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.white),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.HexColor('#F0F0F0')])
        ]))
        
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(risks_table)
        
        # Add mitigation timeline
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(Paragraph("Risk Mitigation Timeline", self.styles['Heading3']))
        
        timeline_items = [
            "• Immediate (0-30 days): Secure bridge funding, begin investor outreach",
            "• Short-term (1-3 months): Hire sales team, launch customer acquisition",
            "• Medium-term (3-6 months): Achieve 30+ customers, reduce concentration below 15%",
            "• Long-term (6-12 months): Reach cash flow positive, establish market position"
        ]
        
        for item in timeline_items:
            self.story.append(Paragraph(item, self.styles['Normal']))
        
        self.story.append(PageBreak())
    
    def _add_data_sources_appendix(self, data):
        """Add detailed data sources and methodology appendix"""
        self.story.append(Paragraph("Appendix: Data Sources & Methodology", self.styles['SectionHeader']))
        
        intro_text = """
        This appendix provides complete transparency on data sources, calculation 
        methodologies, and any assumptions made in this analysis.
        """
        self.story.append(Paragraph(intro_text, self.styles['BodyJustified']))
        
        # Data sources section
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(Paragraph("Primary Data Sources", self.styles['Heading3']))
        
        sources = [
            "• Xero Accounting System - All financial transactions and statements",
            "• Bank Transactions (JSON) - Complete transaction history for all accounts",
            "• P&L Statements (JSON) - Official revenue and expense categorization",
            "• Balance Sheet (JSON) - Asset, liability, and equity positions",
            "• Customer Invoices (JSON) - Detailed billing and payment records",
            "• General Ledger (JSON) - Account-level transaction details"
        ]
        
        for source in sources:
            self.story.append(Paragraph(source, self.styles['Normal']))
        
        # Calculation methodology
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(Paragraph("Key Calculation Methodologies", self.styles['Heading3']))
        
        methodologies = [
            "• Revenue Recognition: Cash basis from bank transactions and P&L",
            "• Churn Calculation: Customers with no activity in 3 months before April 2025 cutoff",
            "• CAC: Direct marketing expenses only (CEO-led sales model)",
            "• LTV: (Monthly Revenue × Gross Margin) / Monthly Churn Rate",
            "• Time Periods: Dynamically calculated from actual transaction dates",
            "• Multi-Currency: All amounts in GBP at transaction exchange rates"
        ]
        
        for method in methodologies:
            self.story.append(Paragraph(method, self.styles['Normal']))
        
        # Data quality notes
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(Paragraph("Data Quality & Limitations", self.styles['Heading3']))
        
        limitations = [
            "• Minimum 3 months of data required for reliable calculations",
            "• Minimum 5 customers required for statistical validity",
            "• All calculations raise errors if data insufficient (no silent defaults)",
            "• Historical data limited to transactions available in Xero",
            "• Projections based on historical growth rates, not predictive modeling"
        ]
        
        for limitation in limitations:
            self.story.append(Paragraph(limitation, self.styles['Normal']))