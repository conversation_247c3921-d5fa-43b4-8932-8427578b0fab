"""Visualization Generator - Creates all financial charts"""

import os
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

class VisualizationGenerator:
    def __init__(self, output_dir):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Color scheme
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#4ECDC4',
            'warning': '#F18F01',
            'success': '#06D6A0',
            'danger': '#E71D36',
            'dark': '#2D3436',
            'light': '#DFE6E9'
        }
        
    def create_revenue_charts(self, revenue_data, analysis):
        """Create all revenue-related charts"""
        # Monthly revenue chart
        self._create_monthly_revenue_chart(revenue_data, analysis)
        
        # Customer concentration chart
        self._create_customer_concentration_chart(revenue_data)
        
        # YoY growth chart
        self._create_yoy_growth_chart(revenue_data)
        
        # Quarterly trend chart
        self._create_quarterly_trend_chart(revenue_data)
        
        # Cumulative revenue chart
        self._create_cumulative_revenue_chart(revenue_data)
    
    def create_expense_charts(self, expense_data, analysis):
        """Create all expense-related charts"""
        # Expense breakdown
        self._create_expense_breakdown_chart(expense_data)
        
        # Monthly expense trends
        self._create_expense_trend_chart(expense_data)
        
        # Burn rate analysis
        self._create_burn_rate_chart(expense_data, analysis)
    
    def create_cashflow_charts(self, cashflow_data):
        """Create all cash flow charts"""
        # Cash flow waterfall
        self._create_cashflow_waterfall(cashflow_data)
        
        # Cash runway analysis
        self._create_cash_runway_chart(cashflow_data)
        
        # Cash efficiency dashboard
        self._create_cash_efficiency_dashboard(cashflow_data)
    
    def create_unit_economics_charts(self, ue_data):
        """Create all unit economics charts"""
        # Cohort retention heatmap
        self._create_cohort_retention_heatmap(ue_data)
        
        # LTV curves
        self._create_ltv_curves(ue_data)
        
        # Unit economics dashboard
        self._create_unit_economics_dashboard(ue_data)
    
    def create_projection_charts(self, projection_data):
        """Create all projection charts"""
        # Revenue forecast
        self._create_revenue_forecast_chart(projection_data)
        
        # Scenario comparison
        self._create_scenario_comparison_chart(projection_data)
        
        # Break-even analysis
        self._create_break_even_chart(projection_data)
        
        # Sensitivity analysis
        self._create_sensitivity_chart(projection_data)
    
    # Revenue Charts Implementation
    def _create_monthly_revenue_chart(self, revenue_data, analysis):
        """Create monthly revenue bar chart with growth line"""
        fig, ax1 = plt.subplots(figsize=(14, 8))
        
        # Get monthly data
        monthly_revenue = analysis.get('monthly_revenue', {})
        if not monthly_revenue:
            return
            
        months = list(monthly_revenue.keys())
        revenue = list(monthly_revenue.values())
        
        # Bar chart
        bars = ax1.bar(months, revenue, color=self.colors['primary'], alpha=0.7)
        ax1.set_xlabel('Month', fontsize=12)
        ax1.set_ylabel('Revenue ($)', fontsize=12)
        ax1.set_title('Monthly Revenue Growth', fontsize=16, fontweight='bold')
        
        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'${height:,.0f}', ha='center', va='bottom', fontsize=9)
        
        # Growth rate line
        if len(revenue) > 1:
            ax2 = ax1.twinx()
            growth_rates = [0] + [(revenue[i] - revenue[i-1])/revenue[i-1]*100 
                                 for i in range(1, len(revenue))]
            ax2.plot(months, growth_rates, color=self.colors['accent'], 
                    marker='o', linewidth=2, markersize=8, label='Growth Rate')
            ax2.set_ylabel('Growth Rate (%)', fontsize=12)
            ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.output_dir / 'monthly_revenue.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_customer_concentration_chart(self, revenue_data):
        """Create customer revenue concentration chart"""
        customer_revenue = revenue_data.get('customer_revenue', {})
        if not customer_revenue:
            return
            
        # Sort customers by revenue
        sorted_customers = sorted(customer_revenue.items(), key=lambda x: x[1], reverse=True)
        top_10 = sorted_customers[:10]
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        customers = [c[0][:20] + '...' if len(c[0]) > 20 else c[0] for c in top_10]
        revenues = [c[1] for c in top_10]
        total_revenue = sum(customer_revenue.values())
        percentages = [r/total_revenue*100 for r in revenues]
        
        # Create pie chart
        colors_list = plt.cm.Set3(np.linspace(0, 1, len(customers)))
        wedges, texts, autotexts = ax.pie(percentages, labels=customers, 
                                          colors=colors_list, autopct='%1.1f%%',
                                          startangle=90)
        
        ax.set_title('Customer Revenue Concentration (Top 10)', fontsize=16, fontweight='bold')
        
        # Add legend with revenue amounts
        legend_labels = [f'{c}: ${r:,.0f}' for c, r in zip(customers, revenues)]
        ax.legend(wedges, legend_labels, loc='center left', bbox_to_anchor=(1, 0, 0.5, 1))
        
        plt.savefig(self.output_dir / 'customer_concentration.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_yoy_growth_chart(self, revenue_data):
        """Create year-over-year growth comparison"""
        yearly_revenue = revenue_data.get('yearly_revenue', {})
        if not yearly_revenue or len(yearly_revenue) < 2:
            return
            
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # Revenue comparison
        years = list(yearly_revenue.keys())
        revenues = list(yearly_revenue.values())
        
        bars = ax1.bar(years, revenues, color=self.colors['primary'])
        ax1.set_title('Annual Revenue', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Revenue ($)')
        
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'${height:,.0f}', ha='center', va='bottom')
        
        # Growth rate
        if len(revenues) > 1:
            growth_rates = [(revenues[i] - revenues[i-1])/revenues[i-1]*100 
                           for i in range(1, len(revenues))]
            ax2.bar(years[1:], growth_rates, color=self.colors['accent'])
            ax2.set_title('YoY Growth Rate', fontsize=14, fontweight='bold')
            ax2.set_ylabel('Growth Rate (%)')
            ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
            
            for i, rate in enumerate(growth_rates):
                ax2.text(i, rate, f'{rate:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'yoy_growth.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_quarterly_trend_chart(self, revenue_data):
        """Create quarterly revenue trend chart"""
        quarterly_revenue = revenue_data.get('quarterly_revenue', {})
        if not quarterly_revenue:
            return
            
        fig, ax = plt.subplots(figsize=(12, 7))
        
        quarters = list(quarterly_revenue.keys())
        revenues = list(quarterly_revenue.values())
        
        # Plot actual revenue
        ax.plot(quarters, revenues, color=self.colors['primary'], 
                marker='o', linewidth=3, markersize=10, label='Actual Revenue')
        
        # Add trend line
        if len(revenues) > 2:
            z = np.polyfit(range(len(revenues)), revenues, 1)
            p = np.poly1d(z)
            ax.plot(quarters, p(range(len(revenues))), 
                   color=self.colors['secondary'], linestyle='--', 
                   linewidth=2, label='Trend')
        
        ax.set_title('Quarterly Revenue Trend', fontsize=16, fontweight='bold')
        ax.set_xlabel('Quarter')
        ax.set_ylabel('Revenue ($)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.output_dir / 'quarterly_trend.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_cumulative_revenue_chart(self, revenue_data):
        """Create cumulative revenue chart"""
        monthly_revenue = revenue_data.get('monthly_revenue', {})
        if not monthly_revenue:
            return
            
        fig, ax = plt.subplots(figsize=(12, 8))
        
        months = list(monthly_revenue.keys())
        revenues = list(monthly_revenue.values())
        cumulative = np.cumsum(revenues)
        
        # Area chart
        ax.fill_between(range(len(months)), cumulative, alpha=0.3, color=self.colors['primary'])
        ax.plot(range(len(months)), cumulative, color=self.colors['primary'], 
                linewidth=3, marker='o', markersize=8)
        
        # Add milestone markers
        milestones = [100000, 250000, 500000, 1000000]
        for milestone in milestones:
            if cumulative[-1] > milestone:
                idx = next((i for i, v in enumerate(cumulative) if v >= milestone), None)
                if idx:
                    ax.axhline(y=milestone, color='gray', linestyle='--', alpha=0.5)
                    ax.text(len(months)-1, milestone, f'${milestone/1000:.0f}K', 
                           va='bottom', ha='right')
        
        ax.set_title('Cumulative Revenue Growth', fontsize=16, fontweight='bold')
        ax.set_xlabel('Month')
        ax.set_ylabel('Cumulative Revenue ($)')
        ax.set_xticks(range(len(months)))
        ax.set_xticklabels(months, rotation=45)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'cumulative_revenue.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # Expense Charts Implementation
    def _create_expense_breakdown_chart(self, expense_data):
        """Create expense category breakdown chart"""
        category_expenses = expense_data.get('category_breakdown', {})
        if not category_expenses:
            return
            
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7))
        
        # Pie chart
        categories = list(category_expenses.keys())
        amounts = list(category_expenses.values())
        
        colors_list = plt.cm.Set3(np.linspace(0, 1, len(categories)))
        wedges, texts, autotexts = ax1.pie(amounts, labels=categories, 
                                           colors=colors_list, autopct='%1.1f%%',
                                           startangle=90)
        ax1.set_title('Expense Breakdown by Category', fontsize=14, fontweight='bold')
        
        # Bar chart
        bars = ax2.bar(categories, amounts, color=colors_list)
        ax2.set_title('Expense Amounts by Category', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Amount ($)')
        ax2.set_xticklabels(categories, rotation=45, ha='right')
        
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'${height:,.0f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'expense_breakdown.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_expense_trend_chart(self, expense_data):
        """Create monthly expense trend chart"""
        monthly_expenses = expense_data.get('monthly_expenses', {})
        if not monthly_expenses:
            return
            
        fig, ax = plt.subplots(figsize=(12, 7))
        
        months = list(monthly_expenses.keys())
        total_expenses = [sum(month.values()) for month in monthly_expenses.values()]
        
        # Stacked area chart for categories
        categories = set()
        for month_data in monthly_expenses.values():
            categories.update(month_data.keys())
        categories = sorted(list(categories))
        
        category_data = {cat: [] for cat in categories}
        for month_data in monthly_expenses.values():
            for cat in categories:
                category_data[cat].append(month_data.get(cat, 0))
        
        # Create stacked area
        bottoms = np.zeros(len(months))
        colors_list = plt.cm.Set3(np.linspace(0, 1, len(categories)))
        
        for i, (cat, data) in enumerate(category_data.items()):
            ax.bar(months, data, bottom=bottoms, label=cat, color=colors_list[i])
            bottoms += np.array(data)
        
        ax.set_title('Monthly Expense Trends by Category', fontsize=16, fontweight='bold')
        ax.set_xlabel('Month')
        ax.set_ylabel('Expenses ($)')
        ax.legend(loc='upper left', bbox_to_anchor=(1, 1))
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.output_dir / 'expense_trends.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_burn_rate_chart(self, expense_data, analysis):
        """Create burn rate analysis chart"""
        burn_data = analysis.get('burn_rate_analysis', {})
        if not burn_data:
            return
            
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Monthly burn rate
        months = list(burn_data.get('monthly_burn', {}).keys())
        burn_rates = list(burn_data.get('monthly_burn', {}).values())
        
        bars = ax1.bar(months, burn_rates, color=self.colors['danger'], alpha=0.7)
        ax1.axhline(y=burn_data.get('avg_burn_rate', 0), 
                   color=self.colors['warning'], linestyle='--', 
                   label=f"Avg: ${burn_data.get('avg_burn_rate', 0):,.0f}")
        ax1.set_title('Monthly Burn Rate', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Burn Rate ($)')
        ax1.legend()
        
        # Efficiency metrics
        efficiency_metrics = burn_data.get('efficiency_metrics', {})
        if efficiency_metrics:
            metrics = list(efficiency_metrics.keys())
            values = list(efficiency_metrics.values())
            
            colors_list = [self.colors['success'] if v > 0 else self.colors['danger'] 
                          for v in values]
            bars2 = ax2.bar(metrics, values, color=colors_list)
            ax2.set_title('Burn Rate Efficiency Metrics', fontsize=14, fontweight='bold')
            ax2.set_ylabel('Score')
            ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
            
            for bar in bars2:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.1f}', ha='center', 
                        va='bottom' if height > 0 else 'top')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.output_dir / 'burn_rate_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # Cash Flow Charts Implementation
    def _create_cashflow_waterfall(self, cashflow_data):
        """Create cash flow waterfall chart"""
        waterfall_data = cashflow_data.get('waterfall_data', {})
        if not waterfall_data:
            return
            
        fig, ax = plt.subplots(figsize=(14, 8))
        
        categories = list(waterfall_data.keys())
        values = list(waterfall_data.values())
        
        # Calculate cumulative values
        cumulative = [0]
        for v in values:
            cumulative.append(cumulative[-1] + v)
        
        # Create waterfall
        for i, (cat, val) in enumerate(zip(categories, values)):
            if val >= 0:
                color = self.colors['success']
                bottom = cumulative[i]
            else:
                color = self.colors['danger']
                bottom = cumulative[i+1]
            
            ax.bar(i, abs(val), bottom=bottom, color=color, alpha=0.7)
            
            # Add value labels
            label_y = bottom + abs(val)/2
            ax.text(i, label_y, f'${val:,.0f}', ha='center', va='center',
                   fontweight='bold', fontsize=10)
        
        # Add connecting lines
        for i in range(len(categories)):
            ax.plot([i-0.4, i+0.4], [cumulative[i+1], cumulative[i+1]], 
                   'k--', alpha=0.5)
        
        ax.set_xticks(range(len(categories)))
        ax.set_xticklabels(categories, rotation=45, ha='right')
        ax.set_title('Cash Flow Waterfall Analysis', fontsize=16, fontweight='bold')
        ax.set_ylabel('Cash Position ($)')
        ax.grid(True, axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'cash_flow_waterfall.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_cash_runway_chart(self, cashflow_data):
        """Create cash runway projection chart"""
        runway_data = cashflow_data.get('runway_projection', {})
        if not runway_data:
            return
            
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # Cash projection
        months = runway_data.get('months', [])
        cash_balance = runway_data.get('cash_balance', [])
        
        ax1.plot(months, cash_balance, color=self.colors['primary'], 
                linewidth=3, marker='o', markersize=8)
        ax1.fill_between(range(len(months)), cash_balance, alpha=0.3, 
                        color=self.colors['primary'])
        ax1.axhline(y=0, color=self.colors['danger'], linestyle='--', alpha=0.7)
        ax1.set_title('Cash Runway Projection', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Months from Now')
        ax1.set_ylabel('Cash Balance ($)')
        ax1.grid(True, alpha=0.3)
        
        # Runway metrics
        metrics = {
            'Current Cash': runway_data.get('current_cash', 0),
            'Monthly Burn': runway_data.get('monthly_burn', 0),
            'Runway (Months)': runway_data.get('runway_months', 0),
            'Zero Cash Date': runway_data.get('zero_cash_date', 'N/A')
        }
        
        y_pos = np.arange(len(metrics))
        values = [v if isinstance(v, (int, float)) else 0 for v in metrics.values()]
        labels = [f'{k}: {v:,.0f}' if isinstance(v, (int, float)) else f'{k}: {v}' 
                 for k, v in metrics.items()]
        
        bars = ax2.barh(y_pos, values, color=self.colors['accent'])
        ax2.set_yticks(y_pos)
        ax2.set_yticklabels(list(metrics.keys()))
        ax2.set_title('Cash Runway Metrics', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Value')
        
        for i, (bar, label) in enumerate(zip(bars, labels)):
            ax2.text(bar.get_width(), bar.get_y() + bar.get_height()/2,
                    f' {list(metrics.values())[i]:,.0f}' if isinstance(list(metrics.values())[i], (int, float)) else f' {list(metrics.values())[i]}',
                    va='center')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'cash_runway_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_cash_efficiency_dashboard(self, cashflow_data):
        """Create cash flow efficiency dashboard"""
        efficiency_data = cashflow_data.get('efficiency_metrics', {})
        if not efficiency_data:
            return
            
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Cash conversion cycle
        if 'conversion_cycle' in efficiency_data:
            cycle_data = efficiency_data['conversion_cycle']
            components = list(cycle_data.keys())
            days = list(cycle_data.values())
            
            bars = ax1.bar(components, days, color=self.colors['primary'])
            ax1.set_title('Cash Conversion Cycle', fontsize=12, fontweight='bold')
            ax1.set_ylabel('Days')
            
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.0f}', ha='center', va='bottom')
        
        # Operating cash flow ratio
        if 'ocf_ratio' in efficiency_data:
            months = list(efficiency_data['ocf_ratio'].keys())
            ratios = list(efficiency_data['ocf_ratio'].values())
            
            ax2.plot(months, ratios, color=self.colors['accent'], 
                    marker='o', linewidth=2, markersize=8)
            ax2.axhline(y=1, color='gray', linestyle='--', alpha=0.5)
            ax2.set_title('Operating Cash Flow Ratio', fontsize=12, fontweight='bold')
            ax2.set_ylabel('Ratio')
            ax2.set_xticklabels(months, rotation=45)
        
        # Free cash flow
        if 'free_cash_flow' in efficiency_data:
            months = list(efficiency_data['free_cash_flow'].keys())
            fcf = list(efficiency_data['free_cash_flow'].values())
            
            colors = [self.colors['success'] if v > 0 else self.colors['danger'] for v in fcf]
            bars = ax3.bar(months, fcf, color=colors)
            ax3.set_title('Free Cash Flow', fontsize=12, fontweight='bold')
            ax3.set_ylabel('Amount ($)')
            ax3.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
            ax3.set_xticklabels(months, rotation=45)
        
        # Cash efficiency score
        if 'efficiency_score' in efficiency_data:
            score = efficiency_data['efficiency_score']
            # Ensure score is within valid range [0, 100]
            score = max(0, min(100, score))
            
            # Handle edge cases
            if score == 0:
                # Show a full "inefficient" pie
                ax4.pie([0.01, 99.99], labels=['Efficient', 'Inefficient'],
                       colors=[self.colors['success'], self.colors['light']],
                       autopct=lambda p: f'{p:.1f}%' if p > 1 else '',
                       startangle=90)
            elif score == 100:
                # Show a full "efficient" pie
                ax4.pie([99.99, 0.01], labels=['Efficient', 'Inefficient'],
                       colors=[self.colors['success'], self.colors['light']],
                       autopct=lambda p: f'{p:.1f}%' if p > 1 else '',
                       startangle=90)
            else:
                # Normal case
                ax4.pie([score, 100-score], labels=['Efficient', 'Inefficient'],
                       colors=[self.colors['success'], self.colors['light']],
                       autopct='%1.1f%%', startangle=90)
            
            ax4.set_title(f'Cash Efficiency Score: {score:.1f}%', 
                         fontsize=12, fontweight='bold')
        
        plt.suptitle('Cash Flow Efficiency Dashboard', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'cash_efficiency_dashboard.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # Unit Economics Charts Implementation
    def _create_cohort_retention_heatmap(self, ue_data):
        """Create cohort retention heatmap"""
        cohort_data = ue_data.get('cohort_retention', {})
        if not cohort_data:
            return
            
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        
        # Convert to DataFrame for heatmap
        cohorts = list(cohort_data.keys())
        max_months = max(len(v) for v in cohort_data.values())
        
        retention_matrix = []
        for cohort in cohorts:
            retention = cohort_data[cohort]
            # Pad with NaN for missing months
            retention.extend([np.nan] * (max_months - len(retention)))
            retention_matrix.append(retention)
        
        # Revenue retention heatmap
        sns.heatmap(retention_matrix, annot=True, fmt='.0f', 
                   cmap='RdYlGn', vmin=0, vmax=100,
                   xticklabels=[f'M{i}' for i in range(max_months)],
                   yticklabels=cohorts, ax=ax1, cbar_kws={'label': 'Retention %'})
        ax1.set_title('Customer Retention by Cohort (%)', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Months Since First Purchase')
        ax1.set_ylabel('Cohort')
        
        # Cohort size visualization
        cohort_sizes = ue_data.get('cohort_sizes', {})
        if cohort_sizes:
            sizes = [cohort_sizes.get(c, 0) for c in cohorts]
            bars = ax2.barh(cohorts, sizes, color=self.colors['primary'])
            ax2.set_title('Cohort Sizes', fontsize=14, fontweight='bold')
            ax2.set_xlabel('Number of Customers')
            
            for bar in bars:
                width = bar.get_width()
                ax2.text(width, bar.get_y() + bar.get_height()/2,
                        f' {width:,.0f}', va='center')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'cohort_retention_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_ltv_curves(self, ue_data):
        """Create LTV development curves by cohort"""
        ltv_curves = ue_data.get('ltv_curves', {})
        if not ltv_curves:
            return
            
        fig, ax = plt.subplots(figsize=(12, 8))
        
        colors_list = plt.cm.tab10(np.linspace(0, 1, len(ltv_curves)))
        
        for i, (cohort, ltv_values) in enumerate(ltv_curves.items()):
            months = list(range(len(ltv_values)))
            ax.plot(months, ltv_values, color=colors_list[i], 
                   linewidth=2, marker='o', markersize=6, label=cohort)
        
        ax.set_title('Customer Lifetime Value Development by Cohort', 
                    fontsize=16, fontweight='bold')
        ax.set_xlabel('Months Since Acquisition')
        ax.set_ylabel('Cumulative LTV ($)')
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'ltv_curves.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_unit_economics_dashboard(self, ue_data):
        """Create comprehensive unit economics dashboard"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # LTV/CAC ratio gauge
        ltv_cac = ue_data.get('ltv_cac_ratio', 0)
        self._create_gauge_chart(ax1, ltv_cac, 'LTV/CAC Ratio', 
                                target=3, max_val=10)
        
        # CAC payback period
        payback = ue_data.get('payback_months', 0)
        months = ['Month ' + str(i) for i in range(1, int(payback) + 6)]
        cumulative_revenue = ue_data.get('payback_curve', [])
        
        if cumulative_revenue:
            ax2.plot(months[:len(cumulative_revenue)], cumulative_revenue, 
                    color=self.colors['primary'], linewidth=3, marker='o')
            ax2.axhline(y=ue_data.get('avg_cac', 0), color=self.colors['danger'], 
                       linestyle='--', label=f'CAC: ${ue_data.get("avg_cac", 0):,.0f}')
            ax2.fill_between(range(len(cumulative_revenue)), cumulative_revenue, 
                           alpha=0.3, color=self.colors['primary'])
            ax2.set_title(f'CAC Payback Period: {payback:.1f} months', 
                         fontsize=12, fontweight='bold')
            ax2.set_xlabel('Months')
            ax2.set_ylabel('Cumulative Revenue per Customer ($)')
            ax2.legend()
        
        # Customer metrics
        customer_metrics = {
            'Avg LTV': ue_data.get('avg_ltv', 0),
            'Avg CAC': ue_data.get('avg_cac', 0),
            'Gross Margin': ue_data.get('gross_margin', 0) * 100,
            'Churn Rate': ue_data.get('monthly_churn', 0) * 100
        }
        
        metrics = list(customer_metrics.keys())
        values = list(customer_metrics.values())
        
        bars = ax3.bar(metrics, values, color=[self.colors['success'], 
                                               self.colors['danger'],
                                               self.colors['primary'],
                                               self.colors['warning']])
        ax3.set_title('Key Unit Economics Metrics', fontsize=12, fontweight='bold')
        ax3.set_ylabel('Value')
        
        for bar, metric in zip(bars, metrics):
            height = bar.get_height()
            if 'Rate' in metric or 'Margin' in metric:
                label = f'{height:.1f}%'
            else:
                label = f'${height:,.0f}'
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    label, ha='center', va='bottom')
        
        # Net revenue retention
        nrr_data = ue_data.get('nrr_by_month', {})
        if nrr_data:
            months = list(nrr_data.keys())[-6:]  # Last 6 months
            nrr_values = [nrr_data[m] for m in months]
            
            ax4.plot(months, nrr_values, color=self.colors['accent'], 
                    linewidth=3, marker='o', markersize=8)
            ax4.axhline(y=100, color='gray', linestyle='--', alpha=0.5)
            ax4.fill_between(range(len(months)), nrr_values, 100, 
                           where=[v > 100 for v in nrr_values],
                           color=self.colors['success'], alpha=0.3)
            ax4.set_title('Net Revenue Retention Trend', fontsize=12, fontweight='bold')
            ax4.set_ylabel('NRR (%)')
            ax4.set_xticklabels(months, rotation=45)
        
        plt.suptitle('Unit Economics Dashboard', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'unit_economics_dashboard.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # Projection Charts Implementation
    def _create_revenue_forecast_chart(self, projection_data):
        """Create revenue forecast with scenarios"""
        scenarios = projection_data.get('scenarios', {})
        if not scenarios:
            return
            
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # Historical data
        historical = projection_data.get('historical', {})
        if historical:
            hist_months = list(historical.keys())
            hist_revenue = list(historical.values())
            ax.plot(hist_months, hist_revenue, color=self.colors['dark'], 
                   linewidth=3, marker='o', markersize=8, label='Historical')
        
        # Scenario projections
        colors = [self.colors['danger'], self.colors['primary'], self.colors['success']]
        linestyles = ['--', '-', '-.']
        
        for i, (scenario_name, scenario_data) in enumerate(scenarios.items()):
            months = scenario_data.get('months', [])
            revenue = scenario_data.get('revenue', [])
            
            ax.plot(months, revenue, color=colors[i % len(colors)], 
                   linestyle=linestyles[i % len(linestyles)],
                   linewidth=2, label=scenario_name)
        
        ax.set_title('Revenue Forecast - Multiple Scenarios', fontsize=16, fontweight='bold')
        ax.set_xlabel('Month')
        ax.set_ylabel('Revenue ($)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.output_dir / 'revenue_forecast.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_scenario_comparison_chart(self, projection_data):
        """Create scenario comparison chart"""
        comparison = projection_data.get('scenario_comparison', {})
        if not comparison:
            return
            
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        scenarios = list(comparison.keys())
        
        # Revenue comparison
        revenues = [comparison[s].get('total_revenue', 0) for s in scenarios]
        bars1 = ax1.bar(scenarios, revenues, color=self.colors['primary'])
        ax1.set_title('Total Revenue by Scenario', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Revenue ($)')
        
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'${height:,.0f}', ha='center', va='bottom')
        
        # Growth rate comparison
        growth_rates = [comparison[s].get('cagr', 0) * 100 for s in scenarios]
        bars2 = ax2.bar(scenarios, growth_rates, color=self.colors['accent'])
        ax2.set_title('CAGR by Scenario', fontsize=12, fontweight='bold')
        ax2.set_ylabel('CAGR (%)')
        
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        # Cash position
        cash_positions = [comparison[s].get('ending_cash', 0) for s in scenarios]
        colors = [self.colors['success'] if v > 0 else self.colors['danger'] 
                 for v in cash_positions]
        bars3 = ax3.bar(scenarios, cash_positions, color=colors)
        ax3.set_title('Ending Cash Position', fontsize=12, fontweight='bold')
        ax3.set_ylabel('Cash ($)')
        ax3.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
        
        # Break-even timeline
        breakeven_months = [comparison[s].get('breakeven_month', 0) for s in scenarios]
        bars4 = ax4.bar(scenarios, breakeven_months, color=self.colors['warning'])
        ax4.set_title('Months to Break-Even', fontsize=12, fontweight='bold')
        ax4.set_ylabel('Months')
        
        for bar in bars4:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.0f}', ha='center', va='bottom')
        
        plt.suptitle('Scenario Comparison Analysis', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'scenario_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_break_even_chart(self, projection_data):
        """Create break-even analysis chart"""
        breakeven = projection_data.get('breakeven_analysis', {})
        if not breakeven:
            return
            
        fig, ax = plt.subplots(figsize=(12, 8))
        
        months = breakeven.get('months', [])
        revenue = breakeven.get('revenue', [])
        costs = breakeven.get('costs', [])
        profit = breakeven.get('profit', [])
        
        # Plot lines
        ax.plot(months, revenue, color=self.colors['success'], 
               linewidth=3, label='Revenue')
        ax.plot(months, costs, color=self.colors['danger'], 
               linewidth=3, label='Costs')
        ax.plot(months, profit, color=self.colors['primary'], 
               linewidth=3, label='Profit', linestyle='--')
        
        # Highlight break-even point
        breakeven_month = breakeven.get('breakeven_month')
        if breakeven_month and breakeven_month < len(months):
            ax.scatter([months[breakeven_month]], [0], 
                      color=self.colors['warning'], s=200, zorder=5)
            ax.annotate('Break-Even Point', 
                       xy=(months[breakeven_month], 0),
                       xytext=(months[breakeven_month], max(revenue) * 0.2),
                       arrowprops=dict(arrowstyle='->', color=self.colors['warning']),
                       fontsize=12, fontweight='bold')
        
        # Fill profit/loss areas
        ax.fill_between(range(len(months)), profit, 0,
                       where=[p < 0 for p in profit],
                       color=self.colors['danger'], alpha=0.2, label='Loss')
        ax.fill_between(range(len(months)), profit, 0,
                       where=[p >= 0 for p in profit],
                       color=self.colors['success'], alpha=0.2, label='Profit')
        
        ax.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
        ax.set_title('Break-Even Analysis', fontsize=16, fontweight='bold')
        ax.set_xlabel('Month')
        ax.set_ylabel('Amount ($)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.output_dir / 'break_even_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_sensitivity_chart(self, projection_data):
        """Create sensitivity analysis chart"""
        sensitivity = projection_data.get('sensitivity_analysis', {})
        if not sensitivity:
            return
            
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # Revenue sensitivity
        revenue_sensitivity = sensitivity.get('revenue_impact', {})
        if revenue_sensitivity:
            factors = list(revenue_sensitivity.keys())
            impacts = list(revenue_sensitivity.values())
            
            colors = [self.colors['success'] if v > 0 else self.colors['danger'] 
                     for v in impacts]
            bars = ax1.barh(factors, impacts, color=colors)
            ax1.set_title('Revenue Sensitivity Analysis', fontsize=14, fontweight='bold')
            ax1.set_xlabel('Impact on Revenue (%)')
            ax1.axvline(x=0, color='gray', linestyle='-', alpha=0.5)
            
            for bar in bars:
                width = bar.get_width()
                ax1.text(width, bar.get_y() + bar.get_height()/2,
                        f' {width:.1f}%', ha='left' if width > 0 else 'right',
                        va='center')
        
        # Profitability sensitivity
        profit_sensitivity = sensitivity.get('profit_impact', {})
        if profit_sensitivity:
            factors = list(profit_sensitivity.keys())
            impacts = list(profit_sensitivity.values())
            
            # Create tornado chart
            factors_sorted = sorted(zip(factors, impacts), 
                                  key=lambda x: abs(x[1]), reverse=True)
            factors = [f[0] for f in factors_sorted]
            impacts = [f[1] for f in factors_sorted]
            
            colors = [self.colors['success'] if v > 0 else self.colors['danger'] 
                     for v in impacts]
            bars = ax2.barh(factors, impacts, color=colors)
            ax2.set_title('Profit Sensitivity Analysis (Tornado Chart)', 
                         fontsize=14, fontweight='bold')
            ax2.set_xlabel('Impact on Profit (%)')
            ax2.axvline(x=0, color='gray', linestyle='-', alpha=0.5)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'sensitivity_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # Helper methods
    def _create_gauge_chart(self, ax, value, title, target=None, max_val=100):
        """Create a gauge/speedometer chart"""
        # Create semicircle
        theta = np.linspace(0, np.pi, 100)
        r = 1
        
        # Background arc
        ax.fill_between(theta, 0, r, color=self.colors['light'], alpha=0.3)
        
        # Value arc
        value_theta = np.pi * (1 - value/max_val)
        theta_value = np.linspace(value_theta, np.pi, 50)
        
        # Color based on value
        if value >= target * 1.5:
            color = self.colors['success']
        elif value >= target:
            color = self.colors['primary']
        elif value >= target * 0.5:
            color = self.colors['warning']
        else:
            color = self.colors['danger']
        
        ax.fill_between(theta_value, 0, r, color=color, alpha=0.7)
        
        # Add target line if provided
        if target:
            target_theta = np.pi * (1 - target/max_val)
            ax.plot([target_theta, target_theta], [0, r], 
                   color=self.colors['dark'], linewidth=3, linestyle='--')
        
        # Add value text
        ax.text(0, -0.2, f'{value:.1f}', fontsize=24, fontweight='bold',
               ha='center', va='center')
        ax.text(0, -0.4, title, fontsize=12, ha='center', va='center')
        
        # Clean up
        ax.set_xlim(-1.2, 1.2)
        ax.set_ylim(-0.5, 1.2)
        ax.axis('off')
    
    def create_financial_health_dashboard(self, financial_data, health_metrics):
        """Create financial health dashboard (for backward compatibility)"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Implement similar to the old deep_financial_analysis.py
        # This ensures compatibility with existing PDF generators
        
        plt.suptitle('Financial Health Dashboard', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'financial_health_dashboard.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def create_margin_benchmarks_chart(self, margin_data):
        """Create margin benchmarks chart (for backward compatibility)"""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Implement margin comparison chart
        # This ensures compatibility with existing PDF generators
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'margin_benchmarks.png', dpi=300, bbox_inches='tight')
        plt.close()