"""
Consolidated Financial Calculations
Single source of truth for all financial metrics
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional
from collections import defaultdict
import logging
import sys
import os

# Add parent directory to path for config import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import CONFIG

logger = logging.getLogger(__name__)

class FinancialCalculator:
    """All financial calculations in one place with proper formulas"""
    
    def __init__(self):
        self.metrics = {}
        
    def calculate_all_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate all financial metrics from raw data"""
        logger.info("Calculating all financial metrics...")
        
        # Revenue metrics
        self.metrics['revenue'] = self._calculate_revenue_metrics(data)
        
        # Process bank transactions for revenue timeline
        bank_transactions = data.get('bank_transactions', [])
        if bank_transactions:
            self.metrics['revenue_timeline'] = self.process_bank_transactions_for_revenue(bank_transactions)
        
        # Process chart of accounts for ledger entries
        accounts = data.get('accounts', {})
        trial_balance = data.get('trial_balance', {})
        if accounts and trial_balance:
            self.metrics['ledger_revenue'] = self.process_chart_of_accounts_entries(accounts, trial_balance)
        
        # Expense metrics
        self.metrics['expenses'] = self._calculate_expense_metrics(data)
        
        # P&L metrics
        self.metrics['profit_loss'] = self._calculate_pl_metrics(data)
        
        # Unit economics (corrected formulas)
        self.metrics['unit_economics'] = self._calculate_unit_economics(data)
        
        # Financial ratios
        self.metrics['financial_ratios'] = self._calculate_financial_ratios()
        
        # Cash flow metrics
        self.metrics['cash_flow'] = self._calculate_cash_flow_metrics(data)
        
        # Growth metrics
        self.metrics['growth'] = self._calculate_growth_metrics(data)
        
        logger.info("All metrics calculated successfully")
        return self.metrics
    
    def _calculate_revenue_metrics(self, data: Dict) -> Dict:
        """Calculate all revenue-related metrics - Use P&L as primary source"""
        metrics = {
            'total_revenue': 0,
            'revenue_by_customer': {},
            'revenue_by_currency': {},
            'customer_count': 0,
            'average_invoice_value': 0,
            'concentration_risk': 0
        }
        
        # First try to get revenue from P&L (includes unbilled revenue)
        pl_revenue = 0
        pl_data = data.get('profit_loss')
        if pl_data:
            for report in pl_data.get('Reports', []):
                if report.get('ReportType') == 'ProfitAndLoss':
                    for section in report.get('Rows', []):
                        if section.get('Title') == 'Income':
                            for row in section.get('Rows', []):
                                if row.get('RowType') == 'SummaryRow' and 'Total Income' in row.get('Cells', [{}])[0].get('Value', ''):
                                    pl_revenue = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
                                    break
        
        # Process invoices for customer-level detail
        invoices_data = data.get('invoices')
        if not invoices_data:
            metrics['total_revenue'] = pl_revenue  # Use P&L revenue if no invoice data
            return metrics
            
        # Normalize data structure
        invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
        
        # Process invoices
        total_invoices = 0
        for invoice in invoices:
            if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                contact = invoice.get('Contact', {})
                contact_id = contact.get('ContactID', 'Unknown')
                contact_name = contact.get('Name', 'Unknown')
                currency = invoice.get('CurrencyCode', 'Unknown')
                amount = float(invoice.get('Total', 0))
                
                # By customer
                if contact_id not in metrics['revenue_by_customer']:
                    metrics['revenue_by_customer'][contact_id] = {
                        'name': contact_name,
                        'total': 0,
                        'invoice_count': 0
                    }
                metrics['revenue_by_customer'][contact_id]['total'] += amount
                metrics['revenue_by_customer'][contact_id]['invoice_count'] += 1
                
                # By currency
                metrics['revenue_by_currency'][currency] = metrics['revenue_by_currency'].get(currency, 0) + amount
                
                # Totals
                # Don't update total_revenue here - we'll use P&L total
                total_invoices += 1
        
        # Use P&L revenue as primary source (includes unbilled revenue)
        if pl_revenue > 0:
            metrics['total_revenue'] = pl_revenue
        else:
            # Fallback to invoice total if no P&L data
            invoice_total = sum(c['total'] for c in metrics['revenue_by_customer'].values())
            metrics['total_revenue'] = invoice_total
        
        # Calculate derived metrics
        if metrics['revenue_by_customer']:
            metrics['customer_count'] = len(metrics['revenue_by_customer'])
            metrics['unique_customers'] = len(metrics['revenue_by_customer'])  # Add this for validator compatibility
            
            # Average invoice value
            if total_invoices > 0:
                invoice_total = sum(c['total'] for c in metrics['revenue_by_customer'].values())
                metrics['average_invoice_value'] = invoice_total / total_invoices
            
            # Concentration risk
            if metrics['total_revenue'] > 0:
                max_customer_revenue = max(c['total'] for c in metrics['revenue_by_customer'].values())
                metrics['concentration_risk'] = (max_customer_revenue / metrics['total_revenue']) * 100
                
                # Find top customer
                for cid, cdata in metrics['revenue_by_customer'].items():
                    if cdata['total'] == max_customer_revenue:
                        metrics['top_customer'] = {
                            'id': cid,
                            'name': cdata['name'],
                            'revenue': cdata['total'],
                            'percentage': metrics['concentration_risk']
                        }
                        break
        
        return metrics
    
    def _calculate_expense_metrics(self, data: Dict) -> Dict:
        """Calculate expense metrics from P&L data"""
        metrics = {
            'total_expenses': 0,
            'expense_categories': {},
            'largest_expense': None,
            'salary_percentage': 0
        }
        
        pl_data = data.get('profit_loss')
        if not pl_data:
            return metrics
            
        for report in pl_data.get('Reports', []):
            if report.get('ReportType') == 'ProfitAndLoss':
                for section in report.get('Rows', []):
                    if 'Operating Expenses' in section.get('Title', ''):
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                                expense_name = row['Cells'][0].get('Value', 'Unknown')
                                expense_value = float(row['Cells'][1].get('Value', 0))
                                
                                metrics['expense_categories'][expense_name] = expense_value
                                metrics['total_expenses'] += expense_value
        
        # Calculate derived metrics
        if metrics['expense_categories']:
            # Largest expense
            largest = max(metrics['expense_categories'].items(), key=lambda x: x[1])
            metrics['largest_expense'] = {
                'category': largest[0],
                'amount': largest[1],
                'percentage': (largest[1] / metrics['total_expenses']) * 100 if metrics['total_expenses'] > 0 else 0
            }
            
            # Salary percentage
            salary_amount = sum(v for k, v in metrics['expense_categories'].items() 
                              if 'salaries' in k.lower())
            if metrics['total_expenses'] > 0:
                metrics['salary_percentage'] = (salary_amount / metrics['total_expenses']) * 100
        
        return metrics
    
    def _calculate_pl_metrics(self, data: Dict) -> Dict:
        """Calculate P&L metrics from actual P&L data"""
        metrics = {
            'total_income': 0,
            'total_expenses': 0,
            'gross_profit': 0,
            'operating_profit': 0,
            'net_profit': 0,
            'gross_margin_pct': 0,
            'operating_margin_pct': 0,
            'net_margin_pct': 0
        }
        
        # Get revenue from P&L data directly
        pl_data = data.get('profit_loss')
        if pl_data:
            for report in pl_data.get('Reports', []):
                if report.get('ReportType') == 'ProfitAndLoss':
                    for section in report.get('Rows', []):
                        # Find Income section
                        if section.get('Title') == 'Income':
                            for row in section.get('Rows', []):
                                if row.get('RowType') == 'SummaryRow' and 'Total Income' in row.get('Cells', [{}])[0].get('Value', ''):
                                    metrics['total_income'] = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
                        
                        # Find Net Profit row
                        if section.get('RowType') == 'Section' and section.get('Title') == '':
                            for row in section.get('Rows', []):
                                if row.get('RowType') == 'Row' and 'Net Profit' in row.get('Cells', [{}])[0].get('Value', ''):
                                    metrics['net_profit'] = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
        
        # Use calculated values from other methods if P&L parsing failed
        if metrics['total_income'] == 0:
            metrics['total_income'] = self.metrics.get('revenue', {}).get('total_revenue', 0)
        
        # Get expenses from our expense calculation
        metrics['total_expenses'] = self.metrics.get('expenses', {}).get('total_expenses', 0)
        
        # Calculate gross profit - for service business with no COGS
        metrics['gross_profit'] = metrics['total_income']
        metrics['operating_profit'] = metrics['total_income'] - metrics['total_expenses']
        
        # Check if there's any COGS in the P&L
        cogs = self._extract_cogs_from_pl(pl_data)
        if cogs > 0:
            # If COGS exists, calculate gross margin normally
            metrics['gross_profit'] = metrics['total_income'] - cogs
            metrics['gross_margin_pct'] = (metrics['gross_profit'] / metrics['total_income'] * 100) if metrics['total_income'] > 0 else 0
        else:
            # For service business with no COGS, gross margin is 100%
            # This is a valid business model assumption, not a default
            metrics['gross_margin_pct'] = 100.0
            logger.info("No COGS found in P&L - treating as pure service business with 100% gross margin")
        
        # Calculate margins
        if metrics['total_income'] > 0:
            metrics['operating_margin_pct'] = (metrics['operating_profit'] / metrics['total_income']) * 100
            metrics['net_margin_pct'] = (metrics['net_profit'] / metrics['total_income']) * 100
        
        return metrics
    
    def _calculate_unit_economics(self, data: Dict) -> Dict:
        """Calculate unit economics with proper formulas"""
        metrics = {
            'avg_cac': 0,
            'avg_ltv': 0,
            'ltv_cac_ratio': 0,
            'payback_months': 0,
            'avg_nrr': 100,
            'monthly_churn': 0,  # Will be calculated from data
            'gross_margin': 0     # Will be calculated from P&L
        }
        
        # Get gross margin from P&L calculations (convert from percentage to decimal)
        gross_margin_pct = self.metrics.get('profit_loss', {}).get('gross_margin_pct')
        if gross_margin_pct is None:
            raise ValueError("Cannot calculate unit economics: Gross margin not available from P&L data")
        metrics['gross_margin'] = gross_margin_pct / 100.0
        
        # Calculate CAC (Customer Acquisition Cost)
        cac_data = self._calculate_cac(data)
        metrics.update(cac_data)
        
        # Calculate LTV (Lifetime Value)
        ltv_data = self._calculate_ltv(data)
        metrics.update(ltv_data)
        
        # LTV/CAC Ratio
        if metrics['avg_cac'] > 0:
            metrics['ltv_cac_ratio'] = metrics['avg_ltv'] / metrics['avg_cac']
        
        # Payback period
        if metrics.get('avg_monthly_revenue', 0) > 0:
            monthly_contribution = metrics['avg_monthly_revenue'] * metrics['gross_margin']
            if monthly_contribution > 0:
                metrics['payback_months'] = metrics['avg_cac'] / monthly_contribution
        
        return metrics
    
    def _calculate_cac(self, data: Dict) -> Dict:
        """Calculate Customer Acquisition Cost for CEO-led sales model"""
        # CEO-led sales with minimal marketing spend
        # Only count direct marketing expenses, no salary allocation
        sm_expenses = 0
        
        expenses = self.metrics.get('expenses', {}).get('expense_categories', {})
        for expense_name, amount in expenses.items():
            # Only include direct marketing/advertising costs
            if any(term in expense_name.lower() for term in ['advertising', 'marketing', 'ads', 'promotion']):
                sm_expenses += amount
        
        # Count new customers (simplified - count unique customers)
        customer_count = self.metrics.get('revenue', {}).get('customer_count', 1)
        
        # Calculate average CAC - will be very low for CEO-led sales
        avg_cac = sm_expenses / customer_count if customer_count > 0 else 0
        
        # Log the calculation for transparency
        logger.info(f"CAC calculation: ${sm_expenses:.2f} marketing / {customer_count} customers = ${avg_cac:.2f} per customer")
        
        return {
            'avg_cac': avg_cac,
            'total_sm_spend': sm_expenses,
            'new_customers': customer_count
        }
    
    def _calculate_ltv(self, data: Dict) -> Dict:
        """Calculate Lifetime Value using industry standard formula"""
        # Get average monthly revenue per customer
        total_revenue = self.metrics.get('revenue', {}).get('total_revenue', 0)
        customer_count = self.metrics.get('revenue', {}).get('customer_count', 1)
        
        # Calculate actual data period from available data
        try:
            months = self._calculate_data_period_months(data)
        except ValueError as e:
            logger.error(f"Failed to calculate data period for LTV: {e}")
            raise
        
        monthly_revenue = total_revenue / months if months > 0 else 0
        avg_monthly_revenue_per_customer = monthly_revenue / customer_count if customer_count > 0 else 0
        
        # Get actual gross margin from P&L calculations
        gross_margin_pct = self.metrics.get('profit_loss', {}).get('gross_margin_pct')
        if gross_margin_pct is None:
            raise ValueError("Cannot calculate LTV: Gross margin not available from P&L data")
        gross_margin = gross_margin_pct / 100.0
        
        # Calculate actual churn from customer data
        monthly_churn = self._calculate_churn_from_data(data)
        
        if monthly_churn > 0:
            ltv = (avg_monthly_revenue_per_customer * gross_margin) / monthly_churn
        else:
            ltv = avg_monthly_revenue_per_customer * gross_margin * 24  # 24-month cap
        
        return {
            'avg_ltv': ltv,
            'avg_monthly_revenue': avg_monthly_revenue_per_customer,
            'gross_margin': gross_margin,
            'monthly_churn': monthly_churn
        }
    
    def _calculate_financial_ratios(self) -> Dict:
        """Calculate key financial ratios"""
        revenue = self.metrics.get('revenue', {}).get('total_revenue', 0)
        expenses = self.metrics.get('expenses', {}).get('total_expenses', 0)
        operating_margin = self.metrics.get('profit_loss', {}).get('operating_margin_pct', 0)
        
        # Monthly metrics (assuming annual data)
        monthly_revenue = revenue / 12
        monthly_expenses = expenses / 12
        monthly_burn = monthly_expenses - monthly_revenue
        
        # Calculate actual growth rate from historical data
        growth_metrics = self._calculate_actual_growth_rate(revenue)
        estimated_growth_rate = growth_metrics.get('annual_growth_rate', 0)
        rule_of_40 = estimated_growth_rate + operating_margin
        
        return {
            'monthly_revenue': monthly_revenue,
            'monthly_burn_rate': max(0, monthly_burn),
            'monthly_expenses': monthly_expenses,
            'rule_of_40': rule_of_40,
            'estimated_growth_rate': estimated_growth_rate
        }
    
    def _calculate_cash_flow_metrics(self, data: Dict) -> Dict:
        """Calculate cash flow metrics from bank transactions"""
        metrics = {
            'total_cash_in': 0,
            'total_cash_out': 0,
            'net_cash_flow': 0,
            'operating_cash_flow': 0,
            'avg_monthly_burn': 0,
            'cash_runway_months': 0,
            'ending_cash_balance': 0
        }
        
        # First, get current cash balance from balance sheet
        balance_sheet = data.get('balance_sheet')
        if balance_sheet:
            cash_balance = self._extract_cash_balance_from_balance_sheet(balance_sheet)
            metrics['ending_cash_balance'] = cash_balance
        
        bank_data = data.get('bank_transactions')
        if not bank_data:
            return metrics
            
        # Process transactions
        monthly_flows = {}
        for transaction in bank_data:
            amount = float(transaction.get('Total', 0))
            tx_type = transaction.get('Type', '')
            date_str = transaction.get('DateString', '')
            
            # Parse date
            try:
                if date_str:
                    date = self._parse_date(date_str)
                    month_key = date.strftime('%Y-%m') if date else 'Unknown'
                else:
                    month_key = 'Unknown'
            except:
                month_key = 'Unknown'
            
            if month_key not in monthly_flows:
                monthly_flows[month_key] = {'in': 0, 'out': 0}
            
            if tx_type == 'RECEIVE':
                monthly_flows[month_key]['in'] += amount
                metrics['total_cash_in'] += amount
            else:
                monthly_flows[month_key]['out'] += amount
                metrics['total_cash_out'] += amount
        
        # Calculate metrics
        metrics['net_cash_flow'] = metrics['total_cash_in'] - metrics['total_cash_out']
        
        # Average monthly burn
        if monthly_flows:
            valid_months = [m for m in monthly_flows if m != 'Unknown']
            if valid_months:
                total_burn = sum(monthly_flows[m]['out'] - monthly_flows[m]['in'] 
                               for m in valid_months)
                metrics['avg_monthly_burn'] = total_burn / len(valid_months)
        
        # Calculate cash runway
        # Use P&L data for more accurate monthly burn calculation
        revenue_metrics = self.metrics.get('revenue', {})
        expense_metrics = self.metrics.get('expenses', {})
        profit_loss_metrics = self.metrics.get('profit_loss', {})
        
        monthly_revenue = revenue_metrics.get('total_revenue', 0) / 12
        monthly_expenses = expense_metrics.get('total_expenses', 0) / 12
        net_monthly_burn = monthly_expenses - monthly_revenue
        
        # Calculate runway
        if net_monthly_burn > 0 and metrics['ending_cash_balance'] > 0:
            metrics['cash_runway_months'] = metrics['ending_cash_balance'] / net_monthly_burn
        elif net_monthly_burn <= 0:
            # Cash flow positive or breakeven
            metrics['cash_runway_months'] = 999  # Effectively unlimited
        else:
            # No cash or negative cash
            metrics['cash_runway_months'] = 0
        
        return metrics
    
    def _calculate_growth_metrics(self, data: Dict) -> Dict:
        """Calculate growth metrics from actual historical data"""
        metrics = {
            'yoy_growth': 0,
            'mom_growth': 0,
            'customer_growth': 0,
            'quarterly_growth': 0
        }
        
        # Get invoice data for time-series analysis
        invoices_data = data.get('invoices')
        if not invoices_data:
            return metrics
            
        invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
        
        # Group revenue by period
        revenue_by_month = {}
        revenue_by_year = {}
        customers_by_month = {}
        
        for invoice in invoices:
            if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                date_str = invoice.get('DateString', invoice.get('Date', ''))
                if date_str:
                    try:
                        date = self._parse_date(date_str)
                        if date:
                            month_key = date.strftime('%Y-%m')
                            year_key = str(date.year)
                            amount = float(invoice.get('Total', 0))
                            contact_id = invoice.get('Contact', {}).get('ContactID', '')
                            
                            # Monthly revenue
                            revenue_by_month[month_key] = revenue_by_month.get(month_key, 0) + amount
                            
                            # Yearly revenue
                            revenue_by_year[year_key] = revenue_by_year.get(year_key, 0) + amount
                            
                            # Monthly customers
                            if month_key not in customers_by_month:
                                customers_by_month[month_key] = set()
                            customers_by_month[month_key].add(contact_id)
                    except:
                        continue
        
        # Calculate YoY growth
        if len(revenue_by_year) >= 2:
            years = sorted(revenue_by_year.keys())
            current_year = years[-1]
            prior_year = years[-2]
            if revenue_by_year[prior_year] > 0:
                metrics['yoy_growth'] = ((revenue_by_year[current_year] - revenue_by_year[prior_year]) / revenue_by_year[prior_year]) * 100
        
        # Calculate MoM growth
        if len(revenue_by_month) >= 2:
            months = sorted(revenue_by_month.keys())
            current_month = months[-1]
            prior_month = months[-2]
            if revenue_by_month[prior_month] > 0:
                metrics['mom_growth'] = ((revenue_by_month[current_month] - revenue_by_month[prior_month]) / revenue_by_month[prior_month]) * 100
        
        # Calculate customer growth
        if len(customers_by_month) >= 2:
            months = sorted(customers_by_month.keys())
            current_customers = len(customers_by_month[months[-1]])
            prior_customers = len(customers_by_month[months[-2]])
            if prior_customers > 0:
                metrics['customer_growth'] = ((current_customers - prior_customers) / prior_customers) * 100
        
        return metrics
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse various date formats"""
        if '/Date(' in date_str:
            timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
            return datetime.fromtimestamp(timestamp)
        else:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
    
    def _get_date_range(self, data: Dict) -> Dict[str, Optional[datetime]]:
        """Get the min and max dates from all available data sources"""
        all_dates = []
        
        # Get dates from invoices
        invoices_data = data.get('invoices')
        if invoices_data:
            invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
            for invoice in invoices:
                for date_field in ['Date', 'DateString', 'FullyPaidOnDate']:
                    if invoice.get(date_field):
                        date = self._parse_date(invoice[date_field])
                        if date:
                            all_dates.append(date)
        
        # Get dates from bank transactions
        bank_data = data.get('bank_transactions', [])
        for txn in bank_data:
            if txn.get('Date'):
                date = self._parse_date(txn['Date'])
                if date:
                    all_dates.append(date)
        
        if not all_dates:
            return {'min_date': None, 'max_date': None}
        
        return {
            'min_date': min(all_dates),
            'max_date': max(all_dates)
        }
    
    def _calculate_data_period_months(self, data: Dict) -> float:
        """Calculate actual months of data from invoice/transaction dates"""
        date_range = self._get_date_range(data)
        if not date_range['min_date'] or not date_range['max_date']:
            raise ValueError("Cannot determine data period: No dated transactions found")
        
        delta = date_range['max_date'] - date_range['min_date']
        months = delta.days / 30.44  # Average days per month
        
        min_months = CONFIG['data_requirements']['min_months_of_data']
        if months < min_months:
            raise ValueError(f"Insufficient data period: {months:.1f} months (minimum {min_months} required)")
        
        logger.info(f"Data period calculated: {months:.1f} months from {date_range['min_date'].date()} to {date_range['max_date'].date()}")
        return months
    
    def process_bank_transactions_for_revenue(self, bank_data: List[Dict]) -> Dict:
        """Process bank transactions to extract revenue timeline
        
        This provides a complete revenue picture including clients without invoices
        """
        revenue_timeline = {
            'monthly': {},
            'quarterly': {},
            'yearly': {},
            'by_customer': {},
            'total': 0
        }
        
        for transaction in bank_data:
            # Only process RECEIVE transactions (revenue)
            if transaction.get('Type') != 'RECEIVE':
                continue
                
            # Get transaction details
            amount = float(transaction.get('Total', 0))
            date_str = transaction.get('DateString', transaction.get('Date', ''))
            contact = transaction.get('Contact', {})
            contact_name = contact.get('Name', 'Unknown')
            contact_id = contact.get('ContactID', 'unknown')
            
            # Parse date
            try:
                date = self._parse_date(date_str)
                if date:
                    # Time period keys
                    month_key = date.strftime('%Y-%m')
                    quarter_key = f"{date.year}-Q{(date.month-1)//3 + 1}"
                    year_key = str(date.year)
                    
                    # Aggregate by time period
                    revenue_timeline['monthly'][month_key] = revenue_timeline['monthly'].get(month_key, 0) + amount
                    revenue_timeline['quarterly'][quarter_key] = revenue_timeline['quarterly'].get(quarter_key, 0) + amount
                    revenue_timeline['yearly'][year_key] = revenue_timeline['yearly'].get(year_key, 0) + amount
                    
                    # By customer
                    if contact_id not in revenue_timeline['by_customer']:
                        revenue_timeline['by_customer'][contact_id] = {
                            'name': contact_name,
                            'total': 0,
                            'transactions': []
                        }
                    
                    revenue_timeline['by_customer'][contact_id]['total'] += amount
                    revenue_timeline['by_customer'][contact_id]['transactions'].append({
                        'date': date.isoformat(),
                        'amount': amount
                    })
                    
                    # Total
                    revenue_timeline['total'] += amount
                    
            except Exception as e:
                logger.warning(f"Error parsing transaction date: {e}")
                continue
        
        return revenue_timeline
    
    def process_chart_of_accounts_entries(self, accounts_data: Dict, trial_balance: Dict) -> Dict:
        """Extract revenue from general ledger entries
        
        This captures revenue recorded in books but not yet in bank transactions
        """
        revenue_accounts = {}
        
        # Handle both list and dict formats
        if isinstance(accounts_data, list):
            accounts = accounts_data
        else:
            accounts = accounts_data.get('Accounts', [])
        
        # Identify revenue accounts from chart of accounts
        for account in accounts:
            account_type = account.get('Type', '')
            account_class = account.get('Class', '')
            
            # Revenue accounts typically have type REVENUE or are in REVENUE class
            if account_type == 'REVENUE' or account_class == 'REVENUE':
                account_id = account.get('AccountID')
                account_name = account.get('Name', '')
                account_code = account.get('Code', '')
                
                revenue_accounts[account_id] = {
                    'name': account_name,
                    'code': account_code,
                    'balance': 0
                }
        
        # Get balances from trial balance
        for report in trial_balance.get('Reports', []):
            if report.get('ReportType') == 'TrialBalance':
                for section in report.get('Rows', []):
                    for row in section.get('Rows', []):
                        if row.get('RowType') == 'Row':
                            # Match account by name or code
                            account_name = row.get('Cells', [{}])[0].get('Value', '')
                            
                            # Check if this is a revenue account
                            for acc_id, acc_info in revenue_accounts.items():
                                if acc_info['name'] in account_name or (acc_info['code'] and acc_info['code'] in account_name):
                                    # Get the balance (usually in the credit column for revenue)
                                    cells = row.get('Cells', [])
                                    if len(cells) >= 3:  # Debit, Credit columns
                                        credit_balance = float(cells[2].get('Value', 0))
                                        revenue_accounts[acc_id]['balance'] = credit_balance
                                        break
        
        return revenue_accounts
    
    def _extract_cogs_from_pl(self, pl_data: Dict) -> float:
        """Extract Cost of Goods Sold from P&L if it exists"""
        cogs = 0
        if pl_data:
            for report in pl_data.get('Reports', []):
                if report.get('ReportType') == 'ProfitAndLoss':
                    for section in report.get('Rows', []):
                        # Look for COGS section
                        if 'Cost of Goods Sold' in section.get('Title', ''):
                            for row in section.get('Rows', []):
                                if row.get('RowType') == 'SummaryRow':
                                    cogs = float(row.get('Cells', [{}, {}])[1].get('Value', 0))
        return cogs
    
    def _calculate_churn_from_data(self, data: Dict) -> float:
        """Calculate monthly churn rate from customer retention data
        Uses April 2025 as cutoff and last payment date as service end
        """
        # Get both invoice and bank transaction data
        invoices_data = data.get('invoices')
        bank_data = data.get('bank_transactions', [])
        
        if not invoices_data and not bank_data:
            raise ValueError("Insufficient data to calculate churn rate: No invoice or bank transaction data available")
            
        invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
        
        # Track last payment date for each customer
        customer_last_payment = {}
        
        # From invoices
        for invoice in invoices:
            if invoice.get('Status') == 'PAID' and invoice.get('FullyPaidOnDate'):
                contact_id = invoice.get('Contact', {}).get('ContactID', '')
                contact_name = invoice.get('Contact', {}).get('Name', '')
                date_str = invoice.get('FullyPaidOnDate')
                
                if date_str and (contact_id or contact_name):
                    try:
                        date = self._parse_date(date_str)
                        if date:
                            key = contact_id or contact_name
                            if key not in customer_last_payment or date > customer_last_payment[key]:
                                customer_last_payment[key] = date
                    except:
                        continue
        
        # From bank transactions - include all RECEIVE transactions with positive amounts
        for txn in bank_data:
            if txn.get('Type') == 'RECEIVE' and txn.get('Contact'):
                contact_name = txn.get('Contact', {}).get('Name', '')
                date_str = txn.get('Date', '')
                amount = float(txn.get('Total', 0))

                # Only include transactions with positive amounts and valid contact names
                if date_str and contact_name and amount > 0:
                    try:
                        date = self._parse_date(date_str)
                        if date and (contact_name not in customer_last_payment or date > customer_last_payment[contact_name]):
                            customer_last_payment[contact_name] = date
                    except:
                        continue
        
        # Use dynamic cutoff date based on latest available data
        # This prevents artificial churn when we have data beyond the configured cutoff
        if customer_last_payment:
            latest_payment_date = max(customer_last_payment.values())
            # Use the later of: configured cutoff date or latest payment date
            configured_cutoff_str = CONFIG['churn_calculation']['cutoff_date']
            configured_cutoff = datetime.strptime(configured_cutoff_str, '%Y-%m-%d')
            cutoff_date = max(configured_cutoff, latest_payment_date)

            logger.info(f"Churn cutoff date: {cutoff_date.strftime('%Y-%m-%d')} "
                       f"(configured: {configured_cutoff_str}, latest payment: {latest_payment_date.strftime('%Y-%m-%d')})")
        else:
            # Fallback to configured date if no payment data
            cutoff_date_str = CONFIG['churn_calculation']['cutoff_date']
            cutoff_date = datetime.strptime(cutoff_date_str, '%Y-%m-%d')
            logger.warning(f"No payment data found, using configured cutoff: {cutoff_date_str}")

        # Classify customers as active or churned
        active_customers = 0
        churned_customers = 0

        # Get active window from configuration
        active_window_months = CONFIG['churn_calculation']['active_customer_window_months']
        active_threshold = cutoff_date - timedelta(days=active_window_months * 30.44)
        
        for customer, last_payment in customer_last_payment.items():
            # Customer is active if they paid within the configured window of cutoff
            if last_payment >= active_threshold:
                active_customers += 1
            else:
                churned_customers += 1
        
        total_customers = active_customers + churned_customers
        
        min_customers = CONFIG['churn_calculation']['min_customers_for_calculation']
        if total_customers < min_customers:
            raise ValueError(f"Insufficient data to calculate churn rate: Only {total_customers} customers found (minimum {min_customers} required)")
        
        # Calculate cohort-based churn for more accuracy
        # Track customer activity by month for better churn calculation
        customer_monthly_activity = {}
        monthly_active_customers = defaultdict(set)
        
        # Process all invoices to track monthly activity
        all_invoices = []
        for invoice in invoices:
            if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                all_invoices.append(invoice)
                
        # Also include bank transactions
        for txn in bank_data:
            if txn.get('Type') == 'RECEIVE' and txn.get('Contact'):
                all_invoices.append(txn)
        
        # Track monthly activity
        for record in all_invoices:
            if 'InvoiceID' in record:  # It's an invoice
                contact_id = record.get('Contact', {}).get('ContactID', '')
                date_str = record.get('Date', '')
            else:  # It's a bank transaction
                contact_id = None
                contact_name = record.get('Contact', {}).get('Name', '')
                date_str = record.get('Date', '')
            
            if date_str and (contact_id or contact_name):
                try:
                    date = self._parse_date(date_str)
                    if date:
                        month_key = f"{date.year}-{date.month:02d}"
                        customer_key = contact_id or contact_name
                        monthly_active_customers[month_key].add(customer_key)
                except:
                    continue
        
        # Calculate monthly churn rates
        sorted_months = sorted(monthly_active_customers.keys())
        monthly_churn_rates = []
        
        # Calculate churn for each month transition
        for i in range(1, len(sorted_months)):
            prev_month = sorted_months[i-1]
            curr_month = sorted_months[i]
            
            # Skip if months are not consecutive (gap in data)
            prev_date = datetime.strptime(prev_month, "%Y-%m")
            curr_date = datetime.strptime(curr_month, "%Y-%m")
            month_diff = (curr_date.year - prev_date.year) * 12 + (curr_date.month - prev_date.month)
            
            if month_diff == 1:  # Only calculate for consecutive months
                prev_customers = monthly_active_customers[prev_month]
                curr_customers = monthly_active_customers[curr_month]
                
                # Customers who were active last month but not this month
                if len(prev_customers) >= 3:  # Need minimum customers for meaningful rate
                    retained = len(prev_customers & curr_customers)
                    churned = len(prev_customers) - retained
                    month_churn_rate = churned / len(prev_customers)
                    
                    # Only include reasonable churn rates (exclude 0% and 100%)
                    if 0 < month_churn_rate < 1:
                        monthly_churn_rates.append(month_churn_rate)
        
        # Calculate average monthly churn
        if len(monthly_churn_rates) >= 3:  # Need at least 3 data points
            # Remove outliers (highest and lowest) if we have enough data
            if len(monthly_churn_rates) > 5:
                monthly_churn_rates.sort()
                monthly_churn_rates = monthly_churn_rates[1:-1]
            
            # Use median for more stable estimate
            monthly_churn = sorted(monthly_churn_rates)[len(monthly_churn_rates)//2]
            
            # Cap at reasonable bounds for B2B SaaS
            monthly_churn = min(monthly_churn, 0.15)  # Cap at 15% monthly
            monthly_churn = max(monthly_churn, 0.01)  # Floor at 1% monthly
        else:
            # Fallback to simple calculation if not enough cohort data
            logger.warning("Insufficient cohort data for churn calculation, using simple method")
            
            # Simple calculation but with adjustments
            apparent_annual_churn = churned_customers / total_customers
            
            # Adjust for data period (if less than a year, annualize properly)
            data_period_months = self.metrics.get('revenue', {}).get('data_period_months', 12)
            if data_period_months < 12:
                # Scale the churn rate
                apparent_annual_churn = apparent_annual_churn * (12 / data_period_months)
            
            # Cap at reasonable bounds
            apparent_annual_churn = min(apparent_annual_churn, 0.80)  # Cap at 80% annual
            
            # Convert to monthly
            monthly_churn = 1 - pow(1 - apparent_annual_churn, 1/12)
        
        # Calculate annual churn from monthly
        annual_churn = 1 - pow(1 - monthly_churn, 12)
        
        logger.info(f"Churn calculation: {churned_customers}/{total_customers} customers, "
                   f"cohort-based: {monthly_churn:.1%} monthly ({annual_churn:.1%} annual)")
        
        return monthly_churn
    
    def _calculate_actual_growth_rate(self, current_revenue: float) -> Dict[str, float]:
        """Calculate actual growth rate from historical revenue data"""
        # This needs access to historical revenue which should come from 
        # the growth metrics calculation that has already processed invoices
        growth_data = self.metrics.get('growth', {})
        
        return {
            'annual_growth_rate': growth_data.get('yoy_growth', 0),
            'monthly_growth_rate': growth_data.get('mom_growth', 0),
            'quarterly_growth_rate': growth_data.get('quarterly_growth', 0)
        }
    
    def _extract_cash_balance_from_balance_sheet(self, balance_sheet_data: Dict) -> float:
        """Extract total cash balance from balance sheet including all bank accounts"""
        total_cash = 0
        
        for report in balance_sheet_data.get('Reports', []):
            if report.get('ReportType') == 'BalanceSheet':
                for section in report.get('Rows', []):
                    # Look for Bank section which contains all cash accounts
                    if section.get('Title') == 'Bank':
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                                account_name = row['Cells'][0].get('Value', '')
                                # Get current year value (first numeric column)
                                value = float(row['Cells'][1].get('Value', 0))
                                # Include all bank accounts (Cash, Wise EUR, Wise USD, Wise GBP, etc.)
                                if any(term in account_name.lower() for term in ['cash', 'wise', 'bank']):
                                    total_cash += value
                                    logger.info(f"Found cash account: {account_name} = {value}")
        
        logger.info(f"Total cash balance from balance sheet: {total_cash}")
        return total_cash