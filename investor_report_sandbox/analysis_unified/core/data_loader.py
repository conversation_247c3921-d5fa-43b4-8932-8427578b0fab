"""
Unified Data Loader with Validation
Handles all data loading operations with error handling and validation
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

class DataLoader:
    """Centralized data loading with validation and error handling"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_cache = {}
        self.errors = []
        self.warnings = []
        
        # Define required data files
        self.data_files = {
            'profit_loss': 'profit_loss_latest.json',
            'balance_sheet': 'balance_sheet_latest.json',
            'trial_balance': 'trial_balance_latest.json',
            'invoices': 'invoices_latest.json',
            'bank_transactions': 'bank_transactions_latest.json',
            'contacts': 'contacts_latest.json',
            'accounts': 'accounts_latest.json',
            'organization': 'organization_latest.json'
        }
        
    def load_all_data(self) -> Dict[str, Any]:
        """Load all financial data with validation"""
        logger.info("Loading all financial data...")
        
        for key, filename in self.data_files.items():
            filepath = self.data_dir / filename
            self.data_cache[key] = self._safe_load_json(filepath, key)
            
        # Validate data integrity
        self._validate_data_integrity()
        
        logger.info(f"Loaded {len(self.data_cache)} data files successfully")
        return self.data_cache
    
    def load_specific(self, data_type: str) -> Optional[Dict]:
        """Load specific data file"""
        if data_type in self.data_cache:
            return self.data_cache[data_type]
            
        if data_type not in self.data_files:
            logger.error(f"Unknown data type: {data_type}")
            return None
            
        filepath = self.data_dir / self.data_files[data_type]
        data = self._safe_load_json(filepath, data_type)
        self.data_cache[data_type] = data
        return data
    
    def _safe_load_json(self, filepath: Path, data_type: str) -> Optional[Dict]:
        """Safely load JSON file with comprehensive error handling"""
        try:
            if not filepath.exists():
                self.errors.append(f"{data_type}: File not found - {filepath}")
                logger.error(f"File not found: {filepath}")
                return None
                
            with open(filepath, 'r') as f:
                data = json.load(f)
                
            # Basic validation
            if not data:
                self.warnings.append(f"{data_type}: Empty data file")
                logger.warning(f"Empty data file: {filepath}")
                
            logger.info(f"Successfully loaded {data_type} from {filepath}")
            return data
            
        except json.JSONDecodeError as e:
            self.errors.append(f"{data_type}: Invalid JSON - {str(e)}")
            logger.error(f"JSON decode error in {filepath}: {str(e)}")
            return None
            
        except Exception as e:
            self.errors.append(f"{data_type}: Load error - {str(e)}")
            logger.error(f"Error loading {filepath}: {str(e)}")
            return None
    
    def _validate_data_integrity(self):
        """Validate loaded data for consistency"""
        # Check critical data files
        critical_files = ['profit_loss', 'invoices']
        for file_type in critical_files:
            if not self.data_cache.get(file_type):
                self.errors.append(f"Critical data missing: {file_type}")
        
        # Validate date ranges
        self._validate_date_consistency()
        
        # Validate financial data structure
        self._validate_financial_structure()
    
    def _validate_date_consistency(self):
        """Ensure date ranges are consistent across data files"""
        date_ranges = {}
        
        # Extract date ranges from different sources
        if self.data_cache.get('invoices'):
            invoices = self._normalize_data_structure(self.data_cache['invoices'], 'Invoices')
            if invoices:
                dates = [self._parse_date(inv.get('Date', inv.get('DateString', ''))) 
                        for inv in invoices if inv.get('Date') or inv.get('DateString')]
                if dates:
                    date_ranges['invoices'] = (min(dates), max(dates))
        
        # Log date ranges
        for source, (start, end) in date_ranges.items():
            logger.info(f"{source} date range: {start} to {end}")
    
    def _validate_financial_structure(self):
        """Validate financial data structure"""
        # Check P&L structure
        if self.data_cache.get('profit_loss'):
            pl_data = self.data_cache['profit_loss']
            if 'Reports' not in pl_data:
                self.warnings.append("P&L data missing standard Reports structure")
        
        # Check Balance Sheet structure
        if self.data_cache.get('balance_sheet'):
            bs_data = self.data_cache['balance_sheet']
            if 'Reports' not in bs_data:
                self.warnings.append("Balance Sheet missing standard Reports structure")
    
    def _normalize_data_structure(self, data: Any, key: str) -> List:
        """Normalize data structure to handle both list and dict formats"""
        if isinstance(data, list):
            return data
        elif isinstance(data, dict) and key in data:
            return data[key]
        return []
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse various date formats"""
        if not date_str:
            return None
            
        try:
            # Handle Xero date format /Date(timestamp)/
            if '/Date(' in date_str:
                timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
                return datetime.fromtimestamp(timestamp)
            # Handle ISO format
            else:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except Exception as e:
            logger.warning(f"Could not parse date: {date_str} - {str(e)}")
            return None
    
    def get_validation_report(self) -> Dict:
        """Get validation report with errors and warnings"""
        return {
            'status': 'ERROR' if self.errors else ('WARNING' if self.warnings else 'OK'),
            'errors': self.errors,
            'warnings': self.warnings,
            'loaded_files': list(self.data_cache.keys())
        }
    
    def clear_cache(self):
        """Clear data cache"""
        self.data_cache.clear()
        self.errors.clear()
        self.warnings.clear()