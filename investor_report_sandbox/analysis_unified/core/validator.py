"""
Financial Data Validator
Ensures data quality and calculation accuracy
"""

import logging
from typing import Dict, Any, List, Tuple
from datetime import datetime
import sys
import os

# Add parent directory to path for config import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import CONFIG
from .calculations import FinancialCalculator

logger = logging.getLogger(__name__)

class FinancialValidator:
    """Comprehensive validation for financial data and calculations"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.validations_performed = []
        
    def validate_all(self, data: Dict[str, Any], calculations: Dict[str, Any]) -> Dict:
        """Run all validations and return comprehensive report"""
        logger.info("Starting comprehensive financial validation...")
        
        # Clear previous results
        self.errors.clear()
        self.warnings.clear()
        self.validations_performed.clear()
        
        # Data sufficiency check (NEW)
        self._validate_data_sufficiency(data)
        
        # Data validations
        self._validate_pl_data(data.get('profit_loss'))
        self._validate_balance_sheet(data.get('balance_sheet'))
        self._validate_customer_concentration(data.get('invoices'), data.get('contacts'))
        
        # Calculation validations
        self._validate_unit_economics(calculations.get('unit_economics'))
        self._validate_financial_ratios(calculations.get('financial_ratios'))
        self._validate_projections(calculations.get('projections'))
        
        # Cross-validations
        self._cross_validate_revenue(data, calculations)
        self._validate_cash_position(data, calculations)
        
        # Data integrity checks
        self._validate_data_completeness(data)
        self._validate_calculation_consistency(calculations)
        self._validate_temporal_consistency(data)
        
        return self._generate_report()
    
    def _validate_pl_data(self, pl_data: Dict) -> None:
        """Validate P&L statement data and calculations"""
        self.validations_performed.append("P&L Statement")
        
        if not pl_data:
            self.errors.append("P&L data is missing")
            return
            
        # Extract values
        total_income = 0
        total_expenses = 0
        
        for report in pl_data.get('Reports', []):
            if report.get('ReportType') == 'ProfitAndLoss':
                for section in report.get('Rows', []):
                    if section.get('Title') == 'Income':
                        total_income = self._extract_section_total(section)
                    elif 'Operating Expenses' in section.get('Title', ''):
                        total_expenses = self._extract_section_total(section)
        
        # Validate margins
        if total_income > 0:
            margin = ((total_income - total_expenses) / total_income) * 100
            
            if margin > 90:
                self.warnings.append(f"Unusually high profit margin: {margin:.1f}%")
            elif margin < -50:
                self.errors.append(f"Severe negative margin: {margin:.1f}%")
                
            # Service business validation
            if margin == 100:
                self.warnings.append("100% margin suggests no COGS - verify service business model")
    
    def _validate_balance_sheet(self, bs_data: Dict) -> None:
        """Validate balance sheet equation with proper account classification"""
        self.validations_performed.append("Balance Sheet")
        
        if not bs_data:
            self.errors.append("Balance Sheet data is missing")
            return
            
        for report in bs_data.get('Reports', []):
            if report.get('ReportType') == 'BalanceSheet':
                assets = 0
                liabilities = 0
                equity = 0
                
                # Process all rows to classify accounts properly
                for section in report.get('Rows', []):
                    section_title = section.get('Title', '')
                    
                    # Process Bank section separately for assets
                    if section_title == 'Bank':
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                                value = float(row['Cells'][1].get('Value', 0))
                                assets += value  # All bank accounts are assets
                    
                    # Process other sections
                    else:
                        for row in section.get('Rows', []):
                            if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                                account_name = row['Cells'][0].get('Value', '')
                                value = float(row['Cells'][1].get('Value', 0))
                                
                                # Skip calculated fields and totals
                                if any(term in account_name for term in ['Net Assets', 'Total']):
                                    continue
                                
                                # Check section context for better classification
                                if 'Current Assets' in section_title or 'Fixed Assets' in section_title:
                                    assets += value
                                elif 'Current Liabilities' in section_title:
                                    # Special case for Directors' Loan in liabilities section
                                    if 'directors' in account_name.lower():
                                        equity += value  # Directors' loan is equity
                                    else:
                                        liabilities += value
                                elif 'Non-Current Liabilities' in section_title:
                                    # Directors' Loan Account should be equity
                                    if 'directors' in account_name.lower():
                                        equity += value  # Directors' loan is equity
                                    else:
                                        liabilities += value
                                elif 'Equity' in section_title:
                                    equity += value
                                else:
                                    # Fallback classification based on account name
                                    if any(term in account_name.lower() for term in ['cash', 'bank', 'receivable', 'equipment', 'prepaid', 'asset', 'wise']):
                                        assets += value
                                    elif any(term in account_name.lower() for term in ['payable', 'accrual', 'vat', 'debt', 'rounding']):
                                        liabilities += value
                                    elif 'loan' in account_name.lower() and 'directors' not in account_name.lower():
                                        liabilities += value
                                    elif any(term in account_name.lower() for term in ['capital', 'share', 'retained', 'earning']):
                                        equity += value
                
                # Check balance sheet equation
                difference = abs(assets - (liabilities + equity))
                if difference > 0.01:  # Allow for rounding
                    self.errors.append(
                        f"Balance sheet doesn't balance: Assets=${assets:,.2f}, "
                        f"L+E=${(liabilities + equity):,.2f}, Diff=${difference:,.2f}"
                    )
                
                # Check for negative or zero assets
                if assets <= 0:
                    self.errors.append(f"Balance sheet shows {'negative' if assets < 0 else 'zero'} assets (${assets:,.2f}) - critical error")
    
    def _validate_customer_concentration(self, invoices_data: Any, contacts_data: Any) -> None:
        """Validate customer concentration risk"""
        self.validations_performed.append("Customer Concentration")
        
        if not invoices_data:
            self.warnings.append("Invoice data missing for customer analysis")
            return
            
        # Normalize data structure
        invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
        
        # Calculate revenue by customer
        customer_revenue = {}
        total_revenue = 0
        
        for invoice in invoices:
            if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                contact_id = invoice.get('Contact', {}).get('ContactID')
                amount = float(invoice.get('Total', 0))
                customer_revenue[contact_id] = customer_revenue.get(contact_id, 0) + amount
                total_revenue += amount
        
        if total_revenue > 0 and customer_revenue:
            # Check concentration
            top_customer_revenue = max(customer_revenue.values())
            concentration = (top_customer_revenue / total_revenue) * 100
            
            if concentration > 80:
                self.errors.append(f"Critical customer concentration: {concentration:.1f}%")
            elif concentration > 50:
                self.warnings.append(f"High customer concentration: {concentration:.1f}%")
            
            # Check customer count
            active_customers = len(customer_revenue)
            if active_customers < 5:
                self.warnings.append(f"Low customer diversification: only {active_customers} active customers")
    
    def _validate_unit_economics(self, ue_data: Dict) -> None:
        """Validate unit economics calculations"""
        self.validations_performed.append("Unit Economics")
        
        if not ue_data:
            return
            
        # LTV/CAC ratio validation
        ltv_cac = ue_data.get('ltv_cac_ratio', 0)
        if ltv_cac > 100:
            self.errors.append(f"Unrealistic LTV/CAC ratio: {ltv_cac:.1f} (industry best is 3-5)")
        elif ltv_cac > 20:
            self.warnings.append(f"Very high LTV/CAC ratio: {ltv_cac:.1f} (verify calculations)")
        elif ltv_cac < 1 and ltv_cac > 0:
            self.errors.append(f"LTV/CAC below 1: {ltv_cac:.1f} (unsustainable)")
        
        # CAC validation
        cac = ue_data.get('avg_cac', 0)
        if 0 < cac < 100:
            self.warnings.append(f"Low CAC of ${cac:.2f} - verify all costs included")
        
        # Payback period
        payback = ue_data.get('payback_months', 0)
        if payback > 24:
            self.warnings.append(f"Long payback period: {payback:.1f} months")
        elif payback == 0:
            self.warnings.append("Zero payback period suggests calculation error")
    
    def _validate_financial_ratios(self, ratios: Dict) -> None:
        """Validate financial ratios are within reasonable bounds"""
        self.validations_performed.append("Financial Ratios")
        
        if not ratios:
            return
            
        # Rule of 40
        rule_of_40 = ratios.get('rule_of_40', 0)
        if rule_of_40 > 100:
            self.warnings.append(f"Rule of 40 score unusually high: {rule_of_40:.1f}")
        
        # Burn rate
        burn_rate = ratios.get('monthly_burn_rate', 0)
        if burn_rate > 100000:
            self.warnings.append(f"High burn rate: ${burn_rate:,.0f}/month")
    
    def _validate_projections(self, projections: Dict) -> None:
        """Validate growth projections are realistic"""
        self.validations_performed.append("Projections")
        
        if not projections:
            return
            
        for scenario, data in projections.items():
            if isinstance(data, dict):
                growth = data.get('total_growth_percent', 0)
                if growth > 1000:
                    self.errors.append(f"{scenario} projection shows unrealistic growth: {growth:.1f}%")
                elif growth > 500:
                    self.warnings.append(f"{scenario} projection shows very high growth: {growth:.1f}%")
    
    def _cross_validate_revenue(self, data: Dict, calculations: Dict) -> None:
        """Cross-validate revenue across different sources"""
        self.validations_performed.append("Revenue Cross-Validation")
        
        revenue_sources = {}
        
        # From invoices
        if data.get('invoices'):
            invoices = data['invoices'] if isinstance(data['invoices'], list) else data['invoices'].get('Invoices', [])
            invoice_total = sum(
                float(inv.get('Total', 0)) 
                for inv in invoices 
                if inv.get('Status') in ['PAID', 'AUTHORISED']
            )
            revenue_sources['invoices'] = invoice_total
        
        # From P&L
        if data.get('profit_loss'):
            for report in data['profit_loss'].get('Reports', []):
                if report.get('ReportType') == 'ProfitAndLoss':
                    for section in report.get('Rows', []):
                        if section.get('Title') == 'Income':
                            revenue_sources['profit_loss'] = self._extract_section_total(section)
        
        # Compare sources
        if len(revenue_sources) > 1:
            values = list(revenue_sources.values())
            min_val = min(values)
            max_val = max(values)
            if max_val > 0:
                variance = ((max_val - min_val) / max_val) * 100
                if variance > 10:
                    self.warnings.append(
                        f"Revenue variance across sources: {variance:.1f}% "
                        f"({', '.join(f'{k}=${v:,.0f}' for k, v in revenue_sources.items())}) "
                        f"- P&L includes unbilled/accrued revenue"
                    )
    
    def _extract_section_total(self, section: Dict) -> float:
        """Extract total from a report section"""
        for row in section.get('Rows', []):
            if row.get('RowType') == 'SummaryRow' and 'Total' in row.get('Cells', [{}])[0].get('Value', ''):
                return float(row['Cells'][1].get('Value', 0))
        return 0
    
    def _validate_data_sufficiency(self, data: Dict):
        """Validate that we have sufficient data for calculations"""
        self.validations_performed.append("Data sufficiency check")
        
        # Check data period
        try:
            calculator = FinancialCalculator()
            # Get date range
            date_range = calculator._get_date_range(data)
            if date_range['min_date'] and date_range['max_date']:
                delta = date_range['max_date'] - date_range['min_date']
                months = delta.days / 30.44
                min_months = CONFIG['data_requirements']['min_months_of_data']
                
                self.validations_performed.append(f"Data period: {months:.1f} months")
                
                if months < min_months:
                    self.errors.append(f"Insufficient data period: {months:.1f} months (minimum {min_months} required)")
                elif months < 6:
                    self.warnings.append(f"Limited data period: {months:.1f} months may affect accuracy of metrics")
            else:
                self.errors.append("Cannot determine data period: No dated transactions found")
        except Exception as e:
            self.errors.append(f"Data period validation error: {str(e)}")
        
        # Check customer count
        customer_count = self._count_unique_customers(data)
        min_customers = CONFIG['validation']['data_quality']['min_customers_for_metrics']
        
        self.validations_performed.append(f"Customer count: {customer_count}")
        
        if customer_count == 0:
            self.errors.append("No customers found in data")
        elif customer_count < min_customers:
            self.warnings.append(f"Limited customer data: {customer_count} customers (recommend {min_customers}+ for reliable metrics)")
        
        # Check for critical data files
        critical_files = ['profit_loss', 'invoices']
        for file_type in critical_files:
            if not data.get(file_type):
                self.errors.append(f"Critical data missing: {file_type}")
    
    def _count_unique_customers(self, data: Dict) -> int:
        """Count unique customers from invoices and bank transactions"""
        unique_customers = set()
        
        # From invoices
        invoices_data = data.get('invoices')
        if invoices_data:
            invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
            for invoice in invoices:
                if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                    contact = invoice.get('Contact', {})
                    contact_id = contact.get('ContactID', '')
                    contact_name = contact.get('Name', '')
                    if contact_id:
                        unique_customers.add(contact_id)
                    elif contact_name:
                        unique_customers.add(contact_name)
        
        # From bank transactions
        bank_data = data.get('bank_transactions', [])
        for txn in bank_data:
            if txn.get('Type') == 'RECEIVE' and txn.get('Contact'):
                contact_name = txn.get('Contact', {}).get('Name', '')
                if contact_name:
                    unique_customers.add(contact_name)
        
        return len(unique_customers)
    
    def _generate_report(self) -> Dict:
        """Generate validation report"""
        return {
            'validation_date': datetime.now().isoformat(),
            'status': 'FAIL' if self.errors else ('WARNING' if self.warnings else 'PASS'),
            'validations_performed': self.validations_performed,
            'errors': self.errors,
            'warnings': self.warnings,
            'summary': {
                'total_errors': len(self.errors),
                'total_warnings': len(self.warnings),
                'validations_run': len(self.validations_performed)
            }
        }
    
    def _validate_cash_position(self, data: Dict, calculations: Dict) -> None:
        """Validate cash position across different sources"""
        self.validations_performed.append("Cash Position Validation")
        
        cash_sources = {}
        
        # From balance sheet
        if data.get('balance_sheet'):
            for report in data['balance_sheet'].get('Reports', []):
                if report.get('ReportType') == 'BalanceSheet':
                    for section in report.get('Rows', []):
                        if 'Bank' in section.get('Title', ''):
                            for row in section.get('Rows', []):
                                if row.get('RowType') == 'Row':
                                    account_name = row['Cells'][0].get('Value', '')
                                    if 'Bank' in account_name:
                                        cash_sources['balance_sheet'] = cash_sources.get('balance_sheet', 0) + float(row['Cells'][1].get('Value', 0))
        
        # From calculations
        if calculations.get('cash_flow'):
            cash_sources['calculated'] = calculations['cash_flow'].get('ending_cash_balance', 0)
        
        # Validate consistency
        if len(cash_sources) > 1:
            values = list(cash_sources.values())
            min_val = min(values)
            max_val = max(values)
            if max_val > 0:
                variance = ((max_val - min_val) / max_val) * 100
                if variance > 5:
                    self.warnings.append(
                        f"Cash position variance: {variance:.1f}% "
                        f"({', '.join(f'{k}=${v:,.0f}' for k, v in cash_sources.items())})"
                    )
        
        # Validate runway
        if calculations.get('cash_flow'):
            # Note: The key is 'cash_runway_months' not 'runway_months'
            runway = calculations['cash_flow'].get('cash_runway_months', 0)
            # Handle infinite runway (cash flow positive) case
            if runway >= 999:
                # Cash flow positive - no warning needed
                pass
            elif runway < 3:
                self.errors.append(f"Critical: Only {runway:.1f} months runway remaining")
            elif runway < 6:
                self.warnings.append(f"Low runway: {runway:.1f} months")
    
    def _validate_data_completeness(self, data: Dict) -> None:
        """Check for missing or incomplete data"""
        self.validations_performed.append("Data Completeness")
        
        required_data_sources = [
            'profit_loss', 'balance_sheet', 'invoices', 
            'bank_transactions', 'contacts', 'accounts'
        ]
        
        missing_sources = []
        empty_sources = []
        
        for source in required_data_sources:
            if source not in data or data[source] is None:
                missing_sources.append(source)
            elif isinstance(data[source], dict):
                # Check if the data has meaningful content
                if not data[source].get('Reports') and not data[source].get('Invoices') and not data[source].get('BankTransactions'):
                    empty_sources.append(source)
            elif isinstance(data[source], list) and len(data[source]) == 0:
                empty_sources.append(source)
        
        if missing_sources:
            self.errors.append(f"Missing data sources: {', '.join(missing_sources)}")
        
        if empty_sources:
            self.warnings.append(f"Empty data sources: {', '.join(empty_sources)}")
        
        # Check date ranges
        date_ranges = self._extract_date_ranges(data)
        if date_ranges:
            min_date = min(date_ranges.values())
            max_date = max(date_ranges.values())
            days_diff = (max_date - min_date).days
            if days_diff > 365:
                self.warnings.append(f"Data spans {days_diff} days - ensure period consistency")
    
    def _validate_calculation_consistency(self, calculations: Dict) -> None:
        """Validate internal consistency of calculations"""
        self.validations_performed.append("Calculation Consistency")
        
        # Check revenue calculations
        if calculations.get('revenue') and calculations.get('unit_economics'):
            revenue_total = calculations['revenue'].get('total_revenue', 0)
            customer_count = calculations['revenue'].get('unique_customers', 1)
            avg_revenue_per_customer = calculations['unit_economics'].get('avg_monthly_revenue', 0) * 12
            
            if customer_count > 0 and avg_revenue_per_customer > 0:
                implied_revenue = customer_count * avg_revenue_per_customer
                if revenue_total > 0:
                    variance = abs(implied_revenue - revenue_total) / revenue_total * 100
                    if variance > 20:
                        self.warnings.append(
                            f"Revenue calculation inconsistency: Total={revenue_total:.0f}, "
                            f"Implied from customers={implied_revenue:.0f} ({variance:.1f}% variance)"
                        )
        
        # Check expense ratios
        if calculations.get('expenses') and calculations.get('revenue'):
            total_expenses = calculations['expenses'].get('total_expenses', 0)
            total_revenue = calculations['revenue'].get('total_revenue', 0)
            
            if total_revenue > 0:
                expense_ratio = total_expenses / total_revenue
                if expense_ratio > 2:
                    self.warnings.append(f"High expense ratio: {expense_ratio:.1f}x revenue")
                elif expense_ratio < 0.1:
                    self.warnings.append(f"Low expense ratio: {expense_ratio:.1f}x revenue - verify all costs included")
        
        # Validate growth metrics
        if calculations.get('growth'):
            yoy_growth = calculations['growth'].get('yoy_growth', 0)
            if yoy_growth > 1000:
                self.errors.append(f"Unrealistic YoY growth: {yoy_growth:.0f}%")
            elif yoy_growth < -50:
                self.warnings.append(f"Severe revenue decline: {yoy_growth:.0f}%")
    
    def _validate_temporal_consistency(self, data: Dict) -> None:
        """Check for temporal inconsistencies in data"""
        self.validations_performed.append("Temporal Consistency")
        
        # Check for future dates
        current_date = datetime.now()
        future_dates = []
        
        # Check invoices
        if data.get('invoices'):
            invoices = data['invoices'] if isinstance(data['invoices'], list) else data['invoices'].get('Invoices', [])
            for invoice in invoices:
                if invoice.get('Date'):
                    try:
                        inv_date = datetime.fromisoformat(invoice['Date'].replace('Z', '+00:00'))
                        if inv_date > current_date:
                            future_dates.append(f"Invoice {invoice.get('InvoiceNumber', 'Unknown')}")
                    except:
                        pass
        
        if future_dates:
            self.warnings.append(f"Found {len(future_dates)} items with future dates")
        
        # Check for stale data
        latest_date = self._find_latest_transaction_date(data)
        if latest_date:
            days_old = (current_date - latest_date).days
            if days_old > 30:
                self.warnings.append(f"Data may be stale - latest transaction {days_old} days old")
            elif days_old > 60:
                self.errors.append(f"Data is stale - latest transaction {days_old} days old")
    
    def _extract_date_ranges(self, data: Dict) -> Dict[str, datetime]:
        """Extract date ranges from various data sources"""
        date_ranges = {}
        
        # P&L dates
        if data.get('profit_loss'):
            for report in data['profit_loss'].get('Reports', []):
                if report.get('ReportDate'):
                    try:
                        date_ranges['profit_loss'] = datetime.fromisoformat(report['ReportDate'].replace('Z', '+00:00'))
                    except:
                        pass
        
        # Balance sheet dates
        if data.get('balance_sheet'):
            for report in data['balance_sheet'].get('Reports', []):
                if report.get('ReportDate'):
                    try:
                        date_ranges['balance_sheet'] = datetime.fromisoformat(report['ReportDate'].replace('Z', '+00:00'))
                    except:
                        pass
        
        return date_ranges
    
    def _find_latest_transaction_date(self, data: Dict) -> datetime:
        """Find the most recent transaction date"""
        latest_date = None
        
        # Check invoices
        if data.get('invoices'):
            invoices = data['invoices'] if isinstance(data['invoices'], list) else data['invoices'].get('Invoices', [])
            for invoice in invoices:
                if invoice.get('Date'):
                    try:
                        inv_date = datetime.fromisoformat(invoice['Date'].replace('Z', '+00:00'))
                        if latest_date is None or inv_date > latest_date:
                            latest_date = inv_date
                    except:
                        pass
        
        # Check bank transactions
        if data.get('bank_transactions'):
            # Handle both list and dict formats
            bank_data = data['bank_transactions']
            if isinstance(bank_data, list):
                transactions = bank_data
            else:
                transactions = bank_data.get('BankTransactions', [])
            for trans in transactions:
                if trans.get('Date'):
                    try:
                        trans_date = datetime.fromisoformat(trans['Date'].replace('Z', '+00:00'))
                        if latest_date is None or trans_date > latest_date:
                            latest_date = trans_date
                    except:
                        pass
        
        return latest_date