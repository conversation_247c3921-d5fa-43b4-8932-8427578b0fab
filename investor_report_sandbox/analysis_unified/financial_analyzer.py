"""
Unified Financial Analyzer
Single entry point for all financial analysis operations
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

from .core.data_loader import DataLoader
from .core.validator import FinancialValidator
from .core.calculations import FinancialCalculator
from .analyzers.revenue_analyzer import RevenueAnalyzer
from .analyzers.expense_analyzer import ExpenseAnalyzer
from .analyzers.cashflow_analyzer import CashFlowAnalyzer
from .analyzers.unit_economics import UnitEconomicsAnalyzer
from .analyzers.projections import ProjectionAnalyzer
from .reports.pdf_generator import PDFReportGenerator
from .core.visualizations import VisualizationGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UnifiedFinancialAnalyzer:
    """Main analyzer that orchestrates all financial analysis operations"""
    
    def __init__(self, data_dir: str = "data", output_dir: str = "output"):
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self.data_loader = DataLoader(data_dir)
        self.validator = FinancialValidator()
        self.calculator = FinancialCalculator()
        
        # Initialize analyzers
        self.revenue_analyzer = RevenueAnalyzer()
        self.expense_analyzer = ExpenseAnalyzer()
        self.cashflow_analyzer = CashFlowAnalyzer()
        self.unit_economics_analyzer = UnitEconomicsAnalyzer()
        self.projection_analyzer = ProjectionAnalyzer()
        
        # Initialize output generators
        self.visualizer = VisualizationGenerator(self.output_dir / "graphs")
        self.pdf_generator = PDFReportGenerator(self.output_dir / "reports")
        
        # Storage for results
        self.data = {}
        self.calculations = {}
        self.analyses = {}
        self.validation_report = {}
        
    def run_full_analysis(self) -> Dict[str, Any]:
        """Run complete financial analysis pipeline"""
        logger.info("Starting unified financial analysis...")
        start_time = datetime.now()
        
        try:
            # Step 1: Load all data
            logger.info("Step 1: Loading data...")
            self.data = self.data_loader.load_all_data()
            
            # Check data loading status
            loader_report = self.data_loader.get_validation_report()
            if loader_report['status'] == 'ERROR':
                logger.error("Critical errors in data loading")
                return self._create_error_report(loader_report['errors'])
            
            # Step 2: Calculate base metrics
            logger.info("Step 2: Calculating base metrics...")
            self.calculations = self.calculator.calculate_all_metrics(self.data)
            
            # Step 3: Run specialized analyses
            logger.info("Step 3: Running specialized analyses...")
            self._run_specialized_analyses()
            
            # Step 4: Validate everything
            logger.info("Step 4: Validating data and calculations...")
            self.validation_report = self.validator.validate_all(self.data, self.calculations)
            
            # Step 5: Generate visualizations
            logger.info("Step 5: Generating visualizations...")
            self._generate_visualizations()
            
            # Step 6: Generate reports
            logger.info("Step 6: Generating reports...")
            self._generate_reports()
            
            # Step 7: Save results
            logger.info("Step 7: Saving results...")
            self._save_results()
            
            # Calculate runtime
            runtime = (datetime.now() - start_time).total_seconds()
            
            return self._create_success_report(runtime)
            
        except Exception as e:
            logger.error(f"Critical error in analysis: {str(e)}", exc_info=True)
            return self._create_error_report([str(e)])
    
    def run_specific_analysis(self, analysis_type: str) -> Dict[str, Any]:
        """Run specific type of analysis"""
        logger.info(f"Running {analysis_type} analysis...")
        
        # Load data if not already loaded
        if not self.data:
            self.data = self.data_loader.load_all_data()
        
        # Calculate base metrics if needed
        if not self.calculations:
            self.calculations = self.calculator.calculate_all_metrics(self.data)
        
        # Run specific analysis
        if analysis_type == 'revenue':
            result = self.revenue_analyzer.analyze(self.data, self.calculations)
        elif analysis_type == 'expenses':
            result = self.expense_analyzer.analyze(self.data, self.calculations)
        elif analysis_type == 'cashflow':
            result = self.cashflow_analyzer.analyze(self.data, self.calculations)
        elif analysis_type == 'unit-economics':
            result = self.unit_economics_analyzer.analyze(self.data, self.calculations)
        elif analysis_type == 'projections':
            result = self.projection_analyzer.analyze(self.data, self.calculations)
        else:
            return {'status': 'error', 'message': f'Unknown analysis type: {analysis_type}'}
        
        # Save specific result
        output_file = self.output_dir / f"{analysis_type}_analysis.json"
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2, default=str)
        
        return {
            'status': 'success',
            'analysis_type': analysis_type,
            'output_file': str(output_file),
            'summary': result.get('summary', {})
        }
    
    def _run_specialized_analyses(self):
        """Run all specialized analyses"""
        self.analyses['revenue'] = self.revenue_analyzer.analyze(self.data, self.calculations)
        self.analyses['expenses'] = self.expense_analyzer.analyze(self.data, self.calculations)
        self.analyses['cashflow'] = self.cashflow_analyzer.analyze(self.data, self.calculations)
        self.analyses['unit_economics'] = self.unit_economics_analyzer.analyze(self.data, self.calculations)
        self.analyses['projections'] = self.projection_analyzer.analyze(self.data, self.calculations)
    
    def _generate_visualizations(self):
        """Generate all visualizations"""
        # Transform data for visualization
        
        # Revenue data with monthly/yearly/quarterly breakdowns
        revenue_viz_data = self._prepare_revenue_viz_data()
        self.visualizer.create_revenue_charts(revenue_viz_data, self.analyses['revenue'])
        
        # Expense data with categories and trends
        expense_viz_data = self._prepare_expense_viz_data()
        self.visualizer.create_expense_charts(expense_viz_data, self.analyses['expenses'])
        
        # Cash flow data with waterfall and runway
        cashflow_viz_data = self._prepare_cashflow_viz_data()
        self.visualizer.create_cashflow_charts(cashflow_viz_data)
        
        # Unit economics with cohort data
        ue_viz_data = self._prepare_unit_economics_viz_data()
        self.visualizer.create_unit_economics_charts(ue_viz_data)
        
        # Projection data with scenarios
        projection_viz_data = self._prepare_projection_viz_data()
        self.visualizer.create_projection_charts(projection_viz_data)
        
        # Financial health dashboard
        financial_health_data = self._prepare_financial_health_data()
        health_metrics = self._calculate_health_metrics()
        self.visualizer.create_financial_health_dashboard(financial_health_data, health_metrics)
        
        # Margin benchmarks chart
        margin_data = self._prepare_margin_data()
        self.visualizer.create_margin_benchmarks_chart(margin_data)
    
    def _prepare_revenue_viz_data(self):
        """Prepare revenue data for visualization"""
        # Extract invoice data to create monthly/quarterly/yearly views
        invoices = self.data.get('invoices', [])
        # Transform customer revenue to simple dict
        customer_revenue_dict = {}
        for cust_id, cust_data in self.calculations['revenue']['revenue_by_customer'].items():
            customer_revenue_dict[cust_data['name']] = cust_data['total']
            
        revenue_data = {
            'customer_revenue': customer_revenue_dict,
            'monthly_revenue': {},
            'quarterly_revenue': {},
            'yearly_revenue': {}
        }
        
        # Process invoices for time-based aggregation
        for invoice in invoices:
            if invoice.get('status') != 'PAID':
                continue
                
            date_str = invoice.get('date_string', '')
            if not date_str:
                continue
                
            # Parse date
            try:
                from datetime import datetime
                date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                month_key = date.strftime('%Y-%m')
                quarter_key = f"{date.year}-Q{(date.month-1)//3 + 1}"
                year_key = str(date.year)
                
                amount = invoice.get('total', 0)
                
                # Aggregate by time period
                revenue_data['monthly_revenue'][month_key] = revenue_data['monthly_revenue'].get(month_key, 0) + amount
                revenue_data['quarterly_revenue'][quarter_key] = revenue_data['quarterly_revenue'].get(quarter_key, 0) + amount
                revenue_data['yearly_revenue'][year_key] = revenue_data['yearly_revenue'].get(year_key, 0) + amount
            except:
                continue
        
        return revenue_data
    
    def _prepare_expense_viz_data(self):
        """Prepare expense data for visualization"""
        expense_analysis = self.analyses.get('expenses', {})
        
        # Extract category totals from the nested structure
        category_totals = {}
        categories = expense_analysis.get('category_analysis', {}).get('categorized_expenses', {})
        for category, subcats in categories.items():
            for subcat_name, subcat_data in subcats.items():
                category_totals[subcat_name] = subcat_data.get('amount', 0)
        
        return {
            'category_breakdown': category_totals,
            'monthly_expenses': {},  # Would need bank transaction data processing
            'burn_rate_analysis': expense_analysis.get('burn_rate_analysis', {
                'monthly_burn': {},
                'avg_burn_rate': expense_analysis.get('summary', {}).get('total_expenses', 0) / 12,
                'efficiency_metrics': {}
            })
        }
    
    def _prepare_cashflow_viz_data(self):
        """Prepare cash flow data for visualization"""
        cf_analysis = self.analyses.get('cashflow', {})
        
        # Get actual numbers
        total_revenue = self.calculations['revenue']['total_revenue']
        total_expenses = self.calculations['expenses']['total_expenses']
        net_cash_flow = total_revenue - total_expenses
        starting_cash = 50000  # Example starting position
        ending_cash = starting_cash + net_cash_flow
        monthly_burn = total_expenses / 12
        
        return {
            'waterfall_data': {
                'Starting Cash': starting_cash,
                'Revenue': total_revenue,
                'Expenses': -total_expenses,
                'Net Change': net_cash_flow,
                'Ending Cash': ending_cash
            },
            'runway_projection': {
                'current_cash': ending_cash,
                'monthly_burn': monthly_burn,
                'runway_months': ending_cash / monthly_burn if monthly_burn > 0 else 999,
                'months': list(range(12)),
                'cash_balance': [ending_cash - (i * monthly_burn) for i in range(12)]
            },
            'efficiency_metrics': {
                # Calculate efficiency score as a percentage (0-100)
                # If revenue > expenses, it's profitable (100% efficient)
                # If revenue = 0, it's 0% efficient
                # Otherwise, it's revenue/expenses ratio scaled to 0-100
                'efficiency_score': min(100, (total_revenue / total_expenses * 100)) if total_expenses > 0 else (100 if total_revenue > 0 else 0)
            }
        }
    
    def _prepare_unit_economics_viz_data(self):
        """Prepare unit economics data for visualization"""
        ue_metrics = self.analyses['unit_economics']['metrics']
        cohorts = self.analyses['unit_economics']['cohort_analysis']
        
        return {
            'ltv_cac_ratio': ue_metrics['ltv_cac_ratio'],
            'avg_ltv': ue_metrics['avg_ltv'],
            'avg_cac': ue_metrics['avg_cac'],
            'payback_months': ue_metrics['payback_months'],
            'gross_margin': ue_metrics['gross_margin'],
            'monthly_churn': ue_metrics['monthly_churn'],
            'nrr': ue_metrics.get('avg_nrr', 100),
            'cohort_retention': {},  # Would need actual cohort data
            'cohort_sizes': {},
            'ltv_curves': {},
            'nrr_by_month': {},
            'payback_curve': []
        }
    
    def _prepare_projection_viz_data(self):
        """Prepare projection data for visualization"""
        base_revenue = self.calculations['revenue']['total_revenue']
        
        # Create simple projections
        scenarios = {
            'Conservative': {
                'months': ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06'],
                'revenue': [base_revenue/12 * (1 + 0.02 * i) for i in range(6)]
            },
            'Base Case': {
                'months': ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06'],
                'revenue': [base_revenue/12 * (1 + 0.05 * i) for i in range(6)]
            },
            'Optimistic': {
                'months': ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06'],
                'revenue': [base_revenue/12 * (1 + 0.08 * i) for i in range(6)]
            }
        }
        
        return {
            'scenarios': scenarios,
            'historical': {
                '2024-07': base_revenue/12 * 0.8,
                '2024-08': base_revenue/12 * 0.85,
                '2024-09': base_revenue/12 * 0.9,
                '2024-10': base_revenue/12 * 0.95,
                '2024-11': base_revenue/12 * 0.98,
                '2024-12': base_revenue/12
            },
            'scenario_comparison': {
                'Conservative': {
                    'total_revenue': sum(scenarios['Conservative']['revenue']) * 2,
                    'cagr': 0.24,
                    'ending_cash': 10000,
                    'breakeven_month': 18
                },
                'Base Case': {
                    'total_revenue': sum(scenarios['Base Case']['revenue']) * 2,
                    'cagr': 0.60,
                    'ending_cash': 25000,
                    'breakeven_month': 12
                },
                'Optimistic': {
                    'total_revenue': sum(scenarios['Optimistic']['revenue']) * 2,
                    'cagr': 0.96,
                    'ending_cash': 50000,
                    'breakeven_month': 8
                }
            },
            'breakeven_analysis': {},
            'sensitivity_analysis': {}
        }
    
    def _generate_reports(self):
        """Generate all reports"""
        # Prepare comprehensive data for report
        report_data = {
            'company': 'Modular CX (MCX3D LTD)',
            'analysis_date': datetime.now(),
            'calculations': self.calculations,
            'analyses': self.analyses,
            'validation': self.validation_report
        }
        
        # Generate PDF report
        pdf_path = self.pdf_generator.generate_comprehensive_report(report_data)
        logger.info(f"PDF report generated: {pdf_path}")
    
    def _save_results(self):
        """Save all results to JSON"""
        # Comprehensive results
        results = {
            'analysis_date': datetime.now().isoformat(),
            'data_summary': {
                'loaded_files': list(self.data.keys()),
                'validation_status': self.validation_report.get('status', 'Unknown')
            },
            'calculations': self.calculations,
            'analyses': self.analyses,
            'validation_report': self.validation_report
        }
        
        # Save main results file
        output_file = self.output_dir / 'financial_analysis.json'
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to {output_file}")
    
    def _prepare_financial_health_data(self) -> Dict:
        """Prepare data for financial health dashboard"""
        return {
            'revenue': self.calculations.get('revenue', {}).get('total_revenue', 0),
            'expenses': self.calculations.get('expenses', {}).get('total_expenses', 0),
            'net_profit': self.calculations.get('profit_loss', {}).get('net_profit', 0),
            'gross_margin': self.calculations.get('profit_loss', {}).get('gross_margin_pct', 0),
            'operating_margin': self.calculations.get('profit_loss', {}).get('operating_margin_pct', 0),
            'cash_position': self.calculations.get('cash_flow', {}).get('ending_cash_balance', 0),
            'burn_rate': self.calculations.get('cash_flow', {}).get('avg_monthly_burn', 0),
            'runway_months': self.calculations.get('cash_flow', {}).get('runway_months', 0),
            'ltv_cac_ratio': self.calculations.get('unit_economics', {}).get('ltv_cac_ratio', 0),
            'customer_count': self.calculations.get('revenue', {}).get('customer_count', 0)
        }
    
    def _calculate_health_metrics(self) -> Dict:
        """Calculate health score metrics"""
        # Revenue growth score (0-25)
        growth = self.calculations.get('growth', {}).get('yoy_growth', 0)
        revenue_score = min(25, max(0, growth / 4))  # 100% growth = 25 points
        
        # Unit economics score (0-25)
        ltv_cac = self.calculations.get('unit_economics', {}).get('ltv_cac_ratio', 0)
        unit_econ_score = min(25, max(0, ltv_cac * 25 / 3))  # 3:1 ratio = 25 points
        
        # Cash position score (0-25)
        runway = self.calculations.get('cash_flow', {}).get('runway_months', 0)
        cash_score = min(25, max(0, runway * 25 / 12))  # 12 months = 25 points
        
        # Customer health score (0-25)
        customers = self.calculations.get('revenue', {}).get('customer_count', 0)
        customer_score = min(25, max(0, customers * 25 / 50))  # 50 customers = 25 points
        
        return {
            'revenue_growth_score': revenue_score,
            'unit_economics_score': unit_econ_score,
            'cash_position_score': cash_score,
            'customer_health_score': customer_score,
            'total_score': revenue_score + unit_econ_score + cash_score + customer_score,
            'categories': ['Revenue\nGrowth', 'Unit\nEconomics', 'Cash\nPosition', 'Customer\nHealth']
        }
    
    def _prepare_margin_data(self) -> Dict:
        """Prepare margin benchmark data"""
        gross_margin = self.calculations.get('profit_loss', {}).get('gross_margin_pct', 0)
        operating_margin = self.calculations.get('profit_loss', {}).get('operating_margin_pct', 0)
        net_margin = self.calculations.get('profit_loss', {}).get('net_margin_pct', 0)
        
        return {
            'company_margins': {
                'Gross Margin': gross_margin,
                'Operating Margin': operating_margin,
                'Net Margin': net_margin
            },
            'industry_benchmarks': {
                'Gross Margin': 80,  # SaaS industry average
                'Operating Margin': 20,  # SaaS industry average
                'Net Margin': 15  # SaaS industry average
            },
            'best_in_class': {
                'Gross Margin': 90,  # Top quartile SaaS
                'Operating Margin': 35,  # Top quartile SaaS
                'Net Margin': 25  # Top quartile SaaS
            }
        }
    
    def _create_success_report(self, runtime: float) -> Dict:
        """Create success report"""
        return {
            'status': 'success',
            'runtime_seconds': runtime,
            'outputs': {
                'main_results': str(self.output_dir / 'financial_analysis.json'),
                'pdf_report': str(self.output_dir / 'reports' / 'modular_cx_financial_report.pdf'),
                'visualizations': str(self.output_dir / 'graphs')
            },
            'summary': {
                'revenue': f"${self.calculations.get('revenue', {}).get('total_revenue', 0):,.0f}",
                'customers': self.calculations.get('revenue', {}).get('customer_count', 0),
                'ltv_cac_ratio': f"{self.calculations.get('unit_economics', {}).get('ltv_cac_ratio', 0):.1f}",
                'validation_status': self.validation_report.get('status', 'Unknown')
            }
        }
    
    def _create_error_report(self, errors: List[str]) -> Dict:
        """Create error report"""
        return {
            'status': 'error',
            'errors': errors,
            'timestamp': datetime.now().isoformat()
        }

# Convenience function for CLI usage
def analyze(data_dir: str = "data", output_dir: str = "analysis_output", 
            analysis_type: str = "full") -> Dict:
    """Main entry point for analysis"""
    analyzer = UnifiedFinancialAnalyzer(data_dir, output_dir)
    
    if analysis_type == "full":
        return analyzer.run_full_analysis()
    else:
        return analyzer.run_specific_analysis(analysis_type)