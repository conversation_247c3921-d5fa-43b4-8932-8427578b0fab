# Unified Financial Analysis System

## Overview

This is the consolidated, single-source-of-truth financial analysis system for Modular CX. It replaces the previous collection of 14 separate scripts with a unified, modular architecture.

## Key Improvements

1. **Single Entry Point**: One CLI command (`analyze.py`) for all analyses
2. **Modular Design**: Clear separation of concerns with reusable components
3. **Consistent Calculations**: All metrics calculated in one place with proper formulas
4. **Comprehensive Validation**: Built-in data quality checks and error handling
5. **Unified Output**: Single JSON output format and PDF report

## Architecture

```
analysis_unified/
├── core/                    # Core functionality
│   ├── data_loader.py      # Unified data loading with validation
│   ├── validator.py        # Comprehensive validation logic
│   ├── calculations.py     # All financial calculations (single source of truth)
│   └── visualizations.py   # Chart generation
│
├── analyzers/              # Specialized analysis modules
│   ├── revenue_analyzer.py # Revenue and customer analysis
│   ├── expense_analyzer.py # Expense and burn rate analysis
│   ├── cashflow_analyzer.py # Cash flow statements
│   ├── unit_economics.py   # LTV, CAC, and unit metrics
│   └── projections.py      # Financial projections
│
├── reports/                # Report generation
│   ├── pdf_generator.py    # PDF report creation
│   └── templates/          # Report templates
│
└── financial_analyzer.py   # Main orchestrator class
```

## Usage

### Complete Analysis
```bash
python analyze.py --full
```

### Specific Analyses
```bash
python analyze.py --revenue          # Revenue analysis only
python analyze.py --expenses         # Expense analysis only
python analyze.py --unit-economics   # Unit economics only
python analyze.py --projections      # Financial projections
python analyze.py --validate         # Data validation only
```

### Options
```bash
python analyze.py --full --data-dir custom_data --output-dir results
python analyze.py --revenue --format json    # JSON output
python analyze.py --full --no-pdf            # Skip PDF generation
python analyze.py --full --quiet             # Minimal output
```

## Output Structure

All analyses produce consistent output:

```
analysis_output/
├── financial_analysis.json      # Complete analysis results
├── reports/
│   └── modular_cx_financial_report.pdf
└── graphs/
    ├── revenue_charts.png
    ├── expense_charts.png
    └── ...
```

## Key Metrics Calculated

### Revenue Metrics
- Total revenue by customer, currency
- Customer concentration risk
- Revenue growth patterns

### Expense Metrics
- Category breakdown
- Burn rate analysis
- Efficiency scores

### Unit Economics (Corrected)
- **CAC**: Including allocated salaries (30%)
- **LTV**: Using formula: (Monthly Revenue × Gross Margin) / Monthly Churn
- **LTV/CAC Ratio**: With realistic bounds checking
- **Payback Period**: Based on contribution margin

### Financial Health
- Rule of 40 score
- Cash runway
- Operating margins
- Growth projections (conservative)

## Validation Checks

The system performs comprehensive validation:

1. **Data Integrity**: File existence, JSON validity
2. **Financial Consistency**: P&L totals, balance sheet equation
3. **Calculation Validation**: Realistic ranges for all metrics
4. **Cross-Validation**: Revenue consistency across sources

## Migration from Old Scripts

### Old Script → New Module Mapping

| Old Script | New Module | Notes |
|------------|------------|-------|
| `deep_financial_analysis.py` | `core/calculations.py` | Calculations only |
| `financial_analysis_improved.py` | Built into data_loader/validator | Error handling integrated |
| `unit_economics_corrected.py` | `analyzers/unit_economics.py` | Using corrected formulas |
| `projections_realistic.py` | `analyzers/projections.py` | Conservative estimates only |
| `data_validator.py` | `core/validator.py` | Expanded validation |
| Multiple PDF generators | `reports/pdf_generator.py` | Single generator |

### Data Compatibility

The new system reads the same data files:
- `data/*_latest.json` files from Xero
- No changes needed to data pipeline

## Development

### Adding New Analyses

1. Create new analyzer in `analyzers/`
2. Implement `analyze(data, calculations)` method
3. Add to `financial_analyzer.py`
4. Update CLI in `analyze.py`

### Testing

```bash
python -m pytest tests/
```

## Benefits Over Previous System

1. **Reduced Code**: ~1,500 lines vs ~3,500 lines
2. **No Duplication**: Single calculation source
3. **Consistent Results**: Same formulas everywhere
4. **Better Maintenance**: Modular structure
5. **Comprehensive Testing**: Unit tests for all calculations
6. **Production Ready**: Error handling, logging, validation

---

*This unified system ensures consistent, accurate financial analysis with a single source of truth for all metrics.*