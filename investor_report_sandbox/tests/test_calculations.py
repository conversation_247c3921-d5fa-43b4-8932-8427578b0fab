#!/usr/bin/env python3
"""
Quick test to verify our calculation fixes
"""

import json
import sys
sys.path.append('analysis_unified')

from core.data_loader import DataLoader
from core.calculations import FinancialCalculator

def main():
    print("Testing Financial Calculations")
    print("=" * 60)
    
    # Load data
    loader = DataLoader()
    data = loader.load_all_data()
    
    # Run calculations
    calculator = FinancialCalculator()
    metrics = calculator.calculate_all_metrics(data)
    
    # Display key metrics
    print("\nKEY METRICS:")
    print(f"Total Revenue: ${metrics['revenue']['total_revenue']:,.2f}")
    print(f"P&L Income: ${metrics['profit_loss']['total_income']:,.2f}")
    
    print(f"\nUnit Economics:")
    print(f"  Monthly Churn: {metrics['unit_economics']['monthly_churn']:.1%}")
    print(f"  Gross Margin: {metrics['unit_economics']['gross_margin']:.1%}")
    print(f"  CAC: ${metrics['unit_economics']['avg_cac']:.2f}")
    print(f"  LTV: ${metrics['unit_economics']['avg_ltv']:.2f}")
    print(f"  LTV/CAC Ratio: {metrics['unit_economics']['ltv_cac_ratio']:.1f}")
    
    print(f"\nGrowth Metrics:")
    print(f"  YoY Growth: {metrics['growth']['yoy_growth']:.1f}%")
    print(f"  MoM Growth: {metrics['growth']['mom_growth']:.1f}%")
    
    print(f"\nCash Flow:")
    print(f"  Monthly Burn: ${metrics['cash_flow']['avg_monthly_burn']:,.2f}")
    print(f"  Runway: {metrics['cash_flow']['runway_months']:.1f} months")
    
    # Save to file
    with open('test_results.json', 'w') as f:
        json.dump(metrics, f, indent=2)
    
    print("\nFull results saved to test_results.json")

if __name__ == "__main__":
    main()