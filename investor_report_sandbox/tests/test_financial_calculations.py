#!/usr/bin/env python3
"""
Unit tests for financial calculations
Ensures accuracy and reliability of metrics using actual data-driven calculations
"""

import unittest
import sys
import os
from datetime import datetime
from unittest.mock import MagicMock, patch

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from analysis_unified.core.calculations import FinancialCalculator

class TestFinancialCalculations(unittest.TestCase):
    
    def setUp(self):
        """Set up test data matching actual Xero data structures"""
        self.calculator = FinancialCalculator()
        
        # Test invoice data structure matching Xero format
        self.test_invoices = [
            {
                'InvoiceID': '1',
                'Status': 'PAID',
                'Total': '1000.00',
                'CurrencyCode': 'GBP',
                'DateString': '/Date(1672531200000+0000)/',  # 2023-01-01
                'Contact': {
                    'ContactID': 'customer1',
                    'Name': 'Test Customer 1'
                }
            },
            {
                'InvoiceID': '2',
                'Status': 'PAID',
                'Total': '2000.00',
                'CurrencyCode': 'GBP',
                'DateString': '/Date(1680307200000+0000)/',  # 2023-04-01
                'Contact': {
                    'ContactID': 'customer1',
                    'Name': 'Test Customer 1'
                }
            },
            {
                'InvoiceID': '3',
                'Status': 'PAID',
                'Total': '500.00',
                'CurrencyCode': 'USD',
                'DateString': '/Date(1688169600000+0000)/',  # 2023-07-01
                'Contact': {
                    'ContactID': 'customer2',
                    'Name': 'Test Customer 2'
                }
            },
            {
                'InvoiceID': '4',
                'Status': 'DRAFT',  # Not paid - should be excluded
                'Total': '1500.00',
                'CurrencyCode': 'GBP',
                'DateString': '/Date(1690848000000+0000)/',
                'Contact': {
                    'ContactID': 'customer3',
                    'Name': 'Test Customer 3'
                }
            }
        ]
        
        # Test P&L data structure matching Xero format
        self.test_pl_data = {
            'Reports': [{
                'ReportType': 'ProfitAndLoss',
                'Rows': [
                    {
                        'Title': 'Income',
                        'Rows': [
                            {
                                'RowType': 'Row',
                                'Cells': [
                                    {'Value': 'Consulting Revenue'},
                                    {'Value': '50000.00'}
                                ]
                            },
                            {
                                'RowType': 'SummaryRow',
                                'Cells': [
                                    {'Value': 'Total Income'},
                                    {'Value': '50000.00'}
                                ]
                            }
                        ]
                    },
                    {
                        'Title': 'Operating Expenses',
                        'Rows': [
                            {
                                'RowType': 'Row',
                                'Cells': [
                                    {'Value': 'Advertising & Marketing'},
                                    {'Value': '1000.00'}
                                ]
                            },
                            {
                                'RowType': 'Row',
                                'Cells': [
                                    {'Value': 'Salaries'},
                                    {'Value': '10000.00'}
                                ]
                            },
                            {
                                'RowType': 'Row',
                                'Cells': [
                                    {'Value': 'Office Expenses'},
                                    {'Value': '2000.00'}
                                ]
                            }
                        ]
                    },
                    {
                        'RowType': 'Section',
                        'Title': '',
                        'Rows': [
                            {
                                'RowType': 'Row',
                                'Cells': [
                                    {'Value': 'Net Profit'},
                                    {'Value': '37000.00'}
                                ]
                            }
                        ]
                    }
                ]
            }]
        }
        
        # Test bank transaction data
        self.test_bank_transactions = [
            {
                'Total': '5000.00',
                'Type': 'RECEIVE',
                'DateString': '/Date(*************+0000)/'  # 2023-02-01
            },
            {
                'Total': '3000.00',
                'Type': 'SPEND',
                'DateString': '/Date(*************+0000)/'  # 2023-03-01
            },
            {
                'Total': '4000.00',
                'Type': 'RECEIVE',
                'DateString': '/Date(*************+0000)/'  # 2023-06-01
            },
            {
                'Total': '2500.00',
                'Type': 'SPEND',
                'DateString': '/Date(*************+0000)/'
            }
        ]
        
        # Complete test data object
        self.test_data = {
            'invoices': self.test_invoices,
            'profit_loss': self.test_pl_data,
            'bank_transactions': self.test_bank_transactions
        }
    
    def test_revenue_metrics_calculation(self):
        """Test revenue metrics calculation from invoice data"""
        metrics = self.calculator._calculate_revenue_metrics(self.test_data)
        
        # Verify total revenue (only PAID invoices)
        self.assertEqual(metrics['total_revenue'], 3500.00, "Total revenue should be 3500 (1000 + 2000 + 500)")
        
        # Verify customer count
        self.assertEqual(metrics['customer_count'], 2, "Should have 2 customers with paid invoices")
        
        # Verify revenue by customer
        self.assertEqual(metrics['revenue_by_customer']['customer1']['total'], 3000.00)
        self.assertEqual(metrics['revenue_by_customer']['customer2']['total'], 500.00)
        
        # Verify revenue by currency
        self.assertEqual(metrics['revenue_by_currency']['GBP'], 3000.00)
        self.assertEqual(metrics['revenue_by_currency']['USD'], 500.00)
        
        # Verify average invoice value
        self.assertAlmostEqual(metrics['average_invoice_value'], 1166.67, places=2)
        
        # Verify concentration risk
        self.assertAlmostEqual(metrics['concentration_risk'], 85.71, places=2)  # 3000/3500 * 100
        
        # Verify top customer
        self.assertEqual(metrics['top_customer']['name'], 'Test Customer 1')
        self.assertEqual(metrics['top_customer']['revenue'], 3000.00)
    
    def test_expense_metrics_calculation(self):
        """Test expense metrics calculation from P&L data"""
        metrics = self.calculator._calculate_expense_metrics(self.test_data)
        
        # Verify total expenses
        self.assertEqual(metrics['total_expenses'], 13000.00)
        
        # Verify expense categories
        self.assertEqual(metrics['expense_categories']['Advertising & Marketing'], 1000.00)
        self.assertEqual(metrics['expense_categories']['Salaries'], 10000.00)
        self.assertEqual(metrics['expense_categories']['Office Expenses'], 2000.00)
        
        # Verify largest expense
        self.assertEqual(metrics['largest_expense']['category'], 'Salaries')
        self.assertEqual(metrics['largest_expense']['amount'], 10000.00)
        self.assertAlmostEqual(metrics['largest_expense']['percentage'], 76.92, places=2)
        
        # Verify salary percentage
        self.assertAlmostEqual(metrics['salary_percentage'], 76.92, places=2)
    
    def test_pl_metrics_calculation(self):
        """Test P&L metrics calculation"""
        # First calculate revenue and expenses to populate metrics
        self.calculator._calculate_revenue_metrics(self.test_data)
        self.calculator._calculate_expense_metrics(self.test_data)
        
        metrics = self.calculator._calculate_pl_metrics(self.test_data)
        
        # Verify income and expenses
        self.assertEqual(metrics['total_income'], 50000.00)
        self.assertEqual(metrics['total_expenses'], 13000.00)
        
        # For service business with no COGS, gross profit = total income
        self.assertEqual(metrics['gross_profit'], 50000.00)
        self.assertEqual(metrics['gross_margin_pct'], 100.0)
        
        # Verify operating profit
        self.assertEqual(metrics['operating_profit'], 37000.00)
        
        # Verify net profit from P&L
        self.assertEqual(metrics['net_profit'], 37000.00)
    
    def test_cac_calculation(self):
        """Test Customer Acquisition Cost calculation"""
        # Set up metrics that CAC calculation depends on
        self.calculator.metrics['revenue'] = {'customer_count': 2}
        self.calculator.metrics['expenses'] = {
            'expense_categories': {
                'Advertising & Marketing': 1000.00,
                'Salaries': 10000.00,
                'Office Expenses': 2000.00
            }
        }
        
        cac_data = self.calculator._calculate_cac(self.test_data)
        
        # Expected: Only direct Marketing expenses (1000)
        # New customers: 2
        # CAC = 1000/2 = 500
        self.assertEqual(cac_data['total_sm_spend'], 1000.00)
        self.assertEqual(cac_data['new_customers'], 2)
        self.assertEqual(cac_data['avg_cac'], 500.00)
    
    def test_ltv_calculation(self):
        """Test Lifetime Value calculation"""
        # Set up metrics that LTV calculation depends on
        self.calculator.metrics['revenue'] = {
            'total_revenue': 36000.00,  # 3000/month for 12 months
            'customer_count': 2
        }
        self.calculator.metrics['profit_loss'] = {
            'gross_margin_pct': 80.0  # 80% gross margin
        }
        
        # Mock churn calculation to return a known value
        with patch.object(self.calculator, '_calculate_churn_from_data', return_value=0.05):  # 5% monthly churn
            ltv_data = self.calculator._calculate_ltv(self.test_data)
        
        # Monthly revenue per customer: 36000 / 12 / 2 = 1500
        # LTV = (1500 * 0.8) / 0.05 = 24000
        self.assertEqual(ltv_data['avg_monthly_revenue'], 1500.00)
        self.assertEqual(ltv_data['avg_ltv'], 24000.00)
    
    def test_ltv_with_zero_churn(self):
        """Test LTV calculation when churn is zero"""
        # Set up metrics
        self.calculator.metrics['revenue'] = {
            'total_revenue': 36000.00,
            'customer_count': 2
        }
        self.calculator.metrics['profit_loss'] = {
            'gross_margin_pct': 80.0
        }
        
        # Mock zero churn
        with patch.object(self.calculator, '_calculate_churn_from_data', return_value=0):
            ltv_data = self.calculator._calculate_ltv(self.test_data)
        
        # With zero churn, LTV should be capped at 24 months
        # Monthly revenue per customer: 1500
        # LTV = 1500 * 0.8 * 24 = 28800
        self.assertEqual(ltv_data['avg_ltv'], 28800.00)
    
    def test_unit_economics_integration(self):
        """Test complete unit economics calculation"""
        # Set up all required metrics
        self.calculator.metrics['revenue'] = {'customer_count': 2, 'total_revenue': 36000}
        self.calculator.metrics['expenses'] = {
            'expense_categories': {
                'Advertising & Marketing': 1000.00,
                'Salaries': 10000.00
            }
        }
        self.calculator.metrics['profit_loss'] = {'gross_margin_pct': 80.0}
        
        with patch.object(self.calculator, '_calculate_churn_from_data', return_value=0.05):
            metrics = self.calculator._calculate_unit_economics(self.test_data)
        
        # Verify CAC
        self.assertEqual(metrics['avg_cac'], 2000.00)
        
        # Verify LTV
        self.assertEqual(metrics['avg_ltv'], 24000.00)
        
        # Verify LTV/CAC ratio
        self.assertEqual(metrics['ltv_cac_ratio'], 12.0)
        
        # Verify payback period
        # Monthly contribution = 1500 * 0.8 = 1200
        # Payback = 2000 / 1200 = 1.67 months
        self.assertAlmostEqual(metrics['payback_months'], 1.67, places=2)
    
    def test_cash_flow_metrics(self):
        """Test cash flow metrics calculation"""
        metrics = self.calculator._calculate_cash_flow_metrics(self.test_data)
        
        # Total cash in: 5000 + 4000 = 9000
        self.assertEqual(metrics['total_cash_in'], 9000.00)
        
        # Total cash out: 3000 + 2500 = 5500
        self.assertEqual(metrics['total_cash_out'], 5500.00)
        
        # Net cash flow: 9000 - 5500 = 3500
        self.assertEqual(metrics['net_cash_flow'], 3500.00)
        
        # Average monthly burn should be negative (more out than in per month)
        # Month 1: 5000 - 3000 = 2000 (positive)
        # Month 2: 4000 - 2500 = 1500 (positive)
        # Average burn = -(2000 + 1500) / 2 = -1750 (negative because it's positive cash flow)
        self.assertEqual(metrics['avg_monthly_burn'], -1750.00)
    
    def test_growth_metrics(self):
        """Test growth metrics calculation"""
        # Add more historical data for growth calculation
        extended_invoices = [
            # 2022 invoices
            {
                'InvoiceID': '5',
                'Status': 'PAID',
                'Total': '800.00',
                'DateString': '2022-08-01T00:00:00Z',
                'Contact': {'ContactID': 'customer1', 'Name': 'Test Customer 1'}
            },
            {
                'InvoiceID': '6',
                'Status': 'PAID',
                'Total': '700.00',
                'DateString': '2022-09-01T00:00:00Z',
                'Contact': {'ContactID': 'customer2', 'Name': 'Test Customer 2'}
            }
        ] + self.test_invoices
        
        test_data = {'invoices': extended_invoices}
        metrics = self.calculator._calculate_growth_metrics(test_data)
        
        # 2022 total: 1500, 2023 total: 3500
        # YoY growth: (3500 - 1500) / 1500 * 100 = 133.33%
        self.assertAlmostEqual(metrics['yoy_growth'], 133.33, places=2)
        
        # MoM growth: August (1500) to September (2000)
        # (2000 - 1500) / 1500 * 100 = 33.33%
        self.assertAlmostEqual(metrics['mom_growth'], 33.33, places=2)
    
    def test_date_parsing(self):
        """Test date parsing for various formats"""
        # Test Xero timestamp format
        xero_date = self.calculator._parse_date('/Date(1690848000000+0000)/')
        self.assertIsInstance(xero_date, datetime)
        self.assertEqual(xero_date.year, 2023)
        self.assertEqual(xero_date.month, 8)
        self.assertEqual(xero_date.day, 1)
        
        # Test ISO format
        iso_date = self.calculator._parse_date('2023-08-01T00:00:00Z')
        self.assertIsInstance(iso_date, datetime)
        self.assertEqual(iso_date.year, 2023)
        self.assertEqual(iso_date.month, 8)
    
    def test_churn_calculation(self):
        """Test customer churn calculation from invoice data"""
        # Create invoice data showing customer churn with dates around April 2025 cutoff
        churn_invoices = [
            # Active customers (paid within 3 months of April 2025 cutoff)
            {'InvoiceID': '1', 'Status': 'PAID', 'DateString': '2025-02-01T00:00:00Z',
             'FullyPaidOnDate': '2025-02-01T00:00:00Z', 'Contact': {'ContactID': 'c1'}},
            {'InvoiceID': '2', 'Status': 'PAID', 'DateString': '2025-03-15T00:00:00Z',
             'FullyPaidOnDate': '2025-03-15T00:00:00Z', 'Contact': {'ContactID': 'c2'}},
            {'InvoiceID': '3', 'Status': 'PAID', 'DateString': '2025-04-01T00:00:00Z',
             'FullyPaidOnDate': '2025-04-01T00:00:00Z', 'Contact': {'ContactID': 'c3'}},
            # Churned customers (last payment before Jan 2025)
            {'InvoiceID': '4', 'Status': 'PAID', 'DateString': '2024-10-01T00:00:00Z',
             'FullyPaidOnDate': '2024-10-01T00:00:00Z', 'Contact': {'ContactID': 'c4'}},
            {'InvoiceID': '5', 'Status': 'PAID', 'DateString': '2024-11-15T00:00:00Z',
             'FullyPaidOnDate': '2024-11-15T00:00:00Z', 'Contact': {'ContactID': 'c5'}},
            # Minimum 5 customers required
            {'InvoiceID': '6', 'Status': 'PAID', 'DateString': '2025-01-15T00:00:00Z',
             'FullyPaidOnDate': '2025-01-15T00:00:00Z', 'Contact': {'ContactID': 'c6'}}
        ]
        
        churn = self.calculator._calculate_churn_from_data({'invoices': churn_invoices})
        
        # 4 active customers (c1, c2, c3, c6), 2 churned (c4, c5)
        # Total: 6 customers
        # Churn rate: 2/6 = 33.3% annual
        # Monthly churn = 1 - (1 - 0.333)^(1/12) ≈ 0.033
        self.assertAlmostEqual(churn, 0.033, places=2)
    
    def test_edge_cases(self):
        """Test edge cases and error handling"""
        # Test with empty data
        empty_data = {'invoices': [], 'profit_loss': None, 'bank_transactions': []}
        
        # Should not crash and return default values
        revenue_metrics = self.calculator._calculate_revenue_metrics(empty_data)
        self.assertEqual(revenue_metrics['total_revenue'], 0)
        self.assertEqual(revenue_metrics['customer_count'], 0)
        
        expense_metrics = self.calculator._calculate_expense_metrics(empty_data)
        self.assertEqual(expense_metrics['total_expenses'], 0)
        
        # Test division by zero handling
        self.calculator.metrics['revenue'] = {'customer_count': 0}
        self.calculator.metrics['expenses'] = {'expense_categories': {}}
        cac_data = self.calculator._calculate_cac(empty_data)
        self.assertEqual(cac_data['avg_cac'], 0)
    
    def test_financial_ratios(self):
        """Test financial ratios calculation"""
        # Set up metrics
        self.calculator.metrics['revenue'] = {'total_revenue': 120000}  # 10k/month
        self.calculator.metrics['expenses'] = {'total_expenses': 96000}  # 8k/month
        self.calculator.metrics['profit_loss'] = {'operating_margin_pct': 20}
        self.calculator.metrics['growth'] = {'yoy_growth': 30}
        
        ratios = self.calculator._calculate_financial_ratios()
        
        # Monthly metrics
        self.assertEqual(ratios['monthly_revenue'], 10000)
        self.assertEqual(ratios['monthly_expenses'], 8000)
        self.assertEqual(ratios['monthly_burn_rate'], 0)  # Profitable, no burn
        
        # Rule of 40: Growth (30) + Operating Margin (20) = 50
        self.assertEqual(ratios['rule_of_40'], 50)
    
    def test_calculate_all_metrics_integration(self):
        """Test the main calculate_all_metrics method"""
        # This tests the full integration of all calculations
        metrics = self.calculator.calculate_all_metrics(self.test_data)
        
        # Verify all main sections are present
        self.assertIn('revenue', metrics)
        self.assertIn('expenses', metrics)
        self.assertIn('profit_loss', metrics)
        self.assertIn('unit_economics', metrics)
        self.assertIn('financial_ratios', metrics)
        self.assertIn('cash_flow', metrics)
        self.assertIn('growth', metrics)
        
        # Verify some key calculated values
        self.assertEqual(metrics['revenue']['total_revenue'], 3500.00)
        self.assertEqual(metrics['expenses']['total_expenses'], 13000.00)
        self.assertGreater(metrics['unit_economics']['avg_cac'], 0)
        self.assertGreater(metrics['unit_economics']['avg_ltv'], 0)


class TestDataValidation(unittest.TestCase):
    """Test data validation and sanity checks"""
    
    def test_revenue_concentration_validation(self):
        """Test customer concentration risk validation"""
        calculator = FinancialCalculator()
        
        # High concentration scenario
        high_concentration_data = {
            'invoices': [
                {'Status': 'PAID', 'Total': '90000', 'CurrencyCode': 'GBP',
                 'Contact': {'ContactID': 'c1', 'Name': 'Big Customer'}},
                {'Status': 'PAID', 'Total': '5000', 'CurrencyCode': 'GBP',
                 'Contact': {'ContactID': 'c2', 'Name': 'Small Customer 1'}},
                {'Status': 'PAID', 'Total': '5000', 'CurrencyCode': 'GBP',
                 'Contact': {'ContactID': 'c3', 'Name': 'Small Customer 2'}}
            ]
        }
        
        metrics = calculator._calculate_revenue_metrics(high_concentration_data)
        
        # Should identify high concentration (90%)
        self.assertEqual(metrics['concentration_risk'], 90.0)
        self.assertEqual(metrics['top_customer']['revenue'], 90000)
    
    def test_margin_validation(self):
        """Test margin calculation validation across different scenarios"""
        calculator = FinancialCalculator()
        
        test_cases = [
            {'revenue': 100000, 'expenses': 80000, 'expected_margin': 20},
            {'revenue': 100000, 'expenses': 100000, 'expected_margin': 0},
            {'revenue': 100000, 'expenses': 120000, 'expected_margin': -20},
            {'revenue': 0, 'expenses': 10000, 'expected_margin': 0},  # Edge case
        ]
        
        for case in test_cases:
            if case['revenue'] > 0:
                margin = ((case['revenue'] - case['expenses']) / case['revenue']) * 100
                self.assertAlmostEqual(margin, case['expected_margin'], places=1)
            else:
                # When revenue is 0, margin calculation should handle gracefully
                margin = 0
                self.assertEqual(margin, case['expected_margin'])
    
    def test_metric_ranges(self):
        """Test that calculated metrics fall within reasonable ranges"""
        calculator = FinancialCalculator()
        
        # Set up reasonable test data
        calculator.metrics = {
            'revenue': {'customer_count': 10, 'total_revenue': 100000},
            'expenses': {'expense_categories': {'Marketing': 5000, 'Salaries': 30000}},
            'profit_loss': {'gross_margin_pct': 85}
        }
        
        with patch.object(calculator, '_calculate_churn_from_data', return_value=0.03):  # 3% churn
            unit_economics = calculator._calculate_unit_economics({})
        
        # LTV/CAC ratio should be positive and reasonable
        self.assertGreater(unit_economics['ltv_cac_ratio'], 0)
        self.assertLess(unit_economics['ltv_cac_ratio'], 50)  # Unrealistic if >50
        
        # Payback period should be reasonable
        self.assertGreater(unit_economics['payback_months'], 0)
        self.assertLess(unit_economics['payback_months'], 36)  # Concerning if >36 months
    
    def test_data_completeness(self):
        """Test handling of incomplete data"""
        calculator = FinancialCalculator()
        
        # Missing contact information
        incomplete_data = {
            'invoices': [
                {'Status': 'PAID', 'Total': '1000', 'Contact': {}}  # Missing ContactID
            ]
        }
        
        # Should handle gracefully
        metrics = calculator._calculate_revenue_metrics(incomplete_data)
        self.assertEqual(metrics['total_revenue'], 1000)
        self.assertIn('Unknown', metrics['revenue_by_customer'])


def run_tests():
    """Run all tests"""
    # Create test runner
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestFinancialCalculations))
    suite.addTests(loader.loadTestsFromTestCase(TestDataValidation))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return success/failure
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)