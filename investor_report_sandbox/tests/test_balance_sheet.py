#!/usr/bin/env python3
"""
Test balance sheet reclassification
"""

import json

# Load balance sheet data
with open('data/balance_sheet_latest.json', 'r') as f:
    bs_data = json.load(f)

assets = 0
liabilities = 0
equity = 0

print("BALANCE SHEET RECLASSIFICATION")
print("=" * 60)

for report in bs_data.get('Reports', []):
    if report.get('ReportType') == 'BalanceSheet':
        for section in report.get('Rows', []):
            for row in section.get('Rows', []):
                if row.get('RowType') == 'Row' and len(row.get('Cells', [])) >= 2:
                    account_name = row['Cells'][0].get('Value', '')
                    value = float(row['Cells'][1].get('Value', 0))
                    
                    # Skip calculated fields
                    if 'Net Assets' in account_name:
                        print(f"  SKIPPING: {account_name}: ${value:,.2f}")
                        continue
                        
                    # Classify based on our rules
                    if any(term in account_name.lower() for term in ['cash', 'bank', 'receivable', 'equipment', 'prepaid', 'asset']):
                        assets += value
                        print(f"  ASSET: {account_name}: ${value:,.2f}")
                    elif 'WISE' in account_name.upper() or 'Wise' in account_name:
                        assets += value
                        print(f"  ASSET (Wise): {account_name}: ${value:,.2f}")
                    elif any(term in account_name.lower() for term in ['payable', 'accrual', 'vat', 'debt']):
                        liabilities += value
                        print(f"  LIABILITY: {account_name}: ${value:,.2f}")
                    elif 'loan' in account_name.lower() and 'directors' not in account_name.lower():
                        liabilities += value
                        print(f"  LIABILITY: {account_name}: ${value:,.2f}")
                    elif 'directors' in account_name.lower() and 'loan' in account_name.lower():
                        equity += value
                        print(f"  EQUITY (Directors): {account_name}: ${value:,.2f}")
                    elif any(term in account_name.lower() for term in ['capital', 'share', 'retained', 'earning']):
                        equity += value
                        print(f"  EQUITY: {account_name}: ${value:,.2f}")
                    elif 'rounding' in account_name.lower():
                        liabilities += value
                        print(f"  LIABILITY (Rounding): {account_name}: ${value:,.2f}")
                    else:
                        print(f"  UNCLASSIFIED: {account_name}: ${value:,.2f}")

print("\n" + "=" * 60)
print(f"TOTAL ASSETS: ${assets:,.2f}")
print(f"TOTAL LIABILITIES: ${liabilities:,.2f}")
print(f"TOTAL EQUITY: ${equity:,.2f}")
print(f"\nA = L + E Check:")
print(f"  Assets: ${assets:,.2f}")
print(f"  L + E: ${liabilities + equity:,.2f}")
print(f"  Difference: ${assets - (liabilities + equity):,.2f}")

if abs(assets - (liabilities + equity)) < 0.01:
    print("\n✅ BALANCE SHEET BALANCES!")
else:
    print("\n❌ BALANCE SHEET DOES NOT BALANCE")