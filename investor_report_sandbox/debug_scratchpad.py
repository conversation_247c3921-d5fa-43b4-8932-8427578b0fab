#!/usr/bin/env python3
"""
Debug <PERSON>ratchpad for MCX3D Financial Analysis Issues
====================================================

This script investigates the calculation issues identified in the validation:
1. Very high LTV/CAC ratio: 44.7
2. Revenue variance across sources: 12.8% 
3. Revenue calculation inconsistency: 49.6% variance
4. Severe revenue decline: -88%
5. High churn rate: 15.0% monthly (85.8% annual)

"""

import json
import sys
import os
from datetime import datetime, timedelta
from collections import defaultdict

# Add analysis_unified to path
sys.path.append('analysis_unified')

from core.data_loader import DataLoader
from core.calculations import FinancialCalculator
from core.validator import FinancialValidator

def main():
    print("="*80)
    print("MCX3D FINANCIAL ANALYSIS DEBUG SESSION")
    print("="*80)

    # Load data
    print("\n1. LOADING DATA...")
    loader = DataLoader()
    data = loader.load_all_data()

    # Check data loading issues
    validation_report = loader.get_validation_report()
    print(f"Data loading status: {validation_report['status']}")
    if validation_report['errors']:
        print("ERRORS:")
        for error in validation_report['errors']:
            print(f"  - {error}")
    if validation_report['warnings']:
        print("WARNINGS:")
        for warning in validation_report['warnings']:
            print(f"  - {warning}")

    print(f"Loaded files: {validation_report['loaded_files']}")

    # Run calculations
    print("\n2. RUNNING CALCULATIONS...")
    calculator = FinancialCalculator()
    metrics = calculator.calculate_all_metrics(data)

    # Debug bank transaction data first
    print("\n3. BANK TRANSACTION ANALYSIS")
    print("-" * 40)
    debug_bank_transactions(data, metrics)

    # Debug revenue calculations
    print("\n4. REVENUE ANALYSIS DEBUG")
    print("-" * 40)
    debug_revenue_calculations(data, metrics)

    # Debug unit economics
    print("\n5. UNIT ECONOMICS DEBUG")
    print("-" * 40)
    debug_unit_economics(data, metrics)

    # Debug churn calculation
    print("\n6. CHURN CALCULATION DEBUG")
    print("-" * 40)
    debug_churn_calculation(data, metrics)

    # Debug customer data
    print("\n7. CUSTOMER DATA DEBUG")
    print("-" * 40)
    debug_customer_data(data, metrics)

    # Summary of issues
    print("\n8. SUMMARY OF ISSUES FOUND")
    print("-" * 40)
    summarize_issues(metrics)

def debug_bank_transactions(data, metrics):
    """Debug bank transaction data for customer payments"""

    bank_data = data.get('bank_transactions', [])
    if not bank_data:
        print("No bank transaction data available")
        return

    print(f"Total bank transactions: {len(bank_data)}")

    # Analyze RECEIVE transactions (customer payments)
    receive_transactions = []
    total_received = 0
    customers_from_bank = set()

    for txn in bank_data:
        if txn.get('Type') == 'RECEIVE':
            # Bank transactions use 'Total' field, not 'Amount'
            amount = float(txn.get('Total', 0))
            contact = txn.get('Contact', {})
            contact_name = contact.get('Name', '') if contact else ''
            date_str = txn.get('Date', '')
            # Bank transactions don't have Description, use other fields
            bank_account = txn.get('BankAccount', {}).get('Name', '')
            description = f"Bank: {bank_account}"

            receive_transactions.append({
                'date': date_str,
                'amount': amount,
                'contact': contact_name,
                'description': description
            })

            total_received += amount
            if contact_name:
                customers_from_bank.add(contact_name)

    print(f"RECEIVE transactions: {len(receive_transactions)}")
    print(f"Total received amount: £{total_received:,.2f}")
    print(f"Unique customers from bank data: {len(customers_from_bank)}")

    # Show top bank payments
    receive_transactions.sort(key=lambda x: x['amount'], reverse=True)
    print(f"\nTop 10 Bank Payments:")
    for i, txn in enumerate(receive_transactions[:10]):
        contact = txn['contact'][:25] if txn['contact'] else 'Unknown'
        print(f"  {i+1:2d}. {contact:<25} £{txn['amount']:>8,.2f} {txn['date'][:10]} {txn['description'][:30]}")

    # Compare with invoice customers
    invoice_customers = set()
    invoices_data = data.get('invoices')
    if invoices_data:
        invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
        for invoice in invoices:
            if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                contact_name = invoice.get('Contact', {}).get('Name', '')
                if contact_name:
                    invoice_customers.add(contact_name)

    print(f"\nCustomer Overlap Analysis:")
    print(f"Customers in invoices: {len(invoice_customers)}")
    print(f"Customers in bank data: {len(customers_from_bank)}")

    # Find customers only in bank data (no invoices)
    bank_only_customers = customers_from_bank - invoice_customers
    invoice_only_customers = invoice_customers - customers_from_bank
    common_customers = customers_from_bank & invoice_customers

    print(f"Customers in both: {len(common_customers)}")
    print(f"Bank-only customers: {len(bank_only_customers)}")
    print(f"Invoice-only customers: {len(invoice_only_customers)}")

    if bank_only_customers:
        print(f"\nBank-only customers (may explain revenue gap):")
        bank_only_revenue = 0
        for txn in receive_transactions:
            if txn['contact'] in bank_only_customers:
                bank_only_revenue += txn['amount']
        print(f"Revenue from bank-only customers: £{bank_only_revenue:,.2f}")

        for customer in list(bank_only_customers)[:5]:  # Show first 5
            customer_total = sum(txn['amount'] for txn in receive_transactions if txn['contact'] == customer)
            print(f"  - {customer}: £{customer_total:,.2f}")

def debug_revenue_calculations(data, metrics):
    """Debug revenue calculation discrepancies"""
    
    # P&L Revenue
    pl_revenue = metrics.get('profit_loss', {}).get('total_income', 0)
    print(f"P&L Total Income: £{pl_revenue:,.2f}")
    
    # Invoice Revenue
    invoice_revenue = sum(c['total'] for c in metrics.get('revenue', {}).get('revenue_by_customer', {}).values())
    print(f"Invoice Total: £{invoice_revenue:,.2f}")
    
    # Total Revenue (what system uses)
    total_revenue = metrics.get('revenue', {}).get('total_revenue', 0)
    print(f"System Total Revenue: £{total_revenue:,.2f}")
    
    # Calculate variance
    if pl_revenue > 0 and invoice_revenue > 0:
        variance = abs(pl_revenue - invoice_revenue) / pl_revenue * 100
        print(f"P&L vs Invoice Variance: {variance:.1f}%")
    
    # Check customer revenue breakdown
    revenue_by_customer = metrics.get('revenue', {}).get('revenue_by_customer', {})
    print(f"\nCustomer Revenue Breakdown ({len(revenue_by_customer)} customers):")
    
    # Sort customers by revenue
    sorted_customers = sorted(revenue_by_customer.items(), 
                            key=lambda x: x[1]['total'], reverse=True)
    
    total_customer_revenue = 0
    for i, (customer_id, customer_data) in enumerate(sorted_customers[:10]):  # Top 10
        revenue = customer_data['total']
        total_customer_revenue += revenue
        name = customer_data['name'][:30]  # Truncate long names
        print(f"  {i+1:2d}. {name:<30} £{revenue:>8,.2f} ({customer_data['invoice_count']} invoices)")
    
    print(f"\nTop 10 customers total: £{total_customer_revenue:,.2f}")
    print(f"All customers total: £{invoice_revenue:,.2f}")
    
    # Check for missing revenue
    if total_revenue > invoice_revenue:
        missing_revenue = total_revenue - invoice_revenue
        print(f"\nMissing revenue (likely unbilled): £{missing_revenue:,.2f}")

def debug_unit_economics(data, metrics):
    """Debug unit economics calculations"""
    
    unit_econ = metrics.get('unit_economics', {})
    
    print(f"CAC: £{unit_econ.get('avg_cac', 0):.2f}")
    print(f"LTV: £{unit_econ.get('avg_ltv', 0):.2f}")
    print(f"LTV/CAC Ratio: {unit_econ.get('ltv_cac_ratio', 0):.1f}")
    print(f"Payback Months: {unit_econ.get('payback_months', 0):.1f}")
    print(f"Gross Margin: {unit_econ.get('gross_margin', 0):.1%}")
    print(f"Monthly Churn: {unit_econ.get('monthly_churn', 0):.1%}")
    
    # Debug CAC calculation
    print(f"\nCAC Breakdown:")
    print(f"  Total S&M Spend: £{unit_econ.get('total_sm_spend', 0):.2f}")
    print(f"  New Customers: {unit_econ.get('new_customers', 0)}")
    
    # Debug LTV calculation
    avg_monthly_revenue = unit_econ.get('avg_monthly_revenue', 0)
    print(f"\nLTV Breakdown:")
    print(f"  Avg Monthly Revenue per Customer: £{avg_monthly_revenue:.2f}")
    print(f"  Customer Lifetime (months): {1/unit_econ.get('monthly_churn', 0.01):.1f}")
    
    # Check if calculations make sense
    expected_ltv = avg_monthly_revenue * (1/unit_econ.get('monthly_churn', 0.01)) * unit_econ.get('gross_margin', 1)
    print(f"  Expected LTV: £{expected_ltv:.2f}")
    print(f"  Calculated LTV: £{unit_econ.get('avg_ltv', 0):.2f}")

def debug_churn_calculation(data, metrics):
    """Debug churn calculation logic"""

    # Get churn from unit economics
    monthly_churn = metrics.get('unit_economics', {}).get('monthly_churn', 0)
    annual_churn = 1 - pow(1 - monthly_churn, 12)

    print(f"Monthly Churn Rate: {monthly_churn:.1%}")
    print(f"Annual Churn Rate: {annual_churn:.1%}")

    # Check customer activity data
    invoices_data = data.get('invoices')
    if invoices_data:
        invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])

        # Track customer last payment dates and payment patterns from BOTH invoices AND bank transactions
        customer_last_payment = {}
        customer_payment_history = defaultdict(list)

        # Process invoices
        for invoice in invoices:
            if invoice.get('Status') == 'PAID' and invoice.get('FullyPaidOnDate'):
                contact_id = invoice.get('Contact', {}).get('ContactID', '')
                contact_name = invoice.get('Contact', {}).get('Name', '')
                date_str = invoice.get('FullyPaidOnDate')
                amount = float(invoice.get('Total', 0))

                if date_str and (contact_id or contact_name):
                    try:
                        # Parse Xero date format
                        if '/Date(' in date_str:
                            timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
                            date = datetime.fromtimestamp(timestamp)
                        else:
                            date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))

                        key = contact_id or contact_name
                        customer_payment_history[key].append((date, amount, 'invoice'))

                        if key not in customer_last_payment or date > customer_last_payment[key]:
                            customer_last_payment[key] = date
                    except Exception as e:
                        print(f"Error parsing invoice date {date_str}: {e}")
                        continue

        # Process bank transactions
        bank_data = data.get('bank_transactions', [])
        for txn in bank_data:
            if txn.get('Type') == 'RECEIVE':
                contact = txn.get('Contact', {})
                contact_name = contact.get('Name', '') if contact else ''
                date_str = txn.get('Date', '')
                amount = float(txn.get('Total', 0))

                if date_str and contact_name and amount > 0:
                    try:
                        # Parse Xero date format
                        if '/Date(' in date_str:
                            timestamp = int(date_str.replace('/Date(', '').replace('+0000)/', '').replace(')/', '')) / 1000
                            date = datetime.fromtimestamp(timestamp)
                        else:
                            date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))

                        key = contact_name
                        customer_payment_history[key].append((date, amount, 'bank'))

                        if key not in customer_last_payment or date > customer_last_payment[key]:
                            customer_last_payment[key] = date
                    except Exception as e:
                        print(f"Error parsing bank date {date_str}: {e}")
                        continue

        print(f"\nCustomer Activity Analysis:")
        print(f"Total customers with payment history: {len(customer_last_payment)}")

        # Show detailed customer payment patterns
        print(f"\nDetailed Customer Payment Analysis:")
        print(f"{'Customer':<30} {'Last Payment':<12} {'Days Ago':<10} {'Total Payments':<15} {'Sources':<15} {'Avg Gap (days)':<15}")
        print("-" * 110)

        current_date = datetime.now()
        cutoff_date = datetime.strptime('2025-04-30', '%Y-%m-%d')
        active_threshold = cutoff_date - timedelta(days=3 * 30.44)  # 3 months

        active_customers = 0
        churned_customers = 0

        # Sort by last payment date (most recent first)
        sorted_customers = sorted(customer_last_payment.items(), key=lambda x: x[1], reverse=True)

        for customer, last_payment in sorted_customers:
            days_ago = (current_date - last_payment).days
            payments = customer_payment_history[customer]
            total_amount = sum(amount for _, amount, _ in payments)

            # Count payment sources
            invoice_count = sum(1 for _, _, source in payments if source == 'invoice')
            bank_count = sum(1 for _, _, source in payments if source == 'bank')
            sources = f"I:{invoice_count} B:{bank_count}"

            # Calculate average gap between payments
            if len(payments) > 1:
                sorted_payments = sorted(payments, key=lambda x: x[0])
                gaps = []
                for i in range(1, len(sorted_payments)):
                    gap = (sorted_payments[i][0] - sorted_payments[i-1][0]).days
                    gaps.append(gap)
                avg_gap = sum(gaps) / len(gaps)
            else:
                avg_gap = 0

            # Determine if active or churned
            status = "ACTIVE" if last_payment >= active_threshold else "CHURNED"
            if last_payment >= active_threshold:
                active_customers += 1
            else:
                churned_customers += 1

            customer_name = customer[:25] if len(customer) > 25 else customer
            print(f"{customer_name:<30} {last_payment.strftime('%Y-%m-%d'):<12} {days_ago:<10} £{total_amount:<14.2f} {sources:<15} {avg_gap:<15.1f} {status}")

        print(f"\nSummary:")
        print(f"Active customers (paid within 3 months of 2025-04-30): {active_customers}")
        print(f"Churned customers: {churned_customers}")
        print(f"Calculated churn rate: {churned_customers/(active_customers + churned_customers):.1%}")

        # Check if cutoff date is reasonable
        print(f"\nCutoff Date Analysis:")
        print(f"Configured cutoff date: 2025-04-30")
        print(f"Days from cutoff to now: {(current_date - cutoff_date).days}")
        print(f"Active threshold: {active_threshold.strftime('%Y-%m-%d')}")

        # Suggest alternative cutoff dates
        latest_payment = max(customer_last_payment.values())
        print(f"Latest payment in data: {latest_payment.strftime('%Y-%m-%d')}")

        # Check data freshness
        data_age_days = (current_date - latest_payment).days
        if data_age_days > 90:
            print(f"⚠️  WARNING: Data may be stale - latest payment is {data_age_days} days old")
            print(f"   Consider using latest payment date as cutoff instead of fixed 2025-04-30")

def debug_customer_data(data, metrics):
    """Debug customer data consistency"""
    
    # Count customers from different sources
    revenue_customers = len(metrics.get('revenue', {}).get('revenue_by_customer', {}))
    print(f"Customers from revenue calculation: {revenue_customers}")
    
    # Count from invoices directly
    invoices_data = data.get('invoices')
    if invoices_data:
        invoices = invoices_data if isinstance(invoices_data, list) else invoices_data.get('Invoices', [])
        unique_customers = set()
        for invoice in invoices:
            if invoice.get('Status') in ['PAID', 'AUTHORISED']:
                contact_id = invoice.get('Contact', {}).get('ContactID', '')
                if contact_id:
                    unique_customers.add(contact_id)
        print(f"Unique customers from invoices: {len(unique_customers)}")
    
    # Count from contacts
    contacts_data = data.get('contacts')
    if contacts_data:
        contacts = contacts_data if isinstance(contacts_data, list) else contacts_data.get('Contacts', [])
        print(f"Total contacts in system: {len(contacts)}")

def summarize_issues(metrics):
    """Summarize the key issues found"""
    
    issues = []
    
    # Check LTV/CAC ratio
    ltv_cac = metrics.get('unit_economics', {}).get('ltv_cac_ratio', 0)
    if ltv_cac > 20:
        issues.append(f"LTV/CAC ratio too high: {ltv_cac:.1f} (typical range: 3-10)")
    
    # Check churn rate
    monthly_churn = metrics.get('unit_economics', {}).get('monthly_churn', 0)
    if monthly_churn > 0.10:  # 10% monthly
        issues.append(f"Monthly churn rate very high: {monthly_churn:.1%} (typical SaaS: 2-5%)")
    
    # Check revenue consistency
    pl_revenue = metrics.get('profit_loss', {}).get('total_income', 0)
    total_revenue = metrics.get('revenue', {}).get('total_revenue', 0)
    if abs(pl_revenue - total_revenue) / max(pl_revenue, total_revenue) > 0.05:  # 5% variance
        issues.append(f"Revenue source inconsistency: P&L={pl_revenue:.0f}, System={total_revenue:.0f}")
    
    print("CRITICAL ISSUES TO FIX:")
    for i, issue in enumerate(issues, 1):
        print(f"{i}. {issue}")
    
    if not issues:
        print("No critical calculation issues detected!")

if __name__ == "__main__":
    main()
