# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Authentication tokens
xero_token.json
xero_session.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
venv_investor/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Keep structure but ignore sensitive data
data/*
!data/.gitkeep
!data/README.md

# Deprecated scripts (archived)
analysis_archive/

# Intermediate analysis outputs
*_analysis.json
*_metrics.json
*_models.json
*_results.json
validation_report.json

# Old reports
reports/archive/

# Log files
*.log
financial_analysis.log

# Security audit outputs
security_audit_report.json