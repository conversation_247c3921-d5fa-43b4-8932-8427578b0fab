# Investor Report Sandbox - Clean Structure

## Directory Organization

```
investor_report_sandbox/
├── analysis_unified/           # Core calculation engine (100% data-driven)
│   ├── analyzers/             # Specialized analysis modules
│   ├── config/                # Configuration files (business rules)
│   │   └── calculation_config.yaml  # Externalized parameters
│   ├── core/                  # Core calculations and data processing
│   │   ├── calculations.py    # Single source of truth (no defaults)
│   │   ├── data_loader.py     # JSON file loader
│   │   ├── validator.py       # Multi-tier validation
│   │   └── visualizations.py  # 10 comprehensive charts
│   ├── reports/               # PDF report generation
│   │   └── pdf_generator.py   # Enhanced with historical analysis
│   ├── __init__.py
│   ├── financial_analyzer.py  # Main orchestrator
│   ├── notebook_data_bridge.py # Enhanced with cohort/retention analysis
│   └── README.md
│
├── archive/                   # Archived files for reference
│   └── validation/           # Old validation scripts
│
├── data/                     # Xero JSON data files (gitignored)
│   ├── accounts_latest.json
│   ├── balance_sheet_latest.json
│   ├── bank_transactions_latest.json
│   ├── contacts_latest.json
│   ├── invoices_latest.json
│   ├── organization_latest.json
│   ├── profit_loss_latest.json
│   ├── trial_balance_latest.json
│   └── README.md
│
├── docs/                     # All documentation
│   ├── architecture/         # System design documentation
│   │   ├── CALCULATION_METHODOLOGY.md
│   │   └── DATA_FLOW_DOCUMENTATION.md
│   ├── implementation/       # Implementation details
│   │   ├── IMPLEMENTATION_SUMMARY.md
│   │   └── REQUIRED_FIXES.md
│   ├── validation/          # Validation and audit trails
│   │   ├── calculation_audit.md
│   │   └── VALIDATION_EXECUTIVE_SUMMARY.md
│   ├── MODULAR_CX_VALUATION_STRATEGY.md
│   ├── VALUATION_EXECUTIVE_BRIEF.md
│   └── VALUATION_SCENARIO_MODEL.md
│
├── notebooks/               # Jupyter notebooks
│   ├── modular_cx_investor_analysis_consolidated.ipynb
│   ├── modular_cx_valuation_analysis.ipynb
│   └── README.md
│
├── output/                  # All generated outputs
│   ├── graphs/             # Visualization PNG files (10 charts)
│   │   ├── revenue_forecast.png
│   │   ├── expense_breakdown.png
│   │   ├── cash_flow_waterfall.png
│   │   ├── unit_economics_dashboard.png
│   │   ├── customer_concentration.png
│   │   ├── cash_runway_analysis.png
│   │   ├── scenario_comparison.png
│   │   ├── cash_efficiency_dashboard.png
│   │   ├── financial_health_dashboard.png  # NEW
│   │   └── margin_benchmarks.png           # NEW
│   ├── reports/            # PDF reports (single source of truth)
│   │   ├── modular_cx_financial_report.pdf
│   │   └── archive_2025_07_28/  # Archived hardcoded reports
│   │       ├── executive_summary_one_pager.md
│   │       ├── financial_reports_2020_2025.md
│   │       └── company_narrative_2020_2025.md
│   └── financial_analysis.json
│
├── scripts/                # Utility scripts
│   ├── security_audit.py
│   ├── xero_auth_step1.py
│   ├── xero_auth_step2.py
│   ├── xero_fetch_data.py
│   └── README.md
│
├── tests/                  # Test files
│   ├── test_balance_sheet.py
│   ├── test_calculations.py
│   └── test_financial_calculations.py
│
├── .claude/               # Claude configuration
│   └── settings.local.json
│
├── analyze.py             # Main CLI entry point
├── CLAUDE.md             # Claude-specific instructions
├── financial_analysis.log # Analysis log file
├── README.md             # Main documentation
├── requirements.txt      # Python dependencies
└── xero_token.json      # Xero authentication (gitignored)
```

## Key Principles (Updated 2025-07-28)

1. **Single Source of Truth**: 
   - All calculations in `analysis_unified/core/calculations.py`
   - PDF report is the only authoritative output
   - Hardcoded markdown reports archived
   
2. **100% Data-Driven**: 
   - No hardcoded values or silent defaults
   - System raises errors when data insufficient
   - Dynamic time period calculation from actual dates
   - Minimum requirements: 3 months data, 5+ customers
   
3. **Historical Analysis**: 
   - Bank transaction revenue for complete 2020-2025 picture
   - Cohort analysis and retention curves
   - Quarterly and annual aggregations
   - Unit economics evolution tracking
   
4. **Enhanced Integration**: 
   - Jupyter notebooks use enhanced data bridge
   - New methods for historical analysis
   - No duplicate calculations anywhere
   
5. **Professional Documentation**: 
   - All docs categorized by type in `docs/`
   - Comprehensive methodology documentation
   - Validation and audit trails
   
6. **Consolidated Output**: 
   - All results go to `output/` directory
   - PDF report with 12 comprehensive sections
   - 10 professional visualizations
   - Archived misleading reports

## File Count Summary

- Python files: 22
- Markdown docs: 14
- Jupyter notebooks: 2
- JSON data files: 8
- Configuration: 2
- Total active files: ~48 (excluding virtual environment)

## Recent Updates (2025-07-28)

### Data Integrity Enhancements
- ✅ Removed all silent defaults - system raises errors
- ✅ Dynamic time period calculation from actual data
- ✅ Configuration-based business rules (calculation_config.yaml)
- ✅ Pre-calculation validation ensures data sufficiency

### Report Consolidation
- ✅ PDF generator is now single source of truth
- ✅ Archived 3 misleading hardcoded markdown reports
- ✅ Added 5 new sections for historical analysis
- ✅ Dynamic executive summary based on actual metrics
- ✅ New `--investor-report` command for CFO-level reports

### Enhanced Capabilities
- ✅ Historical revenue timeline (monthly/quarterly/yearly)
- ✅ Customer cohort analysis with retention curves
- ✅ Unit economics evolution tracking
- ✅ Financial health dashboard visualization
- ✅ Industry margin benchmark comparisons

## Clean Structure Benefits

- ✅ No duplicate files or directories
- ✅ Clear separation of concerns
- ✅ Easy to navigate and understand
- ✅ All temporary files archived
- ✅ Consistent naming conventions
- ✅ Professional organization suitable for investor review
- ✅ 100% data-driven calculations
- ✅ Single authoritative report source