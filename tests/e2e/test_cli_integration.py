#!/usr/bin/env python3
"""
Test script for CLI integration with real Xero data.
This script tests all major CLI commands to ensure they work with actual data.
"""

import sys
import subprocess
import json
import logging
from datetime import datetime
from typing import Any, Union, List, Dict, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_cli_command(cmd_args: List[str], expect_success: bool = True) -> Optional[Dict[str, Any]]:
    """Run a CLI command and return the result."""
    try:
        full_cmd = ["python", "-m", "mcx3d_finance.cli.main"] + cmd_args
        logger.info(f"Running: {' '.join(full_cmd)}")
        
        result = subprocess.run(
            full_cmd,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        if expect_success and result.returncode != 0:
            logger.error(f"Command failed: {result.stderr}")
            return None
        
        return {
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
    except subprocess.TimeoutExpired:
        logger.error("Command timed out")
        return None
    except Exception as e:
        logger.error(f"Error running command: {e}")
        return None


def test_saas_analytics(org_id: int = 1) -> dict[str, bool]:
    """Test SaaS analytics commands."""
    logger.info("=== Testing SaaS Analytics ===")
    
    tests = [
        {
            'name': 'SaaS Metrics Summary',
            'cmd': ['analytics', 'saas-metrics', '--organization-id', str(org_id), '--output', 'summary'],
        },
        {
            'name': 'SaaS Metrics JSON',
            'cmd': ['analytics', 'saas-metrics', '--organization-id', str(org_id), '--output', 'json'],
        },
        {
            'name': 'Growth Analysis',
            'cmd': ['analytics', 'growth-analysis', '--organization-id', str(org_id)],
        },
        {
            'name': 'Health Score',
            'cmd': ['analytics', 'health-score', '--organization-id', str(org_id)],
        }
    ]
    
    results = {}
    for test in tests:
        logger.info(f"Testing: {test['name']}")
        result = run_cli_command(test['cmd'])
        results[test['name']] = result is not None
        
        if result and result['returncode'] == 0:
            logger.info(f"✅ {test['name']} - SUCCESS")
        else:
            logger.error(f"❌ {test['name']} - FAILED")
            if result:
                logger.error(f"Error output: {result['stderr']}")
    
    return results


def test_dcf_valuation(org_id: int = 1) -> dict[str, bool]:
    """Test DCF valuation commands."""
    logger.info("=== Testing DCF Valuation ===")
    
    tests = [
        {
            'name': 'Basic DCF',
            'cmd': ['valuate', 'dcf', '--organization-id', str(org_id), '--output', 'summary'],
        },
        {
            'name': 'DCF with Custom Scenarios',
            'cmd': ['valuate', 'dcf', '--organization-id', str(org_id), '--scenarios', 'base', '--scenarios', 'upside'],
        },
        {
            'name': 'DCF JSON Output',
            'cmd': ['valuate', 'dcf', '--organization-id', str(org_id), '--output', 'json'],
        },
    ]
    
    results = {}
    for test in tests:
        logger.info(f"Testing: {test['name']}")
        result = run_cli_command(test['cmd'])
        results[test['name']] = result is not None
        
        if result and result['returncode'] == 0:
            logger.info(f"✅ {test['name']} - SUCCESS")
        else:
            logger.error(f"❌ {test['name']} - FAILED")
            if result:
                logger.error(f"Error output: {result['stderr']}")
    
    return results


def test_multiples_valuation(org_id: int = 1) -> dict[str, bool]:
    """Test multiples valuation commands."""
    logger.info("=== Testing Multiples Valuation ===")
    
    tests = [
        {
            'name': 'Basic Multiples',
            'cmd': ['valuate', 'multiples', '--organization-id', str(org_id), '--output', 'summary'],
        },
        {
            'name': 'Multiples JSON Output',
            'cmd': ['valuate', 'multiples', '--organization-id', str(org_id), '--output', 'json'],
        },
    ]
    
    results = {}
    for test in tests:
        logger.info(f"Testing: {test['name']}")
        result = run_cli_command(test['cmd'])
        results[test['name']] = result is not None
        
        if result and result['returncode'] == 0:
            logger.info(f"✅ {test['name']} - SUCCESS")
        else:
            logger.error(f"❌ {test['name']} - FAILED")
            if result:
                logger.error(f"Error output: {result['stderr']}")
    
    return results


def test_saas_valuation(org_id: int = 1) -> dict[str, bool]:
    """Test SaaS-specific valuation commands."""
    logger.info("=== Testing SaaS Valuation ===")
    
    tests = [
        {
            'name': 'Basic SaaS Valuation',
            'cmd': ['valuate', 'saas', '--organization-id', str(org_id), '--output', 'summary'],
        },
        {
            'name': 'SaaS Valuation with Custom Multiple',
            'cmd': ['valuate', 'saas', '--organization-id', str(org_id), '--arr-multiple', '10.0'],
        },
    ]
    
    results = {}
    for test in tests:
        logger.info(f"Testing: {test['name']}")
        result = run_cli_command(test['cmd'])
        results[test['name']] = result is not None
        
        if result and result['returncode'] == 0:
            logger.info(f"✅ {test['name']} - SUCCESS")
        else:
            logger.error(f"❌ {test['name']} - FAILED")
            if result:
                logger.error(f"Error output: {result['stderr']}")
    
    return results


def test_report_generation(org_id: int = 1) -> dict[str, bool]:
    """Test financial report generation."""
    logger.info("=== Testing Report Generation ===")
    
    tests = [
        {
            'name': 'Income Statement',
            'cmd': ['generate', 'income-statement', '--organization-id', str(org_id), '--period', '2024', '--format', 'json'],
            'expect_success': False  # May not have 2024 data
        },
        {
            'name': 'Balance Sheet',
            'cmd': ['generate', 'balance-sheet', '--organization-id', str(org_id), '--date', '2024-12-31', '--format', 'json'],
            'expect_success': False  # May not have current data
        },
    ]
    
    results = {}
    for test in tests:
        logger.info(f"Testing: {test['name']}")
        result = run_cli_command(test['cmd'], expect_success=test.get('expect_success', True))
        
        # For reports, we expect them to either succeed or fail gracefully
        success = result is not None and (result['returncode'] == 0 or 'Error' in result['stdout'])
        results[test['name']] = success
        
        if success:
            logger.info(f"✅ {test['name']} - SUCCESS (or graceful failure)")
        else:
            logger.error(f"❌ {test['name']} - FAILED")
    
    return results


def main() -> int:
    """Run all CLI integration tests."""
    logger.info("Starting CLI Integration Tests")
    logger.info(f"Test started at: {datetime.now()}")
    
    # Test with organization ID 1 (assuming it exists in test data)
    org_id = 1
    
    all_results = {}
    
    try:
        # Test all major CLI components
        all_results['SaaS Analytics'] = test_saas_analytics(org_id)
        all_results['DCF Valuation'] = test_dcf_valuation(org_id)  
        all_results['Multiples Valuation'] = test_multiples_valuation(org_id)
        all_results['SaaS Valuation'] = test_saas_valuation(org_id)
        all_results['Report Generation'] = test_report_generation(org_id)
        
    except Exception as e:
        logger.error(f"Error during testing: {e}")
        return 1
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    for category, tests in all_results.items():
        logger.info(f"\n{category}:")
        for test_name, passed in tests.items():
            status = "PASS" if passed else "FAIL"
            logger.info(f"  {test_name}: {status}")
            total_tests += 1
            if passed:
                passed_tests += 1
    
    logger.info(f"\nOVERALL: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED!")
        return 0
    else:
        logger.warning(f"⚠️  {total_tests - passed_tests} tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())