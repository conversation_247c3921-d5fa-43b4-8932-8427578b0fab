#!/usr/bin/env python3
"""
Quick CLI test to validate command structure and error handling.
This tests the CLI without requiring a full database setup.
"""

import subprocess
import sys
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)


def test_cli_help_commands() -> tuple[int, int]:
    """Test that all CLI help commands work."""
    print("🧪 Testing CLI Help Commands...")
    
    commands_to_test: List[List[str]] = [
        ["python", "-m", "mcx3d_finance.cli.main", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "analytics", "--help"],  
        ["python", "-m", "mcx3d_finance.cli.main", "valuate", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "generate", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "sync", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "analytics", "saas-metrics", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "analytics", "growth-analysis", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "analytics", "health-score", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "valuate", "dcf", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "valuate", "multiples", "--help"],
        ["python", "-m", "mcx3d_finance.cli.main", "valuate", "saas", "--help"],
    ]
    
    passed = 0
    total = len(commands_to_test)
    
    for cmd in commands_to_test:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            cmd_name = " ".join(cmd[3:])  # Skip "python -m mcx3d_finance.cli.main"
            
            if result.returncode == 0 and "Usage:" in result.stdout:
                print(f"✅ {cmd_name}")
                passed += 1
            else:
                print(f"❌ {cmd_name}")
                print(f"   Error: {result.stderr}")
                
        except Exception as e:
            print(f"❌ {' '.join(cmd[3:])}")
            print(f"   Exception: {e}")
    
    return passed, total


def test_cli_error_handling() -> tuple[float, int]:
    """Test that CLI commands handle missing arguments gracefully."""
    print("\n🧪 Testing CLI Error Handling...")
    
    commands_to_test: List[Dict[str, Any]] = [
        {
            'cmd': ["python", "-m", "mcx3d_finance.cli.main", "analytics", "saas-metrics"],
            'name': "saas-metrics (missing org-id)",
            'expect_error': True
        },
        {
            'cmd': ["python", "-m", "mcx3d_finance.cli.main", "valuate", "dcf"],
            'name': "dcf (missing org-id)", 
            'expect_error': True
        },
        {
            'cmd': ["python", "-m", "mcx3d_finance.cli.main", "valuate", "saas", "--organization-id", "999"],
            'name': "saas valuation (no DB connection)",
            'expect_error': True  # Should fail gracefully due to DB connection
        },
    ]
    
    passed = 0
    total = len(commands_to_test)
    
    for test in commands_to_test:
        try:
            result = subprocess.run(test['cmd'], capture_output=True, text=True, timeout=60)
            
            if test['expect_error']:
                if result.returncode != 0:
                    # Check that it's a meaningful error, not a crash
                    if "Error:" in result.stdout or "Usage:" in result.stdout or "Missing option" in result.stderr:
                        print(f"✅ {test['name']} - Graceful error handling")
                        passed += 1
                    else:
                        print(f"⚠️  {test['name']} - Error but not graceful")
                        print(f"   Output: {result.stdout[:200]}...")
                        print(f"   Error: {result.stderr[:200]}...")
                        passed += 0.5  # Partial credit
                else:
                    print(f"❌ {test['name']} - Should have failed but didn't")
            else:
                if result.returncode == 0:
                    print(f"✅ {test['name']}")
                    passed += 1
                else:
                    print(f"❌ {test['name']}")
                    print(f"   Error: {result.stderr}")
                    
        except Exception as e:
            print(f"❌ {test['name']}")
            print(f"   Exception: {e}")
    
    return passed, total


def main() -> int:
    """Run quick CLI tests."""
    print("🚀 MCX3D Finance CLI Quick Test")
    print("=" * 50)
    
    total_passed = 0
    total_tests = 0
    
    # Test 1: Help commands
    passed, total = test_cli_help_commands()
    total_passed += passed
    total_tests += total
    print(f"\n📊 Help Commands: {passed}/{total} passed")
    
    # Test 2: Error handling  
    passed, total = test_cli_error_handling()
    total_passed += passed
    total_tests += total
    print(f"📊 Error Handling: {passed}/{total} passed")
    
    # Summary
    print(f"\n" + "=" * 50)
    print(f"📈 OVERALL RESULTS: {total_passed}/{total_tests} passed ({total_passed/total_tests*100:.1f}%)")
    
    if total_passed >= total_tests * 0.8:  # 80% pass rate
        print("🎉 CLI structure and error handling look good!")
        return 0
    else:
        print("⚠️  Some issues detected in CLI structure")
        return 1


if __name__ == "__main__":
    sys.exit(main())