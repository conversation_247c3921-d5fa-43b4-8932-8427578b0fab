"""
Test script for API authentication issues.
Helps diagnose 404 errors and authentication problems.
"""

import requests
import json
import sys
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class APIAuthTester:
    """Test API authentication and endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000") -> None:
        self.base_url = base_url
        self.token = None
        self.headers = {}
    
    def test_health_check(self) -> bool:
        """Test if API is running."""
        logger.info("Testing health check endpoint...")
        try:
            response = requests.get(f"{self.base_url}/health")
            logger.info(f"Health check status: {response.status_code}")
            if response.status_code == 200:
                logger.info(f"Health check response: {response.json()}")
                return True
            else:
                logger.error(f"Health check failed: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Cannot connect to API: {e}")
            return False
    
    def test_login(self, email: str = "<EMAIL>", password: str = "testpass123") -> bool:
        """Test login endpoint."""
        logger.info(f"Testing login for user: {email}")
        try:
            response = requests.post(
                f"{self.base_url}/auth/login",
                json={"email": email, "password": password}
            )
            logger.info(f"Login status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.headers = {"Authorization": f"Bearer {self.token}"}
                logger.info("Login successful, token received")
                return True
            else:
                logger.error(f"Login failed: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Login error: {e}")
            return False
    
    def test_auth_endpoint(self) -> bool:
        """Test the authentication test endpoint."""
        logger.info("Testing authentication endpoint...")
        
        if not self.token:
            logger.error("No token available, please login first")
            return False
        
        try:
            response = requests.get(
                f"{self.base_url}/api/test-auth",
                headers=self.headers
            )
            logger.info(f"Auth test status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"Auth test response: {response.json()}")
                return True
            elif response.status_code == 401:
                logger.error("Authentication failed - invalid token")
                return False
            elif response.status_code == 404:
                logger.error("Endpoint not found - check API routing")
                return False
            else:
                logger.error(f"Auth test failed: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Auth test error: {e}")
            return False
    
    def test_report_endpoints(self, organization_id: int = 1) -> None:
        """Test all report endpoints."""
        if not self.token:
            logger.error("No token available, please login first")
            return
        
        # Test endpoints
        endpoints = [
            {
                "method": "GET",
                "path": f"/api/reports/income-statement?organization_id={organization_id}&start_date=2023-01-01&end_date=2023-12-31",
                "name": "Income Statement"
            },
            {
                "method": "GET",
                "path": f"/api/reports/balance-sheet?organization_id={organization_id}&date=2023-12-31",
                "name": "Balance Sheet"
            },
            {
                "method": "GET",
                "path": f"/api/reports/cash-flow?organization_id={organization_id}&start_date=2023-01-01&end_date=2023-12-31",
                "name": "Cash Flow"
            }
        ]
        
        for endpoint in endpoints:
            logger.info(f"\nTesting {endpoint['name']} endpoint...")
            try:
                if endpoint["method"] == "GET":
                    response = requests.get(
                        f"{self.base_url}{endpoint['path']}",
                        headers=self.headers
                    )
                else:
                    response = requests.post(
                        f"{self.base_url}{endpoint['path']}",
                        headers=self.headers,
                        json=endpoint.get("data", {})
                    )
                
                logger.info(f"{endpoint['name']} status: {response.status_code}")
                
                if response.status_code == 200:
                    logger.info(f"{endpoint['name']} successful")
                    # Don't log full response as it might be large
                    data = response.json()
                    if isinstance(data, dict):
                        logger.info(f"Response keys: {list(data.keys())}")
                elif response.status_code == 401:
                    logger.error(f"{endpoint['name']} - Authentication failed")
                elif response.status_code == 403:
                    logger.error(f"{endpoint['name']} - Access denied (check organization access)")
                elif response.status_code == 404:
                    logger.error(f"{endpoint['name']} - Endpoint not found")
                    logger.error(f"Full URL: {self.base_url}{endpoint['path']}")
                else:
                    logger.error(f"{endpoint['name']} failed: {response.text}")
                    
            except Exception as e:
                logger.error(f"{endpoint['name']} error: {e}")
    
    def test_all_routes(self) -> None:
        """Get all available routes from the API."""
        logger.info("\nFetching all available routes...")
        try:
            response = requests.get(f"{self.base_url}/openapi.json")
            if response.status_code == 200:
                openapi = response.json()
                paths = openapi.get("paths", {})
                logger.info(f"\nAvailable API routes:")
                for path in sorted(paths.keys()):
                    methods = list(paths[path].keys())
                    logger.info(f"  {path} - Methods: {', '.join(methods).upper()}")
            else:
                logger.error(f"Could not fetch OpenAPI spec: {response.status_code}")
        except Exception as e:
            logger.error(f"Error fetching routes: {e}")
    
    def run_all_tests(self) -> None:
        """Run all authentication and API tests."""
        logger.info("=== Starting API Authentication Tests ===\n")
        
        # 1. Health check
        if not self.test_health_check():
            logger.error("API is not running. Please start the API server.")
            return
        
        # 2. Show all routes
        self.test_all_routes()
        
        # 3. Test login
        if not self.test_login():
            logger.error("Login failed. Check credentials or create test user.")
            logger.info("\nTo create a test user, run:")
            logger.info("mcx3d user create --email <EMAIL> --password testpass123")
            return
        
        # 4. Test authentication
        if not self.test_auth_endpoint():
            logger.error("Authentication test failed.")
            return
        
        # 5. Test report endpoints
        self.test_report_endpoints()
        
        logger.info("\n=== API Authentication Tests Complete ===")


def main() -> None:
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test API authentication")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--email", default="<EMAIL>", help="Test user email")
    parser.add_argument("--password", default="testpass123", help="Test user password")
    parser.add_argument("--org-id", type=int, default=1, help="Organization ID to test")
    
    args = parser.parse_args()
    
    tester = APIAuthTester(args.url)
    
    # Override credentials if provided
    if args.email != "<EMAIL>" or args.password != "testpass123":
        tester.test_login(args.email, args.password)
        tester.test_report_endpoints(args.org_id)
    else:
        tester.run_all_tests()


if __name__ == "__main__":
    main()