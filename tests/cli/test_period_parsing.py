"""
Tests for CLI period parsing functionality.
"""
import pytest
from datetime import datetime
from mcx3d_finance.reporting.utils.period_parser import PeriodParser
from mcx3d_finance.exceptions.custom import ReportValidationError


class TestParsePeriod:
    """Test the parse_period function with various period formats."""

    def setup_method(self) -> None:
        self.parser = PeriodParser()

    def test_quarterly_periods(self) -> None:
        """Test quarterly period parsing."""
        # Q1: Jan-Mar
        start, end = self.parser.parse_period("2024-Q1")
        assert start == datetime(2024, 1, 1)
        assert end == datetime(2024, 3, 31)
        
        # Q2: Apr-Jun
        start, end = self.parser.parse_period("2024-Q2")
        assert start == datetime(2024, 4, 1)
        assert end == datetime(2024, 6, 30)
        
        # Q3: Jul-Sep
        start, end = self.parser.parse_period("2024-Q3")
        assert start == datetime(2024, 7, 1)
        assert end == datetime(2024, 9, 30)
        
        # Q4: Oct-Dec
        start, end = self.parser.parse_period("2024-Q4")
        assert start == datetime(2024, 10, 1)
        assert end == datetime(2024, 12, 31)

    def test_monthly_periods(self) -> None:
        """Test monthly period parsing."""
        # January
        start, end = self.parser.parse_period("2024-01")
        assert start == datetime(2024, 1, 1)
        assert end == datetime(2024, 1, 31)
        
        # February (leap year)
        start, end = self.parser.parse_period("2024-02")
        assert start == datetime(2024, 2, 1)
        assert end == datetime(2024, 2, 29)
        
        # February (non-leap year)
        start, end = self.parser.parse_period("2023-02")
        assert start == datetime(2023, 2, 1)
        assert end == datetime(2023, 2, 28)
        
        # April (30 days)
        start, end = self.parser.parse_period("2024-04")
        assert start == datetime(2024, 4, 1)
        assert end == datetime(2024, 4, 30)
        
        # December
        start, end = self.parser.parse_period("2024-12")
        assert start == datetime(2024, 12, 1)
        assert end == datetime(2024, 12, 31)

    def test_yearly_periods(self) -> None:
        """Test yearly period parsing."""
        start, end = self.parser.parse_period("2024")
        assert start == datetime(2024, 1, 1)
        assert end == datetime(2024, 12, 31)
        
        start, end = self.parser.parse_period("2023")
        assert start == datetime(2023, 1, 1)
        assert end == datetime(2023, 12, 31)

    def test_invalid_periods(self) -> None:
        """Test error handling for invalid period formats."""
        with pytest.raises(ReportValidationError):
            self.parser.parse_period("2024-Q5")
        
        with pytest.raises(ReportValidationError):
            self.parser.parse_period("2024-13")
        
        with pytest.raises(ReportValidationError):
            self.parser.parse_period("2024-00")
        
        with pytest.raises(ReportValidationError):
            self.parser.parse_period("invalid")
        
        with pytest.raises(ReportValidationError):
            self.parser.parse_period("24")

    def test_edge_cases(self) -> None:
        """Test edge cases in period parsing."""
        # Valid ranges
        start, end = self.parser.parse_period("2000-Q1")
        assert start == datetime(2000, 1, 1)
        
        start, end = self.parser.parse_period("2099-Q4")
        assert end == datetime(2099, 12, 31)
        
        # Different year formats should fail
        with pytest.raises(ReportValidationError):
            self.parser.parse_period("24-Q1")


class TestParseDate:
    """Test the parse_date function for balance sheet dates."""

    def setup_method(self) -> None:
        self.parser = PeriodParser()

    def test_valid_dates(self) -> None:
        """Test valid date parsing."""
        parsed_date = self.parser.parse_date("2024-03-31")
        assert parsed_date == datetime(2024, 3, 31)
        
        parsed_date = self.parser.parse_date("2024-12-31")
        assert parsed_date == datetime(2024, 12, 31)

    def test_invalid_dates(self) -> None:
        """Test error handling for invalid dates."""
        with pytest.raises(ReportValidationError):
            self.parser.parse_date("2024-3-31")
        
        with pytest.raises(ReportValidationError):
            self.parser.parse_date("2024-02-30")
        
        with pytest.raises(ReportValidationError):
            self.parser.parse_date("2024-13-01")
        
        with pytest.raises(ReportValidationError):
            self.parser.parse_date("03/31/2024")


class TestIntegration:
    """Integration tests for period parsing."""

    def setup_method(self) -> None:
        self.parser = PeriodParser()

    def test_period_consistency(self) -> None:
        """Test that periods are parsed consistently."""
        # Ensure quarterly and monthly parsing don't overlap incorrectly
        q1_start, q1_end = self.parser.parse_period("2024-Q1")
        jan_start, jan_end = self.parser.parse_period("2024-01")
        mar_start, mar_end = self.parser.parse_period("2024-03")
        
        assert q1_start == jan_start  # Q1 starts same as January
        assert q1_end == mar_end      # Q1 ends same as March
        
        # Ensure yearly covers all months
        year_start, year_end = self.parser.parse_period("2024")
        jan_start, _ = self.parser.parse_period("2024-01")
        _, dec_end = self.parser.parse_period("2024-12")
        
        assert year_start == jan_start
        assert year_end == dec_end

    def test_leap_year_handling(self) -> None:
        """Test that leap years are handled correctly."""
        # Leap year
        _, feb_end_2024 = self.parser.parse_period("2024-02")
        assert feb_end_2024 == datetime(2024, 2, 29)
        
        # Non-leap year
        _, feb_end_2023 = self.parser.parse_period("2023-02")
        assert feb_end_2023 == datetime(2023, 2, 28)
        
        # Century year (non-leap)
        _, feb_end_1900 = self.parser.parse_period("1900-02")
        assert feb_end_1900 == datetime(1900, 2, 28)
        
        # Century year (leap)
        _, feb_end_2000 = self.parser.parse_period("2000-02")
        assert feb_end_2000 == datetime(2000, 2, 29)