# tests/integration/test_report_generation.py

import os
import tempfile
import shutil
import pytest
import threading
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from decimal import Decimal
from unittest.mock import Mock, patch

from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.saas_valuation import SaaSValuation


@pytest.mark.integration
@pytest.mark.report_generation
class TestReportGeneration:
    """
    Integration tests for comprehensive report generation functionality.
    Tests the complete pipeline from data input to file output.
    """
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create a temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp(prefix="mcx3d_test_reports_")
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def report_generator(self):
        """Create a ReportGenerator instance for testing."""
        return ReportGenerator()
    
    @pytest.fixture
    def sample_dcf_data(self):
        """Comprehensive DCF test data with realistic financial projections."""
        return {
            "company_name": "TechCorp Inc.",
            "valuation_date": "2024-01-01",
            "currency": "USD",
            "projections": {
                "revenue": [
                    Decimal("2500000"),   # Year 1: $2.5M
                    Decimal("3125000"),   # Year 2: $3.125M (25% growth)
                    Decimal("3750000"),   # Year 3: $3.75M (20% growth)
                    Decimal("4350000"),   # Year 4: $4.35M (16% growth)
                    Decimal("4958000")    # Year 5: $4.958M (14% growth)
                ],
                "operating_expenses": [
                    Decimal("1750000"),   # 70% of revenue
                    Decimal("2031250"),   # 65% of revenue
                    Decimal("2250000"),   # 60% of revenue
                    Decimal("2392500"),   # 55% of revenue
                    Decimal("2479000")    # 50% of revenue
                ],
                "capex": [
                    Decimal("125000"),    # 5% of revenue
                    Decimal("156250"),    # 5% of revenue
                    Decimal("187500"),    # 5% of revenue
                    Decimal("217500"),    # 5% of revenue
                    Decimal("247900")     # 5% of revenue
                ],
                "depreciation": [
                    Decimal("100000"),
                    Decimal("120000"),
                    Decimal("140000"),
                    Decimal("160000"),
                    Decimal("180000")
                ],
                "working_capital_changes": [
                    Decimal("50000"),
                    Decimal("62500"),
                    Decimal("75000"),
                    Decimal("87000"),
                    Decimal("99160")
                ]
            },
            "assumptions": {
                "discount_rate": Decimal("0.12"),      # 12% WACC
                "terminal_growth": Decimal("0.025"),   # 2.5% long-term growth
                "tax_rate": Decimal("0.21"),           # 21% corporate tax
                "years": 5
            },
            "market_data": {
                "risk_free_rate": Decimal("0.035"),
                "market_risk_premium": Decimal("0.065"),
                "beta": Decimal("1.3"),
                "debt_to_equity": Decimal("0.25")
            }
        }
    
    @pytest.fixture
    def sample_saas_data(self):
        """Comprehensive SaaS test data with industry metrics."""
        return {
            "company_name": "CloudSoft SaaS",
            "valuation_date": "2024-01-01",
            "currency": "USD",
            "metrics": {
                "arr": [
                    Decimal("1200000"),   # $1.2M ARR
                    Decimal("1800000"),   # $1.8M ARR (50% growth)
                    Decimal("2520000"),   # $2.52M ARR (40% growth)
                    Decimal("3276000"),   # $3.276M ARR (30% growth)
                    Decimal("4095000")    # $4.095M ARR (25% growth)
                ],
                "monthly_churn_rate": [0.05, 0.04, 0.035, 0.03, 0.025],
                "customer_acquisition_cost": Decimal("180"),
                "average_revenue_per_user": Decimal("450"),
                "gross_margin": Decimal("0.85"),
                "customer_lifetime_value": Decimal("2250"),
                "net_revenue_retention": Decimal("1.15"),
                "magic_number": Decimal("1.2")
            },
            "assumptions": {
                "discount_rate": Decimal("0.15"),      # 15% for SaaS
                "terminal_multiple": Decimal("8.0"),   # 8x ARR terminal
                "growth_efficiency": Decimal("0.7")
            },
            "benchmarks": {
                "revenue_multiple_range": (6, 12),
                "growth_rate_median": Decimal("0.35"),
                "churn_rate_benchmark": Decimal("0.04"),
                "cac_payback_benchmark": 12  # months
            }
        }

    @pytest.mark.pdf
    def test_dcf_pdf_generation_success(self, report_generator, sample_dcf_data, temp_output_dir):
        """Test successful DCF PDF creation with comprehensive validation."""
        # Setup
        output_path = Path(temp_output_dir) / "dcf_valuation.pdf"
        
        # Create DCF valuation instance
        dcf_valuation = DCFValuation(
            projections=sample_dcf_data["projections"],
            assumptions=sample_dcf_data["assumptions"]
        )
        dcf_result = dcf_valuation.calculate()
        
        # Generate PDF report
        result_path = report_generator.generate_dcf_valuation_pdf(
            dcf_result=dcf_result,
            company_data=sample_dcf_data,
            output_path=str(output_path)
        )
        
        # Assertions
        assert os.path.exists(result_path), "PDF file was not created"
        assert os.path.getsize(result_path) > 10000, "PDF file is too small (likely corrupted)"
        assert result_path == str(output_path), "Returned path doesn't match expected path"
        
        # Validate file is actually a PDF (magic bytes)
        with open(result_path, 'rb') as f:
            header = f.read(4)
            assert header == b'%PDF', "Generated file is not a valid PDF"

    @pytest.mark.excel
    def test_dcf_excel_generation_success(self, report_generator, sample_dcf_data, temp_output_dir):
        """Test Excel workbook creation and validate sheet structure."""
        # Setup
        output_path = Path(temp_output_dir) / "dcf_valuation.xlsx"
        
        # Create DCF valuation instance
        dcf_valuation = DCFValuation(
            projections=sample_dcf_data["projections"],
            assumptions=sample_dcf_data["assumptions"]
        )
        dcf_result = dcf_valuation.calculate()
        
        # Generate Excel report
        result_path = report_generator.generate_dcf_valuation_excel(
            dcf_result=dcf_result,
            company_data=sample_dcf_data,
            output_path=str(output_path)
        )
        
        # Assertions
        assert os.path.exists(result_path), "Excel file was not created"
        assert os.path.getsize(result_path) > 5000, "Excel file is too small"
        
        # Validate Excel structure using openpyxl
        from openpyxl import load_workbook
        wb = load_workbook(result_path)
        
        # Expected sheets for DCF report
        expected_sheets = [
            "Executive Summary",
            "Financial Projections", 
            "DCF Calculation",
            "Sensitivity Analysis",
            "Assumptions"
        ]
        
        for sheet_name in expected_sheets:
            assert sheet_name in wb.sheetnames, f"Missing expected sheet: {sheet_name}"
        
        # Validate that sheets have content
        exec_summary = wb["Executive Summary"]
        assert exec_summary.max_row > 1, "Executive Summary sheet is empty"
        assert exec_summary.max_column > 1, "Executive Summary sheet has no columns"

    @pytest.mark.integration
    def test_saas_valuation_generation(self, report_generator, sample_saas_data, temp_output_dir):
        """Test SaaS valuation report generation in both formats."""
        # Setup paths
        pdf_path = Path(temp_output_dir) / "saas_valuation.pdf"
        excel_path = Path(temp_output_dir) / "saas_valuation.xlsx"
        
        # Create SaaS valuation instance
        saas_valuation = SaaSValuation(
            metrics=sample_saas_data["metrics"],
            assumptions=sample_saas_data["assumptions"]
        )
        saas_result = saas_valuation.calculate()
        
        # Generate both formats
        pdf_result = report_generator.generate_saas_valuation_pdf(
            saas_result=saas_result,
            company_data=sample_saas_data,
            output_path=str(pdf_path)
        )
        
        excel_result = report_generator.generate_saas_valuation_excel(
            saas_result=saas_result,
            company_data=sample_saas_data,
            output_path=str(excel_path)
        )
        
        # Validate both files exist
        assert os.path.exists(pdf_result), "SaaS PDF was not created"
        assert os.path.exists(excel_result), "SaaS Excel was not created"
        
        # Basic file validation
        assert os.path.getsize(pdf_result) > 8000, "SaaS PDF is too small"
        assert os.path.getsize(excel_result) > 4000, "SaaS Excel is too small"

    def test_report_file_output_structure(self, report_generator, sample_dcf_data, temp_output_dir):
        """Validate organized directory structure creation."""
        # Create nested output structure
        reports_dir = Path(temp_output_dir) / "reports" / "valuations" / "20240101"
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate reports in structured directory
        dcf_pdf_path = reports_dir / "dcf_analysis.pdf"
        dcf_excel_path = reports_dir / "dcf_analysis.xlsx"
        
        # Create DCF valuation
        dcf_valuation = DCFValuation(
            projections=sample_dcf_data["projections"],
            assumptions=sample_dcf_data["assumptions"]
        )
        dcf_result = dcf_valuation.calculate()
        
        # Generate reports
        pdf_result = report_generator.generate_dcf_valuation_pdf(
            dcf_result=dcf_result,
            company_data=sample_dcf_data,
            output_path=str(dcf_pdf_path)
        )
        
        excel_result = report_generator.generate_dcf_valuation_excel(
            dcf_result=dcf_result,
            company_data=sample_dcf_data,
            output_path=str(dcf_excel_path)
        )
        
        # Validate directory structure
        assert reports_dir.exists(), "Reports directory was not created"
        assert reports_dir.is_dir(), "Reports path is not a directory"
        
        # Validate files in structure
        assert Path(pdf_result).parent == reports_dir, "PDF not in correct directory"
        assert Path(excel_result).parent == reports_dir, "Excel not in correct directory"
        
        # Validate file naming
        assert Path(pdf_result).name == "dcf_analysis.pdf", "PDF has incorrect name"
        assert Path(excel_result).name == "dcf_analysis.xlsx", "Excel has incorrect name"

    def test_report_generation_with_invalid_data(self, report_generator, temp_output_dir):
        """Test error handling for malformed input data."""
        output_path = Path(temp_output_dir) / "invalid_test.pdf"
        
        # Test with missing required fields
        invalid_data = {
            "company_name": "Test Corp",
            # Missing projections and assumptions
        }
        
        with pytest.raises((ValueError, KeyError, AttributeError)) as exc_info:
            # This should fail due to missing required data
            dcf_valuation = DCFValuation(
                projections=invalid_data.get("projections", {}),
                assumptions=invalid_data.get("assumptions", {})
            )
            dcf_result = dcf_valuation.calculate()
            
            report_generator.generate_dcf_valuation_pdf(
                dcf_result=dcf_result,
                company_data=invalid_data,
                output_path=str(output_path)
            )
        
        # Validate appropriate error was raised
        assert exc_info.value is not None
        
        # Test with invalid file path (read-only directory simulation)
        with pytest.raises((PermissionError, OSError)):
            # Attempt to write to non-existent path
            invalid_path = "/nonexistent/readonly/path/test.pdf"
            
            # This should be mocked in real tests, but for demonstration
            # we're testing the error handling path
            pass

    @pytest.mark.slow
    @pytest.mark.performance
    def test_concurrent_report_generation(self, report_generator, sample_dcf_data, temp_output_dir):
        """Test multiple reports generated simultaneously for thread safety."""
        num_concurrent_reports = 5
        results = []
        errors = []
        
        def generate_report(report_id):
            """Generate a single report with unique naming."""
            try:
                # Create unique output path
                output_path = Path(temp_output_dir) / f"concurrent_dcf_{report_id}.pdf"
                
                # Create DCF valuation
                dcf_valuation = DCFValuation(
                    projections=sample_dcf_data["projections"],
                    assumptions=sample_dcf_data["assumptions"]
                )
                dcf_result = dcf_valuation.calculate()
                
                # Generate report
                result_path = report_generator.generate_dcf_valuation_pdf(
                    dcf_result=dcf_result,
                    company_data={
                        **sample_dcf_data,
                        "company_name": f"TechCorp {report_id}"
                    },
                    output_path=str(output_path)
                )
                
                return {
                    "id": report_id,
                    "path": result_path,
                    "size": os.path.getsize(result_path),
                    "success": True
                }
                
            except Exception as e:
                errors.append({"id": report_id, "error": str(e)})
                return {
                    "id": report_id,
                    "success": False,
                    "error": str(e)
                }
        
        # Execute concurrent report generation
        with ThreadPoolExecutor(max_workers=num_concurrent_reports) as executor:
            futures = {
                executor.submit(generate_report, i): i 
                for i in range(num_concurrent_reports)
            }
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        # Validate all reports were generated successfully
        successful_results = [r for r in results if r["success"]]
        assert len(successful_results) == num_concurrent_reports, \
            f"Expected {num_concurrent_reports} successful reports, got {len(successful_results)}"
        
        # Validate no errors occurred
        assert len(errors) == 0, f"Concurrent generation had errors: {errors}"
        
        # Validate all files exist and have reasonable sizes
        for result in successful_results:
            assert os.path.exists(result["path"]), f"Report {result['id']} file missing"
            assert result["size"] > 8000, f"Report {result['id']} file too small: {result['size']}"
        
        # Validate files are unique (no overwrites)
        file_sizes = [r["size"] for r in successful_results]
        paths = [r["path"] for r in successful_results]
        
        assert len(set(paths)) == num_concurrent_reports, "Some reports overwrote others"
        
        # Files should have similar sizes (within 20% variance for PDF generation)
        avg_size = sum(file_sizes) / len(file_sizes)
        for size in file_sizes:
            variance = abs(size - avg_size) / avg_size
            assert variance < 0.2, f"File size variance too high: {variance}"

    @pytest.mark.integration
    def test_report_memory_usage_monitoring(self, report_generator, sample_dcf_data, temp_output_dir):
        """Monitor memory usage during report generation."""
        import psutil
        import gc
        
        # Get baseline memory usage
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate multiple reports while monitoring memory
        max_memory_used = baseline_memory
        output_files = []
        
        for i in range(3):  # Generate 3 reports to test memory management
            gc.collect()  # Force garbage collection
            
            # Monitor memory before generation
            pre_memory = process.memory_info().rss / 1024 / 1024
            
            # Generate report
            output_path = Path(temp_output_dir) / f"memory_test_{i}.pdf"
            dcf_valuation = DCFValuation(
                projections=sample_dcf_data["projections"],
                assumptions=sample_dcf_data["assumptions"]
            )
            dcf_result = dcf_valuation.calculate()
            
            result_path = report_generator.generate_dcf_valuation_pdf(
                dcf_result=dcf_result,
                company_data=sample_dcf_data,
                output_path=str(output_path)
            )
            output_files.append(result_path)
            
            # Monitor memory after generation
            post_memory = process.memory_info().rss / 1024 / 1024
            max_memory_used = max(max_memory_used, post_memory)
            
            # Validate reasonable memory usage (shouldn't exceed 500MB per report)
            memory_increase = post_memory - baseline_memory
            assert memory_increase < 500, \
                f"Excessive memory usage: {memory_increase:.2f}MB increase"
        
        # Validate all files were created
        for file_path in output_files:
            assert os.path.exists(file_path), f"Report file missing: {file_path}"
            assert os.path.getsize(file_path) > 8000, f"Report file too small: {file_path}"
        
        # Final memory check after cleanup
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_growth = final_memory - baseline_memory
        
        # Memory growth should be minimal after cleanup (less than 100MB)
        assert memory_growth < 100, \
            f"Potential memory leak detected: {memory_growth:.2f}MB growth"
    def test_dcf_excel_content_validation(self, report_generator, sample_dcf_data, temp_output_dir):
        """Test DCF Excel report for data accuracy and content validation."""
        # Setup
        output_path = Path(temp_output_dir) / "dcf_valuation_content_check.xlsx"
        
        # Create DCF valuation instance
        dcf_valuation = DCFValuation(
            projections=sample_dcf_data["projections"],
            assumptions=sample_dcf_data["assumptions"]
        )
        dcf_result = dcf_valuation.calculate()
        
        # Generate Excel report
        report_generator.generate_dcf_valuation_excel(
            dcf_result=dcf_result,
            company_data=sample_dcf_data,
            output_path=str(output_path)
        )
        
        # --- Content Validation ---
        from openpyxl import load_workbook
        wb = load_workbook(output_path)
        
        # 1. Validate Executive Summary content
        summary_sheet = wb["Executive Summary"]
        # Check for the calculated equity value (a key result)
        equity_value_cell = summary_sheet["B4"].value
        assert "Equity Value" in equity_value_cell
        # Check that a plausible currency symbol and value is present
        assert equity_value_cell is not None, "Equity value cell is None"
        assert isinstance(equity_value_cell, str), f"Equity value cell is not a string: {type(equity_value_cell)}"
        assert "USD" in equity_value_cell, "Currency 'USD' not found in equity value cell"
        assert dcf_result['equity_value'] / 1_000_000 - 1 < float(equity_value_cell.split('M')[0].split(' ')[-1]) < dcf_result['equity_value'] / 1_000_000 + 1


        # 2. Validate DCF Calculation sheet
        dcf_sheet = wb["DCF Calculation"]
        # Check for a key calculated value: the first year's unlevered free cash flow
        # This should be Revenue - OpEx - Capex + Depreciation - WC Changes, then taxed.
        # (2.5M - 1.75M - 0.125M + 0.1M - 0.05M) * (1 - 0.21) = 675,000 * 0.79 = 533,250
        expected_ufcf_year1 = 533250
        actual_ufcf_year1 = dcf_sheet["B10"].value  # Assuming this is where the first UFCF value is
        
        # Allow for minor rounding differences
        assert abs(Decimal(actual_ufcf_year1) - Decimal(expected_ufcf_year1)) < 1, \
            f"UFCF Year 1 validation failed. Expected ~{expected_ufcf_year1}, got {actual_ufcf_year1}"
