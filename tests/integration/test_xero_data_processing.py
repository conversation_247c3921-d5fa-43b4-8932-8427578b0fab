# tests/integration/test_xero_data_processing.py
import pytest
from unittest.mock import patch, MagicMock
from decimal import Decimal

from mcx3d_finance.integrations.xero_sync import XeroSyncEngine as XeroDataSync
from mcx3d_finance.db.models import Account, Transaction
from mcx3d_finance.db.session import get_db

@pytest.fixture
def db_session_mock(self):
    """Fixture for a mocked database session."""
    db = MagicMock()
    db.query.return_value.filter.return_value.first.return_value = None
    yield db

@pytest.fixture
def xero_api_mock(self):
    """Fixture for a mocked Xero API client."""
    mock = MagicMock()
    # Mock the .accounts.get() call
    mock.accounts.get.return_value = [
        {
            "AccountID": "acc-001",
            "Code": "100",
            "Name": "Sales Revenue",
            "Type": "REVENUE",
            "Status": "ACTIVE",
            "Class": "REVENUE"
        },
        {
            "AccountID": "acc-002",
            "Code": "200",
            "Name": "Cost of Goods Sold",
            "Type": "EXPENSE",
            "Status": "ACTIVE",
            "Class": "EXPENSE"
        }
    ]
    yield mock

@pytest.mark.integration
def test_process_accounts_from_xero(self, db_session_mock, xero_api_mock):
    """
    Test that accounts fetched from Xero are correctly processed and prepared for DB insertion.
    """
    # Arrange
    sync_manager = XeroDataSync(db_session=db_session_mock, organization_id=1)
    
    # Act
    with patch('mcx3d_finance.integrations.xero_sync.get_xero_client', return_value=xero_api_mock):
        sync_manager._sync_accounts()

    # Assert
    # Verify that the database's add_all method was called
    assert db_session_mock.add_all.call_count == 1
    
    # Inspect the objects that were passed to add_all
    added_objects = db_session_mock.add_all.call_args[0][0]
    assert len(added_objects) == 2

    # Check the content of the first account object
    sales_account = added_objects[0]
    assert isinstance(sales_account, Account)
    assert sales_account.xero_account_id == "acc-001"
    assert sales_account.name == "Sales Revenue"
    assert sales_account.type == "REVENUE"
    assert sales_account.account_class == "REVENUE"

    # Check the content of the second account object
    cogs_account = added_objects[1]
    assert isinstance(cogs_account, Account)
    assert cogs_account.xero_account_id == "acc-002"
    assert cogs_account.name == "Cost of Goods Sold"
    assert cogs_account.type == "EXPENSE"
    assert cogs_account.account_class == "EXPENSE"

    # Verify a commit was made
    db_session_mock.commit.assert_called_once()