#!/usr/bin/env python
"""Test script for report generators using real Xero data."""

import os
import sys
from datetime import datetime, timedelta
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mcx3d_finance.reporting.engine.generators.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.reporting.engine.generators.income_statement import IncomeStatementGenerator
from mcx3d_finance.reporting.engine.generators.cash_flow import CashFlowGenerator
from mcx3d_finance.db.session import SessionLocal

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_balance_sheet():
    """Test balance sheet generation."""
    try:
        logger.info("\n=== Testing Balance Sheet Generator ===")
        
        # Use organization ID 2 (Modular CX)
        generator = BalanceSheetGenerator(organization_id=2)
        
        # Generate balance sheet as of today
        as_of_date = datetime.now()
        comparative_date = datetime.now() - timedelta(days=365)
        
        balance_sheet = generator.generate_balance_sheet(
            as_of_date=as_of_date,
            comparative_date=comparative_date
        )
        
        # Print summary
        logger.info(f"Balance Sheet generated for {balance_sheet['header']['company_name']}")
        logger.info(f"Reporting Date: {balance_sheet['header']['reporting_date']}")
        logger.info(f"Total Assets: ${balance_sheet['assets']['total_assets']['current']:,} thousands")
        logger.info(f"Total Liabilities: ${balance_sheet['liabilities_and_equity']['total_liabilities']['current']:,} thousands")
        logger.info(f"Total Equity: ${balance_sheet['liabilities_and_equity']['stockholders_equity']['total_stockholders_equity']['current']:,} thousands")
        
        # Print financial analysis
        if 'financial_analysis' in balance_sheet:
            analysis = balance_sheet['financial_analysis']
            logger.info("\nFinancial Analysis:")
            logger.info(f"  Current Ratio: {analysis['liquidity_ratios']['current_ratio']}")
            logger.info(f"  Debt to Equity: {analysis['leverage_ratios']['debt_to_equity']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing balance sheet: {e}")
        return False


def test_income_statement():
    """Test income statement generation."""
    try:
        logger.info("\n=== Testing Income Statement Generator ===")
        
        # Use organization ID 2 (Modular CX)
        generator = IncomeStatementGenerator(organization_id=2)
        
        # Generate income statement for last 12 months
        to_date = datetime.now()
        from_date = to_date - timedelta(days=365)
        
        # Comparative period (previous year)
        comparative_to = from_date - timedelta(days=1)
        comparative_from = comparative_to - timedelta(days=365)
        
        income_statement = generator.generate_income_statement(
            from_date=from_date,
            to_date=to_date,
            comparative_from=comparative_from,
            comparative_to=comparative_to
        )
        
        # Print summary
        logger.info(f"Income Statement generated for period {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")
        
        totals = income_statement.get('totals', {})
        if totals:
            logger.info(f"Total Revenue: ${totals.get('total_revenue', 0):,.2f}")
            logger.info(f"Gross Profit: ${totals.get('gross_profit', 0):,.2f}")
            logger.info(f"Operating Income: ${totals.get('operating_income', 0):,.2f}")
            logger.info(f"Net Income: ${totals.get('net_income', 0):,.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing income statement: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cash_flow():
    """Test cash flow statement generation."""
    try:
        logger.info("\n=== Testing Cash Flow Statement Generator ===")
        
        # Create database session
        db = SessionLocal()
        
        # Use organization ID 2 (Modular CX)
        generator = CashFlowGenerator(db_session=db)
        
        # Generate cash flow for last 12 months
        to_date = datetime.now()
        from_date = to_date - timedelta(days=365)
        
        cash_flow = generator.generate_cash_flow_statement(
            organization_id=2,
            from_date=from_date,
            to_date=to_date,
            method="indirect",
            include_comparative=True
        )
        
        # Print summary
        logger.info(f"Cash Flow Statement generated using {cash_flow['header']['method']} method")
        logger.info(f"Period: {cash_flow['header']['period_description']}")
        
        # Print cash flow summary
        summary = cash_flow.get('cash_summary', {})
        if summary:
            logger.info(f"\nCash Flow Summary:")
            logger.info(f"  Beginning Cash: ${summary.get('beginning_cash', 0):,.2f}")
            logger.info(f"  Net Change in Cash: ${summary.get('net_change_in_cash', 0):,.2f}")
            logger.info(f"  Ending Cash: ${summary.get('ending_cash', 0):,.2f}")
        
        # Print operating activities
        operating = cash_flow.get('operating_activities', {})
        if operating:
            logger.info(f"\nOperating Activities:")
            logger.info(f"  Net Cash from Operating: ${operating.get('net_cash_from_operating', 0):,.2f}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"Error testing cash flow: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all report generator tests."""
    logger.info("Starting Report Generator Tests")
    logger.info("=" * 50)
    
    # Test each report generator
    results = {
        "Balance Sheet": test_balance_sheet(),
        "Income Statement": test_income_statement(),
        "Cash Flow": test_cash_flow()
    }
    
    # Print results summary
    logger.info("\n" + "=" * 50)
    logger.info("Test Results Summary:")
    for report, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"  {report}: {status}")
    
    # Overall result
    all_passed = all(results.values())
    if all_passed:
        logger.info("\n✅ All tests passed!")
    else:
        logger.info("\n❌ Some tests failed. Check the logs above for details.")
    
    return all_passed


if __name__ == "__main__":
    sys.exit(0 if main() else 1)