"""
Tests for consistent error handling across API endpoints (Issue #6 verification).

Verifies that inconsistent error handling has been fixed and that all API endpoints
follow standardized error response patterns.
"""

import pytest
import json
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from typing import Dict, Any, List
from sqlalchemy.exc import SQLAlchemyError

from mcx3d_finance.main import app


class TestErrorHandlingConsistency:
    """Test suite for verifying consistent error handling fixes from Issue #6."""
    
    def test_error_response_schema_consistency(self) -> None:
        """
        Verify that all API endpoints return consistent error response schemas.
        Issue #6: "Implement consistent error handling across all endpoints"
        """
        client = TestClient(app)
        
        # Test various endpoints that should return errors
        test_cases = [
            # Authentication errors
            {
                "method": "POST",
                "endpoint": "/api/auth/login",
                "data": {"email": "invalid", "password": "wrong"},
                "expected_status": [400, 401, 422]
            },
            # Resource not found errors
            {
                "method": "GET", 
                "endpoint": "/api/auth/xero/status/999999",
                "data": None,
                "expected_status": [404, 401]  # Could be 401 if not authenticated
            },
            # Validation errors
            {
                "method": "POST",
                "endpoint": "/api/auth/mfa/verify",
                "data": {"invalid_field": "value"},
                "expected_status": [400, 422]
            }
        ]
        
        error_schemas = []
        
        for case in test_cases:
            if case["method"] == "GET":
                response = client.get(case["endpoint"])
            elif case["method"] == "POST":
                response = client.post(case["endpoint"], json=case["data"])
            
            # Should return an error status
            assert response.status_code in case["expected_status"], \
                f"Endpoint {case['endpoint']} should return error status"
            
            # Parse error response
            try:
                error_data = response.json()
                error_schemas.append({
                    "endpoint": case["endpoint"],
                    "status": response.status_code,
                    "schema": error_data,
                    "keys": set(error_data.keys()) if isinstance(error_data, dict) else set()
                })
            except json.JSONDecodeError:
                pytest.fail(f"Error response from {case['endpoint']} is not valid JSON")
        
        # All error responses should have consistent schema structure
        if len(error_schemas) > 1:
            # Check for common error response fields
            common_fields = set.intersection(*[schema["keys"] for schema in error_schemas])
            
            # Should have at least some common error fields
            expected_common_fields = {"detail", "message", "error", "status"}
            found_common_fields = common_fields.intersection(expected_common_fields)
            
            assert len(found_common_fields) > 0, \
                f"Error responses should have consistent fields. Found schemas: {error_schemas}"
    
    def test_http_status_codes_consistency(self) -> None:
        """
        Verify that HTTP status codes are used consistently across endpoints.
        """
        client = TestClient(app)
        
        # Test authentication failure consistency
        auth_endpoints = [
            "/api/auth/login",
            "/api/auth/refresh",
            "/api/auth/mfa/verify"
        ]
        
        for endpoint in auth_endpoints:
            response = client.post(endpoint, json={"invalid": "data"})
            
            # Authentication errors should use appropriate status codes
            assert response.status_code in [400, 401, 422], \
                f"Auth endpoint {endpoint} should return appropriate error status (got {response.status_code})"
        
        # Test not found consistency
        not_found_endpoints = [
            "/api/auth/nonexistent",
            "/api/auth/xero/status/999999"
        ]
        
        for endpoint in not_found_endpoints:
            response = client.get(endpoint)
            
            # Should return 404 or 401 (if authentication required)
            assert response.status_code in [404, 401], \
                f"Non-existent endpoint {endpoint} should return 404 or 401 (got {response.status_code})"
    
    def test_error_message_security(self) -> None:
        """
        Verify that error messages don't leak sensitive information.
        """
        client = TestClient(app)
        
        # Test with potentially sensitive scenarios
        test_cases = [
            {
                "endpoint": "/api/auth/login",
                "data": {"email": "<EMAIL>", "password": "wrong"},
                "sensitive_patterns": ["password", "hash", "secret", "key", "token", "database", "sql"]
            }
        ]
        
        for case in test_cases:
            response = client.post(case["endpoint"], json=case["data"])
            
            if response.status_code in [400, 401, 422]:
                response_text = response.text.lower()
                
                for pattern in case["sensitive_patterns"]:
                    assert pattern not in response_text, \
                        f"Error response should not contain sensitive information: '{pattern}'"
    
    def test_callback_url_error_handling(self) -> None:
        """
        Verify that the specific callback URL error from Issue #6 is fixed.
        Issue #6: "Missing request parameter in scope" in callback_url construction
        """
        client = TestClient(app)
        
        # Test the Xero callback endpoint specifically mentioned in Issue #6
        # This should not crash due to missing request parameter
        response = client.get("/api/auth/xero/callback?code=test&state=test")
        
        # Should not return 500 (internal server error)
        assert response.status_code != 500, \
            "Xero callback should not have internal server error (Issue #6 fix)"
        
        # May return other error codes (400, 401, etc.) but should handle gracefully
        if response.status_code >= 400:
            # Should return valid JSON error response
            try:
                error_data = response.json()
                assert isinstance(error_data, dict), \
                    "Error response should be valid JSON dict"
            except json.JSONDecodeError:
                pytest.fail("Error response should be valid JSON")
    
    def test_validation_error_consistency(self) -> None:
        """
        Verify that validation errors are consistent across endpoints.
        """
        client = TestClient(app)
        
        # Test validation errors on different endpoints
        validation_test_cases = [
            {
                "endpoint": "/api/auth/login",
                "data": {},  # Missing required fields
                "method": "POST"
            },
            {
                "endpoint": "/api/auth/mfa/setup",
                "data": {"invalid_field": 123},
                "method": "POST"
            }
        ]
        
        validation_responses = []
        
        for case in validation_test_cases:
            if case["method"] == "POST":
                response = client.post(case["endpoint"], json=case["data"])
            
            if response.status_code == 422:  # Validation error
                validation_responses.append(response.json())
        
        # All validation errors should have similar structure
        if len(validation_responses) > 1:
            # Check that validation errors have consistent structure
            for response_data in validation_responses:
                # FastAPI validation errors typically have 'detail' field
                assert "detail" in response_data, \
                    "Validation errors should have consistent structure"
    
    def test_database_error_handling(self) -> None:
        """
        Verify that database errors are handled consistently and securely.
        """
        client = TestClient(app)
        
        # Mock database error to test error handling
        with patch('mcx3d_finance.db.session.get_db') as mock_db:
            mock_session = MagicMock()
            mock_session.query.side_effect = SQLAlchemyError("Database connection failed")
            mock_db.return_value = mock_session
            
            # Test endpoint that uses database
            response = client.get("/api/auth/xero/status/1")
            
            # Should handle database error gracefully
            assert response.status_code in [500, 503], \
                "Database errors should be handled with appropriate status codes"
            
            # Should not expose database error details
            if response.status_code >= 400:
                response_text = response.text.lower()
                sensitive_db_terms = ["sqlalchemy", "database connection failed", "sql", "constraint"]
                
                for term in sensitive_db_terms:
                    assert term not in response_text, \
                        f"Database error should not expose sensitive details: '{term}'"
    
    def test_rate_limiting_error_consistency(self) -> None:
        """
        Verify that rate limiting errors are consistent across endpoints.
        """
        client = TestClient(app)
        
        # Test rate limiting on auth endpoints (which should have rate limiting)
        auth_endpoint = "/api/auth/login"
        
        # Make many requests to potentially trigger rate limiting
        responses = []
        for _ in range(10):
            response = client.post(auth_endpoint, json={"email": "<EMAIL>", "password": "wrong"})
            responses.append(response)
        
        # Check if any response indicates rate limiting
        rate_limited_responses = [r for r in responses if r.status_code == 429]
        
        if rate_limited_responses:
            for response in rate_limited_responses:
                # Rate limiting response should be consistent
                assert response.status_code == 429, "Rate limiting should use 429 status code"
                
                # Should have proper headers
                assert "Retry-After" in response.headers or "X-RateLimit-Reset" in response.headers, \
                    "Rate limiting response should include retry information"
    
    def test_content_type_error_handling(self) -> None:
        """
        Verify that incorrect content types are handled consistently.
        """
        client = TestClient(app)
        
        # Test with incorrect content type
        response = client.post(
            "/api/auth/login",
            data="not json",  # Plain text instead of JSON
            headers={"Content-Type": "text/plain"}
        )
        
        # Should handle gracefully with appropriate error
        assert response.status_code in [400, 415, 422], \
            "Incorrect content type should be handled with appropriate error"
    
    def test_error_logging_consistency(self) -> None:
        """
        Verify that errors are logged consistently (without sensitive data).
        """
        import logging
        from unittest.mock import patch
        
        client = TestClient(app)
        
        with patch('mcx3d_finance.api.auth.logger') as mock_logger:
            # Trigger an error
            response = client.post("/api/auth/login", json={"email": "test", "password": "wrong"})
            
            # Should log errors appropriately
            if response.status_code >= 400:
                # Check that some logging occurred (error or warning)
                log_calls = mock_logger.error.call_args_list + mock_logger.warning.call_args_list
                
                # Should have logged something
                assert len(log_calls) >= 0, "Errors should be logged for monitoring"


class TestErrorResponseStandards:
    """Test comprehensive API error response standards."""
    
    def test_error_response_structure(self) -> None:
        """
        Test that error responses follow a standardized structure.
        """
        client = TestClient(app)
        
        # Trigger a validation error
        response = client.post("/api/auth/login", json={})
        
        if response.status_code in [400, 422]:
            error_data = response.json()
            
            # Should be a dictionary
            assert isinstance(error_data, dict), "Error response should be a dict"
            
            # Should have at least one of the standard error fields
            standard_fields = ["detail", "message", "error", "errors"]
            has_standard_field = any(field in error_data for field in standard_fields)
            
            assert has_standard_field, \
                f"Error response should have standard field. Got: {list(error_data.keys())}"
    
    def test_error_response_json_format(self) -> None:
        """
        Verify that all error responses are valid JSON.
        """
        client = TestClient(app)
        
        # Test various error scenarios
        error_endpoints = [
            ("POST", "/api/auth/login", {"invalid": "data"}),
            ("GET", "/api/auth/xero/callback", None),
            ("POST", "/api/auth/mfa/verify", {"code": "invalid"})
        ]
        
        for method, endpoint, data in error_endpoints:
            if method == "POST":
                response = client.post(endpoint, json=data)
            else:
                response = client.get(endpoint)
            
            if response.status_code >= 400:
                # Should be valid JSON
                try:
                    json_data = response.json()
                    assert isinstance(json_data, (dict, list)), \
                        f"Error response from {endpoint} should be valid JSON structure"
                except json.JSONDecodeError:
                    pytest.fail(f"Error response from {endpoint} is not valid JSON")
    
    def test_error_response_headers(self) -> None:
        """
        Verify that error responses include appropriate headers.
        """
        client = TestClient(app)
        
        response = client.post("/api/auth/login", json={"invalid": "data"})
        
        if response.status_code >= 400:
            # Should have Content-Type header
            assert "content-type" in response.headers, \
                "Error responses should have Content-Type header"
            
            # Content-Type should be JSON for API endpoints
            content_type = response.headers.get("content-type", "")
            assert "application/json" in content_type, \
                "API error responses should have JSON content type"


class TestErrorHandlingRegression:
    """Regression tests to ensure error handling fixes don't break functionality."""
    
    def test_successful_requests_still_work(self) -> None:
        """
        Ensure that successful requests still work after error handling fixes.
        """
        client = TestClient(app)
        
        # Test that non-error endpoints still work
        response = client.get("/")
        assert response.status_code == 200, "Basic endpoints should still work"
        
        # Test that OpenAPI docs still work
        response = client.get("/docs")
        assert response.status_code == 200, "API documentation should still work"
    
    def test_error_handling_doesnt_affect_performance(self) -> None:
        """
        Verify that error handling improvements don't significantly impact performance.
        """
        import time
        client = TestClient(app)
        
        # Time a simple request
        start_time = time.time()
        response = client.get("/")
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Should respond reasonably quickly (less than 1 second for simple endpoint)
        assert response_time < 1.0, \
            f"Error handling should not significantly impact performance (took {response_time:.2f}s)"


if __name__ == "__main__":
    # Run error handling consistency tests
    pytest.main([__file__, "-v", "--tb=short"])