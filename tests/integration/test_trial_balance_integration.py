import unittest
from unittest.mock import patch, MagicMock
import pandas as pd
from mcx3d_finance.integrations.xero_client import XeroClient
from mcx3d_finance.core.financials.balance_sheet import UKBalanceSheetGenerator as BalanceSheet
from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator as IncomeStatement
from mcx3d_finance.db.session import get_db as get_session
from mcx3d_finance.db.models import Account as XeroAccount

class TestTrialBalanceIntegration(unittest.TestCase):

    @patch('mcx3d_finance.integrations.xero_client.XeroClient.get_trial_balance')
    def test_trial_balance_integration(self, mock_get_trial_balance):
        # Mock the trial balance data
        mock_trial_balance_data = {
            'accounts': [
                {'AccountID': '1', 'Name': 'Bank', 'Class': 'ASSET', 'Type': 'BANK', 'Balance': 10000.00, 'ReportingCode': 'ASS.CUR.BANK'},
                {'AccountID': '2', 'Name': 'Accounts Receivable', 'Class': 'ASSET', 'Type': 'CURRENT', 'Balance': 5000.00, 'ReportingCode': 'ASS.CUR.AR'},
                {'AccountID': '3', 'Name': 'Computer Equipment', 'Class': 'ASSET', 'Type': 'FIXED', 'Balance': 7500.00, 'ReportingCode': 'ASS.FIX.TECH'},
                {'AccountID': '4', 'Name': 'Accounts Payable', 'Class': 'LIABILITY', 'Type': 'CURRENT', 'Balance': -3000.00, 'ReportingCode': 'LIA.CUR.AP'},
                {'AccountID': '5', 'Name': 'Sales', 'Class': 'REVENUE', 'Type': 'REVENUE', 'Balance': -25000.00, 'ReportingCode': 'REV.SAL'},
                {'AccountID': '6', 'Name': 'Rent', 'Class': 'EXPENSE', 'Type': 'EXPENSE', 'Balance': 5500.00, 'ReportingCode': 'EXP.GEN.RENT'},
                {'AccountID': '7', 'Name': 'Retained Earnings', 'Class': 'EQUITY', 'Type': 'EQUITY', 'Balance': 0.0, 'ReportingCode': 'EQU.RET'},
            ]
        }
        mock_get_trial_balance.return_value = pd.DataFrame(mock_trial_balance_data['accounts'])

        # --- Test Balance Sheet ---
        with patch('mcx3d_finance.db.query_optimizers.QueryOptimizer.get_organization_with_all_relationships') as mock_get_org:
            mock_get_org.return_value = MagicMock()
            with patch('mcx3d_finance.core.financials.balance_sheet.UKBalanceSheetGenerator._calculate_uk_balance_sheet') as mock_calc_bs:
                mock_calc_bs.return_value = {
                    "fixed_assets": {"total": 7500.00},
                    "current_assets": {"total": 15000.00},
                    "creditors_due_within_one_year": {"total": 3000.00},
                    "creditors_due_after_one_year": {"total": 0},
                    "provisions_for_liabilities": {"total": 0},
                    "capital_and_reserves": {"total": 19500.00},
                    "net_assets": 19500.00,
                    "total_equity": 19500.00
                }
                balance_sheet = BalanceSheet(organization_id=1)
                balance_sheet_data = balance_sheet.generate_balance_sheet(as_of_date=pd.to_datetime('2023-12-31'))
                self.assertIsNotNone(balance_sheet_data)
                self.assertGreater(balance_sheet_data['fixed_assets']['total'], 0)

        # --- Test Income Statement ---
        with patch('mcx3d_finance.db.query_optimizers.QueryOptimizer.get_organization_with_all_relationships') as mock_get_org:
            mock_get_org.return_value = MagicMock()
            with patch('mcx3d_finance.core.financials.income_statement.UKProfitAndLossGenerator._calculate_uk_pnl') as mock_calc_pnl:
                mock_calc_pnl.return_value = {
                    "turnover": 25000.00,
                    "cost_of_sales": 5500.00,
                    "distribution_costs": 0,
                    "administrative_expenses": 5500.00,
                    "interest_receivable": 0,
                    "interest_payable": 0,
                    "taxation": 0
                }
                income_statement = IncomeStatement(organization_id=1)
                pnl_data = income_statement.generate_profit_and_loss(from_date=pd.to_datetime('2023-01-01'), to_date=pd.to_datetime('2023-12-31'))
                self.assertIsNotNone(pnl_data)
                self.assertGreater(pnl_data['turnover'], 0)


if __name__ == '__main__':
    unittest.main()