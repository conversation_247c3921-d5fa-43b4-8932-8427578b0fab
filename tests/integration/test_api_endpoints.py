#!/usr/bin/env python
"""Test script to verify the API endpoints are working."""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_api_imports() -> bool:
    """Test that all API imports work correctly."""
    try:
        logger.info("Testing API imports...")
        
        # Test importing the main FastAPI app
        from mcx3d_finance.main import app
        logger.info("✅ Main FastAPI app imported successfully")
        
        # Test importing the new xero import routes
        from mcx3d_finance.api import xero_import_routes
        logger.info("✅ Xero import routes imported successfully")
        
        # Test importing schemas
        from mcx3d_finance.api.schemas import (
            XeroImportRequest,
            XeroImportResponse,
            XeroAuthStatusResponse
        )
        logger.info("✅ New schemas imported successfully")
        
        # Test creating schema instances
        import_request = XeroImportRequest(
            organization_id=2,
            incremental=False,
            force_refresh=False
        )
        logger.info(f"✅ Created import request: {import_request}")
        
        # Check if routes are available
        routes = [getattr(route, 'path', str(route)) for route in app.routes]
        xero_routes = [route for route in routes if 'xero' in str(route)]
        logger.info(f"✅ Available Xero routes: {xero_routes}")
        
        logger.info("\n✅ All API imports and basic functionality working!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing API imports: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_models() -> bool:
    """Test that database models and queries work."""
    try:
        logger.info("\nTesting database models...")
        
        from mcx3d_finance.db.session import SessionLocal
        from mcx3d_finance.db.models import Organization, SyncStatus
        
        db = SessionLocal()
        
        # Test querying organizations
        orgs = db.query(Organization).all()
        logger.info(f"✅ Found {len(orgs)} organizations")
        
        # Test querying sync status
        sync_statuses = db.query(SyncStatus).all()
        logger.info(f"✅ Found {len(sync_statuses)} sync status records")
        
        db.close()
        logger.info("✅ Database models and queries working!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing database models: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_import_service() -> bool:
    """Test that the import service can be instantiated."""
    try:
        logger.info("\nTesting import service...")
        
        from mcx3d_finance.integrations.xero_data_import import XeroDataImportService
        
        # Test creating service instance
        import_service = XeroDataImportService(organization_id=2)
        logger.info("✅ Import service created successfully")
        
        # Test that the service has the expected methods
        methods = ['import_all_data', 'import_incremental_data']
        for method in methods:
            if hasattr(import_service, method):
                logger.info(f"✅ Method {method} available")
            else:
                logger.warning(f"⚠️ Method {method} not found")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing import service: {e}")
        import traceback
        traceback.print_exc()
        return False


def main() -> bool:
    """Run all tests."""
    logger.info("Starting API endpoint tests...")
    logger.info("=" * 60)
    
    tests = [
        test_api_imports,
        test_database_models,
        test_import_service
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            logger.error(f"Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    logger.info("\n" + "=" * 60)
    logger.info("Test Results Summary:")
    
    test_names = [test.__name__ for test in tests]
    for test_name, result in zip(test_names, results):
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    all_passed = all(results)
    if all_passed:
        logger.info("\n✅ All tests passed! API endpoints are ready to use.")
    else:
        logger.info("\n❌ Some tests failed. Check the logs above for details.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)