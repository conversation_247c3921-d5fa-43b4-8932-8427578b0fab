#!/usr/bin/env python3
"""
Test script to verify auth endpoints after consolidation.
Tests the key authentication endpoints to ensure they're working correctly.
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_health_check() -> bool:
    """Test if the API is running."""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print("✅ Health check:", response.status_code == 200)
        return response.status_code == 200
    except Exception as e:
        print("❌ Health check failed:", str(e))
        return False

def test_auth_endpoints() -> None:
    """Test the main auth endpoints."""
    print("\n=== Testing Auth Endpoints ===\n")
    
    # Test login endpoint (should fail with invalid credentials but return 401)
    print("1. Testing POST /api/auth/login")
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/login",
            json={"email": "<EMAIL>", "password": "testpassword"},
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 401:
            print("✅ Login endpoint working (correctly rejected invalid credentials)")
        elif response.status_code == 500:
            print("⚠️  Login endpoint returned 500 (server error)")
            print(f"   This might be due to database or Redis connection issues")
            print(f"   Response: {response.text}")
        else:
            print(f"⚠️  Login endpoint returned unexpected status: {response.status_code}")
            print(f"   Response: {response.text}")
            print(f"   Headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ Login endpoint error: {str(e)}")
    
    # Test rate limiting
    print("\n2. Testing rate limiting on login endpoint")
    try:
        # Make multiple requests to trigger rate limit
        for i in range(6):
            response = requests.post(
                f"{BASE_URL}/api/auth/login",
                json={"email": "<EMAIL>", "password": "testpassword"},
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 429:
                print(f"✅ Rate limiting working (triggered after {i+1} attempts)")
                print(f"   Rate limit message: {response.json().get('detail', {}).get('message', 'N/A')}")
                break
        else:
            print("⚠️  Rate limiting might not be working (didn't trigger after 6 attempts)")
    except Exception as e:
        print(f"❌ Rate limiting test error: {str(e)}")
    
    # Test Xero authorize endpoint (should require authentication)
    print("\n3. Testing GET /api/auth/xero/authorize")
    try:
        response = requests.get(f"{BASE_URL}/api/auth/xero/authorize")
        if response.status_code == 403:  # Should fail without auth
            print("✅ Xero authorize endpoint properly protected")
        else:
            print(f"⚠️  Xero authorize endpoint returned unexpected status: {response.status_code}")
    except Exception as e:
        print(f"❌ Xero authorize endpoint error: {str(e)}")
    
    # Test Xero callback endpoint
    print("\n4. Testing GET /api/auth/xero/callback")
    try:
        response = requests.get(
            f"{BASE_URL}/api/auth/xero/callback",
            params={"code": "test", "state": "test"}
        )
        # Should return redirect or error due to invalid state
        if response.status_code in [302, 422, 500]:
            print("✅ Xero callback endpoint accessible")
        else:
            print(f"⚠️  Xero callback endpoint returned unexpected status: {response.status_code}")
    except Exception as e:
        print(f"❌ Xero callback endpoint error: {str(e)}")
    
    # Test MFA setup endpoint (should require authentication)
    print("\n5. Testing POST /api/auth/mfa/setup")
    try:
        response = requests.post(f"{BASE_URL}/api/auth/mfa/setup")
        if response.status_code == 403:  # Should fail without auth
            print("✅ MFA setup endpoint properly protected")
        else:
            print(f"⚠️  MFA setup endpoint returned unexpected status: {response.status_code}")
    except Exception as e:
        print(f"❌ MFA setup endpoint error: {str(e)}")

def test_cors_headers() -> None:
    """Test CORS configuration."""
    print("\n=== Testing CORS Configuration ===\n")
    
    try:
        # Test preflight request
        response = requests.options(
            f"{BASE_URL}/api/auth/login",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
        )
        
        cors_headers = {
            "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
            "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
            "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers"),
            "Access-Control-Max-Age": response.headers.get("Access-Control-Max-Age")
        }
        
        print("CORS Headers received:")
        for header, value in cors_headers.items():
            if value:
                print(f"  {header}: {value}")
        
        # Check if configuration is production-appropriate
        if cors_headers["Access-Control-Allow-Origin"] == "http://localhost:3000":
            print("✅ CORS origin properly configured (not using wildcard)")
        
        if cors_headers["Access-Control-Allow-Methods"] and "*" not in cors_headers["Access-Control-Allow-Methods"]:
            print("✅ CORS methods properly configured (not using wildcard)")
            
        if cors_headers["Access-Control-Allow-Headers"] and "*" not in cors_headers["Access-Control-Allow-Headers"]:
            print("✅ CORS headers properly configured (not using wildcard)")
            
        if cors_headers["Access-Control-Max-Age"] == "3600":
            print("✅ CORS max-age properly set to 1 hour")
            
    except Exception as e:
        print(f"❌ CORS test error: {str(e)}")

def main() -> None:
    print("Testing MCX3D Finance Auth Endpoints\n")
    print("Make sure the API server is running on http://localhost:8000")
    print("=" * 50)
    
    # First check if API is running
    if not test_health_check():
        print("\n❌ API server is not running. Please start it with: uvicorn mcx3d_finance.main:app --reload")
        sys.exit(1)
    
    # Run tests
    test_auth_endpoints()
    test_cors_headers()
    
    print("\n" + "=" * 50)
    print("Testing complete!")
    print("\nNote: Some endpoints require authentication to fully test.")
    print("The important thing is that they're accessible and returning expected error codes.")

if __name__ == "__main__":
    main()