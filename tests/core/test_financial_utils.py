"""
Comprehensive tests for the financial utilities module.
Tests all utility methods including currency operations, percentage calculations,
growth calculations, financial analysis, and data validation.
"""

import pytest
import sys
import os
import numpy as np
from decimal import Decimal, InvalidOperation
from datetime import datetime, timed<PERSON>ta
from typing import Union

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from mcx3d_finance.core.financial_utils import (
    FinancialUtils,
    round_currency,
    calculate_percentage_change,
    format_currency
)


class TestCurrencyOperations:
    """Test currency-related operations."""
    
    def test_round_currency_normal_cases(self):
        """Test rounding currency with normal values."""
        # Test various decimal places
        assert FinancialUtils.round_currency(10.234) == Decimal('10.23')
        assert FinancialUtils.round_currency(10.235) == Decimal('10.24')  # Round half up
        assert FinancialUtils.round_currency(10.236) == Decimal('10.24')
        assert FinancialUtils.round_currency(10.999) == Decimal('11.00')
        
    def test_round_currency_edge_cases(self):
        """Test rounding currency with edge cases."""
        # Zero and negative values
        assert FinancialUtils.round_currency(0) == Decimal('0.00')
        assert FinancialUtils.round_currency(-10.234) == Decimal('-10.23')
        assert FinancialUtils.round_currency(-10.235) == Decimal('-10.24')
        
        # Very large numbers
        assert FinancialUtils.round_currency(999999999.994) == Decimal('999999999.99')
        assert FinancialUtils.round_currency(999999999.995) == Decimal('1000000000.00')
        
        # Very small numbers
        assert FinancialUtils.round_currency(0.001) == Decimal('0.00')
        assert FinancialUtils.round_currency(0.005) == Decimal('0.01')
        
    def test_round_currency_type_variations(self):
        """Test rounding with different input types."""
        # Integer input
        assert FinancialUtils.round_currency(10) == Decimal('10.00')
        
        # Float input
        assert FinancialUtils.round_currency(10.5) == Decimal('10.50')
        
        # Decimal input
        assert FinancialUtils.round_currency(Decimal('10.234')) == Decimal('10.23')
        
    def test_round_currency_invalid_inputs(self):
        """Test rounding with invalid inputs."""
        # Test None
        with pytest.raises(ValueError, match="Invalid currency value"):
            FinancialUtils.round_currency(None)
            
        # Test string that can't be converted
        with pytest.raises(ValueError, match="Invalid currency value"):
            FinancialUtils.round_currency("not a number")
            
        # Test infinity
        with pytest.raises(ValueError, match="Invalid currency value"):
            FinancialUtils.round_currency(float('inf'))
            
        # Test NaN
        with pytest.raises(ValueError, match="Invalid currency value"):
            FinancialUtils.round_currency(float('nan'))
    
    def test_format_currency_gbp(self):
        """Test formatting GBP currency."""
        # Standard formatting
        assert FinancialUtils.format_currency(1234.56) == "£1,234.56"
        assert FinancialUtils.format_currency(1234567.89) == "£1,234,567.89"
        assert FinancialUtils.format_currency(0) == "£0.00"
        assert FinancialUtils.format_currency(-1234.56) == "£-1,234.56"
        
        # Without symbol
        assert FinancialUtils.format_currency(1234.56, include_symbol=False) == "1,234.56 GBP"
        
    def test_format_currency_multiple_currencies(self):
        """Test formatting with different currencies."""
        amount = 1234.56
        
        # USD
        assert FinancialUtils.format_currency(amount, "USD") == "$1,234.56"
        
        # EUR
        assert FinancialUtils.format_currency(amount, "EUR") == "€1,234.56"
        
        # JPY
        assert FinancialUtils.format_currency(amount, "JPY") == "¥1,234.56"
        
        # Unknown currency
        assert FinancialUtils.format_currency(amount, "XYZ") == "1,234.56 XYZ"
        
    def test_format_currency_rounding(self):
        """Test that format_currency properly rounds values."""
        # Should round to 2 decimal places
        assert FinancialUtils.format_currency(1234.567) == "£1,234.57"
        assert FinancialUtils.format_currency(1234.564) == "£1,234.56"
        

class TestPercentageCalculations:
    """Test percentage-related calculations."""
    
    def test_round_percentage_normal_cases(self):
        """Test rounding percentages with normal values."""
        # Test various decimal places
        assert FinancialUtils.round_percentage(10.23456) == Decimal('10.2346')
        assert FinancialUtils.round_percentage(10.23454) == Decimal('10.2345')
        assert FinancialUtils.round_percentage(10.23455) == Decimal('10.2346')  # Round half up
        
    def test_round_percentage_edge_cases(self):
        """Test rounding percentages with edge cases."""
        # Zero and negative
        assert FinancialUtils.round_percentage(0) == Decimal('0.0000')
        assert FinancialUtils.round_percentage(-10.23456) == Decimal('-10.2346')
        
        # Very small percentages
        assert FinancialUtils.round_percentage(0.00001) == Decimal('0.0000')
        assert FinancialUtils.round_percentage(0.00005) == Decimal('0.0001')
        
    def test_round_percentage_invalid_inputs(self):
        """Test percentage rounding with invalid inputs."""
        with pytest.raises(ValueError, match="Invalid percentage value"):
            FinancialUtils.round_percentage(None)
            
        with pytest.raises(ValueError, match="Invalid percentage value"):
            FinancialUtils.round_percentage("not a number")
    
    @pytest.mark.parametrize("current,previous,expected", [
        (150, 100, 50.0),           # 50% increase
        (50, 100, -50.0),           # 50% decrease
        (100, 100, 0.0),            # No change
        (200, 50, 300.0),           # 300% increase
        (0, 100, -100.0),           # Complete loss
        (100, 0, None),             # Division by zero
        (-50, 100, -150.0),         # Negative current value
        (100, -50, -300.0),         # Negative previous value
    ])
    def test_calculate_percentage_change(self, current, previous, expected):
        """Test percentage change calculation with various scenarios."""
        result = FinancialUtils.calculate_percentage_change(current, previous)
        if expected is None:
            assert result is None
        else:
            assert result == pytest.approx(expected)
    
    def test_calculate_percentage_change_decimal_inputs(self):
        """Test percentage change with Decimal inputs."""
        result = FinancialUtils.calculate_percentage_change(
            Decimal('150'), 
            Decimal('100')
        )
        assert result == 50.0
        
    def test_calculate_percentage_change_invalid_inputs(self):
        """Test percentage change with invalid inputs."""
        # Should return None for invalid inputs
        assert FinancialUtils.calculate_percentage_change("invalid", 100) is None
        assert FinancialUtils.calculate_percentage_change(100, "invalid") is None
    
    @pytest.mark.parametrize("revenue,cost,expected", [
        (1000, 600, 40.0),          # 40% margin
        (1000, 1000, 0.0),          # Break even
        (1000, 1200, -20.0),        # Loss
        (0, 500, None),             # Zero revenue
        (1000, 0, 100.0),           # Zero cost
    ])
    def test_calculate_margin(self, revenue, cost, expected):
        """Test margin calculation with various scenarios."""
        result = FinancialUtils.calculate_margin(revenue, cost)
        if expected is None:
            assert result is None
        else:
            assert result == pytest.approx(expected)


class TestGrowthAndTimeCalculations:
    """Test growth rate and time-based calculations."""
    
    @pytest.mark.parametrize("start,end,periods,expected", [
        (100, 200, 1, 100.0),       # 100% growth in 1 year
        (100, 200, 2, 41.421356),   # ~41.42% CAGR over 2 years
        (100, 300, 3, 44.224957),   # ~44.22% CAGR over 3 years
        (200, 100, 2, -29.289322),  # Negative growth
        (100, 100, 5, 0.0),         # No growth
    ])
    def test_calculate_growth_rate(self, start, end, periods, expected):
        """Test CAGR calculation with various scenarios."""
        result = FinancialUtils.calculate_growth_rate(start, end, periods)
        if expected is None:
            assert result is None
        else:
            assert result == pytest.approx(expected, rel=1e-5)
    
    def test_calculate_growth_rate_edge_cases(self):
        """Test growth rate calculation edge cases."""
        # Invalid inputs should return None
        assert FinancialUtils.calculate_growth_rate(0, 100, 1) is None
        assert FinancialUtils.calculate_growth_rate(100, 0, 1) is None
        assert FinancialUtils.calculate_growth_rate(-100, 200, 1) is None
        assert FinancialUtils.calculate_growth_rate(100, 200, 0) is None
        assert FinancialUtils.calculate_growth_rate(100, 200, -1) is None
    
    def test_calculate_days_between_calendar_days(self):
        """Test calculating calendar days between dates."""
        start = datetime(2024, 1, 1)
        end = datetime(2024, 1, 10)
        
        assert FinancialUtils.calculate_days_between(start, end) == 9
        
        # Same day
        assert FinancialUtils.calculate_days_between(start, start) == 0
        
        # Reverse order (negative days)
        assert FinancialUtils.calculate_days_between(end, start) == -9
    
    def test_calculate_days_between_business_days(self):
        """Test calculating business days between dates."""
        # Monday to Friday (5 business days)
        start = datetime(2024, 1, 1)  # Monday
        end = datetime(2024, 1, 5)    # Friday
        assert FinancialUtils.calculate_days_between(start, end, business_days_only=True) == 5
        
        # Friday to next Monday (2 business days - Friday and Monday)
        start = datetime(2024, 1, 5)   # Friday
        end = datetime(2024, 1, 8)     # Monday
        assert FinancialUtils.calculate_days_between(start, end, business_days_only=True) == 2
        
        # Full week (5 business days)
        start = datetime(2024, 1, 1)   # Monday
        end = datetime(2024, 1, 7)     # Sunday
        assert FinancialUtils.calculate_days_between(start, end, business_days_only=True) == 5
        
        # Weekend only (0 business days)
        start = datetime(2024, 1, 6)   # Saturday
        end = datetime(2024, 1, 7)     # Sunday
        assert FinancialUtils.calculate_days_between(start, end, business_days_only=True) == 0


class TestFinancialAnalysis:
    """Test financial analysis calculations."""
    
    def test_calculate_npv_basic(self):
        """Test basic NPV calculation."""
        # Example: Initial investment of 1000, returns of 300, 400, 500, 600
        cash_flows = [300, 400, 500, 600]
        discount_rate = 0.10  # 10%
        initial_investment = 1000
        
        npv = FinancialUtils.calculate_npv(cash_flows, discount_rate, initial_investment)
        
        # Manual calculation for verification
        expected = -1000 + 300/(1.1) + 400/(1.1**2) + 500/(1.1**3) + 600/(1.1**4)
        assert npv == pytest.approx(expected, rel=1e-5)
    
    def test_calculate_npv_with_embedded_investment(self):
        """Test NPV when initial investment is in cash flows."""
        # First cash flow is negative (investment)
        cash_flows = [-1000, 300, 400, 500, 600]
        discount_rate = 0.10
        
        npv = FinancialUtils.calculate_npv(cash_flows, discount_rate)
        
        expected = -1000 + 300/(1.1) + 400/(1.1**2) + 500/(1.1**3) + 600/(1.1**4)
        assert npv == pytest.approx(expected, rel=1e-5)
    
    def test_calculate_npv_negative_result(self):
        """Test NPV with negative result (bad investment)."""
        cash_flows = [100, 100, 100]
        discount_rate = 0.15
        initial_investment = 500
        
        npv = FinancialUtils.calculate_npv(cash_flows, discount_rate, initial_investment)
        assert npv < 0  # Should be negative (bad investment)
    
    def test_calculate_npv_invalid_inputs(self):
        """Test NPV with invalid inputs."""
        with pytest.raises(ValueError, match="Error calculating NPV"):
            FinancialUtils.calculate_npv([], 0.1)  # Empty cash flows
    
    def test_calculate_irr_basic(self):
        """Test basic IRR calculation."""
        # Example: Initial investment of 1000, returns that yield ~14.3% IRR
        cash_flows = [-1000, 300, 400, 500, 600]
        
        irr = FinancialUtils.calculate_irr(cash_flows)
        assert irr is not None
        assert irr == pytest.approx(14.3, abs=0.1)  # ~14.3% IRR
    
    def test_calculate_irr_edge_cases(self):
        """Test IRR calculation edge cases."""
        # No negative cash flows (no investment)
        assert FinancialUtils.calculate_irr([100, 200, 300]) is None
        
        # No positive cash flows (no returns)
        assert FinancialUtils.calculate_irr([-100, -200, -300]) is None
        
        # All zeros
        assert FinancialUtils.calculate_irr([0, 0, 0]) is None
    
    def test_calculate_irr_multiple_sign_changes(self):
        """Test IRR with multiple sign changes (may have multiple IRRs)."""
        # This might fail or return one of multiple possible IRRs
        cash_flows = [-1000, 2500, -1500]
        irr = FinancialUtils.calculate_irr(cash_flows)
        # Just verify it returns a value or None (implementation dependent)
        assert irr is None or isinstance(irr, float)
    
    def test_calculate_payback_period_simple(self):
        """Test simple payback period calculation."""
        # Investment pays back exactly in period 2
        cash_flows = [400, 600, 500]
        initial_investment = 1000
        
        payback = FinancialUtils.calculate_payback_period(cash_flows, initial_investment)
        assert payback == 2.0
    
    def test_calculate_payback_period_fractional(self):
        """Test payback period with fractional result."""
        # Investment pays back partway through period 2
        cash_flows = [400, 800, 500]
        initial_investment = 1000
        
        payback = FinancialUtils.calculate_payback_period(cash_flows, initial_investment)
        # After period 0: -600 remaining
        # Period 1 cash flow: 800
        # Fraction: 600/800 = 0.75
        assert payback == pytest.approx(1.75)
    
    def test_calculate_payback_period_immediate(self):
        """Test payback in first period."""
        cash_flows = [1200, 500, 500]
        initial_investment = 1000
        
        payback = FinancialUtils.calculate_payback_period(cash_flows, initial_investment)
        assert payback == 0
    
    def test_calculate_payback_period_never(self):
        """Test when investment never pays back."""
        cash_flows = [100, 200, 300]
        initial_investment = 1000
        
        payback = FinancialUtils.calculate_payback_period(cash_flows, initial_investment)
        assert payback is None
    
    def test_calculate_payback_period_negative_flows(self):
        """Test payback with some negative cash flows."""
        # Has negative flow in middle
        cash_flows = [600, -100, 700]
        initial_investment = 1000
        
        payback = FinancialUtils.calculate_payback_period(cash_flows, initial_investment)
        # After period 0: -400
        # After period 1: -500 (negative flow)
        # Period 2: 700, fraction: 500/700 = 0.714
        assert payback == pytest.approx(2.714, abs=0.01)


class TestDataValidation:
    """Test data validation functionality."""
    
    def test_validate_financial_data_valid(self):
        """Test validation of valid financial data."""
        data = {
            "revenue": 100000,
            "cost": 60000,
            "profit": 40000,
            "margin_percent": 40.0,
            "currency": "GBP",
            "date": "2024-01-01"
        }
        
        validated = FinancialUtils.validate_financial_data(data)
        assert validated == data
    
    def test_validate_financial_data_with_none(self):
        """Test validation with None values."""
        data = {
            "revenue": 100000,
            "cost": None,  # Missing cost
            "profit": 100000
        }
        
        validated = FinancialUtils.validate_financial_data(data)
        assert validated["cost"] is None
        assert validated["revenue"] == 100000
    
    def test_validate_financial_data_numeric_types(self):
        """Test validation with different numeric types."""
        data = {
            "int_value": 100,
            "float_value": 100.5,
            "decimal_value": Decimal("100.25")
        }
        
        validated = FinancialUtils.validate_financial_data(data)
        assert validated == data
    
    def test_validate_financial_data_invalid_numerics(self):
        """Test validation with invalid numeric values."""
        # Test NaN
        with pytest.raises(ValueError, match="Invalid numeric value for revenue"):
            FinancialUtils.validate_financial_data({"revenue": float('nan')})
        
        # Test infinity
        with pytest.raises(ValueError, match="Invalid numeric value for cost"):
            FinancialUtils.validate_financial_data({"cost": float('inf')})
        
        # Test negative infinity
        with pytest.raises(ValueError, match="Invalid numeric value for loss"):
            FinancialUtils.validate_financial_data({"loss": float('-inf')})
    
    def test_validate_financial_data_mixed_types(self):
        """Test validation with mixed data types."""
        data = {
            "revenue": 100000,
            "company_name": "Test Corp",
            "is_profitable": True,
            "tags": ["tech", "saas"],
            "metadata": {"source": "xero"}
        }
        
        validated = FinancialUtils.validate_financial_data(data)
        assert validated == data


class TestConvenienceFunctions:
    """Test the convenience functions for backward compatibility."""
    
    def test_round_currency_convenience(self):
        """Test the round_currency convenience function."""
        assert round_currency(10.234) == Decimal('10.23')
        assert round_currency(10.235) == Decimal('10.24')
    
    def test_calculate_percentage_change_convenience(self):
        """Test the calculate_percentage_change convenience function."""
        assert calculate_percentage_change(150, 100) == 50.0
        assert calculate_percentage_change(50, 100) == -50.0
    
    def test_format_currency_convenience(self):
        """Test the format_currency convenience function."""
        assert format_currency(1234.56) == "£1,234.56"
        assert format_currency(1234.56, "USD") == "$1,234.56"


class TestEdgeCasesAndPerformance:
    """Test edge cases and performance considerations."""
    
    def test_very_large_numbers(self):
        """Test handling of very large numbers."""
        large_number = 10**15  # 1 quadrillion
        
        # Currency rounding
        rounded = FinancialUtils.round_currency(large_number + 0.999)
        assert rounded == Decimal(str(large_number + 1))
        
        # Percentage calculation
        change = FinancialUtils.calculate_percentage_change(large_number * 2, large_number)
        assert change == 100.0
        
        # Formatting
        formatted = FinancialUtils.format_currency(large_number)
        assert "," in formatted  # Should have thousand separators
    
    def test_very_small_positive_numbers(self):
        """Test handling of very small positive numbers."""
        tiny = 0.000001
        
        # Should round to 0.00
        assert FinancialUtils.round_currency(tiny) == Decimal('0.00')
        
        # Percentage change with tiny values
        change = FinancialUtils.calculate_percentage_change(tiny * 2, tiny)
        assert change == 100.0
    
    def test_precision_edge_cases(self):
        """Test precision edge cases with banker's rounding."""
        # Test banker's rounding (round half to even)
        # Note: We're using ROUND_HALF_UP, not banker's rounding
        assert FinancialUtils.round_currency(10.125) == Decimal('10.13')  # Round up
        assert FinancialUtils.round_currency(10.135) == Decimal('10.14')  # Round up
        assert FinancialUtils.round_currency(10.145) == Decimal('10.15')  # Round up
    
    def test_currency_symbols_completeness(self):
        """Test all supported currency symbols."""
        currencies = ["GBP", "USD", "EUR", "JPY", "AUD", "CAD", "CHF", "CNY", "SEK", "NZD"]
        
        for currency in currencies:
            formatted = FinancialUtils.format_currency(1234.56, currency)
            # Should either have a symbol at start or currency code at end
            assert formatted[0] in "£$€¥AFC" or formatted.endswith(f" {currency}")
    
    @pytest.mark.parametrize("periods", [1, 5, 10, 25, 50, 100])
    def test_npv_with_many_periods(self, periods):
        """Test NPV calculation with varying number of periods."""
        cash_flows = [100] * periods
        discount_rate = 0.10
        initial_investment = 500
        
        npv = FinancialUtils.calculate_npv(cash_flows, discount_rate, initial_investment)
        
        # NPV should be calculable for any reasonable number of periods
        assert isinstance(npv, float)
        assert not np.isnan(npv)
        assert not np.isinf(npv)


class TestRealWorldScenarios:
    """Test real-world UK financial scenarios."""
    
    def test_uk_vat_calculation(self):
        """Test calculations involving UK VAT (20%)."""
        net_amount = 100.00
        vat_rate = 0.20
        
        gross_amount = net_amount * (1 + vat_rate)
        formatted = FinancialUtils.format_currency(gross_amount, "GBP")
        assert formatted == "£120.00"
        
        # Reverse calculation
        vat_amount = gross_amount - net_amount
        assert FinancialUtils.round_currency(vat_amount) == Decimal('20.00')
    
    def test_uk_corporation_tax_scenario(self):
        """Test UK corporation tax calculation scenario."""
        profit_before_tax = 150000
        tax_rate = 0.19  # UK corporation tax rate
        
        tax_amount = profit_before_tax * tax_rate
        profit_after_tax = profit_before_tax - tax_amount
        
        assert FinancialUtils.round_currency(tax_amount) == Decimal('28500.00')
        assert FinancialUtils.round_currency(profit_after_tax) == Decimal('121500.00')
    
    def test_multi_year_investment_analysis(self):
        """Test a realistic multi-year investment scenario."""
        # UK startup investment scenario
        initial_investment = 500000  # £500k seed funding
        yearly_revenues = [50000, 150000, 400000, 800000, 1200000]  # 5 years
        yearly_costs = [200000, 300000, 350000, 500000, 700000]
        
        cash_flows = []
        for revenue, cost in zip(yearly_revenues, yearly_costs):
            cash_flows.append(revenue - cost)
        
        # Calculate metrics
        discount_rate = 0.12  # 12% required return
        
        npv = FinancialUtils.calculate_npv(cash_flows, discount_rate, initial_investment)
        irr = FinancialUtils.calculate_irr([-initial_investment] + cash_flows)
        payback = FinancialUtils.calculate_payback_period(yearly_revenues, initial_investment)
        
        # Verify calculations make sense
        assert isinstance(npv, float)
        assert irr is not None  # Should be calculable
        assert payback is not None  # Should pay back within 5 years
    
    def test_saas_metrics_calculation(self):
        """Test SaaS-specific financial calculations."""
        # Monthly recurring revenue scenario
        mrr_start = 50000
        mrr_end = 75000
        months = 12
        
        # Calculate monthly growth rate
        monthly_growth = FinancialUtils.calculate_growth_rate(mrr_start, mrr_end, months)
        
        # Calculate ARR
        arr_start = mrr_start * 12
        arr_end = mrr_end * 12
        
        formatted_arr = FinancialUtils.format_currency(arr_end, "GBP")
        assert formatted_arr == "£900,000.00"
        
        # Churn calculation
        customers_start = 100
        customers_lost = 5
        churn_rate = FinancialUtils.calculate_percentage_change(
            customers_start - customers_lost,
            customers_start
        )
        assert churn_rate == -5.0  # 5% churn
    
    def test_working_days_calculation_uk_scenario(self):
        """Test UK working days calculation (excluding weekends)."""
        # UK financial year end scenario
        financial_year_start = datetime(2023, 4, 1)  # 1 April (Saturday)
        financial_year_end = datetime(2024, 3, 31)   # 31 March (Sunday)
        
        total_days = FinancialUtils.calculate_days_between(
            financial_year_start,
            financial_year_end
        )
        
        business_days = FinancialUtils.calculate_days_between(
            financial_year_start,
            financial_year_end,
            business_days_only=True
        )
        
        # Verify counts
        assert total_days == 365  # Non-leap year
        # Approximately 260 business days in a year (52 weeks * 5 days)
        assert 250 <= business_days <= 262