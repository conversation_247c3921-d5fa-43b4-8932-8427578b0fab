"""
Comprehensive tests for core financial statement generation.
"""

from datetime import datetime, timezone
from mcx3d_finance.reporting.engine.generators.balance_sheet import BalanceSheetGenerator


class TestFinancialStatements:
    """Test core financial statement generation logic."""

    def test_balance_sheet_generation(self, db_session, sample_organization):
        """Test basic balance sheet generation structure."""
        generator = BalanceSheetGenerator(organization_id=sample_organization.id)

        balance_sheet = generator.generate_balance_sheet(
            as_of_date=datetime.now(timezone.utc)
        )

        # Verify basic structure
        assert 'header' in balance_sheet
        assert 'assets' in balance_sheet
        assert 'liabilities_and_equity' in balance_sheet
        assert 'financial_analysis' in balance_sheet

        # Verify the balance equation holds true, even with no data
        total_assets = balance_sheet['assets']['total_assets']['current']
        total_liab_equity = balance_sheet['liabilities_and_equity']['total_liabilities_and_equity']['current']
        assert abs(total_assets - total_liab_equity) < 0.01

    def test_balance_sheet_with_detailed_accounts(self, db_session, detailed_organization_with_transactions):
        """
        Test balance sheet with a detailed chart of accounts and transactions to verify classification and calculations.
        """
        org_id = detailed_organization_with_transactions
        generator = BalanceSheetGenerator(organization_id=org_id)

        report_date = datetime(2023, 12, 31, tzinfo=timezone.utc)
        balance_sheet = generator.generate_balance_sheet(as_of_date=report_date)

        assets = balance_sheet['assets']
        liabilities_and_equity = balance_sheet['liabilities_and_equity']

        # Based on the data in the 'detailed_organization_with_transactions' fixture:
        # Current Assets = 150k + 75k + 50k + 5k = 280k
        # Non-Current Assets = 300k + 50k = 350k
        # Total Assets = 630k
        # Current Liabilities = 40k + 15k + 25k = 80k
        # Non-Current Liabilities = 150k
        # Total Liabilities = 230k
        # Equity = 100k + 300k = 400k
        # Total Liabilities + Equity = 630k

        # Verify subtotals using the correct nested keys
        assert abs(assets['current_assets']['total_current_assets']['current'] - 280000.00) < 0.01
        assert abs(assets['non_current_assets']['total_non_current_assets']['current'] - 350000.00) < 0.01
        assert abs(liabilities_and_equity['current_liabilities']['total_current_liabilities']['current'] - 80000.00) < 0.01
        assert abs(liabilities_and_equity['non_current_liabilities']['total_non_current_liabilities']['current'] - 150000.00) < 0.01
        assert abs(liabilities_and_equity['stockholders_equity']['total_stockholders_equity']['current'] - 400000.00) < 0.01

        # Verify main totals
        assert abs(assets['total_assets']['current'] - 630000.00) < 0.01
        assert abs(liabilities_and_equity['total_liabilities']['current'] - 230000.00) < 0.01
        assert abs(liabilities_and_equity['total_liabilities_and_equity']['current'] - 630000.00) < 0.01

        # Re-verify the golden rule: Assets = Liabilities + Equity
        total_assets = assets['total_assets']['current']
        total_liab_equity = liabilities_and_equity['total_liabilities_and_equity']['current']
        assert abs(total_assets - total_liab_equity) < 0.01
