"""
Comprehensive test suite for enhanced Xero OAuth implementation.
"""

import json
import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from freezegun import freeze_time
from datetime import datetime, timezone, timedelta

from mcx3d_finance.auth.xero_oauth import <PERSON>eroAuthManager, StateStorage
from mcx3d_finance.auth.oauth_security import (
    RedirectURIValidator, 
    OAuthRateLimiter, 
    validate_oauth_request
)
from mcx3d_finance.exceptions.auth import (
    XeroAuthorizationError, 
    InvalidStateError, 
    XeroTokenRefreshError
)


class TestStateStorageEnhancements:
    """Test enhanced StateStorage functionality."""
    
    def test_singleton_pattern(self):
        """Test that StateStorage follows singleton pattern."""
        storage1 = StateStorage()
        storage2 = StateStorage()
        assert storage1 is storage2
    
    def test_namespaced_keys(self):
        """Test that keys are properly namespaced."""
        storage = StateStorage()
        
        # Mock Redis to capture the actual key used
        with patch.object(storage, '_redis_client') as mock_redis:
            mock_redis.setex = Mock()
            storage._redis_available = True
            
            storage.set("test_key", "test_value", 60)
            
            # Verify the key was namespaced
            mock_redis.setex.assert_called_once_with(
                "mcx3d:oauth:test_key", 60, "test_value"
            )
    
    def test_periodic_cleanup(self):
        """Test periodic cleanup of expired entries."""
        storage = StateStorage()
        storage._redis_available = False  # Force memory storage
        
        # Add entries with different expiry times
        current_time = time.time()
        
        with patch('time.time', return_value=current_time):
            storage.set("fresh_key", "fresh_value", 300)  # 5 minutes
            storage.set("old_key", "old_value", 1)  # 1 second
        
        # Advance time and trigger cleanup
        with patch('time.time', return_value=current_time + 2):
            storage.set("trigger_cleanup", "value", 60)
        
        # Check that old key was cleaned up
        assert storage.get("fresh_key") == "fresh_value"
        assert storage.get("old_key") is None


class TestRedirectURIValidator:
    """Test redirect URI validation with security enhancements."""
    
    def test_development_redirect_uris(self):
        """Test development environment redirect URI validation."""
        validator = RedirectURIValidator()
        
        with patch('mcx3d_finance.auth.oauth_security.settings') as mock_settings:
            mock_settings.debug = True
            mock_settings.environment = 'development'
            
            # Valid development URIs
            assert validator.validate_redirect_uri('http://localhost:8000/api/auth/xero/callback')
            assert validator.validate_redirect_uri('http://127.0.0.1:8000/api/auth/xero/callback')
            
            # Invalid URIs should raise exception
            with pytest.raises(XeroAuthorizationError):
                validator.validate_redirect_uri('http://evil.com/callback')
    
    def test_production_redirect_uris(self):
        """Test production environment redirect URI validation."""
        validator = RedirectURIValidator()
        
        with patch('mcx3d_finance.auth.oauth_security.settings') as mock_settings:
            mock_settings.debug = False
            mock_settings.environment = 'production'
            
            # Valid production URIs
            assert validator.validate_redirect_uri('https://app.mcx3d.com/api/auth/xero/callback')
            
            # Invalid URIs should raise exception
            with pytest.raises(XeroAuthorizationError):
                validator.validate_redirect_uri('http://localhost:8000/api/auth/xero/callback')
            
            with pytest.raises(XeroAuthorizationError):
                validator.validate_redirect_uri('https://evil.com/callback')
    
    def test_malformed_uri_validation(self):
        """Test validation of malformed URIs."""
        validator = RedirectURIValidator()
        
        with pytest.raises(XeroAuthorizationError):
            validator.validate_redirect_uri('')
        
        with pytest.raises(XeroAuthorizationError):
            validator.validate_redirect_uri('not-a-uri')


class TestOAuthRateLimiter:
    """Test OAuth rate limiting functionality."""
    
    def test_redis_rate_limiting(self):
        """Test rate limiting with Redis backend."""
        limiter = OAuthRateLimiter()
        
        with patch.object(limiter, '_redis_client') as mock_redis:
            limiter.redis_available = True
            
            # Mock Redis pipeline
            mock_pipe = Mock()
            mock_pipe.execute.return_value = [None, 0, None, None]  # Count = 0
            mock_redis.pipeline.return_value = mock_pipe
            
            # First request should be allowed
            assert limiter.check_rate_limit('127.0.0.1', 'test', 5, 300)
            
            # Mock high count to test limit
            mock_pipe.execute.return_value = [None, 5, None, None]  # Count = 5
            assert not limiter.check_rate_limit('127.0.0.1', 'test', 5, 300)
    
    def test_memory_rate_limiting(self):
        """Test rate limiting with memory fallback."""
        limiter = OAuthRateLimiter()
        limiter.redis_available = False
        
        # Make requests up to limit
        for i in range(5):
            assert limiter.check_rate_limit('127.0.0.1', 'test', 5, 300)
        
        # Next request should be blocked
        assert not limiter.check_rate_limit('127.0.0.1', 'test', 5, 300)
    
    def test_rate_limit_window_expiry(self):
        """Test that rate limits reset after window expires."""
        limiter = OAuthRateLimiter()
        limiter.redis_available = False
        
        current_time = time.time()
        
        # Fill up the rate limit
        with patch('time.time', return_value=current_time):
            for i in range(5):
                assert limiter.check_rate_limit('127.0.0.1', 'test', 5, 60)
            assert not limiter.check_rate_limit('127.0.0.1', 'test', 5, 60)
        
        # Advance time beyond window
        with patch('time.time', return_value=current_time + 61):
            assert limiter.check_rate_limit('127.0.0.1', 'test', 5, 60)


class TestXeroAuthManagerEnhancements:
    """Test enhanced XeroAuthManager functionality."""
    
    @pytest.fixture
    def auth_manager(self):
        """Create XeroAuthManager instance for testing."""
        with patch('mcx3d_finance.auth.xero_oauth.get_xero_config') as mock_config:
            mock_config.return_value = {
                'client_id': 'test_client_id',
                'client_secret': 'test_client_secret',
                'redirect_uri': 'http://localhost:8000/api/auth/xero/callback',
                'scopes': 'accounting.transactions offline_access'
            }
            return XeroAuthManager()
    
    def test_generate_auth_url_with_security_validation(self, auth_manager):
        """Test auth URL generation with security validation."""
        with patch('mcx3d_finance.auth.xero_oauth.validate_oauth_request') as mock_validate:
            with patch('mcx3d_finance.auth.xero_oauth.oauth_audit_logger') as mock_audit:
                with patch('requests_oauthlib.OAuth2Session') as mock_oauth:
                    mock_session = Mock()
                    mock_session.authorization_url.return_value = ('http://auth.url', 'state')
                    mock_oauth.return_value = mock_session
                    
                    url, state = auth_manager.generate_auth_url(client_ip='127.0.0.1')
                    
                    # Verify security validation was called
                    mock_validate.assert_called_once()
                    
                    # Verify audit logging
                    mock_audit.log_oauth_event.assert_called_once()
                    assert mock_audit.log_oauth_event.call_args[1]['event_type'] == 'auth_url_generated'
    
    def test_handle_callback_with_security_validation(self, auth_manager):
        """Test callback handling with security validation."""
        callback_url = 'http://localhost:8000/api/auth/xero/callback?code=test_code&state=test_state'
        
        with patch('mcx3d_finance.auth.xero_oauth.validate_oauth_request') as mock_validate:
            with patch.object(auth_manager.state_storage, 'get') as mock_get:
                mock_get.side_effect = ['valid', 'test_verifier']
                
                with patch.object(auth_manager.state_storage, 'delete'):
                    with patch('requests_oauthlib.OAuth2Session') as mock_oauth:
                        # Mock successful token exchange
                        mock_session = Mock()
                        mock_session.fetch_token.return_value = {
                            'access_token': 'test_token',
                            'refresh_token': 'test_refresh',
                            'expires_in': 1800
                        }
                        mock_oauth.return_value = mock_session
                        
                        with patch.object(auth_manager, '_get_tenant_info') as mock_tenant:
                            mock_tenant.return_value = {
                                'tenantId': 'test_tenant',
                                'tenantName': 'Test Org'
                            }
                            
                            with patch.object(auth_manager, '_store_token_info') as mock_store:
                                mock_store.return_value = {
                                    'organization_id': 1,
                                    'tenant_name': 'Test Org'
                                }
                                
                                result = auth_manager.handle_callback(callback_url, client_ip='127.0.0.1')
                                
                                # Verify security validation was called
                                mock_validate.assert_called_once()
                                assert result['success']
    
    def test_oauth_error_audit_logging(self, auth_manager):
        """Test that OAuth errors are properly audit logged."""
        callback_url = 'http://localhost:8000/api/auth/xero/callback?error=access_denied&state=test_state'
        
        with patch('mcx3d_finance.auth.xero_oauth.oauth_audit_logger') as mock_audit:
            result = auth_manager.handle_callback(callback_url, client_ip='127.0.0.1')
            
            # Verify error was audit logged
            mock_audit.log_oauth_event.assert_called()
            call_args = mock_audit.log_oauth_event.call_args
            assert call_args[1]['event_type'] == 'oauth_callback'
            assert call_args[1]['status'] == 'failure'
            assert 'access_denied' in call_args[1]['details']['error']


class TestOAuthSecurity:
    """Test comprehensive OAuth security functionality."""
    
    def test_validate_oauth_request_success(self):
        """Test successful OAuth request validation."""
        with patch('mcx3d_finance.auth.oauth_security.oauth_rate_limiter') as mock_limiter:
            mock_limiter.check_rate_limit.return_value = True
            
            with patch('mcx3d_finance.auth.oauth_security.redirect_validator') as mock_validator:
                mock_validator.validate_redirect_uri.return_value = True
                
                with patch('mcx3d_finance.auth.oauth_security.oauth_audit_logger') as mock_audit:
                    result = validate_oauth_request(
                        'http://localhost:8000/api/auth/xero/callback', 
                        '127.0.0.1'
                    )
                    
                    assert result is True
                    mock_audit.log_oauth_event.assert_called_once()
    
    def test_validate_oauth_request_rate_limit_exceeded(self):
        """Test OAuth request validation with rate limit exceeded."""
        with patch('mcx3d_finance.auth.oauth_security.oauth_rate_limiter') as mock_limiter:
            mock_limiter.check_rate_limit.return_value = False
            
            with pytest.raises(XeroAuthorizationError, match="Rate limit exceeded"):
                validate_oauth_request(
                    'http://localhost:8000/api/auth/xero/callback', 
                    '127.0.0.1'
                )


class TestOAuthIntegration:
    """Integration tests for complete OAuth flow."""
    
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client for testing."""
        mock_client = Mock()
        mock_client.ping.return_value = True
        mock_client.setex = Mock()
        mock_client.get = Mock()
        mock_client.delete = Mock()
        return mock_client
    
    @pytest.fixture
    def mock_database(self):
        """Mock database session for testing."""
        mock_db = Mock()
        mock_org = Mock()
        mock_org.id = 1
        mock_org.name = 'Test Organization'
        mock_org.xero_tenant_id = 'test_tenant_id'
        mock_db.query.return_value.filter.return_value.first.return_value = mock_org
        mock_db.query.return_value.get.return_value = mock_org
        return mock_db
    
    def test_complete_oauth_flow_success(self, mock_redis, mock_database):
        """Test complete OAuth flow from start to finish."""
        with patch('mcx3d_finance.auth.xero_oauth.get_redis_client', return_value=mock_redis):
            with patch('mcx3d_finance.auth.xero_oauth.SessionLocal', return_value=mock_database):
                with patch('mcx3d_finance.auth.xero_oauth.get_xero_config') as mock_config:
                    mock_config.return_value = {
                        'client_id': 'test_client_id',
                        'client_secret': 'test_client_secret',
                        'redirect_uri': 'http://localhost:8000/api/auth/xero/callback',
                        'scopes': 'accounting.transactions offline_access'
                    }
                    
                    auth_manager = XeroAuthManager()
                    
                    # Step 1: Generate auth URL
                    with patch('requests_oauthlib.OAuth2Session') as mock_oauth:
                        mock_session = Mock()
                        mock_session.authorization_url.return_value = (
                            'https://login.xero.com/authorize?client_id=test', 
                            'test_state'
                        )
                        mock_oauth.return_value = mock_session
                        
                        url, state = auth_manager.generate_auth_url(client_ip='127.0.0.1')
                        
                        assert url is not None
                        assert state is not None
                    
                    # Step 2: Handle callback
                    callback_url = f'http://localhost:8000/api/auth/xero/callback?code=test_code&state={state}'
                    
                    # Mock state storage returns
                    mock_redis.get.side_effect = ['valid', 'test_verifier']
                    
                    with patch('requests_oauthlib.OAuth2Session') as mock_oauth:
                        mock_session = Mock()
                        mock_session.fetch_token.return_value = {
                            'access_token': 'test_access_token',
                            'refresh_token': 'test_refresh_token',
                            'expires_in': 1800
                        }
                        mock_oauth.return_value = mock_session
                        
                        with patch('requests.get') as mock_get:
                            # Mock tenant info response
                            mock_response = Mock()
                            mock_response.status_code = 200
                            mock_response.json.return_value = [{
                                'tenantId': 'test_tenant_id',
                                'tenantName': 'Test Organization'
                            }]
                            mock_get.return_value = mock_response
                            
                            result = auth_manager.handle_callback(callback_url, client_ip='127.0.0.1')
                            
                            assert result['success'] is True
                            assert result['organization_id'] == 1
                            assert result['tenant_name'] == 'Test Organization'


class TestTokenRefreshSecurity:
    """Test token refresh with enhanced security."""
    
    def test_token_refresh_with_distributed_locking(self):
        """Test that token refresh uses distributed locking properly."""
        with patch('mcx3d_finance.auth.xero_oauth.get_xero_config') as mock_config:
            mock_config.return_value = {
                'client_id': 'test_client_id',
                'client_secret': 'test_client_secret',
                'redirect_uri': 'http://localhost:8000/api/auth/xero/callback',
                'scopes': 'accounting.transactions offline_access'
            }
            
            auth_manager = XeroAuthManager()
            
            with patch('mcx3d_finance.auth.xero_oauth.SessionLocal') as mock_session:
                mock_db = Mock()
                mock_org = Mock()
                mock_org.id = 1
                mock_org.xero_token = json.dumps({
                    'access_token': 'old_token',
                    'refresh_token': 'refresh_token'
                })
                mock_org.token_expires_at = datetime.now(timezone.utc) - timedelta(minutes=10)
                mock_db.query.return_value.get.return_value = mock_org
                mock_session.return_value = mock_db
                
                with patch('mcx3d_finance.auth.xero_oauth.get_redis_client') as mock_redis:
                    with patch('mcx3d_finance.auth.xero_oauth.TokenRefreshLock') as mock_lock:
                        mock_lock_instance = Mock()
                        mock_lock.return_value = mock_lock_instance
                        mock_lock_instance.__enter__ = Mock(return_value=mock_lock_instance)
                        mock_lock_instance.__exit__ = Mock(return_value=None)
                        
                        with patch.object(auth_manager, '_refresh_token') as mock_refresh:
                            mock_refresh.return_value = {
                                'access_token': 'new_token',
                                'refresh_token': 'new_refresh_token',
                                'expires_in': 1800
                            }
                            
                            token = auth_manager.get_valid_token(1)
                            
                            # Verify distributed lock was used
                            mock_lock.assert_called_once()
                            assert token == 'new_token'


if __name__ == '__main__':
    pytest.main([__file__, '-v'])