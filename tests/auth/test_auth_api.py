"""
Tests for OAuth2 API endpoints.
"""
import pytest
import json
import base64
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock
from fastapi import HTTPException
from fastapi.testclient import TestClient

from mcx3d_finance.api.auth import router, LoginRequest
from mcx3d_finance.db.models import User, Organization, UserOrganization


@pytest.fixture
def test_client():
    """Create test client for API endpoints."""
    from fastapi import FastAPI
    app = FastAPI()
    app.include_router(router)
    return TestClient(app)


@pytest.fixture
def mock_user(db_session):
    """Create a test user."""
    user = User(
        id=1,
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User",
        is_active=True,
        created_at=datetime.now(timezone.utc)
    )
    db_session.add(user)
    db_session.commit()
    return user


@pytest.fixture
def mock_organization(db_session):
    """Create a test organization."""
    org = Organization(
        id=1,
        name="Test Organization",
        xero_tenant_id="test-tenant-id",
        created_at=datetime.now(timezone.utc)
    )
    db_session.add(org)
    db_session.commit()
    return org


@pytest.fixture
def auth_headers():
    """Create authorization headers with valid token."""
    with patch('mcx3d_finance.api.auth_middleware.create_access_token') as mock_create:
        mock_create.return_value = "test_token"
        token = mock_create({"sub": "1", "organizations": [1]})
        return {"Authorization": f"Bearer {token}"}


class TestLoginEndpoint:
    """Test user login endpoint."""

    def test_login_success(self, test_client, mock_user, db_session):
        """Test successful login."""
        # Create user organization
        user_org = UserOrganization(
            user_id=mock_user.id,
            organization_id=1,
            role="admin"
        )
        db_session.add(user_org)
        db_session.commit()
        
        with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
            with patch('mcx3d_finance.api.auth_middleware.verify_password', return_value=True):
                with patch('mcx3d_finance.api.auth_middleware.check_account_lockout', return_value=(False, 0)):
                    with patch('mcx3d_finance.api.auth_middleware.is_mfa_enabled', return_value=False):
                        with patch('mcx3d_finance.api.auth_middleware.clear_failed_logins'):
                            with patch('mcx3d_finance.utils.audit_logger.audit_logger.log_event'):
                                with patch('mcx3d_finance.utils.rate_limiter.auth_limiter.get_client_ip', return_value="127.0.0.1"):
                                    with patch('mcx3d_finance.utils.rate_limiter.auth_limiter.check_and_raise'):
                                        with patch('mcx3d_finance.utils.input_validator.input_validator.validate_and_sanitize') as mock_validate:
                                            mock_validate.return_value = {"email": "<EMAIL>", "password": "password"}
                                            with patch('mcx3d_finance.utils.session_manager.session_manager.create_session') as mock_session:
                                                mock_session.return_value = {
                                                    "access_token": "test_access_token",
                                                    "refresh_token": "test_refresh_token",
                                                    "session_id": "test_session_id"
                                                }
                                                
                                                response = test_client.post(
                                                    "/api/auth/login",
                                                    json={"email": "<EMAIL>", "password": "password"}
                                                )
        
        assert response.status_code == 200
        data = response.json()
        assert data["access_token"] == "test_access_token"
        assert data["token_type"] == "bearer"
        assert data["user"]["email"] == "<EMAIL>"
        assert data["user"]["organizations"] == [1]

    def test_login_invalid_credentials(self, test_client, db_session):
        """Test login with invalid credentials."""
        with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
            response = test_client.post(
                "/api/auth/login",
                json={"email": "<EMAIL>", "password": "wrong"}
            )
        
        assert response.status_code == 401
        assert "Invalid email or password" in response.json()["detail"]

    def test_login_inactive_user(self, test_client, mock_user, db_session):
        """Test login with inactive user."""
        mock_user.is_active = False
        db_session.commit()
        
        with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
            response = test_client.post(
                "/api/auth/login",
                json={"email": "<EMAIL>", "password": "password"}
            )
        
        assert response.status_code == 401
        assert "Invalid email or password" in response.json()["detail"]


class TestXeroAuthorizeEndpoint:
    """Test Xero OAuth2 authorization endpoint."""

    def test_xero_authorize_success(self, test_client):
        """Test successful Xero authorization initiation."""
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1", "organizations": [1]}
            
            with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                mock_manager = Mock()
                mock_manager.generate_auth_url.return_value = "https://xero.com/auth"
                mock_auth.return_value = mock_manager
                
                response = test_client.get(
                    "/api/auth/xero/authorize?organization_id=1",
                    headers={"Authorization": "Bearer test_token"}
                )
        
        assert response.status_code == 200
        data = response.json()
        assert data["auth_url"] == "https://xero.com/auth"
        assert "Redirect user" in data["message"]

    def test_xero_authorize_no_auth_url(self, test_client):
        """Test authorization when URL generation fails."""
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1"}
            
            with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                mock_manager = Mock()
                mock_manager.generate_auth_url.return_value = None
                mock_auth.return_value = mock_manager
                
                response = test_client.get(
                    "/api/auth/xero/authorize",
                    headers={"Authorization": "Bearer test_token"}
                )
        
        assert response.status_code == 500
        assert "Failed to generate authorization URL" in response.json()["detail"]

    def test_xero_authorize_no_auth(self, test_client):
        """Test authorization without authentication."""
        # Should require authentication
        response = test_client.get("/api/auth/xero/authorize")
        assert response.status_code in [401, 422]  # Depends on FastAPI version


class TestXeroCallbackEndpoint:
    """Test Xero OAuth2 callback endpoint."""

    def test_xero_callback_success(self, test_client, db_session):
        """Test successful OAuth callback."""
        # Create state data
        state_data = {
            "user_id": "1",
            "organization_id": 1,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        state = base64.urlsafe_b64encode(
            json.dumps(state_data).encode()
        ).decode().rstrip("=")
        
        with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
            with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                mock_manager = Mock()
                mock_manager.handle_callback.return_value = {
                    "success": True,
                    "organization_id": 1,
                    "organization_name": "Test Org",
                    "tenant_id": "test-tenant"
                }
                mock_auth.return_value = mock_manager
                
                # Mock request object
                with patch('mcx3d_finance.api.auth.request') as mock_request:
                    mock_request.url.scheme = "http"
                    mock_request.url.netloc = "localhost:8000"
                    mock_request.url.path = "/api/auth/xero/callback"
                    
                    response = test_client.get(
                        f"/api/auth/xero/callback?code=test_code&state={state}"
                    )
        
        assert response.status_code == 302  # Redirect
        assert "/success?organization_id=1" in response.headers["location"]

    def test_xero_callback_invalid_state(self, test_client, db_session):
        """Test callback with invalid state."""
        with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
            response = test_client.get(
                "/api/auth/xero/callback?code=test_code&state=invalid_state"
            )
        
        assert response.status_code == 302
        assert "/error?message=Invalid+state+parameter" in response.headers["location"]

    def test_xero_callback_auth_failure(self, test_client, db_session):
        """Test callback when authentication fails."""
        state_data = {"user_id": "1"}
        state = base64.urlsafe_b64encode(
            json.dumps(state_data).encode()
        ).decode().rstrip("=")
        
        with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
            with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                mock_manager = Mock()
                mock_manager.handle_callback.return_value = {
                    "success": False,
                    "error": "Authentication failed"
                }
                mock_auth.return_value = mock_manager
                
                response = test_client.get(
                    f"/api/auth/xero/callback?code=test_code&state={state}"
                )
        
        assert response.status_code == 302
        assert "/error?message=Authentication+failed" in response.headers["location"]


class TestXeroRefreshEndpoint:
    """Test Xero token refresh endpoint."""

    def test_refresh_token_success(self, test_client):
        """Test successful token refresh."""
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1", "organizations": [1]}
            
            with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                mock_manager = Mock()
                mock_manager.get_valid_token.return_value = {"access_token": "new_token"}
                mock_auth.return_value = mock_manager
                
                response = test_client.post(
                    "/api/auth/xero/refresh/1",
                    headers={"Authorization": "Bearer test_token"}
                )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "refreshed successfully" in data["message"]

    def test_refresh_token_no_access(self, test_client):
        """Test token refresh without organization access."""
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1", "organizations": [2, 3]}
            
            response = test_client.post(
                "/api/auth/xero/refresh/1",
                headers={"Authorization": "Bearer test_token"}
            )
        
        assert response.status_code == 403
        assert "do not have access" in response.json()["detail"]

    def test_refresh_token_not_found(self, test_client):
        """Test token refresh when no token exists."""
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1", "organizations": [1]}
            
            with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                mock_manager = Mock()
                mock_manager.get_valid_token.return_value = None
                mock_auth.return_value = mock_manager
                
                response = test_client.post(
                    "/api/auth/xero/refresh/1",
                    headers={"Authorization": "Bearer test_token"}
                )
        
        assert response.status_code == 404
        assert "No token found" in response.json()["detail"]


class TestXeroRevokeEndpoint:
    """Test Xero token revocation endpoint."""

    def test_revoke_token_success(self, test_client, mock_user, mock_organization, db_session):
        """Test successful token revocation."""
        # Create user organization with admin role
        user_org = UserOrganization(
            user_id=mock_user.id,
            organization_id=mock_organization.id,
            role="admin"
        )
        db_session.add(user_org)
        db_session.commit()
        
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_current:
            mock_current.return_value = {"user_id": str(mock_user.id)}
            
            with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
                with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                    mock_manager = Mock()
                    mock_manager.revoke_token.return_value = True
                    mock_auth.return_value = mock_manager
                    
                    response = test_client.delete(
                        f"/api/auth/xero/revoke/{mock_organization.id}",
                        headers={"Authorization": "Bearer test_token"}
                    )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "revoked successfully" in data["message"]

    def test_revoke_token_no_admin(self, test_client, mock_user, mock_organization, db_session):
        """Test token revocation without admin access."""
        # Create user organization with non-admin role
        user_org = UserOrganization(
            user_id=mock_user.id,
            organization_id=mock_organization.id,
            role="user"
        )
        db_session.add(user_org)
        db_session.commit()
        
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_current:
            mock_current.return_value = {"user_id": str(mock_user.id)}
            
            with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
                response = test_client.delete(
                    f"/api/auth/xero/revoke/{mock_organization.id}",
                    headers={"Authorization": "Bearer test_token"}
                )
        
        assert response.status_code == 403
        assert "Admin access required" in response.json()["detail"]


class TestXeroStatusEndpoint:
    """Test Xero authentication status endpoint."""

    def test_auth_status_authenticated(self, test_client, mock_organization, db_session):
        """Test auth status when authenticated."""
        mock_organization.token_expires_at = datetime.now(timezone.utc)
        db_session.commit()
        
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1", "organizations": [mock_organization.id]}
            
            with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
                with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                    mock_manager = Mock()
                    mock_manager.get_valid_token.return_value = {"access_token": "valid"}
                    mock_auth.return_value = mock_manager
                    
                    response = test_client.get(
                        f"/api/auth/xero/status/{mock_organization.id}",
                        headers={"Authorization": "Bearer test_token"}
                    )
        
        assert response.status_code == 200
        data = response.json()
        assert data["authenticated"] is True
        assert data["organization_id"] == mock_organization.id
        assert data["organization_name"] == "Test Organization"

    def test_auth_status_not_authenticated(self, test_client, mock_organization, db_session):
        """Test auth status when not authenticated."""
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1", "organizations": [mock_organization.id]}
            
            with patch('mcx3d_finance.api.auth.get_db', return_value=db_session):
                with patch('mcx3d_finance.api.auth.XeroAuthManager') as mock_auth:
                    mock_manager = Mock()
                    mock_manager.get_valid_token.return_value = None
                    mock_auth.return_value = mock_manager
                    
                    response = test_client.get(
                        f"/api/auth/xero/status/{mock_organization.id}",
                        headers={"Authorization": "Bearer test_token"}
                    )
        
        assert response.status_code == 200
        data = response.json()
        assert data["authenticated"] is False


class TestLogoutEndpoint:
    """Test user logout endpoint."""

    def test_logout_success(self, test_client):
        """Test successful logout."""
        with patch('mcx3d_finance.api.auth.get_current_user') as mock_user:
            mock_user.return_value = {"user_id": "1"}
            
            response = test_client.post(
                "/api/auth/logout",
                headers={"Authorization": "Bearer test_token"}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "Logged out successfully" in data["message"]