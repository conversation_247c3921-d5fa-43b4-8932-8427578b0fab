setup.py:1: error: Skipping analyzing "setuptools": module is installed, but missing library stubs or py.typed marker  [import-untyped]
scripts/development/explore_xero.py:6: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/tasks/example.py:4: error: Untyped decorator makes function "add" untyped  [misc]
mcx3d_finance/tasks/calculation.py:4: error: Untyped decorator makes function "multiply" untyped  [misc]
tests/test_auth_endpoints.py:6: error: Library stubs not installed for "requests"  [import-untyped]
tests/test_auth_endpoints.py:17: error: Returning Any from function declared to return "bool"  [no-any-return]
tests/test_api_authentication.py:6: error: Library stubs not installed for "requests"  [import-untyped]
tests/test_api_authentication.py:23: error: Need type annotation for "headers" (hint: "headers: Dict[<type>, <type>] = ...")  [var-annotated]
tests/e2e/test_cli_integration.py:19: error: X | Y syntax for unions requires Python 3.10  [syntax]
tests/e2e/test_cli_integration.py:76: error: Argument 1 to "run_cli_command" has incompatible type "Sequence[str]"; expected "list[str]"  [arg-type]
tests/e2e/test_cli_integration.py:86: error: Incompatible return value type (got "dict[Sequence[str], bool]", expected "dict[str, bool]")  [return-value]
tests/e2e/test_cli_integration.py:111: error: Argument 1 to "run_cli_command" has incompatible type "Sequence[str]"; expected "list[str]"  [arg-type]
tests/e2e/test_cli_integration.py:121: error: Incompatible return value type (got "dict[Sequence[str], bool]", expected "dict[str, bool]")  [return-value]
tests/e2e/test_cli_integration.py:142: error: Argument 1 to "run_cli_command" has incompatible type "Sequence[str]"; expected "list[str]"  [arg-type]
tests/e2e/test_cli_integration.py:152: error: Incompatible return value type (got "dict[Sequence[str], bool]", expected "dict[str, bool]")  [return-value]
tests/e2e/test_cli_integration.py:173: error: Argument 1 to "run_cli_command" has incompatible type "Sequence[str]"; expected "list[str]"  [arg-type]
tests/e2e/test_cli_integration.py:183: error: Incompatible return value type (got "dict[Sequence[str], bool]", expected "dict[str, bool]")  [return-value]
tests/e2e/test_cli_integration.py:206: error: Argument 1 to "run_cli_command" has incompatible type "object"; expected "list[str]"  [arg-type]
tests/e2e/test_cli_integration.py:206: error: Argument "expect_success" to "run_cli_command" has incompatible type "object"; expected "bool"  [arg-type]
tests/e2e/test_cli_integration.py:217: error: Incompatible return value type (got "dict[object, Union[Any, bool]]", expected "dict[str, bool]")  [return-value]
tests/e2e/quick_cli_test.py:81: error: No overload variant of "run" matches argument types "object", "bool", "bool", "int"  [call-overload]
tests/e2e/quick_cli_test.py:81: note: Possible overload variants:
tests/e2e/quick_cli_test.py:81: note:     def run(args: Union[Union[str, bytes, PathLike[str], PathLike[bytes]], Sequence[Union[str, bytes, PathLike[str], PathLike[bytes]]]], bufsize: int = ..., executable: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., stdin: Union[None, int, IO[Any]] = ..., stdout: Union[None, int, IO[Any]] = ..., stderr: Union[None, int, IO[Any]] = ..., preexec_fn: Optional[Callable[[], Any]] = ..., close_fds: bool = ..., shell: bool = ..., cwd: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., env: Optional[Union[Mapping[bytes, Union[str, bytes, PathLike[str], PathLike[bytes]]], Mapping[str, Union[str, bytes, PathLike[str], PathLike[bytes]]]]] = ..., universal_newlines: Optional[bool] = ..., startupinfo: Any = ..., creationflags: int = ..., restore_signals: bool = ..., start_new_session: bool = ..., pass_fds: Collection[int] = ..., *, capture_output: bool = ..., check: bool = ..., encoding: Optional[str] = ..., errors: Optional[str] = ..., input: Optional[str] = ..., text: Literal[True], timeout: Optional[float] = ..., user: Union[str, int, None] = ..., group: Union[str, int, None] = ..., extra_groups: Optional[Iterable[Union[str, int]]] = ..., umask: int = ...) -> CompletedProcess[str]
tests/e2e/quick_cli_test.py:81: note:     def run(args: Union[Union[str, bytes, PathLike[str], PathLike[bytes]], Sequence[Union[str, bytes, PathLike[str], PathLike[bytes]]]], bufsize: int = ..., executable: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., stdin: Union[None, int, IO[Any]] = ..., stdout: Union[None, int, IO[Any]] = ..., stderr: Union[None, int, IO[Any]] = ..., preexec_fn: Optional[Callable[[], Any]] = ..., close_fds: bool = ..., shell: bool = ..., cwd: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., env: Optional[Union[Mapping[bytes, Union[str, bytes, PathLike[str], PathLike[bytes]]], Mapping[str, Union[str, bytes, PathLike[str], PathLike[bytes]]]]] = ..., universal_newlines: Optional[bool] = ..., startupinfo: Any = ..., creationflags: int = ..., restore_signals: bool = ..., start_new_session: bool = ..., pass_fds: Collection[int] = ..., *, capture_output: bool = ..., check: bool = ..., encoding: str, errors: Optional[str] = ..., input: Optional[str] = ..., text: Optional[bool] = ..., timeout: Optional[float] = ..., user: Union[str, int, None] = ..., group: Union[str, int, None] = ..., extra_groups: Optional[Iterable[Union[str, int]]] = ..., umask: int = ...) -> CompletedProcess[str]
tests/e2e/quick_cli_test.py:81: note:     def run(args: Union[Union[str, bytes, PathLike[str], PathLike[bytes]], Sequence[Union[str, bytes, PathLike[str], PathLike[bytes]]]], bufsize: int = ..., executable: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., stdin: Union[None, int, IO[Any]] = ..., stdout: Union[None, int, IO[Any]] = ..., stderr: Union[None, int, IO[Any]] = ..., preexec_fn: Optional[Callable[[], Any]] = ..., close_fds: bool = ..., shell: bool = ..., cwd: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., env: Optional[Union[Mapping[bytes, Union[str, bytes, PathLike[str], PathLike[bytes]]], Mapping[str, Union[str, bytes, PathLike[str], PathLike[bytes]]]]] = ..., universal_newlines: Optional[bool] = ..., startupinfo: Any = ..., creationflags: int = ..., restore_signals: bool = ..., start_new_session: bool = ..., pass_fds: Collection[int] = ..., *, capture_output: bool = ..., check: bool = ..., encoding: Optional[str] = ..., errors: str, input: Optional[str] = ..., text: Optional[bool] = ..., timeout: Optional[float] = ..., user: Union[str, int, None] = ..., group: Union[str, int, None] = ..., extra_groups: Optional[Iterable[Union[str, int]]] = ..., umask: int = ...) -> CompletedProcess[str]
tests/e2e/quick_cli_test.py:81: note:     def run(args: Union[Union[str, bytes, PathLike[str], PathLike[bytes]], Sequence[Union[str, bytes, PathLike[str], PathLike[bytes]]]], bufsize: int = ..., executable: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., stdin: Union[None, int, IO[Any]] = ..., stdout: Union[None, int, IO[Any]] = ..., stderr: Union[None, int, IO[Any]] = ..., preexec_fn: Optional[Callable[[], Any]] = ..., close_fds: bool = ..., shell: bool = ..., cwd: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., env: Optional[Union[Mapping[bytes, Union[str, bytes, PathLike[str], PathLike[bytes]]], Mapping[str, Union[str, bytes, PathLike[str], PathLike[bytes]]]]] = ..., *, universal_newlines: Literal[True], startupinfo: Any = ..., creationflags: int = ..., restore_signals: bool = ..., start_new_session: bool = ..., pass_fds: Collection[int] = ..., capture_output: bool = ..., check: bool = ..., encoding: Optional[str] = ..., errors: Optional[str] = ..., input: Optional[str] = ..., text: Optional[bool] = ..., timeout: Optional[float] = ..., user: Union[str, int, None] = ..., group: Union[str, int, None] = ..., extra_groups: Optional[Iterable[Union[str, int]]] = ..., umask: int = ...) -> CompletedProcess[str]
tests/e2e/quick_cli_test.py:81: note:     def run(args: Union[Union[str, bytes, PathLike[str], PathLike[bytes]], Sequence[Union[str, bytes, PathLike[str], PathLike[bytes]]]], bufsize: int = ..., executable: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., stdin: Union[None, int, IO[Any]] = ..., stdout: Union[None, int, IO[Any]] = ..., stderr: Union[None, int, IO[Any]] = ..., preexec_fn: Optional[Callable[[], Any]] = ..., close_fds: bool = ..., shell: bool = ..., cwd: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., env: Optional[Union[Mapping[bytes, Union[str, bytes, PathLike[str], PathLike[bytes]]], Mapping[str, Union[str, bytes, PathLike[str], PathLike[bytes]]]]] = ..., universal_newlines: Optional[Literal[False]] = ..., startupinfo: Any = ..., creationflags: int = ..., restore_signals: bool = ..., start_new_session: bool = ..., pass_fds: Collection[int] = ..., *, capture_output: bool = ..., check: bool = ..., encoding: None = ..., errors: None = ..., input: Optional[Buffer] = ..., text: Optional[Literal[False]] = ..., timeout: Optional[float] = ..., user: Union[str, int, None] = ..., group: Union[str, int, None] = ..., extra_groups: Optional[Iterable[Union[str, int]]] = ..., umask: int = ...) -> CompletedProcess[bytes]
tests/e2e/quick_cli_test.py:81: note:     def run(args: Union[Union[str, bytes, PathLike[str], PathLike[bytes]], Sequence[Union[str, bytes, PathLike[str], PathLike[bytes]]]], bufsize: int = ..., executable: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., stdin: Union[None, int, IO[Any]] = ..., stdout: Union[None, int, IO[Any]] = ..., stderr: Union[None, int, IO[Any]] = ..., preexec_fn: Optional[Callable[[], Any]] = ..., close_fds: bool = ..., shell: bool = ..., cwd: Optional[Union[str, bytes, PathLike[str], PathLike[bytes]]] = ..., env: Optional[Union[Mapping[bytes, Union[str, bytes, PathLike[str], PathLike[bytes]]], Mapping[str, Union[str, bytes, PathLike[str], PathLike[bytes]]]]] = ..., universal_newlines: Optional[bool] = ..., startupinfo: Any = ..., creationflags: int
[...384784 characters omitted...]
ned]
mcx3d_finance/tasks/report_tasks.py:461: error: Untyped decorator makes function "generate_cash_flow_async" untyped  [misc]
mcx3d_finance/tasks/report_tasks.py:462: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/tasks/report_tasks.py:525: error: Module has no attribute "calculate_cash_flow"  [attr-defined]
mcx3d_finance/tasks/report_tasks.py:570: error: "ReportGenerator" has no attribute "generate_cash_flow"; maybe "generate_cash_flow_pdf" or "generate_cash_flow_excel"?  [attr-defined]
mcx3d_finance/cli/reports.py:70: error: Incompatible types in assignment (expression has type "int", variable has type "str")  [assignment]
mcx3d_finance/cli/reports.py:71: error: Argument 1 to "datetime" has incompatible type "str"; expected "SupportsIndex"  [arg-type]
mcx3d_finance/cli/reports.py:72: error: Argument 1 to "datetime" has incompatible type "str"; expected "SupportsIndex"  [arg-type]
mcx3d_finance/cli/reports.py:83: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:131: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:164: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:224: error: Argument 3 to "check_data_availability" has incompatible type "Optional[str]"; expected "str"  [arg-type]
mcx3d_finance/cli/reports.py:224: error: Argument 4 to "check_data_availability" has incompatible type "Optional[str]"; expected "str"  [arg-type]
mcx3d_finance/cli/reports.py:292: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:379: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:427: error: Module has no attribute "CashFlowGenerator"; maybe "UKCashFlowGenerator"?  [attr-defined]
mcx3d_finance/cli/reports.py:430: error: Argument 1 to "strptime" of "datetime" has incompatible type "Optional[str]"; expected "str"  [arg-type]
mcx3d_finance/cli/reports.py:431: error: Argument 1 to "strptime" of "datetime" has incompatible type "Optional[str]"; expected "str"  [arg-type]
mcx3d_finance/cli/reports.py:478: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:495: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:512: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/reports.py:558: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/financial_docs.py:28: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/financial_docs.py:28: note: Use "-> None" if function does not return a value
mcx3d_finance/cli/financial_docs.py:75: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/financial_docs.py:157: error: Argument 1 to "for_sample_data" of "FinancialDocumentationBuilder" has incompatible type "Column[int]"; expected "int"  [arg-type]
mcx3d_finance/cli/financial_docs.py:167: error: Incompatible types in assignment (expression has type "Union[Any, str, None]", variable has type "Column[int]")  [assignment]
mcx3d_finance/cli/financial_docs.py:184: error: Argument "organization_id" to "XeroClient" has incompatible type "Column[int]"; expected "int"  [arg-type]
mcx3d_finance/cli/financial_docs.py:223: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/financial_docs.py:228: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/financial_docs.py:246: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/financial_docs.py:296: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/financial_docs.py:357: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:23: error: Function is missing a return type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:33: error: Function is missing a return type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:55: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:64: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:73: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:104: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:109: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:128: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:132: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:151: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:154: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:173: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:179: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:201: error: Function is missing a type annotation  [no-untyped-def]
tests/integration/test_parallel_sync.py:225: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:25: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:25: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:43: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:43: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:57: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:57: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:63: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:63: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:77: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:77: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:85: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:85: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:91: error: Name "click" is not defined  [name-defined]
tests/cli/test_reports.py:95: error: Name "click" is not defined  [name-defined]
tests/cli/test_reports.py:99: error: Name "click" is not defined  [name-defined]
tests/cli/test_reports.py:108: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:116: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:127: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:132: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:138: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:143: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:153: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:160: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:184: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:210: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:226: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:243: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:265: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:290: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:300: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:300: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:310: error: Function is missing a return type annotation  [no-untyped-def]
tests/cli/test_reports.py:310: note: Use "-> None" if function does not return a value
tests/cli/test_reports.py:326: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:344: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:363: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:383: error: Function is missing a type annotation  [no-untyped-def]
tests/cli/test_reports.py:395: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/reporting/utils/output_manager.py:134: error: Incompatible types in assignment (expression has type "Path", variable has type "str")  [assignment]
mcx3d_finance/reporting/utils/output_manager.py:135: error: "str" has no attribute "parent"  [attr-defined]
mcx3d_finance/reporting/utils/output_manager.py:148: error: "str" has no attribute "resolve"  [attr-defined]
mcx3d_finance/reporting/utils/output_manager.py:180: error: Incompatible types in assignment (expression has type "Path", variable has type "str")  [assignment]
mcx3d_finance/reporting/utils/output_manager.py:182: error: "str" has no attribute "exists"  [attr-defined]
mcx3d_finance/reporting/utils/output_manager.py:185: error: "str" has no attribute "stat"  [attr-defined]
mcx3d_finance/reporting/utils/output_manager.py:192: error: "str" has no attribute "suffix"  [attr-defined]
mcx3d_finance/reporting/utils/output_manager.py:193: error: "str" has no attribute "parent"  [attr-defined]
mcx3d_finance/reporting/engine/report_engine.py:63: error: Need type annotation for "_generators" (hint: "_generators: Dict[<type>, <type>] = ...")  [var-annotated]
mcx3d_finance/reporting/engine/report_engine.py:64: error: Need type annotation for "_formatters" (hint: "_formatters: Dict[<type>, <type>] = ...")  [var-annotated]
mcx3d_finance/reporting/engine/report_engine.py:66: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/engine/report_engine.py:126: error: Returning Any from function declared to return "dict[str, Any]"  [no-any-return]
mcx3d_finance/reporting/engine/report_engine.py:158: error: Returning Any from function declared to return "dict[str, Any]"  [no-any-return]
mcx3d_finance/reporting/engine/report_engine.py:161: error: Item "str" of "Union[ReportType, str]" has no attribute "value"  [union-attr]
mcx3d_finance/reporting/engine/report_engine.py:185: error: Argument 1 to "parse_period" of "PeriodParser" has incompatible type "Union[str, datetime]"; expected "str"  [arg-type]
mcx3d_finance/reporting/engine/report_engine.py:218: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/reporting/engine/report_engine.py:238: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/reporting/engine/report_engine.py:242: error: Cannot find implementation or library stub for module named "mcx3d_finance.reporting.engine.formatters.pdf_formatter"  [import-not-found]
mcx3d_finance/reporting/engine/report_engine.py:245: error: Cannot find implementation or library stub for module named "mcx3d_finance.reporting.engine.formatters.excel_formatter"  [import-not-found]
mcx3d_finance/reporting/engine/report_engine.py:248: error: Cannot find implementation or library stub for module named "mcx3d_finance.reporting.engine.formatters.json_formatter"  [import-not-found]
mcx3d_finance/reporting/engine/report_engine.py:251: error: Cannot find implementation or library stub for module named "mcx3d_finance.reporting.engine.formatters.html_formatter"  [import-not-found]
mcx3d_finance/reporting/engine/report_engine.py:266: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:35: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:35: note: Use "-> None" if function does not return a value
mcx3d_finance/cli/valuation.py:72: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:227: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:297: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:417: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:535: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:611: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:649: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:687: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/valuation.py:792: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:21: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:31: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:31: note: Use "-> None" if function does not return a value
mcx3d_finance/cli/data.py:36: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:36: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/cli/data.py:73: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:78: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:161: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:208: error: Incompatible import of "StorageService" (imported name has type "type[OptimizedXeroDataStorageService]", local name has type "type[ParallelXeroDataStorageService]")  [assignment]
mcx3d_finance/cli/data.py:211: error: Incompatible import of "StorageService" (imported name has type "type[XeroDataStorageService]", local name has type "type[ParallelXeroDataStorageService]")  [assignment]
mcx3d_finance/cli/data.py:219: error: Need type annotation for "bar"  [var-annotated]
mcx3d_finance/cli/data.py:223: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:291: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:318: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:344: error: Untyped decorator makes function "download_data" untyped  [misc]
mcx3d_finance/cli/data.py:345: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:461: error: Untyped decorator makes function "export_data" untyped  [misc]
mcx3d_finance/cli/data.py:462: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:519: error: Need type annotation for "export_data" (hint: "export_data: Dict[<type>, <type>] = ...")  [var-annotated]
mcx3d_finance/cli/data.py:524: error: "type[object]" has no attribute "organization_id"  [attr-defined]
mcx3d_finance/cli/data.py:558: error: "type[object]" has no attribute "organization_id"  [attr-defined]
mcx3d_finance/cli/data.py:590: error: Incompatible types in assignment (expression has type "Path", variable has type "Optional[str]")  [assignment]
mcx3d_finance/cli/data.py:594: error: Library stubs not installed for "pandas"  [import-untyped]
mcx3d_finance/cli/data.py:594: note: Hint: "python3 -m pip install pandas-stubs"
mcx3d_finance/cli/data.py:600: error: "type[object]" has no attribute "organization_id"  [attr-defined]
mcx3d_finance/cli/data.py:644: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/data.py:644: note: Use "-> None" if function does not return a value
mcx3d_finance/cli/analytics.py:18: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:18: note: Use "-> None" if function does not return a value
mcx3d_finance/cli/analytics.py:31: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:98: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:156: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:201: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:274: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:319: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:366: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/analytics.py:519: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/xero_import_routes.py:33: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/xero_import_routes.py:84: error: Incompatible types in assignment (expression has type "datetime", variable has type "Column[datetime]")  [assignment]
mcx3d_finance/api/xero_import_routes.py:85: error: Incompatible types in assignment (expression has type "None", variable has type "Column[str]")  [assignment]
mcx3d_finance/api/xero_import_routes.py:122: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/xero_import_routes.py:160: error: "MCPXeroBridge" has no attribute "get_organization_details"; maybe "_get_organization_tokens"?  [attr-defined]
mcx3d_finance/api/xero_import_routes.py:181: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/xero_import_routes.py:225: error: List item 0 has incompatible type "Column[str]"; expected "str"  [list-item]
mcx3d_finance/api/xero_import_routes.py:236: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/xero_import_routes.py:288: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/xero_import_routes.py:309: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/xero_import_routes.py:351: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/webhook_routes.py:25: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/financial_docs.py:42: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/financial_docs.py:109: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/financial_docs.py:119: error: Incompatible types in "await" (actual type "Optional[Any]", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:124: error: Module "mcx3d_finance.tasks.report_tasks" has no attribute "app"  [attr-defined]
mcx3d_finance/api/financial_docs.py:158: error: Incompatible types in "await" (actual type "bool", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:168: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/financial_docs.py:177: error: Module "mcx3d_finance.tasks.report_tasks" has no attribute "app"  [attr-defined]
mcx3d_finance/api/financial_docs.py:220: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/financial_docs.py:234: error: Incompatible types in "await" (actual type "Optional[Any]", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:258: error: Incompatible types in "await" (actual type "bool", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:271: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/financial_docs.py:363: error: Incompatible types in "await" (actual type "Optional[Any]", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:380: error: Incompatible types in "await" (actual type "bool", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:393: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/api/financial_docs.py:409: error: Incompatible types in "await" (actual type "Optional[Any]", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:429: error: Incompatible types in "await" (actual type "bool", expected type "Awaitable[Any]")  [misc]
mcx3d_finance/api/financial_docs.py:442: error: Function is missing a return type annotation  [no-untyped-def]
tests/integration/test_data_pipeline.py:14: error: Function is missing a return type annotation  [no-untyped-def]
tests/integration/test_data_pipeline.py:14: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
tests/integration/test_data_pipeline.py:35: error: Unexpected keyword argument "data" for "ReportGenerator"  [call-arg]
mcx3d_finance/reporting/generator.py:59: note: "ReportGenerator" defined here
tests/integration/test_data_pipeline.py:35: error: Unexpected keyword argument "report_type" for "ReportGenerator"  [call-arg]
mcx3d_finance/reporting/generator.py:59: note: "ReportGenerator" defined here
tests/integration/test_data_pipeline.py:35: error: Unexpected keyword argument "format" for "ReportGenerator"  [call-arg]
mcx3d_finance/reporting/generator.py:59: note: "ReportGenerator" defined here
tests/integration/test_data_pipeline.py:38: error: "ReportGenerator" has no attribute "generate"  [attr-defined]
tests/integration/test_data_pipeline.py:44: error: Function is missing a return type annotation  [no-untyped-def]
tests/integration/test_data_pipeline.py:44: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
tests/integration/test_data_pipeline.py:69: error: Value of type "Coroutine[Any, Any, Any]" must be used  [unused-coroutine]
tests/integration/test_data_pipeline.py:69: note: Are you missing an await?
tests/integration/test_data_pipeline.py:69: error: Argument 1 to "xero_webhook" has incompatible type "dict[str, list[dict[str, dict[str, str]]]]"; expected "Request"  [arg-type]
mcx3d_finance/main.py:28: error: Missing positional arguments "scope", "receive", "send" in call to "add_middleware" of "Starlette"  [call-arg]
mcx3d_finance/main.py:28: error: Argument 1 to "add_middleware" of "Starlette" has incompatible type "type[ErrorHandlingMiddleware]"; expected "_MiddlewareFactory[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]]]"  [arg-type]
mcx3d_finance/main.py:28: note: Following member(s) of "ErrorHandlingMiddleware" have conflicts:
mcx3d_finance/main.py:28: note:     Expected:
mcx3d_finance/main.py:28: note:         def __call__(Callable[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]], Awaitable[None]], /, scope: MutableMapping[str, Any], receive: Callable[[], Awaitable[MutableMapping[str, Any]]], send: Callable[[MutableMapping[str, Any]], Awaitable[None]]) -> Callable[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]], Awaitable[None]]
mcx3d_finance/main.py:28: note:     Got:
mcx3d_finance/main.py:28: note:         def __init__(app: Callable[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]], Awaitable[None]], dispatch: Optional[Callable[[Request, Callable[[Request], Awaitable[Response]]], Awaitable[Response]]] = ...) -> ErrorHandlingMiddleware
mcx3d_finance/main.py:45: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/reporting/interfaces/task_adapter.py:15: error: Skipping analyzing ".exceptions.reporting": module is installed, but missing library stubs or py.typed marker  [import-untyped]
mcx3d_finance/reporting/interfaces/task_adapter.py:16: error: Skipping analyzing ".utils.audit_logger": module is installed, but missing library stubs or py.typed marker  [import-untyped]
mcx3d_finance/reporting/interfaces/task_adapter.py:35: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/interfaces/task_adapter.py:143: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/interfaces/task_adapter.py:244: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/interfaces/task_adapter.py:353: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/interfaces/task_adapter.py:461: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/interfaces/task_adapter.py:646: error: Returning Any from function declared to return "Exception"  [no-any-return]
mcx3d_finance/reporting/interfaces/task_adapter.py:649: error: Returning Any from function declared to return "Exception"  [no-any-return]
mcx3d_finance/reporting/interfaces/cli_adapter.py:218: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/interfaces/api_adapter.py:82: error: Incompatible return value type (got "FileResponse", expected "dict[str, Any]")  [return-value]
mcx3d_finance/reporting/interfaces/api_adapter.py:139: error: Incompatible return value type (got "FileResponse", expected "dict[str, Any]")  [return-value]
mcx3d_finance/reporting/interfaces/api_adapter.py:202: error: Incompatible return value type (got "FileResponse", expected "dict[str, Any]")  [return-value]
mcx3d_finance/reporting/interfaces/api_adapter.py:299: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
mcx3d_finance/reporting/interfaces/api_adapter.py:356: error: Module "mcx3d_finance.tasks.report_tasks" has no attribute "generate_comprehensive_report_async"  [attr-defined]
mcx3d_finance/reporting/interfaces/api_adapter.py:461: error: Argument "path" to "FileResponse" has incompatible type "Optional[Any]"; expected "Union[str, PathLike[str]]"  [arg-type]
mcx3d_finance/reporting/interfaces/api_adapter.py:537: error: "ReportEngine" has no attribute "_validate_organization"  [attr-defined]
mcx3d_finance/cli/main.py:33: error: Function is missing a type annotation  [no-untyped-def]
mcx3d_finance/cli/main.py:67: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/main.py:97: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/main.py:97: note: Use "-> None" if function does not return a value
mcx3d_finance/cli/main.py:119: error: Function is missing a return type annotation  [no-untyped-def]
mcx3d_finance/cli/main.py:119: note: Use "-> None" if function does not return a value
test_financial_docs.py:22: error: Function is missing a return type annotation  [no-untyped-def]
test_financial_docs.py:22: note: Use "-> None" if function does not return a value
test_financial_docs.py:37: error: Function is missing a return type annotation  [no-untyped-def]
test_financial_docs.py:56: error: Function is missing a return type annotation  [no-untyped-def]
test_financial_docs.py:113: error: Function is missing a return type annotation  [no-untyped-def]
test_financial_docs.py:143: error: Function is missing a return type annotation  [no-untyped-def]
test_financial_docs.py:143: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:19: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:19: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:45: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:45: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:64: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:64: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:83: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:83: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:105: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:105: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:126: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:126: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:157: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:157: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:178: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:178: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:204: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:204: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:227: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:227: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:249: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:249: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:279: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:279: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:295: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:295: note: Use "-> None" if function does not return a value
tests/security/test_security_headers.py:320: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_security_headers.py:320: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:27: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:27: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:55: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:55: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:85: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:85: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:108: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:108: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:131: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:131: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:150: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:150: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:185: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:185: note: Use "-> None" if function does not return a value
tests/security/test_endpoint_consolidation.py:210: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_endpoint_consolidation.py:210: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:20: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:20: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:48: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:48: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:77: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:77: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:117: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:117: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:137: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:137: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:162: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:162: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:168: error: Module "mcx3d_finance.main" has no attribute "allowed_origins_str"  [attr-defined]
tests/security/test_cors_configuration.py:187: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:187: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:204: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:204: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:238: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:238: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:246: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:246: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:269: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:269: note: Use "-> None" if function does not return a value
tests/security/test_cors_configuration.py:293: error: Function is missing a return type annotation  [no-untyped-def]
tests/security/test_cors_configuration.py:293: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:22: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:22: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:48: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:54: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:82: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:82: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:119: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:125: error: Function is missing a type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:176: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:176: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:213: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:213: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:249: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:249: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:274: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:274: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:304: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:314: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:336: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:336: note: Use "-> None" if function does not return a value
tests/performance/test_api_performance.py:375: error: Function is missing a return type annotation  [no-untyped-def]
tests/performance/test_api_performance.py:375: note: Use "-> None" if function does not return a value
tests/integration/test_error_handling.py:56: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:57: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:58: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:59: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:62: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:63: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:69: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:75: error: Value of type "object" is not indexable  [index]
tests/integration/test_error_handling.py:138: error: Argument 1 to "post" of "TestClient" has incompatible type "Collection[str]"; expected "Union[URL, str]"  [arg-type]
tests/integration/test_error_handling.py:196: error: Argument 1 to "post" of "TestClient" has incompatible type "Collection[str]"; expected "Union[URL, str]"  [arg-type]
tests/integration/test_error_handling.py:273: error: Argument "data" to "post" of "TestClient" has incompatible type "str"; expected "Optional[Mapping[str, Union[str, Iterable[str], bytes]]]"  [arg-type]
tests/integration/test_api_endpoints.py:47: error: "BaseRoute" has no attribute "path"  [attr-defined]
tests/e2e/test_cli_exports.py:293: error: Item "None" of "Optional[Path]" has no attribute "rglob"  [union-attr]
tests/e2e/test_cli_exports.py:294: error: Item "None" of "Optional[Path]" has no attribute "rglob"  [union-attr]
Found 2878 errors in 214 files (checked 270 source files)