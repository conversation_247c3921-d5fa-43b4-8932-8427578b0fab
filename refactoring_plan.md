# Mypy Type Error Remediation Plan

## 1. Phase 1: Configuration and Baseline

The initial phase focuses on establishing a project-wide `mypy` configuration that enforces a baseline level of strictness. The existing `mypy.ini` will be updated to include more robust checks, ensuring that all new code adheres to a higher standard of type safety.

### Proposed `mypy.ini` Configuration

```ini
[mypy]
python_version = 3.9
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# Stricter checks
disallow_untyped_calls = True
warn_redundant_casts = True
warn_unused_ignores = True
no_implicit_optional = True

# Ignore missing imports for third-party libraries without stubs
[mypy-xero_python.*]
ignore_missing_imports = True

[mypy-psutil]
ignore_missing_imports = True

[mypy-plotly.*]
ignore_missing_imports = True

[mypy-reportlab.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True
```

## 2. Phase 2: Incremental Rollout - The "Leaf-to-Root" Approach

This phase focuses on incrementally adding type hints to the codebase, starting with the "leaves" (utility functions, data models) and moving toward the "roots" (business logic, API endpoints).

### Initial Target Modules

- `mcx3d_finance/utils/`
- `mcx3d_finance/db/models.py`
- `mcx3d_finance/api/schemas.py`

## 3. Phase 3: Addressing High-Impact Areas

This phase will focus on the modules with the highest number of `mypy` errors. Based on the error analysis, the following modules will be prioritized:

- `tests/cli/test_reports.py`
- `mcx3d_finance/cli/reports.py`
- `mcx3d_finance/reporting/engine/generators/financial_statement_generator.py`

## 4. Phase 4: Enforcing Type Safety in CI/CD

To prevent new type errors from being introduced, `mypy` will be integrated into the CI/CD pipeline. This will be achieved by adding a `mypy` check to the `.pre-commit-config.yaml` file.

### `.pre-commit-config.yaml` Integration

```yaml
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: v0.910
  hooks:
    - id: mypy
      additional_dependencies: [
        "pydantic",
        "sqlalchemy",
        "types-requests",
      ]
```

## 5. Guiding Principles

- **Clarity over complexity**: Prioritize clear and understandable type hints.
- **Test coverage**: Ensure that all changes are covered by existing tests.
- **Incremental changes**: Make small, incremental changes to minimize disruption.
- **Team collaboration**: Encourage team members to participate in the refactoring effort.