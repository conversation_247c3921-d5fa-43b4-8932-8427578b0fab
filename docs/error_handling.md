# MCX3D Finance Error Handling Guide

This guide documents the standardized error handling patterns implemented across the MCX3D Finance system.

## Table of Contents
- [Overview](#overview)
- [Error Hierarchy](#error-hierarchy)
- [Error Handling Patterns](#error-handling-patterns)
- [API Error Responses](#api-error-responses)
- [Error Recovery Strategies](#error-recovery-strategies)
- [Logging and Monitoring](#logging-and-monitoring)
- [Best Practices](#best-practices)

## Overview

The MCX3D Finance system implements a comprehensive error handling strategy that ensures:
- Consistent error responses across all API endpoints
- Proper error context and correlation tracking
- User-friendly error messages
- Detailed technical information for debugging
- Automatic error recovery where appropriate
- Structured logging for monitoring and analysis

## Error Hierarchy

### Base Exception Classes

```python
MCX3DException (base class)
├── MCX3DAuthenticationError
│   ├── InvalidCredentialsError
│   ├── AccountLockedError
│   ├── TokenExpiredError
│   └── SessionExpiredError
├── MCX3DFinancialException
│   ├── FinancialCalculationError
│   ├── ValuationError
│   └── ProjectionError
├── MCX3DIntegrationException
│   ├── XeroIntegrationError
│   ├── APIConnectionError
│   ├── RateLimitError
│   └── ServiceUnavailableError
├── MCX3DValidationException
│   ├── ValidationError
│   ├── DataIntegrityError
│   └── BusinessRuleValidationError
└── MCX3DReportingException
    ├── ReportGenerationError
    └── ReportDataValidationError
```

### Exception Properties

All MCX3D exceptions include:
- `message`: Technical error message for logging
- `user_message`: User-friendly error message
- `error_code`: Unique error code for tracking
- `context`: Dictionary of contextual information
- `correlation_id`: Request correlation ID
- `timestamp`: When the error occurred
- `severity`: Error severity level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## Error Handling Patterns

### 1. Using Error Decorators

```python
from mcx3d_finance.exceptions.handlers import handle_errors

@handle_errors(
    operation="calculate_valuation",
    exceptions=[ValuationError, FinancialCalculationError]
)
def calculate_dcf_valuation(data):
    # Function implementation
    pass
```

### 2. Using Error Context Manager

```python
from mcx3d_finance.exceptions.handlers import error_boundary

def process_financial_data(data):
    with error_boundary(
        "process_financial_data",
        organization_id=data['org_id'],
        correlation_id=request.correlation_id
    ):
        # Processing logic
        pass
```

### 3. Database Transaction Handling

```python
from mcx3d_finance.db.error_handling import database_transaction

with database_transaction(
    session, 
    operation="update_organization",
    entity_type="Organization",
    entity_id=org_id
):
    org = session.query(Organization).get(org_id)
    org.name = new_name
    session.flush()
```

### 4. Creating Custom Exceptions

```python
from mcx3d_finance.exceptions.handlers import calculation_error

# For calculation errors
raise calculation_error(
    calculation_type="DCF Valuation",
    message="Discount rate cannot be negative",
    input_data={"discount_rate": discount_rate}
)

# For validation errors
from mcx3d_finance.exceptions.handlers import validation_error

raise validation_error(
    field="start_date",
    value=start_date,
    message="Date must be in YYYY-MM-DD format"
)
```

## API Error Responses

### Standard Error Response Format

```json
{
    "error": "Validation Error",
    "detail": "Invalid date format provided",
    "error_code": "VALIDATION_DATE_FORMAT",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "validation_errors": [
        {
            "field": "start_date",
            "message": "Date must be in YYYY-MM-DD format",
            "type": "value_error.date",
            "context": {"pattern": "YYYY-MM-DD"}
        }
    ],
    "debug_info": {
        "technical_message": "datetime.strptime() failed",
        "context": {...},
        "traceback": "..."
    }
}
```

### HTTP Status Code Mapping

| Exception Type | HTTP Status Code | Description |
|---------------|------------------|-------------|
| ValidationError | 400 | Bad Request |
| MCX3DAuthenticationError | 401 | Unauthorized |
| MFARequiredError | 403 | Forbidden |
| SessionNotFoundError | 404 | Not Found |
| DataIntegrityError | 409 | Conflict |
| FinancialCalculationError | 422 | Unprocessable Entity |
| AccountLockedError | 423 | Locked |
| RateLimitError | 429 | Too Many Requests |
| MCX3DSystemError | 500 | Internal Server Error |
| XeroIntegrationError | 502 | Bad Gateway |
| ServiceUnavailableError | 503 | Service Unavailable |

## Error Recovery Strategies

### 1. Circuit Breaker Pattern

```python
from mcx3d_finance.exceptions.recovery import with_circuit_breaker

@with_circuit_breaker(
    name="xero_api",
    config=CircuitBreakerConfig(
        failure_threshold=5,
        recovery_timeout=60.0
    )
)
def call_xero_api():
    # API call implementation
    pass
```

### 2. Retry with Exponential Backoff

```python
from mcx3d_finance.exceptions.recovery import with_retry, RetryConfig

@with_retry(
    config=RetryConfig(
        max_attempts=5,
        initial_delay=1.0,
        exponential_base=2.0
    )
)
def unstable_operation():
    # Operation that might fail transiently
    pass
```

### 3. Graceful Degradation

```python
from mcx3d_finance.exceptions.recovery import GracefulDegradation

degradation = GracefulDegradation(cache_client=redis_client)

@degradation.with_fallback(
    operation="get_exchange_rates",
    cache_key="exchange_rates_latest",
    cache_ttl=3600
)
def get_current_exchange_rates():
    # Fetch from external API
    pass
```

## Logging and Monitoring

### Structured Logging

All errors are logged with structured JSON format including:
- Correlation ID for request tracking
- Error context and metadata
- User and organization context
- Performance metrics (duration, etc.)
- Full stack traces in non-production

### Using the Error Logger

```python
from mcx3d_finance.utils.error_logging import error_logger, log_exception

# Log an exception with context
log_exception(
    error,
    message="Failed to process payment",
    payment_id=payment_id,
    amount=amount,
    correlation_id=correlation_id
)

# Log an operation
error_logger.log_operation(
    operation="generate_report",
    status="success",
    duration_ms=1234.5,
    metadata={"report_type": "income_statement"}
)
```

### Audit Logging

```python
from mcx3d_finance.utils.error_logging import audit_logger

# Log financial operations
audit_logger.log_financial_operation(
    operation_type="update",
    entity_type="transaction",
    entity_id=transaction_id,
    user_id=current_user.id,
    organization_id=org_id,
    changes={"amount": {"old": 100, "new": 150}}
)
```

## Best Practices

### 1. Always Provide Context

```python
# Good
raise FinancialCalculationError(
    message="Invalid cash flow projection",
    user_message="The cash flow for year 3 cannot be negative",
    context={
        "year": 3,
        "cash_flow": cash_flow,
        "calculation_type": "DCF"
    }
)

# Bad
raise Exception("Invalid cash flow")
```

### 2. Use Appropriate Exception Types

```python
# Good - specific exception type
raise ValuationError("Terminal growth rate exceeds discount rate")

# Bad - generic exception
raise Exception("Invalid valuation parameters")
```

### 3. Include User-Friendly Messages

```python
# Good
raise DataIntegrityError(
    message="Duplicate invoice number in database",
    user_message="An invoice with this number already exists. Please use a different invoice number."
)

# Bad
raise DataIntegrityError("UNIQUE constraint failed: invoices.invoice_number")
```

### 4. Handle Errors at the Right Level

```python
# Handle at service layer
def calculate_financial_metrics(data):
    try:
        # Calculation logic
        return results
    except FinancialCalculationError:
        # Service-level handling
        raise
    except Exception as e:
        # Convert unexpected errors
        raise FinancialCalculationError(
            "Unexpected error in metric calculation",
            original_exception=e
        )
```

### 5. Use Correlation IDs

```python
from mcx3d_finance.utils.error_logging import set_correlation_id

# Set correlation ID at request entry
set_correlation_id(request.headers.get("X-Correlation-ID", str(uuid4())))

# It will be automatically included in all error contexts
```

### 6. Log Errors Appropriately

```python
# Log with appropriate severity
if isinstance(error, ValidationError):
    logger.warning("Validation failed", extra={"error": error.to_dict()})
elif isinstance(error, MCX3DSystemError):
    logger.error("System error", exc_info=True, extra={"error": error.to_dict()})
```

### 7. Test Error Scenarios

```python
def test_invalid_discount_rate():
    with pytest.raises(ValuationError) as exc_info:
        calculate_dcf(cash_flows=[100, 200], discount_rate=-0.1)
    
    assert "cannot be negative" in str(exc_info.value)
    assert exc_info.value.error_code.startswith("MCX3D_VALUATION")
```

## Integration Examples

### API Endpoint with Error Handling

```python
@router.post("/reports/generate")
async def generate_report(
    request: ReportRequest,
    current_user: User = Depends(get_current_user)
):
    with error_boundary(
        "generate_report",
        user_id=current_user.id,
        organization_id=request.organization_id,
        report_type=request.report_type
    ):
        # Validate request
        if request.start_date > request.end_date:
            raise validation_error(
                field="date_range",
                value=f"{request.start_date} to {request.end_date}",
                message="End date must be after start date"
            )
        
        # Generate report
        result = await report_service.generate(request)
        return result
```

### Service Method with Recovery

```python
@with_retry(config=RetryConfig(max_attempts=3))
@with_circuit_breaker(name="xero_sync")
async def sync_xero_data(organization_id: int):
    with database_transaction(
        session,
        operation="sync_xero_data",
        entity_type="Organization",
        entity_id=organization_id
    ):
        try:
            # Sync logic
            data = await xero_client.fetch_data()
            await process_and_store(data)
        except XeroIntegrationError as e:
            # Log and convert to user-friendly error
            logger.error(f"Xero sync failed: {e}")
            raise ServiceUnavailableError(
                message=str(e),
                user_message="Unable to sync with Xero. Please try again later.",
                context={"organization_id": organization_id}
            )
```

## Monitoring and Alerts

### Error Rate Monitoring

Monitor these key metrics:
- Error rate by exception type
- Error rate by endpoint
- Circuit breaker state changes
- Retry attempt rates
- Database deadlock frequency

### Alert Thresholds

Configure alerts for:
- Error rate > 5% for any endpoint
- Circuit breaker open events
- Database connection pool exhaustion
- Authentication failure spikes
- 5xx error rate > 1%

### Performance Impact

Track error handling overhead:
- Retry delay impact on response times
- Circuit breaker rejection rates
- Error logging performance
- Database rollback frequency

## Migration Guide

For existing code, follow these steps:

1. Replace generic try/except blocks with `@handle_errors` decorator
2. Convert `raise Exception()` to appropriate MCX3D exception types
3. Add user-friendly messages to all exceptions
4. Include correlation IDs in error contexts
5. Use `error_boundary` for operation-level error handling
6. Implement retry logic for transient failures
7. Add circuit breakers for external service calls

## Conclusion

This standardized error handling approach ensures:
- Consistent error responses across the application
- Better debugging through correlation tracking
- Improved user experience with friendly messages
- Automatic recovery from transient failures
- Comprehensive monitoring and alerting capabilities

For questions or additional patterns, consult the technical team or refer to the code examples in the `tests/` directory.