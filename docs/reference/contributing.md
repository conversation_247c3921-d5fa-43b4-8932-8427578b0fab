# Contributing Guidelines

Guidelines for contributing to the MCX3D Financial System project.

## 🎯 Overview

We welcome contributions to the MCX3D Financial System! This document provides guidelines for contributing code, documentation, bug reports, and feature requests.

### Code of Conduct

- **Be respectful**: Treat everyone with respect and professionalism
- **Be collaborative**: Work together to improve the project
- **Be inclusive**: Welcome contributors from all backgrounds
- **Be constructive**: Provide helpful feedback and suggestions

---

## 🚀 Getting Started

### Development Environment Setup

1. **Clone the Repository**
   ```bash
   git clone https://github.com/mcx3d/mcx3d-financials.git
   cd mcx3d-financials/v2
   ```

2. **Set Up Development Environment**
   ```bash
   # Copy environment template
   cp .env.example .env.development
   
   # Generate security keys
   python -m mcx3d_finance.utils.generate_keys >> .env.development
   
   # Start development environment
   docker-compose up --build
   ```

3. **Verify Setup**
   ```bash
   # Run tests
   docker-compose exec web pytest
   
   # Check code quality
   docker-compose exec web black mcx3d_finance/ --check
   docker-compose exec web flake8 mcx3d_finance/
   docker-compose exec web mypy mcx3d_finance/
   ```

### Development Tools

| Tool | Purpose | Command |
|------|---------|---------|
| **Black** | Code formatting | `black mcx3d_finance/` |
| **Flake8** | Code linting | `flake8 mcx3d_finance/` |
| **MyPy** | Type checking | `mypy mcx3d_finance/` |
| **pytest** | Testing | `pytest` |
| **pre-commit** | Git hooks | `pre-commit install` |

---

## 🔀 Contribution Workflow

### Branch Naming Convention

Use descriptive branch names with prefixes:

```bash
# Feature branches
feature/add-cash-flow-reports
feature/improve-xero-sync
feature/enhance-security

# Bug fix branches
fix/balance-sheet-calculation
fix/xero-token-refresh
fix/docker-permissions

# Documentation branches
docs/update-api-reference
docs/add-deployment-guide
docs/fix-typos

# Refactoring branches
refactor/extract-financial-calculator
refactor/improve-error-handling
refactor/optimize-database-queries
```

### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow coding standards
   - Add tests for new functionality
   - Update documentation
   - Ensure all tests pass

3. **Commit Changes**
   ```bash
   # Use conventional commits
   git commit -m "feat: add cash flow statement generation
   
   - Implement indirect method cash flow calculation
   - Add PDF and Excel export options
   - Include comparative period support
   - Add comprehensive test coverage
   
   Closes #123"
   ```

4. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   # Create pull request through GitHub interface
   ```

### Commit Message Format

Use [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### Commit Types

| Type | Description | Example |
|------|-------------|---------|
| `feat` | New feature | `feat: add SaaS valuation model` |
| `fix` | Bug fix | `fix: correct balance sheet totals` |
| `docs` | Documentation | `docs: update API reference` |
| `style` | Code style changes | `style: format code with black` |
| `refactor` | Code refactoring | `refactor: extract report generator` |
| `test` | Add/update tests | `test: add integration tests for Xero sync` |
| `chore` | Maintenance tasks | `chore: update dependencies` |
| `perf` | Performance improvements | `perf: optimize financial calculations` |

#### Examples

```bash
# Feature addition
git commit -m "feat(reports): add comparative balance sheet analysis

- Support multiple comparison periods
- Add variance calculations
- Include percentage change analysis
- Export to PDF and Excel formats

Closes #456"

# Bug fix
git commit -m "fix(xero): handle token refresh edge case

- Fix race condition in token refresh
- Add proper error handling
- Include retry mechanism
- Add unit tests for edge cases

Fixes #789"

# Documentation update
git commit -m "docs: improve Xero integration guide

- Add OAuth 2.0 troubleshooting section
- Include webhook setup instructions
- Update configuration examples
- Add FAQ section"
```

---

## 🧪 Testing Guidelines

### Test Structure

```
tests/
├── unit/                 # Fast, isolated unit tests
│   ├── core/            # Business logic tests
│   ├── api/             # API endpoint tests
│   └── integrations/    # Integration component tests
├── integration/         # Database and external service tests
│   ├── database/        # Database integration tests
│   └── xero/           # Xero API integration tests
├── e2e/                # End-to-end system tests
└── performance/        # Performance and load tests
```

### Test Requirements

- **Unit tests**: ≥85% code coverage required
- **Integration tests**: Test database operations and external APIs
- **E2E tests**: Test complete user workflows
- **Performance tests**: Ensure response times and resource usage

### Writing Tests

#### Unit Test Example
```python
# tests/unit/core/test_financial_calculators.py
import pytest
from decimal import Decimal
from mcx3d_finance.core.financial_calculators import UKFinancialCalculator

class TestUKFinancialCalculator:
    def test_calculate_current_ratio(self):
        """Test current ratio calculation."""
        calculator = UKFinancialCalculator(organization_id=1)
        
        # Setup test data
        current_assets = Decimal('100000')
        current_liabilities = Decimal('50000')
        
        # Execute
        ratio = calculator.calculate_current_ratio(
            current_assets, current_liabilities
        )
        
        # Assert
        assert ratio == Decimal('2.00')
        
    def test_calculate_current_ratio_zero_liabilities(self):
        """Test current ratio with zero liabilities."""
        calculator = UKFinancialCalculator(organization_id=1)
        
        with pytest.raises(ValueError, match="Division by zero"):
            calculator.calculate_current_ratio(
                Decimal('100000'), Decimal('0')
            )
```

#### Integration Test Example
```python
# tests/integration/test_xero_sync.py
import pytest
from mcx3d_finance.integrations.xero import XeroDataSync
from mcx3d_finance.db.models import Organization

@pytest.mark.integration
class TestXeroDataSync:
    def test_sync_accounts_from_xero(self, test_db, xero_client_mock):
        """Test syncing chart of accounts from Xero."""
        # Setup
        org = Organization.create(name="Test Org")
        sync = XeroDataSync(organization_id=org.id)
        
        # Mock Xero response
        xero_client_mock.accounts.all.return_value = [
            {"AccountID": "123", "Name": "Sales", "Type": "REVENUE"},
            {"AccountID": "456", "Name": "Rent", "Type": "EXPENSE"}
        ]
        
        # Execute
        result = sync.sync_accounts()
        
        # Assert
        assert result.success is True
        assert result.accounts_created == 2
```

### Running Tests

```bash
# Run all tests
docker-compose exec web pytest

# Run specific test categories
docker-compose exec web pytest -m unit
docker-compose exec web pytest -m integration
docker-compose exec web pytest -m e2e

# Run with coverage
docker-compose exec web pytest --cov=mcx3d_finance --cov-report=html

# Run specific test file
docker-compose exec web pytest tests/unit/core/test_financial_calculators.py -v

# Run with debugging
docker-compose exec web pytest tests/unit/core/test_financial_calculators.py::TestUKFinancialCalculator::test_profit_and_loss_generation -v -s
```

---

## 📝 Code Standards

### Python Code Style

#### Formatting with Black
```bash
# Format all code
black mcx3d_finance/

# Check formatting
black mcx3d_finance/ --check

# Format specific file
black mcx3d_finance/core/financial_calculators.py
```

#### Linting with Flake8
```bash
# Lint all code
flake8 mcx3d_finance/

# Lint specific file
flake8 mcx3d_finance/core/financial_calculators.py
```

#### Type Checking with MyPy
```bash
# Type check all code
mypy mcx3d_finance/

# Type check specific module
mypy mcx3d_finance/core/
```

### Code Quality Standards

#### Function Documentation
```python
def calculate_dcf_valuation(
    cash_flows: List[Decimal],
    discount_rate: Decimal,
    terminal_growth_rate: Decimal
) -> DCFValuationResult:
    """Calculate discounted cash flow valuation.
    
    Args:
        cash_flows: List of projected annual cash flows
        discount_rate: Discount rate for present value calculation
        terminal_growth_rate: Growth rate for terminal value
        
    Returns:
        DCFValuationResult containing valuation details
        
    Raises:
        ValueError: If discount rate or growth rate is invalid
        
    Example:
        >>> cash_flows = [Decimal('100000'), Decimal('110000')]
        >>> result = calculate_dcf_valuation(
        ...     cash_flows, Decimal('0.10'), Decimal('0.03')
        ... )
        >>> result.enterprise_value
        Decimal('1500000')
    """
    # Implementation here
```

#### Class Documentation
```python
class UKFinancialCalculator:
    """UK FRS 102 compliant financial statement calculator.
    
    This class provides methods for generating financial statements
    and calculating financial ratios in compliance with UK GAAP
    (FRS 102) accounting standards.
    
    Attributes:
        organization_id: ID of the organization
        accounting_period: Current accounting period
        
    Example:
        >>> calculator = UKFinancialCalculator(organization_id=1)
        >>> balance_sheet = calculator.generate_balance_sheet(
        ...     date(2024, 12, 31)
        ... )
    """
```

#### Error Handling
```python
# Good: Specific exception types with clear messages
def validate_organization_access(user_id: int, organization_id: int) -> None:
    """Validate user has access to organization."""
    if not organization_exists(organization_id):
        raise OrganizationNotFoundError(
            f"Organization {organization_id} does not exist"
        )
    
    if not user_has_access(user_id, organization_id):
        raise UnauthorizedAccessError(
            f"User {user_id} does not have access to organization {organization_id}"
        )

# Bad: Generic exceptions
def validate_organization_access(user_id: int, organization_id: int) -> None:
    if not organization_exists(organization_id):
        raise Exception("Invalid organization")  # Too generic
```

#### Database Operations
```python
# Good: Use repositories and proper error handling
class TransactionRepository:
    def get_transactions_by_date_range(
        self,
        organization_id: int,
        start_date: date,
        end_date: date
    ) -> List[Transaction]:
        """Get transactions within date range."""
        try:
            return self.session.query(Transaction).filter(
                Transaction.organization_id == organization_id,
                Transaction.date >= start_date,
                Transaction.date <= end_date
            ).all()
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving transactions: {e}")
            raise DatabaseError("Failed to retrieve transactions") from e
```

### Security Guidelines

#### Input Validation
```python
from pydantic import BaseModel, validator

class FinancialReportRequest(BaseModel):
    organization_id: int
    start_date: date
    end_date: date
    
    @validator('organization_id')
    def validate_organization_id(cls, v):
        if v <= 0:
            raise ValueError('Organization ID must be positive')
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v
```

#### SQL Injection Prevention
```python
# Good: Use ORM or parameterized queries
def get_transactions(organization_id: int, account_code: str) -> List[Transaction]:
    return session.query(Transaction).filter(
        Transaction.organization_id == organization_id,
        Transaction.account_code == account_code
    ).all()

# Bad: String concatenation
def get_transactions(organization_id: int, account_code: str) -> List[Transaction]:
    query = f"SELECT * FROM transactions WHERE organization_id = {organization_id}"
    return session.execute(query).fetchall()  # SQL injection risk
```

#### Sensitive Data Handling
```python
import logging
from mcx3d_finance.security import encrypt_field, decrypt_field

# Good: Log without sensitive data
logger.info(f"Processing Xero sync for organization {organization_id}")

# Bad: Log sensitive data
logger.info(f"Xero token: {xero_token}")  # Never log tokens

# Good: Encrypt sensitive data
encrypted_token = encrypt_field(xero_token)
organization.xero_token_encrypted = encrypted_token

# Access sensitive data securely
xero_token = decrypt_field(organization.xero_token_encrypted)
```

---

## 📚 Documentation Standards

### API Documentation

Use FastAPI automatic documentation with detailed docstrings:

```python
from fastapi import APIRouter, Depends, HTTPException
from typing import List

@router.post("/reports/balance-sheet", response_model=BalanceSheetResponse)
async def generate_balance_sheet(
    request: BalanceSheetRequest,
    current_user: User = Depends(get_current_user)
) -> BalanceSheetResponse:
    """Generate balance sheet report.
    
    Generate a balance sheet report for the specified organization and date.
    The report can be generated in multiple formats (PDF, Excel, JSON, HTML).
    
    Args:
        request: Balance sheet generation request parameters
        current_user: Authenticated user making the request
        
    Returns:
        BalanceSheetResponse containing the generated report
        
    Raises:
        HTTPException: 404 if organization not found
        HTTPException: 403 if user lacks permission
        HTTPException: 400 if request parameters are invalid
        
    Example:
        ```json
        {
            "organization_id": 1,
            "date": "2024-12-31",
            "format": "pdf",
            "include_comparatives": true
        }
        ```
    """
```

### Code Comments

```python
class XeroRateLimiter:
    """Rate limiter for Xero API calls."""
    
    def __init__(self, requests_per_second: int = 5):
        self.requests_per_second = requests_per_second
        # Use token bucket algorithm for smooth rate limiting
        self._tokens = requests_per_second
        self._last_update = time.time()
    
    def acquire(self) -> bool:
        """Acquire a token for making an API request.
        
        Returns:
            True if token acquired, False if rate limit exceeded
        """
        now = time.time()
        
        # Add tokens based on elapsed time
        elapsed = now - self._last_update
        self._tokens = min(
            self.requests_per_second,
            self._tokens + elapsed * self.requests_per_second
        )
        self._last_update = now
        
        # Check if we have tokens available
        if self._tokens >= 1:
            self._tokens -= 1
            return True
        
        return False
```

### README Updates

When adding new features, update relevant README files:

- Main project README
- Module-specific READMEs
- API documentation
- User guides

---

## 🐛 Bug Reports

### Bug Report Template

When reporting bugs, use this template:

```markdown
## Bug Description
Brief description of the bug.

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What you expected to happen.

## Actual Behavior
What actually happened.

## Environment
- OS: [e.g., Ubuntu 20.04]
- Docker version: [e.g., 20.10.7]
- Python version: [e.g., 3.9.5]
- MCX3D version: [e.g., 2.0.0]

## Additional Context
Any additional information, logs, or screenshots.

## Possible Solution
If you have ideas for fixing the bug.
```

### Critical Bug Process

For critical bugs affecting production:

1. **Label as `critical`** in GitHub issue
2. **Notify team immediately** via Slack/email
3. **Create hotfix branch** from main
4. **Implement minimal fix** with tests
5. **Fast-track review** and deployment
6. **Follow up with comprehensive fix** if needed

---

## ✨ Feature Requests

### Feature Request Template

```markdown
## Feature Description
Clear description of the requested feature.

## Use Case
Why is this feature needed? What problem does it solve?

## Proposed Solution
How should this feature work?

## Alternative Solutions
Other ways to solve the same problem.

## Additional Context
Any additional information or examples.

## Priority
- [ ] Critical (blocking current work)
- [ ] High (important for upcoming milestone)
- [ ] Medium (would be nice to have)
- [ ] Low (future consideration)
```

### Feature Development Process

1. **Create GitHub issue** with feature request template
2. **Discuss in team meeting** or GitHub comments
3. **Create technical design** document if complex
4. **Get approval** from maintainers
5. **Implement feature** following development workflow
6. **Add tests and documentation**
7. **Submit pull request** for review

---

## 🔍 Code Review Guidelines

### Review Checklist

#### Functionality
- [ ] Code solves the stated problem
- [ ] Edge cases are handled appropriately
- [ ] Error handling is comprehensive
- [ ] Performance implications considered

#### Code Quality
- [ ] Code follows project standards
- [ ] Functions and classes are appropriately sized
- [ ] Names are clear and descriptive
- [ ] Comments explain complex logic

#### Testing
- [ ] Unit tests cover new functionality
- [ ] Integration tests for database changes
- [ ] Edge cases are tested
- [ ] Test coverage meets requirements (≥85%)

#### Security
- [ ] Input validation is implemented
- [ ] SQL injection risks eliminated
- [ ] Sensitive data is protected
- [ ] Authentication/authorization correct

#### Documentation
- [ ] Code is documented appropriately
- [ ] API documentation updated
- [ ] User-facing changes documented
- [ ] Breaking changes noted

### Review Process

1. **Automated checks** must pass first
2. **Self-review** your own code
3. **Request reviewers** (at least 2 for major changes)
4. **Address feedback** promptly and thoroughly
5. **Update based on review** comments
6. **Maintainer approval** required for merge

### Providing Good Feedback

#### Constructive Comments
```markdown
# Good: Specific and constructive
Consider using a more descriptive variable name here. 
`total_revenue` would be clearer than `tr`.

# Good: Suggests improvement
This could be optimized using a dictionary lookup instead of 
multiple if-statements. Would improve readability and performance.

# Bad: Not constructive
This is wrong.

# Bad: Too vague
Clean this up.
```

#### Approving Changes
```markdown
# When approving
LGTM! Great work on the error handling. The test coverage 
is excellent and the documentation is clear.

# When requesting changes
Looks good overall! Just a few minor items:
1. Add input validation for the date parameter
2. Update the docstring to include the new parameter
3. Consider adding a test for the edge case we discussed
```

---

## 🚀 Release Process

### Version Numbering

We use [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes (e.g., 1.0.0 → 2.0.0)
- **MINOR**: New features (e.g., 1.0.0 → 1.1.0)
- **PATCH**: Bug fixes (e.g., 1.0.0 → 1.0.1)

### Release Workflow

1. **Create release branch** from develop
   ```bash
   git checkout -b release/2.1.0
   ```

2. **Update version numbers** in relevant files
3. **Update CHANGELOG.md** with release notes
4. **Test thoroughly** in staging environment
5. **Create pull request** to main branch
6. **Tag release** after merge
   ```bash
   git tag -a v2.1.0 -m "Release version 2.1.0"
   git push origin v2.1.0
   ```

### Changelog Format

```markdown
# Changelog

## [2.1.0] - 2024-01-15

### Added
- New SaaS valuation model with industry benchmarks
- Webhook support for real-time Xero synchronization
- Multi-currency support for international operations

### Changed
- Improved performance of financial calculations (30% faster)
- Updated UI for better user experience
- Enhanced error messages with actionable guidance

### Fixed
- Fixed balance sheet totals calculation edge case
- Resolved Xero token refresh race condition
- Corrected PDF generation on certain Linux distributions

### Security
- Added rate limiting to prevent API abuse
- Improved input validation for all endpoints
- Enhanced audit logging for financial operations

### Deprecated
- Legacy API endpoints (will be removed in 3.0.0)
- Old configuration format (migration guide available)
```

---

## 🤝 Community

### Getting Help

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and discussions
- **Documentation**: Check the docs first
- **Stack Overflow**: Use tag `mcx3d-financial`

### Communication Channels

- **GitHub Issues**: Primary communication for development
- **Pull Request Comments**: Code review discussions
- **Team Slack**: Internal team communication (contributors)

### Recognition

Contributors are recognized in:
- **CONTRIBUTORS.md** file
- **Release notes** for significant contributions
- **Annual contributor summary**

---

## 📋 Contributor Checklist

### Before Your First Contribution

- [ ] Read and understand the contributing guidelines
- [ ] Set up development environment
- [ ] Run tests to ensure everything works
- [ ] Install pre-commit hooks
- [ ] Familiarize yourself with the codebase structure

### For Each Contribution

- [ ] Create feature branch with descriptive name
- [ ] Write tests for new functionality
- [ ] Update documentation as needed
- [ ] Run all tests and ensure they pass
- [ ] Check code formatting with Black
- [ ] Verify linting passes with Flake8
- [ ] Confirm type checking passes with MyPy
- [ ] Write clear commit messages
- [ ] Create descriptive pull request
- [ ] Respond to review feedback promptly

### Before Requesting Review

- [ ] Self-review your changes
- [ ] Test manually in development environment
- [ ] Ensure all CI checks pass
- [ ] Update relevant documentation
- [ ] Add yourself to CONTRIBUTORS.md if first contribution

---

## 🎉 Thank You!

Thank you for contributing to MCX3D Financial System! Your contributions help make financial reporting more accessible and reliable for businesses everywhere.

### Questions?

If you have any questions about contributing, please:

1. Check the [documentation](../README.md)
2. Search [existing issues](https://github.com/mcx3d/mcx3d-financials/issues)
3. Create a new issue with the `question` label
4. Reach out to the maintainers

---

**Happy coding!** 🚀

---

**Last Updated**: January 2025  
**Guidelines Version**: 2.0.0  
**Project**: MCX3D Financial System v2.0+