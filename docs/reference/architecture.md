# System Architecture

Complete architectural overview of the MCX3D Financial System.

## 🏗️ System Overview

MCX3D Financial System is a **financial analytics platform** designed for UK financial compliance with modern cloud-native architecture principles.

### Key Characteristics
- **Multi-tenant** financial platform with organization-scoped data
- **UK FRS 102 compliant** financial calculations and reporting
- **Enterprise-grade security** with comprehensive audit trails
- **Real-time data synchronization** with Xero accounting platform
- **Scalable microservices** architecture with Docker containerization

---

## 🎯 Application Entry Points

The system provides two main application entry points:

| Entry Point | Purpose | Use Case |
|-------------|---------|----------|
| `mcx3d_finance/main.py` | Basic FastAPI application | Development and testing |
| `mcx3d_finance/main_with_monitoring.py` | Production application | Production with monitoring, metrics, BI |

### Development Entry Point
```python
# mcx3d_finance/main.py
# Basic FastAPI app for development
# - Simple health checks
# - Core API endpoints
# - Minimal middleware
```

### Production Entry Point
```python
# mcx3d_finance/main_with_monitoring.py
# Full production application with:
# - Comprehensive monitoring (Prometheus metrics)
# - Business intelligence collection
# - Audit logging and correlation IDs
# - Multi-channel alerting
# - Advanced health checks (basic/comprehensive/business)
```

---

## 🏢 Layered Architecture

The system follows a clean layered architecture pattern:

```
┌─────────────────────────────────────────────────────────────┐
│                        API Layer                            │
│                   (mcx3d_finance/api/)                      │
├─────────────────────────────────────────────────────────────┤
│                    Business Logic Layer                     │
│                   (mcx3d_finance/core/)                     │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                             │
│                    (mcx3d_finance/db/)                      │
├─────────────────────────────────────────────────────────────┤
│                   Integration Layer                         │
│               (mcx3d_finance/integrations/)                 │
├─────────────────────────────────────────────────────────────┤
│                 Monitoring & Security                       │
│         (mcx3d_finance/monitoring/, security/)              │
└─────────────────────────────────────────────────────────────┘
```

### API Layer (`mcx3d_finance/api/`)
**Responsibilities**: External interface and request handling
- **FastAPI endpoints** with OpenAPI documentation
- **Authentication/Authorization** with JWT + MFA support
- **Multi-format report generation** (PDF, Excel, HTML, JSON)
- **Real-time health monitoring** (basic/comprehensive/business levels)
- **Comprehensive security middleware** (CORS, CSRF, rate limiting)

**Key Components**:
- `/auth/` - Authentication and authorization endpoints
- `/reports/` - Financial report generation endpoints
- `/organizations/` - Multi-tenant organization management
- `/health/` - System health and monitoring endpoints

### Business Logic Layer (`mcx3d_finance/core/`)
**Responsibilities**: Core financial calculations and business rules
- **UK FRS 102 compliant** financial calculations
- **Valuation models** (DCF, multiples, SaaS-specific)
- **Data validation** with multi-tier quality assurance
- **Account classification** and transaction processing
- **Performance optimization** with Redis caching

**Key Components**:
- `financial_calculators.py` - Core financial statement generation
- `account_classifications.py` - UK GAAP account classifications
- `data_validation.py` - Multi-tier validation engine
- `valuation/` - DCF, multiples, and SaaS valuation models

### Data Layer (`mcx3d_finance/db/`)
**Responsibilities**: Data persistence and management
- **Multi-tenant PostgreSQL schema** with SQLAlchemy ORM
- **Organization → Account → Transaction → Invoice** hierarchy
- **Bank reconciliation** and transaction matching
- **Comprehensive audit trails** and sync status tracking

**Key Components**:
- `models/` - SQLAlchemy data models
- `migrations/` - Alembic database migrations
- `repositories/` - Data access layer abstractions

### Integration Layer (`mcx3d_finance/integrations/`)
**Responsibilities**: External system integrations
- **Xero OAuth 2.0** with intelligent rate limiting
- **Real-time webhook processing** and data synchronization
- **MCP (Model Control Protocol)** for enhanced data processing
- **Fault-tolerant design** with circuit breakers and retry logic

**Key Components**:
- `xero/` - Xero API integration and OAuth handling
- `webhooks/` - Real-time webhook processing
- `rate_limiter.py` - Intelligent API rate limiting
- `circuit_breaker.py` - Fault tolerance patterns

### Monitoring & Security Layer
**Responsibilities**: System observability and security
- **Prometheus metrics** with business intelligence
- **Correlation ID tracking** and structured logging
- **Multi-channel alerting** (email, Slack, PagerDuty)
- **Enterprise security** (encryption, audit logging, MFA)

---

## 🔧 Key Design Patterns

### Multi-Tenant Architecture
**Pattern**: Organization-scoped data isolation
- All data operations are organization-scoped
- `Organization` model is central with relationships to all financial entities
- Row-level security ensures data isolation between tenants
- Shared application infrastructure with isolated data

```python
# Example: Organization-scoped query
def get_transactions(organization_id: int):
    return session.query(Transaction).filter(
        Transaction.organization_id == organization_id
    ).all()
```

### UK Financial Compliance
**Pattern**: Standards-based financial calculations
- Built around **FRS 102 (UK GAAP)** standards
- Financial calculations in `core/financial_calculators.py`
- Account classifications in `core/account_classifications.py`
- Ensures compliance with UK reporting requirements

### Async Processing
**Pattern**: Background task processing for heavy operations
- **Celery** background tasks for reports, valuations, data imports
- Redis as message broker and result backend
- Task definitions in `tasks/` directory
- Monitoring and retry mechanisms for failed tasks

```python
# Example: Async report generation
@celery_app.task
def generate_balance_sheet(organization_id: int, date: str):
    # Heavy processing in background
    pass
```

### External Integration Strategy
**Pattern**: Robust external API integration
- **OAuth 2.0** authentication with Xero
- **Intelligent rate limiting** to respect API limits
- **Circuit breaker pattern** for fault tolerance
- **Real-time synchronization** via webhooks
- **Comprehensive error handling** and retry logic

### Security-First Design
**Pattern**: Defense in depth security approach
- **Field-level encryption** for sensitive data
- **Comprehensive audit logging** for all operations
- **MFA support** for enhanced authentication
- **Middleware-based security** (CORS, CSRF, rate limiting)
- **JWT token-based** authentication with refresh tokens

---

## 💾 Data Architecture

### Entity Relationship Overview
```
Organization (Tenant Root)
├── Users (Many-to-Many via UserOrganization)
├── Accounts (Chart of Accounts)  
│   └── Transactions
│       └── Transaction Lines
├── Invoices
│   └── Invoice Lines
├── Bank Accounts
│   └── Bank Transactions
└── Sync Status (Integration tracking)
```

### Multi-Tenant Data Model
- **Organization**: Root entity for tenant isolation
- **User-Organization**: Many-to-many relationship for user access
- **Account Hierarchy**: UK-compliant chart of accounts structure
- **Transaction Model**: Double-entry accounting transactions
- **Audit Trail**: Comprehensive change tracking

### Database Technology Stack
- **PostgreSQL 15+**: Primary database with advanced features
- **SQLAlchemy ORM**: Python database abstraction layer
- **Alembic**: Database migration management
- **Connection Pooling**: Optimized database connections
- **Backup Strategy**: Automated backups with point-in-time recovery

---

## 🔗 Integration Architecture

### Xero Integration
```
MCX3D ←→ Xero API
├── OAuth 2.0 Authentication
├── Data Synchronization (Accounts, Transactions, Invoices)
├── Real-time Webhooks
├── Rate Limiting (5 req/s)
└── Error Handling & Retry Logic
```

**Integration Flow**:
1. **OAuth Setup**: User authorizes MCX3D to access Xero data
2. **Data Import**: Initial full sync of historical data
3. **Incremental Sync**: Regular updates for new/changed data
4. **Real-time Updates**: Webhook notifications for immediate changes
5. **Error Recovery**: Automatic retry with exponential backoff

### MCP (Model Control Protocol)
Enhanced data processing capabilities:
- **Structured data processing** for complex financial calculations
- **Model validation** and integrity checking
- **Protocol-based communication** between system components

---

## 🚀 Deployment Architecture

### Development Environment
```
Docker Compose Stack:
├── Web Service (FastAPI)
├── Worker Service (Celery)
├── Database (PostgreSQL)
├── Cache (Redis)
└── Nginx (Reverse Proxy)
```

### Production Environment
```
Docker Swarm / Kubernetes:
├── Load Balancer (Nginx/Cloud LB)
├── Web Services (Multiple replicas)
├── Worker Services (Auto-scaling)
├── Database Cluster (PostgreSQL)
├── Cache Cluster (Redis)
├── Monitoring Stack (Prometheus/Grafana)
└── Log Aggregation (ELK Stack)
```

### Scaling Patterns
- **Horizontal scaling**: Multiple web service replicas
- **Worker scaling**: Celery workers based on queue depth
- **Database scaling**: Read replicas and connection pooling
- **Cache scaling**: Redis cluster for distributed caching

---

## 📊 Monitoring Architecture

### Metrics Collection
- **Application Metrics**: Custom business metrics via Prometheus
- **System Metrics**: Container and host-level monitoring
- **Database Metrics**: PostgreSQL performance monitoring
- **Integration Metrics**: Xero API usage and error rates

### Health Check Levels
1. **Basic Health** (`/health/`): Simple service availability
2. **Comprehensive Health** (`/health/comprehensive`): Full system validation
3. **Business Health** (`/health/business`): Business-specific KPIs and metrics

### Alerting Strategy
- **Multi-channel alerts**: Email, Slack, PagerDuty
- **Intelligent deduplication**: Prevent alert spam
- **Escalation policies**: Progressive alert escalation
- **Business impact assessment**: Priority-based alert handling

---

## 🔒 Security Architecture

### Authentication & Authorization
- **JWT-based authentication** with refresh token rotation
- **Multi-factor authentication** (MFA) support
- **Role-based access control** (RBAC) with organization scoping
- **API key management** for service-to-service communication

### Data Protection
- **Field-level encryption** for sensitive financial data
- **Encryption at rest** for database storage
- **Encryption in transit** with TLS 1.3
- **Key management** with secure key rotation

### Audit & Compliance
- **Comprehensive audit logging** for all data changes
- **Correlation ID tracking** across all system components
- **Data retention policies** for compliance requirements
- **Regular security assessments** and penetration testing

---

## 🎯 Performance Architecture

### Caching Strategy
- **Redis caching** for frequently accessed data
- **Query result caching** for expensive financial calculations
- **Session caching** for user authentication state
- **API response caching** for static content

### Optimization Techniques
- **Database indexing** for query performance
- **Connection pooling** for database efficiency
- **Async processing** for long-running operations
- **Response compression** for bandwidth optimization

### Performance Monitoring
- **Response time tracking** for all API endpoints
- **Database query performance** monitoring
- **Cache hit ratio** optimization
- **Resource utilization** tracking and alerting

---

## 📚 Technology Stack

### Core Technologies
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Web Framework** | FastAPI | Latest | REST API and documentation |
| **Database** | PostgreSQL | 15+ | Primary data storage |
| **Cache** | Redis | 6.2+ | Caching and task queue |
| **Task Queue** | Celery | Latest | Background job processing |
| **ORM** | SQLAlchemy | 2.0+ | Database abstraction |
| **Migration** | Alembic | Latest | Database schema management |

### Integration Technologies
| Component | Technology | Purpose |
|-----------|------------|---------|
| **HTTP Client** | httpx | External API integration |
| **OAuth** | authlib | OAuth 2.0 implementation |
| **Webhooks** | FastAPI + ngrok | Real-time data updates |
| **Rate Limiting** | slowapi | API rate limiting |

### Monitoring & Security
| Component | Technology | Purpose |
|-----------|------------|---------|
| **Metrics** | Prometheus | Application monitoring |
| **Logging** | Structured logging | Observability |
| **Security** | JWT, bcrypt | Authentication & encryption |
| **Testing** | pytest | Test automation |

---

## 🔄 Development Workflow

### Code Quality Pipeline
1. **Code Formatting**: Black for Python code formatting
2. **Linting**: Flake8 for code quality checks  
3. **Type Checking**: MyPy for static type analysis
4. **Testing**: pytest with 85%+ coverage requirement
5. **Security Scanning**: Automated vulnerability detection

### Deployment Pipeline
1. **Build**: Docker image creation and optimization
2. **Test**: Automated test suite execution
3. **Security**: Security scanning and compliance checks
4. **Deploy**: Staged deployment with health checks
5. **Monitor**: Post-deployment monitoring and alerting

---

## 📋 Architecture Decision Records (ADRs)

### Key Architectural Decisions

1. **FastAPI Framework**: Chosen for automatic OpenAPI documentation, type safety, and performance
2. **PostgreSQL Database**: Selected for ACID compliance, JSON support, and financial data integrity
3. **Multi-tenant Architecture**: Organization-scoped data isolation for SaaS deployment
4. **UK FRS 102 Compliance**: Built-in compliance with UK accounting standards
5. **Async Processing**: Celery for background tasks to improve user experience
6. **Docker Containerization**: Consistent deployment across environments
7. **OAuth 2.0 Integration**: Industry-standard authentication for Xero integration

### Trade-offs and Considerations

**Consistency vs Performance**: Chose strong consistency over eventual consistency for financial data integrity  
**Monolith vs Microservices**: Selected modular monolith for faster development while maintaining modularity  
**SQL vs NoSQL**: PostgreSQL chosen for ACID properties and complex financial queries  
**Sync vs Async**: Hybrid approach with sync APIs and async background processing  

---

## 🚀 Future Architecture Considerations

### Scalability Roadmap
- **Microservices migration** for independent service scaling
- **Event-driven architecture** for real-time data processing
- **Multi-region deployment** for global availability
- **API versioning strategy** for backward compatibility

### Technology Evolution
- **GraphQL API** for flexible client queries
- **Machine learning integration** for financial insights
- **Blockchain integration** for audit trail immutability
- **Real-time analytics** with streaming data processing

---

This architecture provides a robust, scalable, and secure foundation for the MCX3D Financial System while maintaining compliance with UK financial standards and enabling seamless integration with external accounting platforms.