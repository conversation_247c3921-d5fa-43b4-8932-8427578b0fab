# Configuration Reference

Complete configuration reference for MCX3D Financial System covering all environment variables, settings, and configuration options.

## 📋 Overview

MCX3D Financial System uses environment-based configuration with support for multiple environments (development, staging, production). Configuration is managed through environment variables and configuration files.

### Configuration Sources (Priority Order)

1. **Environment Variables** - Highest priority
2. **Environment-specific files** (`.env.production`, `.env.development`)
3. **Default configuration** - Lowest priority

### Configuration Files

| File | Purpose | Environment |
|------|---------|-------------|
| `.env.example` | Template with all options | All |
| `.env.development` | Development settings | Development |
| `.env.staging` | Staging settings | Staging |
| `.env.production` | Production settings | Production |

---

## 🌍 Environment Detection

The system automatically detects the environment from these variables (in order):

```bash
# Environment detection (first match wins)
ENVIRONMENT=production
ENV=production
APP_ENV=production
```

### Supported Environments

| Environment | Description | Default Debug | Default Features |
|-------------|-------------|---------------|------------------|
| `development` | Local development | `true` | All features, debug logging |
| `staging` | Pre-production testing | `false` | All features, limited logging |
| `production` | Live production | `false` | Optimized for performance |
| `testing` | Automated testing | `false` | Test-specific configurations |

---

## 🔧 Core Application Settings

### Application Configuration

```bash
# Application name and version
APP_NAME="MCX3D Financial System"
APP_VERSION="2.0.0"

# Environment and debug settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# Server configuration
HOST=0.0.0.0
PORT=8000
WORKERS=4  # Number of worker processes (production)

# Timezone and locale
TIMEZONE=UTC
LOCALE=en_GB

# API settings
API_PREFIX=/api
API_VERSION=v1
ENABLE_DOCS=true  # Disable in production for security
```

### Security Configuration

```bash
# Secret keys (generate with: python -m mcx3d_finance.utils.generate_keys)
SECRET_KEY=your-secret-key-here-64-chars-minimum
ENCRYPTION_KEY=your-fernet-encryption-key-here

# JWT settings
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30
JWT_ALGORITHM=HS256

# Session configuration
SESSION_COOKIE_NAME=mcx3d_session
SESSION_COOKIE_SECURE=true  # HTTPS only
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict
SESSION_MAX_AGE=86400  # 24 hours in seconds

# CSRF protection
CSRF_SECRET_KEY=your-csrf-secret-key
CSRF_COOKIE_NAME=csrftoken
CSRF_COOKIE_SECURE=true
CSRF_COOKIE_HTTPONLY=false  # Must be false for CSRF
```

### CORS Configuration

```bash
# Cross-Origin Resource Sharing
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
CORS_ORIGIN_REGEX=https://.*\.yourdomain\.com
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOW_HEADERS=*
CORS_MAX_AGE=86400
```

---

## 🗄️ Database Configuration

### PostgreSQL Settings

```bash
# Primary database connection
DATABASE_URL=postgresql://user:password@localhost:5432/mcx3d_finance
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mcx3d_finance
DB_USER=mcx3d_user
DB_PASSWORD=secure-password-here

# Connection pool settings
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Database engine settings
DB_ECHO=false  # Log all SQL queries (development only)
DB_ECHO_POOL=false  # Log connection pool events
DB_AUTOCOMMIT=false
DB_AUTOFLUSH=false

# Read replica configuration (optional)
DB_READ_REPLICA_URL=*******************************************/mcx3d_finance
DB_READ_REPLICA_ENABLED=false
```

### Migration Settings

```bash
# Alembic migration configuration
ALEMBIC_CONFIG=alembic.ini
ALEMBIC_SCRIPT_LOCATION=migrations
ALEMBIC_VERSION_LOCATIONS=migrations/versions

# Auto-migration settings
AUTO_MIGRATE_ON_STARTUP=false  # Set to true for development
MIGRATION_TIMEOUT=300  # 5 minutes
```

---

## 🚀 Redis Configuration

### Cache and Queue Settings

```bash
# Redis connection
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=redis-password-here

# Connection pool settings
REDIS_POOL_MAX_CONNECTIONS=100
REDIS_POOL_RETRY_ON_TIMEOUT=true
REDIS_SOCKET_TIMEOUT=30
REDIS_SOCKET_CONNECT_TIMEOUT=30

# Cache configuration
CACHE_TTL_DEFAULT=3600  # 1 hour
CACHE_TTL_USER_SESSION=86400  # 24 hours
CACHE_TTL_REPORTS=1800  # 30 minutes
CACHE_TTL_FINANCIAL_DATA=7200  # 2 hours

# Cache key prefixes
CACHE_KEY_PREFIX=mcx3d
CACHE_KEY_USER_SESSION=sessions
CACHE_KEY_REPORTS=reports
CACHE_KEY_FINANCIAL_DATA=financial
```

### Celery Task Queue

```bash
# Celery broker and result backend
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Task configuration
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# Task execution settings
CELERY_TASK_TIME_LIMIT=3600  # 1 hour
CELERY_TASK_SOFT_TIME_LIMIT=3000  # 50 minutes
CELERY_TASK_MAX_RETRIES=3
CELERY_TASK_RETRY_DELAY=60  # 1 minute

# Worker settings
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000
```

---

## 🔗 Xero Integration Configuration

### OAuth 2.0 Settings

```bash
# Xero application credentials (from https://developer.xero.com)
XERO_CLIENT_ID=your-xero-client-id
XERO_CLIENT_SECRET=your-xero-client-secret

# OAuth configuration
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback
XERO_SCOPES=accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access
XERO_STATE_EXPIRY=3600  # 1 hour

# API endpoints
XERO_API_BASE_URL=https://api.xero.com
XERO_IDENTITY_BASE_URL=https://identity.xero.com
XERO_API_VERSION=2.0

# Token management
XERO_TOKEN_REFRESH_THRESHOLD=300  # Refresh if expires in 5 minutes
XERO_TOKEN_MAX_REFRESH_ATTEMPTS=3
XERO_OFFLINE_ACCESS_ENABLED=true
```

### Rate Limiting and API Configuration

```bash
# Xero API rate limiting
XERO_RATE_LIMIT_PER_SECOND=5
XERO_RATE_LIMIT_PER_MINUTE=60
XERO_RATE_LIMIT_BURST=10

# Request configuration
XERO_REQUEST_TIMEOUT=30
XERO_MAX_RETRIES=3
XERO_RETRY_BACKOFF_FACTOR=2
XERO_RETRY_STATUS_CODES=429,500,502,503,504

# Batch processing
XERO_BATCH_SIZE=100
XERO_MAX_PARALLEL_REQUESTS=3
XERO_PAGINATION_SIZE=100

# Circuit breaker settings
XERO_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
XERO_CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
XERO_CIRCUIT_BREAKER_EXPECTED_EXCEPTION=RequestException
```

### Webhook Configuration

```bash
# Webhook settings (optional)
XERO_WEBHOOK_KEY=your-secure-webhook-key
XERO_WEBHOOK_ENDPOINT=/api/webhooks/xero
ENABLE_XERO_WEBHOOKS=false

# Webhook security
XERO_WEBHOOK_SIGNATURE_HEADER=X-Xero-Signature
XERO_WEBHOOK_TIMESTAMP_TOLERANCE=300  # 5 minutes
```

---

## 📧 Email Configuration

### SMTP Settings

```bash
# SMTP server configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
SMTP_USE_SSL=false

# Email defaults
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=MCX3D Financial System
EMAIL_REPLY_TO=<EMAIL>

# Email templates
EMAIL_TEMPLATE_DIR=templates/email
EMAIL_DEFAULT_TEMPLATE=default.html
EMAIL_USE_HTML=true
EMAIL_USE_PLAIN_TEXT=true
```

### Email Features

```bash
# Report delivery
EMAIL_REPORTS_ENABLED=true
EMAIL_REPORTS_MAX_SIZE=25000000  # 25MB
EMAIL_REPORTS_COMPRESS=true

# Notification settings
EMAIL_NOTIFICATIONS_ENABLED=true
EMAIL_DIGEST_ENABLED=true
EMAIL_DIGEST_FREQUENCY=daily  # daily, weekly, monthly

# Delivery settings
EMAIL_MAX_RETRIES=3
EMAIL_RETRY_DELAY=300  # 5 minutes
EMAIL_BATCH_SIZE=50
```

---

## 📊 Monitoring and Logging

### Logging Configuration

```bash
# Log levels
LOG_LEVEL=INFO
LOG_FORMAT=json  # json, text
LOG_FILE=/var/log/mcx3d/app.log
LOG_MAX_FILE_SIZE=100MB
LOG_BACKUP_COUNT=5

# Structured logging
LOG_INCLUDE_TIMESTAMP=true
LOG_INCLUDE_LEVEL=true
LOG_INCLUDE_CORRELATION_ID=true
LOG_INCLUDE_USER_ID=true

# Component-specific logging
LOG_LEVEL_DATABASE=WARNING
LOG_LEVEL_XERO=INFO
LOG_LEVEL_CELERY=INFO
LOG_LEVEL_AUTH=INFO
```

### Monitoring and Metrics

```bash
# Prometheus metrics
ENABLE_METRICS=true
METRICS_PORT=8001
METRICS_PATH=/metrics
METRICS_INCLUDE_BUSINESS=true

# Health checks
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_DATABASE=true
HEALTH_CHECK_REDIS=true
HEALTH_CHECK_XERO=true

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SLOW_QUERY_THRESHOLD=1000  # milliseconds
PERFORMANCE_SLOW_REQUEST_THRESHOLD=5000  # milliseconds
```

### Error Tracking

```bash
# Sentry integration (optional)
SENTRY_DSN=your-sentry-dsn-here
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1

# Error handling
ERROR_INCLUDE_TRACEBACK=false  # true in development
ERROR_INCLUDE_REQUEST_DATA=false
ERROR_NOTIFICATION_ENABLED=true
```

---

## 🛡️ Security Configuration

### Rate Limiting

```bash
# Global rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_STRATEGY=fixed-window  # fixed-window, sliding-window
RATE_LIMIT_REDIS_PREFIX=rate_limit

# API rate limits (requests per minute per IP)
RATE_LIMIT_API_GLOBAL=1000
RATE_LIMIT_API_AUTH=10
RATE_LIMIT_API_REPORTS=100
RATE_LIMIT_API_XERO=50

# User-specific rate limits (requests per minute per user)
RATE_LIMIT_USER_GLOBAL=500
RATE_LIMIT_USER_REPORTS=50
RATE_LIMIT_USER_XERO=25
```

### Content Security Policy

```bash
# CSP configuration
CSP_ENABLED=true
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline'
CSP_STYLE_SRC='self' 'unsafe-inline'
CSP_IMG_SRC='self' data: https:
CSP_FONT_SRC='self'
CSP_CONNECT_SRC='self' https://api.xero.com
CSP_REPORT_URI=/api/security/csp-report
```

### Security Headers

```bash
# Security headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000  # 1 year
HSTS_INCLUDE_SUBDOMAINS=true
HSTS_PRELOAD=true

# Content type options
X_CONTENT_TYPE_OPTIONS=nosniff
X_FRAME_OPTIONS=DENY
X_XSS_PROTECTION=1; mode=block
REFERRER_POLICY=strict-origin-when-cross-origin
```

---

## 📈 Performance Configuration

### Caching Strategy

```bash
# Query caching
ENABLE_QUERY_CACHE=true
QUERY_CACHE_TTL=3600
QUERY_CACHE_MAX_SIZE=1000

# Response caching
ENABLE_RESPONSE_CACHE=true
RESPONSE_CACHE_TTL=300
RESPONSE_CACHE_VARY_HEADERS=Authorization,Accept-Language

# Static file caching
STATIC_FILE_CACHE_MAX_AGE=86400  # 1 day
STATIC_FILE_CACHE_CONTROL=public, max-age=86400
```

### Response Optimization

```bash
# Compression
ENABLE_RESPONSE_COMPRESSION=true
COMPRESSION_LEVEL=6
COMPRESSION_MIN_SIZE=500

# Content optimization
ENABLE_MINIFICATION=true
ENABLE_BUNDLING=true
STATIC_FILE_OPTIMIZATION=true
```

### Database Optimization

```bash
# Query optimization
DB_QUERY_TIMEOUT=30
DB_SLOW_QUERY_THRESHOLD=1000
DB_ENABLE_QUERY_LOGGING=false

# Connection optimization
DB_CONNECTION_LIFETIME=3600
DB_CONNECTION_PING_INTERVAL=300
DB_CONNECTION_POOL_PRE_PING=true
```

---

## 📊 Business Configuration

### Financial Settings

```bash
# Default financial settings
DEFAULT_CURRENCY=GBP
DEFAULT_FISCAL_YEAR_END=31-12
DEFAULT_ACCOUNTING_METHOD=accrual  # accrual, cash

# Reporting preferences
FINANCIAL_YEAR_START_MONTH=1  # January
REPORTING_DECIMAL_PLACES=2
SHOW_ZERO_BALANCES=false
GROUP_SIMILAR_ACCOUNTS=true

# Compliance settings
ACCOUNTING_STANDARD=FRS102  # UK GAAP
TAX_JURISDICTION=UK
ENABLE_AUDIT_TRAIL=true
REQUIRE_APPROVAL_WORKFLOW=false
```

### Report Generation

```bash
# Default report settings
REPORT_DEFAULT_FORMAT=pdf
REPORT_INCLUDE_LOGO=true
REPORT_INCLUDE_CHARTS=true
REPORT_INCLUDE_NOTES=true

# Report output settings
REPORT_OUTPUT_DIR=./reports
REPORT_TEMPLATE_DIR=./templates/reports
REPORT_TEMP_DIR=/tmp/mcx3d_reports
REPORT_CLEANUP_AFTER_DAYS=30

# PDF generation settings
PDF_ENGINE=reportlab
PDF_PAGE_SIZE=A4
PDF_ORIENTATION=portrait
PDF_MARGIN_TOP=20
PDF_MARGIN_BOTTOM=20
PDF_MARGIN_LEFT=20
PDF_MARGIN_RIGHT=20
```

### SaaS Metrics Configuration

```bash
# SaaS KPI calculation settings
SAAS_REVENUE_RECOGNITION=subscription  # subscription, usage
SAAS_CHURN_CALCULATION=monthly  # monthly, annual
SAAS_MRR_CALCULATION=normalized  # normalized, raw
SAAS_ARR_MULTIPLE=12

# Industry benchmarking
ENABLE_INDUSTRY_BENCHMARKS=true
BENCHMARK_DATA_SOURCE=external_api
BENCHMARK_UPDATE_FREQUENCY=monthly
```

---

## 🔧 Development Configuration

### Development-Specific Settings

```bash
# Development mode settings
DEBUG=true
RELOAD_ON_CHANGE=true
ENABLE_DEBUG_TOOLBAR=true
SHOW_STACK_TRACES=true

# Development database
DEV_DB_AUTO_CREATE=true
DEV_DB_SAMPLE_DATA=true
DEV_DB_RESET_ON_STARTUP=false

# Development email (use mailcatcher or similar)
DEV_EMAIL_BACKEND=console  # console, smtp, file
DEV_EMAIL_FILE_PATH=/tmp/emails
```

### Testing Configuration

```bash
# Test database
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/mcx3d_test
CREATE_TEST_DB=true
TEARDOWN_TEST_DB=true

# Test settings
TEST_TIMEOUT=30
TEST_PARALLEL=true
TEST_COVERAGE_THRESHOLD=85
TEST_VERBOSE=false

# Mock services
MOCK_XERO_API=true
MOCK_EMAIL_SERVICE=true
MOCK_EXTERNAL_APIS=true
```

---

## 🚀 Production Configuration

### Production Optimizations

```bash
# Production mode
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=INFO

# Performance settings
WORKERS=4  # Adjust based on CPU cores
WORKER_CLASS=uvicorn.workers.UvicornWorker
WORKER_TIMEOUT=300
KEEPALIVE=65

# Security hardening
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000
```

### Production Database

```bash
# Connection pooling
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Backup settings
DB_BACKUP_ENABLED=true
DB_BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
DB_BACKUP_RETENTION_DAYS=30
DB_BACKUP_COMPRESSION=true
```

### Production Monitoring

```bash
# Enhanced monitoring
ENABLE_APM=true
APM_SERVICE_NAME=mcx3d-financial
APM_SERVER_URL=your-apm-server

# Alerting
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
ALERT_SLACK_WEBHOOK=your-slack-webhook-url
ALERT_PAGERDUTY_KEY=your-pagerduty-key

# Health check intervals
HEALTH_CHECK_INTERVAL=30
DEEP_HEALTH_CHECK_INTERVAL=300
BUSINESS_HEALTH_CHECK_INTERVAL=600
```

---

## 🔍 Configuration Validation

### Required Environment Variables

The following environment variables are required for the system to start:

#### Minimal Configuration
```bash
SECRET_KEY=required
DATABASE_URL=required
REDIS_URL=required
```

#### Production Requirements
```bash
SECRET_KEY=required
ENCRYPTION_KEY=required
DATABASE_URL=required
REDIS_URL=required
XERO_CLIENT_ID=required
XERO_CLIENT_SECRET=required
SMTP_HOST=required
SMTP_USERNAME=required
SMTP_PASSWORD=required
```

### Configuration Validation Commands

```bash
# Validate current configuration
docker-compose exec web python -m mcx3d_finance.cli.main config validate

# Show current configuration (redacted)
docker-compose exec web python -m mcx3d_finance.cli.main config show

# Test specific configuration sections
docker-compose exec web python -m mcx3d_finance.cli.main config test --section database
docker-compose exec web python -m mcx3d_finance.cli.main config test --section redis
docker-compose exec web python -m mcx3d_finance.cli.main config test --section xero
```

---

## 📚 Configuration Examples

### Development Environment (.env.development)

```bash
# Development configuration example
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Database
DATABASE_URL=postgresql://mcx3d_user:password@localhost:5432/mcx3d_dev

# Redis
REDIS_URL=redis://localhost:6379/0

# Security (development keys)
SECRET_KEY=dev-secret-key-minimum-64-characters-long-for-security
ENCRYPTION_KEY=dev-encryption-key-32-bytes-base64-encoded

# Xero (sandbox credentials)
XERO_CLIENT_ID=your-sandbox-client-id
XERO_CLIENT_SECRET=your-sandbox-client-secret
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback

# Email (development)
SMTP_HOST=localhost
SMTP_PORT=1025  # MailCatcher port
SMTP_USERNAME=dev
SMTP_PASSWORD=dev
```

### Production Environment (.env.production)

```bash
# Production configuration example
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Database (production)
DATABASE_URL=postgresql://mcx3d:<EMAIL>:5432/mcx3d_prod

# Redis (production)
REDIS_URL=redis://:<EMAIL>:6379/0

# Security (strong production keys)
SECRET_KEY=production-secret-key-64-characters-or-more-very-secure
ENCRYPTION_KEY=production-encryption-key-from-key-generator

# Xero (production credentials)
XERO_CLIENT_ID=your-production-client-id
XERO_CLIENT_SECRET=your-production-client-secret
XERO_REDIRECT_URI=https://yourdomain.com/api/auth/xero/callback

# Email (production SMTP)
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=secure-smtp-password
SMTP_USE_TLS=true

# CORS (production origins)
CORS_ORIGINS=https://app.yourdomain.com,https://yourdomain.com

# Security hardening
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true
RATE_LIMIT_ENABLED=true
```

---

## 🔑 Security Keys Generation

### Generate All Required Keys

Use the built-in key generator:

```bash
# Generate all security keys
python -m mcx3d_finance.utils.generate_keys

# Output format:
SECRET_KEY=generated-secret-key-64-characters
ENCRYPTION_KEY=generated-fernet-encryption-key
JWT_SECRET_KEY=generated-jwt-secret-key
CSRF_SECRET_KEY=generated-csrf-secret-key
```

### Manual Key Generation

```bash
# Generate SECRET_KEY (64+ characters)
python -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(64))"

# Generate ENCRYPTION_KEY (Fernet key)
python -c "from cryptography.fernet import Fernet; print('ENCRYPTION_KEY=' + Fernet.generate_key().decode())"

# Generate random password (32 characters)
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

---

## 📋 Configuration Checklist

### Development Setup
- [ ] Copy `.env.example` to `.env.development`
- [ ] Generate security keys
- [ ] Configure database connection
- [ ] Configure Redis connection
- [ ] Set up Xero sandbox credentials
- [ ] Configure SMTP (optional for development)
- [ ] Test configuration with `config validate`

### Production Setup
- [ ] Create `.env.production` with production values
- [ ] Generate strong security keys
- [ ] Configure production database with connection pooling
- [ ] Configure Redis cluster
- [ ] Set up production Xero application
- [ ] Configure production SMTP service
- [ ] Set proper CORS origins
- [ ] Enable rate limiting and security headers
- [ ] Configure monitoring and alerting
- [ ] Test all configurations thoroughly

### Security Checklist
- [ ] All secret keys are cryptographically secure
- [ ] Database passwords are strong and unique
- [ ] HTTPS is enforced in production
- [ ] CORS origins are restricted to known domains
- [ ] Rate limiting is enabled
- [ ] Security headers are configured
- [ ] Sensitive configuration is not logged
- [ ] Environment variables are not committed to version control

---

## 🆘 Troubleshooting Configuration

### Common Configuration Issues

#### Database Connection Issues
```bash
# Test database connection
docker-compose exec web python -c "
from mcx3d_finance.db.database import get_db_session
try:
    session = next(get_db_session())
    print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
"
```

#### Redis Connection Issues
```bash
# Test Redis connection
docker-compose exec web python -c "
import redis
from mcx3d_finance.core.config import get_redis_client
try:
    client = get_redis_client()
    client.ping()
    print('Redis connection successful')
except Exception as e:
    print(f'Redis connection failed: {e}')
"
```

#### Xero Configuration Issues
```bash
# Validate Xero configuration
docker-compose exec web python -c "
from mcx3d_finance.integrations.xero.config import validate_xero_config
try:
    validate_xero_config()
    print('Xero configuration valid')
except Exception as e:
    print(f'Xero configuration invalid: {e}')
"
```

### Configuration Debugging

Enable configuration debugging:

```bash
# Show all configuration (with sensitive values redacted)
DEBUG_CONFIG=true docker-compose exec web python -m mcx3d_finance.cli.main config show

# Validate all configuration sections
docker-compose exec web python -m mcx3d_finance.cli.main config validate --verbose
```

---

**Last Updated**: January 2025  
**Configuration Version**: 2.0.0  
**Compatibility**: MCX3D Financial System v2.0+