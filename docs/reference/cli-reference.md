# CLI Reference

Complete command-line interface reference for MCX3D Financial System.

## 📋 Overview

The MCX3D CLI provides comprehensive command-line access to all system functionality including report generation, data management, Xero integration, and system operations.

### Command Structure
```bash
# All commands follow this pattern:
docker-compose exec web python -m mcx3d_finance.cli.main <command> <subcommand> [options]

# Alternative short form (if mcx3d-finance is in PATH):
mcx3d-finance <command> <subcommand> [options]
```

### Global Options
| Option | Description | Default |
|--------|-------------|---------|
| `--help, -h` | Show help message | |
| `--verbose, -v` | Enable verbose output | `false` |
| `--debug` | Enable debug logging | `false` |
| `--config FILE` | Use custom configuration file | |
| `--organization-id ID` | Target organization (required for most commands) | |

---

## 📊 Report Generation Commands

### Balance Sheet

Generate balance sheet reports in various formats.

#### Basic Usage
```bash
# Generate balance sheet for specific date
mcx3d-finance generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --format pdf

# With comparative periods
mcx3d-finance generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --compare-periods 2 \
  --format excel
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--date` | date | Balance sheet date (YYYY-MM-DD) | ✅ |
| `--format` | choice | Output format: `pdf`, `excel`, `json`, `html` | ❌ (default: `json`) |
| `--compare-periods` | int | Number of comparative periods | ❌ |
| `--output-file` | path | Custom output file path | ❌ |
| `--template` | choice | Report template: `standard`, `executive`, `detailed` | ❌ (default: `standard`) |
| `--include-notes` | flag | Include explanatory notes | ❌ |

#### Examples
```bash
# Executive summary format
mcx3d-finance generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --format pdf \
  --template executive

# With custom output location
mcx3d-finance generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --format excel \
  --output-file ./reports/custom_balance_sheet.xlsx
```

### Income Statement

Generate profit & loss statements for specified periods.

#### Basic Usage
```bash
# Generate income statement for period
mcx3d-finance generate income-statement \
  --organization-id 1 \
  --period 2024-Q4 \
  --format pdf

# Year-to-date income statement
mcx3d-finance generate income-statement \
  --organization-id 1 \
  --from-date 2024-01-01 \
  --to-date 2024-12-31 \
  --format excel
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--period` | string | Period (YYYY-QN, YYYY-MM, YYYY) | ❌* |
| `--from-date` | date | Start date (YYYY-MM-DD) | ❌* |
| `--to-date` | date | End date (YYYY-MM-DD) | ❌* |
| `--format` | choice | Output format: `pdf`, `excel`, `json`, `html` | ❌ (default: `json`) |
| `--compare-periods` | int | Number of comparative periods | ❌ |
| `--template` | choice | Template: `standard`, `executive`, `detailed` | ❌ |
| `--include-breakdown` | flag | Include detailed account breakdown | ❌ |

*Either `--period` OR `--from-date`/`--to-date` is required

#### Examples
```bash
# Monthly income statement
mcx3d-finance generate income-statement \
  --organization-id 1 \
  --period 2024-12 \
  --format pdf

# Custom date range with comparison
mcx3d-finance generate income-statement \
  --organization-id 1 \
  --from-date 2024-07-01 \
  --to-date 2024-12-31 \
  --compare-periods 1 \
  --format excel
```

### Cash Flow Statement

Generate cash flow statements showing operating, investing, and financing activities.

#### Basic Usage
```bash
# Generate cash flow statement
mcx3d-finance generate cash-flow \
  --organization-id 1 \
  --period 2024 \
  --format pdf
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--period` | string | Period (YYYY-QN, YYYY) | ❌* |
| `--from-date` | date | Start date (YYYY-MM-DD) | ❌* |
| `--to-date` | date | End date (YYYY-MM-DD) | ❌* |
| `--format` | choice | Output format: `pdf`, `excel`, `json`, `html` | ❌ (default: `json`) |
| `--method` | choice | Method: `direct`, `indirect` | ❌ (default: `indirect`) |
| `--template` | choice | Template: `standard`, `detailed` | ❌ |

### Batch Report Generation

Generate multiple reports simultaneously.

#### Basic Usage
```bash
# Generate all reports for an organization
mcx3d-finance generate all-reports \
  --organization-id 1 \
  --period 2024 \
  --formats pdf,excel

# Generate reports for multiple organizations
mcx3d-finance generate balance-sheet \
  --organizations 1,2,3 \
  --date 2024-12-31 \
  --format pdf
```

---

## 💰 Valuation Commands

### DCF Valuation

Perform discounted cash flow analysis.

#### Basic Usage
```bash
# DCF analysis with config file
mcx3d-finance valuate dcf \
  --organization-id 1 \
  --config dcf_config.json \
  --export pdf

# DCF with inline parameters
mcx3d-finance valuate dcf \
  --organization-id 1 \
  --projection-years 5 \
  --discount-rate 0.12 \
  --terminal-growth-rate 0.03 \
  --export excel
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--config` | path | DCF configuration file | ❌* |
| `--projection-years` | int | Years to project (1-10) | ❌* |
| `--discount-rate` | float | Discount rate (0.01-0.50) | ❌* |
| `--terminal-growth-rate` | float | Terminal growth rate (0.00-0.10) | ❌* |
| `--export` | choice | Export format: `pdf`, `excel`, `json` | ❌ |
| `--scenarios` | choice | Scenarios: `base`, `optimistic`, `pessimistic`, `all` | ❌ (default: `base`) |

*Either `--config` OR inline parameters required

#### DCF Configuration File Format
```json
{
  "projection_years": 5,
  "discount_rate": 0.12,
  "terminal_growth_rate": 0.03,
  "scenarios": {
    "base": {
      "revenue_growth": [0.15, 0.12, 0.10, 0.08, 0.05],
      "margin_improvement": [0.02, 0.01, 0.01, 0.00, 0.00]
    },
    "optimistic": {
      "revenue_growth": [0.25, 0.20, 0.15, 0.12, 0.10],
      "margin_improvement": [0.04, 0.03, 0.02, 0.01, 0.01]
    },
    "pessimistic": {
      "revenue_growth": [0.08, 0.06, 0.04, 0.02, 0.02],
      "margin_improvement": [0.00, 0.00, -0.01, -0.01, 0.00]
    }
  }
}
```

### Multiples Valuation

Perform industry multiples analysis.

#### Basic Usage
```bash
# Industry multiples analysis
mcx3d-finance valuate multiples \
  --organization-id 1 \
  --industry-sector technology \
  --multiples "EV/Revenue,P/E,EV/EBITDA" \
  --export excel
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--industry-sector` | choice | Industry sector | ✅ |
| `--multiples` | string | Comma-separated multiples list | ❌ |
| `--export` | choice | Export format: `pdf`, `excel`, `json` | ❌ |
| `--peer-group` | path | Custom peer group file | ❌ |

#### Supported Multiples
- `EV/Revenue` - Enterprise Value to Revenue
- `EV/EBITDA` - Enterprise Value to EBITDA
- `P/E` - Price to Earnings
- `P/B` - Price to Book
- `P/S` - Price to Sales
- `EV/FCF` - Enterprise Value to Free Cash Flow

### SaaS Valuation

Specialized valuation for SaaS businesses.

#### Basic Usage
```bash
# SaaS-specific valuation
mcx3d-finance valuate saas \
  --organization-id 1 \
  --export pdf
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--export` | choice | Export format: `pdf`, `excel`, `json` | ❌ |
| `--include-kpis` | flag | Include detailed SaaS KPIs | ❌ |
| `--benchmark` | flag | Include industry benchmarks | ❌ |

---

## 📈 SaaS Analytics Commands

### Generate SaaS KPIs

Calculate and display SaaS key performance indicators.

#### Basic Usage
```bash
# SaaS KPI dashboard
mcx3d-finance generate saas-kpis \
  --organization-id 1 \
  --period 2024-Q4
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--period` | string | Period (YYYY-QN, YYYY-MM) | ✅ |
| `--format` | choice | Output format: `json`, `csv`, `excel` | ❌ (default: `json`) |
| `--include-trends` | flag | Include historical trends | ❌ |
| `--benchmark` | flag | Include industry benchmarks | ❌ |

#### Available KPIs
- **Monthly Recurring Revenue (MRR)**
- **Annual Recurring Revenue (ARR)**
- **Customer Acquisition Cost (CAC)**
- **Lifetime Value (LTV)**
- **Churn Rate (Monthly/Annual)**
- **Net Revenue Retention (NRR)**
- **Gross Revenue Retention (GRR)**
- **Average Revenue Per User (ARPU)**
- **Customer Lifetime (Months)**
- **Payback Period (Months)**

---

## 🔄 Data Synchronization Commands

### Xero Integration

Synchronize data with Xero accounting platform.

#### Full Synchronization
```bash
# Complete data sync from Xero
mcx3d-finance sync xero \
  --organization-id 1 \
  --full-sync
```

#### Incremental Synchronization
```bash
# Sync recent changes only
mcx3d-finance sync xero \
  --organization-id 1 \
  --from-date 2024-01-01

# Daily incremental sync
mcx3d-finance sync xero \
  --organization-id 1 \
  --incremental
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--full-sync` | flag | Perform complete data sync | ❌ |
| `--incremental` | flag | Sync only recent changes | ❌ |
| `--from-date` | date | Start date for sync (YYYY-MM-DD) | ❌ |
| `--to-date` | date | End date for sync (YYYY-MM-DD) | ❌ |
| `--data-types` | string | Comma-separated data types to sync | ❌ |
| `--batch-size` | int | Batch size for API calls (50-1000) | ❌ (default: 100) |
| `--dry-run` | flag | Preview changes without applying | ❌ |

#### Supported Data Types
- `accounts` - Chart of accounts
- `transactions` - General ledger transactions
- `contacts` - Customers and suppliers
- `invoices` - Sales and purchase invoices
- `bank-transactions` - Bank account transactions
- `bank-transfers` - Inter-account transfers

#### Examples
```bash
# Sync specific data types
mcx3d-finance sync xero \
  --organization-id 1 \
  --data-types "accounts,transactions,invoices" \
  --from-date 2024-01-01

# Preview sync changes
mcx3d-finance sync xero \
  --organization-id 1 \
  --incremental \
  --dry-run
```

---

## 🏢 Organization Management

### List Organizations

Display all organizations in the system.

#### Basic Usage
```bash
# List all organizations
mcx3d-finance list organizations

# List with detailed information
mcx3d-finance list organizations --detailed
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--detailed` | flag | Show detailed organization information | ❌ |
| `--format` | choice | Output format: `table`, `json`, `csv` | ❌ (default: `table`) |
| `--active-only` | flag | Show only active organizations | ❌ |

### Show Organization Details

Display detailed information about a specific organization.

#### Basic Usage
```bash
# Show organization details
mcx3d-finance show organization --id 1

# Include connection status
mcx3d-finance show organization --id 1 --include-status
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--id` | int | Organization ID | ✅ |
| `--include-status` | flag | Include connection and sync status | ❌ |
| `--format` | choice | Output format: `json`, `yaml`, `table` | ❌ (default: `table`) |

---

## 📤 Data Export/Import

### Export Data

Export financial data in various formats.

#### Basic Usage
```bash
# Export all data for organization
mcx3d-finance export data \
  --organization-id 1 \
  --format csv \
  --output-dir ./exports

# Export specific data types
mcx3d-finance export data \
  --organization-id 1 \
  --data-types "transactions,invoices" \
  --format json \
  --output-dir ./exports
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--format` | choice | Export format: `csv`, `json`, `excel` | ✅ |
| `--output-dir` | path | Output directory | ✅ |
| `--data-types` | string | Comma-separated data types | ❌ (default: all) |
| `--from-date` | date | Start date filter (YYYY-MM-DD) | ❌ |
| `--to-date` | date | End date filter (YYYY-MM-DD) | ❌ |
| `--compress` | flag | Compress output files | ❌ |

### Import Data

Import financial data from files.

#### Basic Usage
```bash
# Import from CSV file
mcx3d-finance import data \
  --file ./data/transactions.csv \
  --organization-id 1 \
  --data-type transactions

# Import with validation
mcx3d-finance import data \
  --file ./data/accounts.json \
  --organization-id 1 \
  --data-type accounts \
  --validate-only
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--file` | path | Input file path | ✅ |
| `--organization-id` | int | Organization ID | ✅ |
| `--data-type` | choice | Data type: `accounts`, `transactions`, `invoices` | ✅ |
| `--validate-only` | flag | Validate without importing | ❌ |
| `--batch-size` | int | Batch size for processing | ❌ (default: 100) |
| `--skip-errors` | flag | Continue on validation errors | ❌ |

---

## 🎛️ System Management

### Health Checks

Check system health and status.

#### Basic Usage
```bash
# Basic health check
mcx3d-finance health check

# Comprehensive health check
mcx3d-finance health check --comprehensive

# Business health check
mcx3d-finance health check --business
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--comprehensive` | flag | Perform detailed system checks | ❌ |
| `--business` | flag | Include business-specific KPIs | ❌ |
| `--format` | choice | Output format: `json`, `table` | ❌ (default: `table`) |

### Database Management

Manage database operations.

#### Basic Usage
```bash
# Check database connection
mcx3d-finance db check

# Run pending migrations
mcx3d-finance db migrate

# Reset database (development only)
mcx3d-finance db reset --confirm
```

#### Options for `db reset`
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--confirm` | flag | Confirm database reset operation | ✅ |

### Cache Management

Manage system cache.

#### Basic Usage
```bash
# Clear all cache
mcx3d-finance cache clear

# Clear specific cache keys
mcx3d-finance cache clear --keys "reports,organizations"

# Show cache statistics
mcx3d-finance cache stats
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--keys` | string | Specific cache keys to clear | ❌ |

---

## 📅 Scheduling and Automation

### Create Scheduled Tasks

Set up automated report generation and data sync.

#### Basic Usage
```bash
# Schedule monthly reports
mcx3d-finance schedule create \
  --name "Monthly P&L" \
  --organization-id 1 \
  --task "generate_income_statement" \
  --frequency monthly \
  --format pdf \
  --email <EMAIL>

# Schedule daily Xero sync
mcx3d-finance schedule create \
  --name "Daily Xero Sync" \
  --organization-id 1 \
  --task "sync_xero" \
  --frequency daily \
  --time "02:00"
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--name` | string | Schedule name | ✅ |
| `--organization-id` | int | Organization ID | ✅ |
| `--task` | choice | Task type | ✅ |
| `--frequency` | choice | Frequency: `daily`, `weekly`, `monthly` | ✅ |
| `--time` | time | Execution time (HH:MM) | ❌ (default: `00:00`) |
| `--format` | choice | Output format for reports | ❌ |
| `--email` | string | Email recipients (comma-separated) | ❌ |

#### Supported Tasks
- `generate_balance_sheet`
- `generate_income_statement`
- `generate_cash_flow`
- `sync_xero`
- `data_validation`
- `cache_cleanup`

### List Scheduled Tasks

View all scheduled tasks.

#### Basic Usage
```bash
# List all schedules
mcx3d-finance schedule list

# List schedules for specific organization
mcx3d-finance schedule list --organization-id 1
```

### Remove Scheduled Tasks

Remove scheduled tasks.

#### Basic Usage
```bash
# Remove specific schedule
mcx3d-finance schedule remove --id 1

# Remove all schedules for organization
mcx3d-finance schedule remove --organization-id 1 --all
```

---

## 🔍 Analysis and Validation

### Data Validation

Validate data integrity and quality.

#### Basic Usage
```bash
# Validate all data for organization
mcx3d-finance validate data --organization-id 1

# Validate specific data types
mcx3d-finance validate data \
  --organization-id 1 \
  --data-types "transactions,accounts"

# Validate Xero import data
mcx3d-finance validate xero-data --organization-id 1
```

#### Options
| Option | Type | Description | Required |
|--------|------|-------------|----------|
| `--organization-id` | int | Organization ID | ✅ |
| `--data-types` | string | Comma-separated data types | ❌ |
| `--fix-issues` | flag | Attempt to fix validation issues | ❌ |
| `--report` | flag | Generate validation report | ❌ |

### Financial Analysis

Perform financial analysis and ratio calculations.

#### Basic Usage
```bash
# Calculate financial ratios
mcx3d-finance analyze ratios \
  --organization-id 1 \
  --date 2024-12-31

# Multi-year trend analysis
mcx3d-finance analyze trends \
  --organization-id 1 \
  --start-year 2020 \
  --end-year 2024 \
  --metrics "revenue,profit,assets"

# Industry benchmarking
mcx3d-finance analyze benchmark \
  --organization-id 1 \
  --industry technology \
  --metrics "revenue-growth,profit-margin,roe"
```

#### Available Financial Ratios
- **Liquidity Ratios**: Current Ratio, Quick Ratio, Cash Ratio
- **Profitability Ratios**: ROE, ROA, Gross Margin, Net Margin
- **Leverage Ratios**: Debt-to-Equity, Interest Coverage, Debt Ratio
- **Efficiency Ratios**: Asset Turnover, Inventory Turnover, Receivables Turnover

---

## 🔧 Configuration and Testing

### Test Connections

Test external service connections.

#### Basic Usage
```bash
# Test Xero connection
mcx3d-finance test xero-connection --organization-id 1

# Test database connection
mcx3d-finance test database

# Test all connections
mcx3d-finance test all
```

### Configuration Management

Manage system configuration.

#### Basic Usage
```bash
# Show current configuration
mcx3d-finance config show

# Validate configuration
mcx3d-finance config validate

# Set configuration value
mcx3d-finance config set --key "XERO_CLIENT_ID" --value "new-value"
```

---

## 📊 Examples and Use Cases

### Daily Operations Workflow
```bash
# 1. Check system health
mcx3d-finance health check --comprehensive

# 2. Sync latest data from Xero
mcx3d-finance sync xero --organization-id 1 --incremental

# 3. Validate imported data
mcx3d-finance validate data --organization-id 1

# 4. Generate daily reports
mcx3d-finance generate income-statement \
  --organization-id 1 \
  --period $(date +%Y-%m) \
  --format pdf
```

### Month-End Reporting
```bash
# Generate complete financial statement package
mcx3d-finance generate all-reports \
  --organization-id 1 \
  --period $(date +%Y-%m) \
  --formats pdf,excel \
  --template executive

# Calculate and export financial ratios
mcx3d-finance analyze ratios \
  --organization-id 1 \
  --date $(date +%Y-%m-01) \
  --export excel
```

### Business Valuation Workflow
```bash
# 1. Generate SaaS KPIs
mcx3d-finance generate saas-kpis \
  --organization-id 1 \
  --period 2024-Q4 \
  --include-trends

# 2. Perform DCF analysis
mcx3d-finance valuate dcf \
  --organization-id 1 \
  --config dcf_config.json \
  --scenarios all \
  --export pdf

# 3. Industry multiples analysis
mcx3d-finance valuate multiples \
  --organization-id 1 \
  --industry-sector technology \
  --export excel
```

---

## 🆘 Error Handling and Troubleshooting

### Common Error Patterns

#### Authentication Errors
```bash
# Error: "Organization not found or access denied"
# Solution: Verify organization ID and user permissions
mcx3d-finance show organization --id 1

# Error: "Xero token expired"
# Solution: Re-authorize Xero connection
# Use the setup wizard to refresh tokens
```

#### Data Validation Errors
```bash
# Error: "Invalid account codes detected"
# Solution: Run data validation with fix option
mcx3d-finance validate data --organization-id 1 --fix-issues

# Error: "Transaction balance mismatch"
# Solution: Check double-entry accounting integrity
mcx3d-finance validate data --organization-id 1 --data-types transactions
```

#### Performance Issues
```bash
# Error: Command timeout or slow performance
# Solution: Use batch processing and incremental operations
mcx3d-finance sync xero --organization-id 1 --batch-size 50 --incremental
```

### Debug Mode

Enable debug output for troubleshooting:

```bash
# Enable debug logging for any command
mcx3d-finance --debug generate balance-sheet --organization-id 1 --date 2024-12-31

# Use verbose output for more details
mcx3d-finance --verbose sync xero --organization-id 1 --incremental
```

---

## 📚 Additional Resources

- **[User Guide](../user-guide/README.md)** - Complete user documentation
- **[Installation Guide](../getting-started/installation.md)** - System setup instructions
- **[API Reference](../developer/api-reference.md)** - REST API documentation
- **[Troubleshooting Guide](../user-guide/troubleshooting.md)** - Common issues and solutions
- **[Configuration Reference](./configuration.md)** - Complete configuration options

---

**Last Updated**: January 2025  
**CLI Version**: 2.0.0  
**Compatibility**: MCX3D Financial System v2.0+