# Basic Usage Guide

Learn the core workflows and features of the MCX3D Financial System.

## 🎯 Core Workflows

### 1. Generate Financial Reports

The primary function of MCX3D is generating professional financial reports.

#### Balance Sheet
```bash
# Generate balance sheet for specific date
python -m mcx3d_finance.cli.main generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --format pdf

# With comparative periods
python -m mcx3d_finance.cli.main generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --compare-periods 2 \
  --format excel
```

#### Income Statement
```bash
# Generate income statement for period
python -m mcx3d_finance.cli.main generate income-statement \
  --organization-id 1 \
  --period 2024-Q4 \
  --format pdf

# Year-to-date income statement
python -m mcx3d_finance.cli.main generate income-statement \
  --organization-id 1 \
  --from-date 2024-01-01 \
  --to-date 2024-12-31 \
  --format excel
```

#### Cash Flow Statement
```bash
# Generate cash flow statement
python -m mcx3d_finance.cli.main generate cash-flow \
  --organization-id 1 \
  --period 2024 \
  --format pdf
```

### 2. Data Management

#### List Organizations
```bash
# View all organizations
python -m mcx3d_finance.cli.main list organizations

# View organization details
python -m mcx3d_finance.cli.main show organization --id 1
```

#### Data Import and Export
```bash
# Export financial data
python -m mcx3d_finance.cli.main export data \
  --organization-id 1 \
  --format csv \
  --output-dir ./exports

# Import from CSV
python -m mcx3d_finance.cli.main import data \
  --file ./data/sample_transactions.csv \
  --organization-id 1
```

### 3. Xero Integration

#### Connect to Xero
```bash
# Set up Xero OAuth (interactive)
python scripts/setup/xero_oauth_setup.py

# Or use the authentication helper
python scripts/development/xero_auth_helper.py
```

#### Sync Data from Xero
```bash
# Full data sync
python -m mcx3d_finance.cli.main sync xero \
  --organization-id 1 \
  --full-sync

# Incremental sync (faster)
python -m mcx3d_finance.cli.main sync xero \
  --organization-id 1 \
  --from-date 2024-01-01
```

## 📊 Report Types Available

### Financial Statements (NASDAQ-Compliant)

#### Balance Sheet
- **Assets**: Current and non-current assets
- **Liabilities**: Current and long-term liabilities  
- **Equity**: Share capital and retained earnings
- **Comparative Periods**: Previous year comparison

#### Income Statement
- **Revenue**: Operating and non-operating income
- **Expenses**: Cost of goods sold, operating expenses
- **Earnings**: Gross profit, operating profit, net income
- **Per Share Metrics**: Earnings per share calculations

#### Cash Flow Statement
- **Operating Activities**: Cash from core business operations
- **Investing Activities**: Capital expenditures and investments
- **Financing Activities**: Debt and equity transactions
- **Net Cash Flow**: Overall cash position changes

### Valuation Reports

#### DCF Analysis
```bash
# Discounted Cash Flow analysis
python -m mcx3d_finance.cli.main valuate dcf \
  --organization-id 1 \
  --projection-years 5 \
  --discount-rate 0.12 \
  --terminal-growth-rate 0.03
```

#### Multiples Valuation
```bash
# Industry multiples analysis
python -m mcx3d_finance.cli.main valuate multiples \
  --organization-id 1 \
  --industry-sector technology \
  --multiples "EV/Revenue,P/E,EV/EBITDA"
```

### SaaS Analytics

#### Key Performance Indicators
```bash
# SaaS KPI dashboard
python -m mcx3d_finance.cli.main generate saas-kpis \
  --organization-id 1 \
  --period 2024-Q4

# KPIs include:
# - Monthly Recurring Revenue (MRR)
# - Annual Recurring Revenue (ARR)
# - Customer Acquisition Cost (CAC)
# - Lifetime Value (LTV)
# - Churn Rate
# - Net Revenue Retention
```

## 🔧 Output Formats

### Available Formats
- **PDF**: Professional reports with charts and formatting
- **Excel**: Spreadsheets with formulas and pivot tables
- **HTML**: Web-friendly reports with interactive elements
- **JSON**: Structured data for API integration
- **CSV**: Raw data for analysis

### Format-Specific Options

#### PDF Reports
```bash
# High-quality PDF with charts
python -m mcx3d_finance.cli.main generate balance-sheet \
  --format pdf \
  --include-charts \
  --template professional

# Executive summary format
python -m mcx3d_finance.cli.main generate income-statement \
  --format pdf \
  --template executive-summary
```

#### Excel Reports
```bash
# Excel with multiple sheets
python -m mcx3d_finance.cli.main generate balance-sheet \
  --format excel \
  --include-supporting-schedules \
  --create-pivot-tables
```

## 🎛️ Advanced Features

### Batch Processing

#### Generate Multiple Reports
```bash
# Generate all reports for an organization
python -m mcx3d_finance.cli.main generate all-reports \
  --organization-id 1 \
  --period 2024 \
  --formats pdf,excel

# Generate reports for multiple organizations
python -m mcx3d_finance.cli.main generate balance-sheet \
  --organizations 1,2,3 \
  --date 2024-12-31 \
  --format pdf
```

### Automation and Scheduling

#### Scheduled Reports
```bash
# Set up monthly automated reports
python -m mcx3d_finance.cli.main schedule create \
  --name "Monthly P&L" \
  --organization-id 1 \
  --report-type income-statement \
  --frequency monthly \
  --format pdf \
  --email <EMAIL>
```

### Custom Configurations

#### Report Customization
```bash
# Custom balance sheet categories
python -m mcx3d_finance.cli.main generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --config custom-categories.json

# Custom KPI calculations
python -m mcx3d_finance.cli.main generate saas-kpis \
  --organization-id 1 \
  --config custom-kpis.json
```

## 🔍 Data Analysis

### Financial Ratios
```bash
# Calculate financial ratios
python -m mcx3d_finance.cli.main analyze ratios \
  --organization-id 1 \
  --date 2024-12-31

# Ratios include:
# - Liquidity ratios (Current, Quick, Cash)
# - Profitability ratios (ROE, ROA, Gross Margin)
# - Leverage ratios (Debt-to-Equity, Interest Coverage)
# - Efficiency ratios (Asset Turnover, Inventory Turnover)
```

### Trend Analysis
```bash
# Multi-year trend analysis
python -m mcx3d_finance.cli.main analyze trends \
  --organization-id 1 \
  --start-year 2020 \
  --end-year 2024 \
  --metrics "revenue,profit,assets"
```

### Benchmarking
```bash
# Industry benchmarking
python -m mcx3d_finance.cli.main analyze benchmark \
  --organization-id 1 \
  --industry technology \
  --metrics "revenue-growth,profit-margin,roe"
```

## 📱 API Usage

### Authentication
```bash
# Get API token
curl -X POST "http://localhost:8000/api/auth/token" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'
```

### Generate Reports via API
```bash
# Balance sheet via API
curl -X POST "http://localhost:8000/api/reports/balance-sheet" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "organization_id": 1,
    "date": "2024-12-31",
    "format": "json",
    "include_comparatives": true
  }'

# Income statement via API
curl -X POST "http://localhost:8000/api/reports/income-statement" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "organization_id": 1,
    "from_date": "2024-01-01",
    "to_date": "2024-12-31",
    "format": "pdf"
  }'
```

## 💡 Best Practices

### Data Quality
1. **Regular Sync**: Keep Xero data synchronized daily
2. **Validation**: Review data validation reports before generating reports
3. **Backup**: Export data regularly for backup purposes
4. **Audit Trail**: Monitor all data changes and report generation

### Performance Optimization
1. **Incremental Sync**: Use date ranges for large datasets
2. **Batch Processing**: Generate multiple reports in batches
3. **Format Selection**: Use JSON for API integration, PDF for presentation
4. **Caching**: Leverage system caching for repeated operations

### Security
1. **API Keys**: Rotate API keys regularly
2. **Access Control**: Use role-based permissions
3. **Audit Logs**: Monitor system access and changes
4. **Data Encryption**: Ensure sensitive data is encrypted

## 🆘 Common Issues

### Report Generation Fails
```bash
# Check data completeness
python -m mcx3d_finance.cli.main validate data --organization-id 1

# Verify account mappings
python -m mcx3d_finance.cli.main check accounts --organization-id 1
```

### Xero Sync Issues
```bash
# Check Xero connection
python -m mcx3d_finance.cli.main test xero-connection --organization-id 1

# Reset Xero token
python scripts/setup/xero_oauth_setup.py --reset
```

### Performance Issues
```bash
# Check system health
curl http://localhost:8000/health

# Monitor resource usage
docker stats mcx3d_web
```

## 📚 Next Steps

- **[Xero Integration](./xero-integration.md)** - Connect to Xero accounting
- **[Troubleshooting](./troubleshooting.md)** - Resolve common issues
- **[API Reference](../developer/api-reference.md)** - Build custom integrations
- **[Advanced Configuration](../reference/configuration-options.md)** - Customize the system