# MCX3D Financial System Troubleshooting Guide

This comprehensive guide covers common issues and their solutions based on real debugging scenarios encountered in the MCX3D Financial system. It includes critical fixes, performance optimizations, debugging tools, and emergency procedures.

## 🚨 Critical Issues

### Financial Reports Showing Zero Values

**Symptoms:**
- All financial reports (Income Statement, Balance Sheet, Cash Flow) show zero values
- Script runs to completion without errors
- Database contains organization data

**Root Cause Analysis:**
This issue typically involves multiple interconnected problems:

1. **Xero Connection Failure**: Organization exists but lacks Xero authentication token
2. **Missing Historical Data**: Local transaction data doesn't cover requested date ranges
3. **Fallback Mechanism Issues**: Local data fallback not working properly

**Diagnostic Steps:**

```bash
# 1. Check organization and Xero token status
python -c "
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
db = SessionLocal()
orgs = db.query(Organization).all()
for org in orgs:
    print(f'ID: {org.id}, Name: {org.name}, Xero Tenant ID: {org.xero_tenant_id}, Has Token: {bool(org.xero_token)}')
db.close()
"

# 2. Check available transaction data by year
python -c "
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Invoice, BankTransaction
from sqlalchemy import func, extract
db = SessionLocal()
org_id = 2  # Replace with your organization ID

print('Invoice data by year:')
invoice_counts = db.query(
    extract('year', Invoice.date).label('year'),
    func.count(Invoice.id).label('count')
).filter(Invoice.organization_id == org_id).group_by('year').all()
for year, count in invoice_counts:
    print(f'  Year {int(year)}: {count} invoices')

print('\\nBank transaction data by year:')
bank_counts = db.query(
    extract('year', BankTransaction.date).label('year'),
    func.count(BankTransaction.id).label('count')
).filter(BankTransaction.organization_id == org_id).group_by('year').all()
for year, count in bank_counts:
    print(f'  Year {int(year)}: {count} transactions')
db.close()
"
```

**Solution:**

1. **Create Historical Data** (if missing):
```bash
# Generate historical data for the required years
python -c "
from mcx3d_finance.cli.seed import create_historical_data_for_organization
create_historical_data_for_organization(2, years_back=6)  # Creates 2019-2024 data
"
```

2. **Verify Data Creation**:
```bash
# Re-run the diagnostic script above to confirm data exists
```

3. **Test Report Generation**:
```bash
python -c "
from mcx3d_finance.reporting.comprehensive_report import ComprehensiveReportGenerator
generator = ComprehensiveReportGenerator(2, 2024, use_xero_trial_balance=True)
report = generator.generate()
print('SUCCESS: Report generated!')
print('Turnover:', report['financial_statements']['income_statement']['turnover'])
"
```

### Type Error: Decimal and Float Division

**Symptoms:**
```
TypeError: unsupported operand type(s) for /: 'decimal.Decimal' and 'float'
```

**Root Cause:**
Financial calculations mixing `Decimal` (from database) and `float` types in division operations.

**Common Locations:**
- `mcx3d_finance/core/financials/balance_sheet.py` - Financial ratios calculations
- `mcx3d_finance/core/financials/income_statement.py` - Profitability margins
- `mcx3d_finance/reporting/financial_ratios.py` - All ratio calculations

**Solution:**
Convert all operands to `float` before division:

```python
# ❌ Incorrect - mixing types
ratio = decimal_value / float_value

# ✅ Correct - consistent types
ratio = float(decimal_value) / float(float_value)
```

**Example Fix:**
```python
# Before
ratios["current_ratio"] = (current_assets / current_liabilities) if current_liabilities else 0

# After  
ratios["current_ratio"] = (float(current_assets) / float(current_liabilities)) if current_liabilities else 0
```

### JSON Serialization Error with Decimal Types

**Symptoms:**
```
TypeError: Type <class 'decimal.Decimal'> not serializable
```

**Root Cause:**
JSON serializer cannot handle `Decimal` objects from financial calculations.

**Solution:**
Update the `json_serial` function to handle `Decimal` types:

```python
from decimal import Decimal

def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    if isinstance(obj, Decimal):
        return float(obj)
    raise TypeError("Type %s not serializable" % type(obj))
```

### N+1 Query Problem (HIGHEST PRIORITY)

**Symptoms:**
- Thousands of SQL queries during Xero sync
- Extremely slow synchronization performance (20+ minutes)
- High database CPU usage during sync operations

**Root Cause:**
Individual database queries for each contact lookup during invoice and transaction processing.

**Files Affected:**
- `mcx3d_finance/integrations/xero_data_storage.py` (lines 248-254, 300-307)

**Solution:**
Implement batch loading for contacts to eliminate N+1 queries:

```python
# Before processing invoices/transactions, bulk load contacts:
contact_ids = [item.get("contact_id") for item in items if item.get("contact_id")]
contacts = db.query(Contact).filter(
    Contact.organization_id == org_id,
    Contact.xero_contact_id.in_(contact_ids)
).all()
contact_map = {c.xero_contact_id: c.id for c in contacts}

# Then use map instead of individual queries:
invoice_data["contact_id"] = contact_map.get(xero_contact_id)
```

**Expected Impact:** 90% reduction in sync time (from 20+ minutes to <5 minutes)

### Cash Flow PDF Generation NotImplementedError

**Symptoms:**
- Cash flow reports return `NotImplementedError`
- PDF generation fails for cash flow statements
- Excel export also fails

**Root Cause:**
Missing implementation in report generation endpoints.

**File:** `mcx3d_finance/api/reports.py` (line 327)

**Solution:**
Replace placeholder with actual implementation:

```python
# Replace NotImplementedError with actual implementation:
if format == "pdf":
    report_generator.generate_cash_flow_pdf(report_data, output_path)
elif format == "excel":
    report_generator.generate_cash_flow_excel(report_data, output_path)

# Implement methods in ReportGenerator class
```

### Empty PDF Files (22 bytes)

**Symptoms:**
- PDF generation appears to succeed but creates empty files
- File size is exactly 22 bytes
- No error messages in logs

**Root Cause:**
Data not passed correctly to PDF generator or exceptions being silently swallowed.

**Debug Steps:**
1. Add logging before PDF generation:
```python
logger.info(f"Generating PDF with data: {len(report_data)} records")
```

2. Verify `report_data` contains actual data:
```python
if not report_data:
    logger.warning("No data available for PDF generation")
    return
```

3. Check for silent exceptions in PDF generation:
```python
try:
    pdf_generator.generate(report_data, output_path)
    logger.info(f"PDF generated successfully: {os.path.getsize(output_path)} bytes")
except Exception as e:
    logger.error(f"PDF generation failed: {e}")
    raise
```

### Long-Running CLI Commands Timeout

**Symptoms:**
- DCF valuation and analytics commands timeout
- CLI hangs on large datasets
- No progress indicators

**Files Affected:**
- `mcx3d_finance/cli/valuation.py`
- `mcx3d_finance/cli/analytics.py`

**Solution:**
Add async processing option to CLI commands:

```python
@click.option("--async", "async_mode", is_flag=True, help="Run asynchronously")

# In command implementation:
if async_mode:
    task = valuation_task.delay(params)
    click.echo(f"Task ID: {task.id}")
    click.echo("Use 'celery -A mcx3d_finance.tasks.celery_app result <task_id>' to check status")
else:
    # Existing sync code
```

### API 404 Errors on Report Endpoints

**Symptoms:**
- Report endpoints return 404 errors
- Routes appear to be configured correctly
- Authentication seems to be working

**Root Cause:**
Authentication middleware blocking requests or route registration issues.

**Debug Steps:**
1. Check if routes are registered:
```python
for route in app.routes:
    print(f"{route.methods} {route.path}")
```

2. Test without authentication temporarily:
```python
# Temporarily comment out @require_auth decorator
```

3. Verify auth token format:
```bash
curl -H "Authorization: Bearer <token>" -v http://localhost:8000/api/reports/balance-sheet
```

4. Check CORS configuration for API access.

## 🔧 Development Issues

### Database Connection Issues

**Symptoms:**
- Connection timeouts
- "Too many connections" errors
- Slow query performance

**Solutions:**

1. **Check Connection Pool Settings**:
```python
# In database configuration
SQLALCHEMY_DATABASE_URL = "postgresql://user:pass@localhost/db?pool_size=20&max_overflow=30"
```

2. **Verify Database Status**:
```bash
# Check active connections
psql -d mcx3d_financials -c "SELECT count(*) FROM pg_stat_activity;"
```

### Import/Module Issues

**Symptoms:**
- `ModuleNotFoundError`
- Import circular dependencies

**Solutions:**

1. **Check Python Path**:
```bash
export PYTHONPATH="${PYTHONPATH}:/path/to/mcx3d_financials/v2"
```

2. **Use Relative Imports**:
```python
# ✅ Correct
from ...integrations.xero_client import XeroClient

# ❌ Avoid absolute imports in modules
from mcx3d_finance.integrations.xero_client import XeroClient
```

## 📊 Performance Issues

### Slow Report Generation

**Diagnostic Commands:**
```bash
# Check database query performance
python -c "
import time
from mcx3d_finance.reporting.comprehensive_report import ComprehensiveReportGenerator
start = time.time()
generator = ComprehensiveReportGenerator(2, 2024)
report = generator.generate()
print(f'Report generation took: {time.time() - start:.2f} seconds')
"
```

**Optimization Tips:**
1. Use database indexes on frequently queried columns
2. Implement query result caching
3. Use batch processing for large datasets

### Quick Performance Wins

#### Database Indexing
Add indexes for common query patterns:

```sql
-- Add indexes for common queries
CREATE INDEX idx_invoices_org_xero ON invoices(organization_id, xero_invoice_id);
CREATE INDEX idx_contacts_org_xero ON contacts(organization_id, xero_contact_id);
CREATE INDEX idx_transactions_org_date ON transactions(organization_id, date);
CREATE INDEX idx_bank_transactions_org_date ON bank_transactions(organization_id, date);
```

#### Query Batching
Use SQLAlchemy bulk operations for improved performance:

```python
# Use SQLAlchemy bulk operations instead of individual saves
db.bulk_insert_mappings(Invoice, invoice_dicts)
db.bulk_update_mappings(Invoice, update_dicts)

# For large datasets, use batch processing:
batch_size = 1000
for i in range(0, len(data), batch_size):
    batch = data[i:i + batch_size]
    db.bulk_insert_mappings(Model, batch)
    db.commit()
```

#### Eager Loading
Reduce N+1 queries with eager loading:

```python
# Load related data in one query
invoices = db.query(Invoice)\
    .options(joinedload(Invoice.contact))\
    .options(joinedload(Invoice.line_items))\
    .filter(Invoice.organization_id == org_id)\
    .all()
```

#### Connection Pooling Optimization
Optimize database connection settings:

```python
# In database configuration
engine = create_engine(
    DATABASE_URL,
    pool_size=20,           # Number of connections to maintain
    max_overflow=30,        # Additional connections when needed
    pool_pre_ping=True,     # Verify connections before use
    pool_recycle=3600       # Recycle connections every hour
)
```

## 🔍 Debugging Tools

### Enable Debug Logging

```python
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('mcx3d_finance')
logger.setLevel(logging.DEBUG)
```

### Database Query Debugging

```python
# Enable SQLAlchemy query logging
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
```

### Memory Usage Monitoring

```python
import psutil
import os

def check_memory():
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"Memory usage: {memory_mb:.2f} MB")
```

### Advanced Debugging Tools

#### Enable Query Logging
Monitor all SQL queries for performance analysis:

```python
# In config.py or application startup
SQLALCHEMY_ECHO = True  # See all SQL queries in console

# For production debugging, use logging:
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
```

#### Profile Slow Code
Identify performance bottlenecks in your code:

```python
import cProfile
import pstats

# Profile a specific function or code block
profiler = cProfile.Profile()
profiler.enable()

# ... code to profile ...

profiler.disable()
stats = pstats.Stats(profiler).sort_stats('cumulative')
stats.print_stats(20)  # Show top 20 functions by execution time
```

#### Monitor Celery Tasks
Debug background task processing:

```bash
# Watch active tasks
celery -A mcx3d_finance.tasks.celery_app inspect active

# Check task results
celery -A mcx3d_finance.tasks.celery_app result <task_id>

# Monitor task queue
celery -A mcx3d_finance.tasks.celery_app inspect reserved

# Check worker status
celery -A mcx3d_finance.tasks.celery_app inspect stats
```

#### API Testing Commands
Validate API endpoints during development:

```bash
# Test report endpoints
curl -X GET "http://localhost:8000/api/reports/income-statement?organization_id=1&start_date=2024-01-01&end_date=2024-12-31&format=json" \
  -H "Authorization: Bearer <TOKEN>"

# Test health endpoint
curl -f http://localhost:8000/health

# Test with verbose output
curl -v -H "Authorization: Bearer <TOKEN>" \
  http://localhost:8000/api/reports/balance-sheet?organization_id=1
```

#### Performance Benchmark Testing
Run systematic performance tests:

```bash
# Run performance tests with benchmarking
pytest -m performance -v --benchmark-only

# Test specific N+1 query fix
python -m mcx3d_finance.cli.main sync xero -o 1 --no-async --optimize

# Generate baseline performance metrics
pytest -m performance --benchmark-save=baseline
```

## 🚨 Emergency Procedures

### Emergency Rollback

If critical issues occur after deployment, follow these steps:

#### 1. Revert Code Changes
```bash
# Revert the problematic commit
git revert HEAD
git push origin main

# For multiple commits, revert to known good state
git reset --hard <good_commit_hash>
git push --force origin main  # Use with caution!
```

#### 2. Restore Database
```bash
# Restore from backup (ensure backup is recent)
pg_restore -d mcx3d_finance backup_before_deploy.sql

# Or rollback specific migration
alembic downgrade -1  # Go back one migration
```

#### 3. Clear Application Cache
```bash
# Clear Redis cache
redis-cli FLUSHDB

# Clear application-level caches
rm -rf /tmp/mcx3d_cache/*
```

#### 4. Restart Services
```bash
# Restart API service
systemctl restart mcx3d-finance-api

# Restart background workers
systemctl restart mcx3d-finance-celery

# Restart database (if necessary)
systemctl restart postgresql

# Restart Redis
systemctl restart redis
```

#### 5. Verify System Health
```bash
# Check service status
systemctl status mcx3d-finance-api
systemctl status mcx3d-finance-celery

# Test API endpoints
curl -f http://localhost:8000/health

# Verify database connectivity
python -c "from mcx3d_finance.db.session import SessionLocal; db = SessionLocal(); print('DB OK')"
```

### Communication During Emergencies

1. **Immediate Notification**: Alert stakeholders of the issue and rollback
2. **Status Updates**: Provide updates every 15-30 minutes during active incidents
3. **Resolution Confirmation**: Confirm system is fully operational
4. **Post-Incident Review**: Schedule follow-up to prevent recurrence

## 📊 Success Metrics & Monitoring

### Performance Benchmarks
Monitor these metrics after implementing fixes:

| Metric | Before Fix | Target | How to Measure |
|--------|------------|--------|----------------|
| **Xero Sync Queries** | 5000+ | <50 | Query monitor/logs |
| **Sync Time** | 20+ min | <5 min | CLI timer |
| **API Response Time** | >2s | <200ms | APM tools |
| **PDF Generation** | Fails | <2s | Report logs |
| **Error Rate** | >5% | <0.1% | Error tracking |
| **Memory Usage** | >1GB | <500MB | System monitoring |

### Monitoring Commands

#### System Health Check
```bash
# Comprehensive system health
python -c "
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, User
import redis
import requests

# Database health
try:
    db = SessionLocal()
    org_count = db.query(Organization).count()
    user_count = db.query(User).count()
    print(f'✅ Database: {org_count} orgs, {user_count} users')
    db.close()
except Exception as e:
    print(f'❌ Database error: {e}')

# Redis health
try:
    r = redis.Redis()
    r.ping()
    print('✅ Redis: Connected')
except Exception as e:
    print(f'❌ Redis error: {e}')

# API health
try:
    response = requests.get('http://localhost:8000/health', timeout=5)
    print(f'✅ API: Status {response.status_code}')
except Exception as e:
    print(f'❌ API error: {e}')
"
```

#### Performance Monitoring
```bash
# Monitor query performance
python -c "
from mcx3d_finance.db.session import SessionLocal
db = SessionLocal()
result = db.execute('SELECT COUNT(*) FROM pg_stat_statements WHERE calls > 1000;')
print(f'High-frequency queries: {result.scalar()}')
db.close()
"

# Check memory usage
python -c "
import psutil
memory = psutil.virtual_memory()
print(f'Memory usage: {memory.percent}% ({memory.used/1024/1024/1024:.1f}GB used)')
"
```

### Automated Alerts

Set up monitoring for these critical thresholds:

- **API Response Time** > 1 second (Warning) / > 5 seconds (Critical)
- **Database Connections** > 80% of pool (Warning) / > 95% (Critical)
- **Error Rate** > 1% (Warning) / > 5% (Critical)
- **Memory Usage** > 80% (Warning) / > 95% (Critical)
- **Disk Space** < 20% free (Warning) / < 10% (Critical)

## 📋 Fix Priority Guidelines

### Day 1 (Immediate)
1. **N+1 Query Issues** - Immediate 90% performance gain
2. **Critical API Failures** - Restore basic functionality

### Day 2-3 (High Priority)
3. **Missing Feature Implementation** - Unblock user workflows
4. **PDF Generation Issues** - Complete reporting functionality

### Day 4-5 (Medium Priority)
5. **Performance Optimizations** - Improve user experience
6. **Monitoring & Alerting** - Prevent future issues

### Ongoing (Low Priority)
7. **Code Quality Improvements** - Technical debt reduction
8. **Documentation Updates** - Knowledge sharing

## 📞 Getting Help

### Before Reporting Issues

1. **Check Logs**: Review application logs for error details
2. **Verify Data**: Ensure required data exists in the database
3. **Test Isolation**: Try to reproduce the issue with minimal code
4. **Check Dependencies**: Verify all required packages are installed

### Useful Diagnostic Commands

```bash
# System health check
python -c "
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
try:
    db = SessionLocal()
    count = db.query(Organization).count()
    print(f'✅ Database connection OK - {count} organizations found')
    db.close()
except Exception as e:
    print(f'❌ Database connection failed: {e}')
"

# Quick report test
python -c "
try:
    from mcx3d_finance.reporting.comprehensive_report import ComprehensiveReportGenerator
    generator = ComprehensiveReportGenerator(2, 2024)
    report = generator.generate()
    print('✅ Report generation working')
except Exception as e:
    print(f'❌ Report generation failed: {e}')
"
```

---

**Last Updated**: July 25, 2025  
**Based on**: Real debugging scenarios and solutions from MCX3D Financial system development
