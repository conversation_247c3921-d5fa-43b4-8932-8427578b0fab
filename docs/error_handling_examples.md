# MCX3D Financial Platform - Error Handling Implementation Examples

## Example 1: Improving Alert Manager Error Handling

### Current Implementation (Basic Try/Except)

```python
async def _send_email(self, alert_data: Dict[str, Any]):
    """Send alert via email."""
    try:
        # ... email sending logic ...
        logger.info(f"Email alert sent for: {alert_data['title']}")
    except Exception as e:
        logger.error(f"Failed to send email alert: {e}")
```

### Improved Implementation

```python
from mcx3d_finance.exceptions.handlers import error_boundary
from mcx3d_finance.exceptions.recovery import with_retry, with_circuit_breaker, RetryConfig
from mcx3d_finance.exceptions.base import MCX3DException

class AlertDeliveryError(MCX3DException):
    """Raised when alert delivery fails."""
    pass

class AlertManager:
    @with_retry(config=RetryConfig(
        max_attempts=3,
        initial_delay=1.0,
        max_delay=5.0,
        retryable_exceptions=(smtplib.SMTPException, ConnectionError)
    ))
    @with_circuit_breaker(name="email_alerts", failure_threshold=5, reset_timeout=300)
    async def _send_email(self, alert_data: Dict[str, Any]):
        """Send alert via email with retry and circuit breaker."""
        with error_boundary(
            "send_email_alert",
            alert_title=alert_data.get('title'),
            severity=alert_data.get('severity'),
            reraise=True
        ):
            if not self._validate_email_config():
                raise AlertDeliveryError(
                    message="Email configuration incomplete",
                    user_message="Email alerts are not configured",
                    context={"config_keys": list(self.email_config.keys())}
                )
            
            msg = self._build_email_message(alert_data)
            
            # Send with timeout
            try:
                await asyncio.wait_for(
                    self._smtp_send(msg),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                raise AlertDeliveryError(
                    message="Email send timeout",
                    user_message="Email delivery timed out",
                    context={"timeout": 30, "recipient_count": len(self.email_config['to_emails'])}
                )
            
            logger.info(
                "Email alert sent successfully",
                alert_title=alert_data['title'],
                severity=alert_data['severity'],
                recipients=self.email_config['to_emails']
            )
```

## Example 2: Improving Data Persistence with Transactions

### Current Implementation

```python
def persist_all_data(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        with SessionLocal() as db:
            self._persist_accounts(db, processed_data["accounts"])
            self._persist_contacts(db, processed_data["contacts"])
            db.commit()
    except Exception as e:
        logger.error(f"Error persisting data: {e}")
        return {"success": False, "error": str(e)}
```

### Improved Implementation

```python
from mcx3d_finance.exceptions.handlers import error_boundary
from mcx3d_finance.exceptions.recovery import with_retry, RetryConfig
from sqlalchemy.exc import IntegrityError, OperationalError

@with_retry(config=RetryConfig(
    max_attempts=2,
    retryable_exceptions=(OperationalError,)  # Only retry on transient DB errors
))
def persist_all_data(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
    with error_boundary(
        "persist_all_data",
        organization_id=self.organization_id,
        data_types=list(processed_data.keys())
    ) as error_context:
        
        with SessionLocal() as db:
            try:
                # Use savepoint for partial rollback capability
                with db.begin_nested():
                    results = self._persist_with_savepoints(db, processed_data)
                
                db.commit()
                
                logger.info(
                    "Data persistence completed",
                    correlation_id=error_context.correlation_id,
                    organization_id=self.organization_id,
                    results=results
                )
                
                return {
                    "success": True,
                    "results": results,
                    "correlation_id": error_context.correlation_id
                }
                
            except IntegrityError as e:
                db.rollback()
                raise DataIntegrityError(
                    message=f"Data integrity violation: {str(e)}",
                    user_message="Some data could not be saved due to conflicts",
                    context={"constraint": str(e.orig)}
                )

def _persist_with_savepoints(self, db: Session, processed_data: Dict[str, Any]) -> Dict[str, Any]:
    results = {}
    
    # Persist each data type with its own savepoint
    for data_type, data_items in processed_data.items():
        with db.begin_nested() as sp:
            try:
                method_name = f"_persist_{data_type}"
                persist_method = getattr(self, method_name)
                count = persist_method(db, data_items)
                results[data_type] = {"success": True, "count": count}
            except Exception as e:
                sp.rollback()
                results[data_type] = {"success": False, "error": str(e)}
                logger.error(
                    f"Failed to persist {data_type}",
                    error=str(e),
                    organization_id=self.organization_id
                )
    
    return results
```

## Example 3: Implementing Fallback Strategies

### Currency Converter with Multiple Fallbacks

```python
class CurrencyConverter:
    def get_exchange_rate(self, from_currency: str, to_currency: str, date: date) -> float:
        """Get exchange rate with multiple fallback strategies."""
        
        # Strategy 1: Live API (with circuit breaker)
        with error_boundary(
            "get_exchange_rate_live",
            from_currency=from_currency,
            to_currency=to_currency,
            reraise=False
        ):
            if not self._is_circuit_open("exchange_api"):
                rate = self._fetch_from_api(from_currency, to_currency, date)
                if rate:
                    self._cache_rate(from_currency, to_currency, date, rate)
                    return rate
        
        # Strategy 2: Recent cache (within 24 hours)
        with error_boundary(
            "get_exchange_rate_cache",
            reraise=False
        ):
            cached_rate = self._get_cached_rate(
                from_currency, to_currency, date, 
                max_age_hours=24
            )
            if cached_rate:
                logger.warning(
                    "Using cached exchange rate",
                    age_hours=cached_rate['age_hours'],
                    from_currency=from_currency,
                    to_currency=to_currency
                )
                return cached_rate['rate']
        
        # Strategy 3: Historical average (last 30 days)
        with error_boundary(
            "get_exchange_rate_historical",
            reraise=False
        ):
            historical_rate = self._get_historical_average(
                from_currency, to_currency, date, days=30
            )
            if historical_rate:
                logger.warning(
                    "Using historical average exchange rate",
                    from_currency=from_currency,
                    to_currency=to_currency,
                    average_over_days=30
                )
                return historical_rate
        
        # Strategy 4: Static fallback rates
        fallback_rate = self._get_static_fallback_rate(from_currency, to_currency)
        logger.error(
            "Using static fallback exchange rate",
            from_currency=from_currency,
            to_currency=to_currency,
            rate=fallback_rate
        )
        
        # Trigger alert for manual intervention
        self._alert_exchange_rate_failure(from_currency, to_currency, date)
        
        return fallback_rate
```

## Example 4: Batch Processing with Partial Failure Handling

```python
def process_financial_transactions(self, transactions: List[Dict]) -> Dict[str, Any]:
    """Process transactions with partial failure handling."""
    
    results = {
        "processed": [],
        "failed": [],
        "skipped": [],
        "summary": {}
    }
    
    # Process in batches for better error isolation
    batch_size = 100
    for i in range(0, len(transactions), batch_size):
        batch = transactions[i:i + batch_size]
        batch_results = self._process_batch(batch)
        
        results["processed"].extend(batch_results["processed"])
        results["failed"].extend(batch_results["failed"])
        results["skipped"].extend(batch_results["skipped"])
    
    # Generate summary
    results["summary"] = {
        "total": len(transactions),
        "processed": len(results["processed"]),
        "failed": len(results["failed"]),
        "skipped": len(results["skipped"]),
        "success_rate": len(results["processed"]) / len(transactions) * 100
    }
    
    # Alert if failure rate is high
    if results["summary"]["success_rate"] < 95:
        self._alert_high_failure_rate(results["summary"])
    
    return results

def _process_batch(self, batch: List[Dict]) -> Dict[str, List]:
    batch_results = {"processed": [], "failed": [], "skipped": []}
    
    with SessionLocal() as db:
        for transaction in batch:
            transaction_id = transaction.get("id", "unknown")
            
            # Skip if already processed
            if self._is_already_processed(db, transaction_id):
                batch_results["skipped"].append({
                    "id": transaction_id,
                    "reason": "already_processed"
                })
                continue
            
            # Process with error boundary
            with error_boundary(
                "process_transaction",
                transaction_id=transaction_id,
                reraise=False
            ) as error_context:
                try:
                    processed_tx = self._process_single_transaction(
                        db, transaction
                    )
                    batch_results["processed"].append(processed_tx)
                    
                except ValidationError as e:
                    batch_results["failed"].append({
                        "id": transaction_id,
                        "error": e.to_dict(),
                        "recoverable": True
                    })
                    
                except MCX3DException as e:
                    batch_results["failed"].append({
                        "id": transaction_id,
                        "error": e.to_dict(),
                        "recoverable": False
                    })
        
        # Commit successful transactions
        db.commit()
    
    return batch_results
```

## Example 5: Async Error Handling with Context Propagation

```python
class AsyncReportGenerator:
    async def generate_financial_report(
        self, 
        org_id: int, 
        report_type: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate report with proper async error handling."""
        
        # Create correlation ID for entire operation
        correlation_id = str(uuid.uuid4())
        
        async with error_boundary(
            "generate_financial_report",
            correlation_id=correlation_id,
            organization_id=org_id,
            report_type=report_type
        ):
            # Validate inputs
            await self._validate_report_parameters(parameters)
            
            # Fetch data with timeout
            try:
                data = await asyncio.wait_for(
                    self._fetch_report_data(org_id, parameters),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                raise ReportGenerationError(
                    message="Data fetch timeout",
                    user_message="Report data is taking too long to load",
                    context={"timeout_seconds": 30}
                )
            
            # Generate report with progress tracking
            progress_tracker = ProgressTracker(correlation_id)
            
            try:
                async with progress_tracker:
                    report = await self._render_report(
                        report_type, 
                        data, 
                        parameters,
                        progress_callback=progress_tracker.update
                    )
                    
                return {
                    "success": True,
                    "report": report,
                    "correlation_id": correlation_id,
                    "generation_time": progress_tracker.elapsed_time
                }
                
            except MemoryError:
                raise ReportMemoryError(
                    message="Insufficient memory for report generation",
                    user_message="Report is too large to generate",
                    context={
                        "data_size": len(data),
                        "report_type": report_type
                    }
                )
```

## Example 6: Health Check with Graceful Degradation

```python
class HealthChecker:
    async def comprehensive_health_check(self) -> Dict[str, Any]:
        """Perform health check with graceful handling of component failures."""
        
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {},
            "degraded_features": []
        }
        
        # Check each component independently
        components = [
            ("database", self._check_database),
            ("redis", self._check_redis),
            ("xero_api", self._check_xero_api),
            ("email_service", self._check_email_service),
            ("background_jobs", self._check_background_jobs)
        ]
        
        for component_name, check_func in components:
            with error_boundary(
                f"health_check_{component_name}",
                component=component_name,
                reraise=False
            ):
                try:
                    result = await asyncio.wait_for(
                        check_func(),
                        timeout=5.0
                    )
                    health_status["components"][component_name] = result
                    
                except asyncio.TimeoutError:
                    health_status["components"][component_name] = {
                        "status": "timeout",
                        "message": "Health check timed out",
                        "critical": component_name in ["database", "redis"]
                    }
                    
                except Exception as e:
                    health_status["components"][component_name] = {
                        "status": "error",
                        "message": str(e),
                        "critical": component_name in ["database", "redis"]
                    }
        
        # Determine overall health
        critical_failures = [
            name for name, comp in health_status["components"].items()
            if comp.get("critical") and comp["status"] != "healthy"
        ]
        
        if critical_failures:
            health_status["status"] = "unhealthy"
            health_status["critical_failures"] = critical_failures
        elif any(comp["status"] != "healthy" for comp in health_status["components"].values()):
            health_status["status"] = "degraded"
            health_status["degraded_features"] = self._get_degraded_features(
                health_status["components"]
            )
        
        return health_status
```

## Summary

These examples demonstrate:

1. **Consistent use of error_boundary** for operation tracking
2. **Recovery patterns** (retry, circuit breaker) for external services
3. **Fallback strategies** for critical operations
4. **Partial failure handling** in batch operations
5. **Async error handling** with proper timeout management
6. **Graceful degradation** when components fail

Key principles applied:
- Always include correlation IDs
- Use structured logging
- Implement appropriate recovery strategies
- Handle partial failures gracefully
- Provide meaningful error messages
- Monitor and alert on failures