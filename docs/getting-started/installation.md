# Installation Guide

Complete guide for setting up the MCX3D Financial System from scratch.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Docker**: Version 20.10+ with Docker Compose
- **Memory**: Minimum 4GB RAM, 8GB recommended
- **Storage**: 2GB free disk space
- **Network**: Internet access for package downloads

### Required Services
- **PostgreSQL**: Database for financial data (provided via Docker)
- **Redis**: Caching and task queue (provided via Docker)

## 🚀 Quick Installation

### Option 1: Docker (Recommended)

```bash
# 1. Clone the repository
git clone https://github.com/mcx3d/mcx3d-financials.git
cd mcx3d-financials/v2

# 2. Set up environment variables
cp .env.example .env.development
# Edit .env.development with your configuration

# 3. Start all services
docker-compose up --build

# 4. Verify installation
curl http://localhost:8000/health
```

The system will be available at:
- **API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### Option 2: Local Development

```bash
# 1. Clone and setup
git clone https://github.com/mcx3d/mcx3d-financials.git
cd mcx3d-financials/v2

# 2. Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/macOS
# or: venv\Scripts\activate  # Windows

# 3. Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 4. Start external services
docker-compose up -d db redis

# 5. Set up database
alembic upgrade head

# 6. Start the application
uvicorn mcx3d_finance.main:app --reload --port 8000
```

## ⚙️ Configuration

### Environment Variables

Create `.env.development` (development) or `.env.production` (production):

```bash
# Database Configuration
DATABASE_URL=postgresql://mcx3d_user:password@localhost:5432/mcx3d_finance

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security Keys (Generate with: python -m mcx3d_finance.utils.generate_keys)
SECRET_KEY=your_secret_key_minimum_32_characters_long
ENCRYPTION_KEY=your_base64_encoded_fernet_encryption_key

# Xero Integration (Optional)
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
```

### Generate Security Keys

```bash
# Generate all required keys
python -m mcx3d_finance.utils.generate_keys

# Or generate specific keys
python -m mcx3d_finance.utils.generate_keys --type jwt
python -m mcx3d_finance.utils.generate_keys --type fernet
```

### Database Setup

```bash
# Create database migration
alembic revision --autogenerate -m "Initial setup"

# Apply migrations
alembic upgrade head

# Verify database connection
python -c "from mcx3d_finance.db.session import SessionLocal; print('Database connected!')"
```

## 🧪 Verification

### Health Check

```bash
# Check system health
curl http://localhost:8000/health

# Expected response:
{
  "status": "healthy",
  "version": "2.0.0",
  "checks": {
    "database": "ok",
    "redis": "ok"
  }
}
```

### API Documentation

Visit http://localhost:8000/docs for interactive API documentation.

### Run Test Suite

```bash
# Run all tests
docker-compose exec web pytest

# Run specific test categories
docker-compose exec web pytest -m unit
docker-compose exec web pytest -m integration
```

## 🔧 Advanced Configuration

### Production Deployment

For production deployment, see [Deployment Guide](../operations/deployment.md).

### Custom Configuration

```bash
# Custom database settings
DATABASE_URL=********************************/dbname

# Custom Redis settings
REDIS_URL=redis://user:pass@host:6379/0

# Performance tuning
WORKER_PROCESSES=4
MAX_CONNECTIONS=100
```

### SSL/TLS Configuration

```bash
# Enable HTTPS (production)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
FORCE_HTTPS=true
```

## 🐳 Docker Configuration

### Custom Docker Compose

```yaml
# docker-compose.override.yml
version: '3.8'
services:
  web:
    environment:
      - CUSTOM_SETTING=value
    ports:
      - "8080:8000"  # Custom port mapping
```

### Development with Hot Reload

```bash
# Start with development configuration
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# The application will reload automatically on code changes
```

## 🔍 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>
```

#### Database Connection Failed
```bash
# Check database container
docker-compose logs db

# Reset database
docker-compose down -v
docker-compose up -d db
```

#### Redis Connection Failed
```bash
# Check Redis container
docker-compose logs redis

# Test Redis connection
docker-compose exec redis redis-cli ping
```

#### Permission Denied
```bash
# Fix Docker permissions (Linux)
sudo usermod -aG docker $USER
# Log out and back in

# Fix file permissions
sudo chown -R $USER:$USER .
```

### Logs and Debugging

```bash
# View application logs
docker-compose logs web

# View all services logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f web
```

### Performance Issues

```bash
# Monitor resource usage
docker stats

# Check database performance
docker-compose exec db psql -U mcx3d_user -d mcx3d_finance -c "SELECT * FROM pg_stat_activity;"
```

## 📚 Next Steps

After successful installation:

1. **[5-Minute Quickstart](./quickstart.md)** - Generate your first report
2. **[Configuration Guide](./configuration.md)** - Detailed configuration options
3. **[User Guide](../user-guide/README.md)** - Learn how to use the system
4. **[Xero Integration](../user-guide/xero-integration.md)** - Connect to Xero (optional)

## 🆘 Getting Help

- **Common Issues**: [Troubleshooting Guide](../user-guide/troubleshooting.md)
- **Configuration**: [Configuration Reference](../reference/configuration-options.md)
- **API Setup**: [Developer Guide](../developer/README.md)
- **Production**: [Deployment Guide](../operations/deployment.md)