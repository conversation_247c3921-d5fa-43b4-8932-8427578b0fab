# 5-Minute Quickstart

Get up and running with MCX3D Financial System in 5 minutes.

## ⚡ Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- 5 minutes of your time
- Internet connection

**Don't have Docker?** Follow the [Full Installation Guide](./installation.md) first.

## 🚀 Quick Setup

### Step 1: Get the Code (30 seconds)

```bash
git clone https://github.com/mcx3d/mcx3d-financials.git
cd mcx3d-financials/v2
```

### Step 2: Configure Environment (1 minute)

```bash
# Copy example environment file
cp .env.example .env.development

# Generate security keys
python -m mcx3d_finance.utils.generate_keys >> .env.development
```

### Step 3: Start the System (2 minutes)

```bash
# Start all services (database, redis, application)
docker-compose up --build

# Wait for the message: "Application startup complete"
```

The system will be ready at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs

### Step 4: Verify Everything Works (30 seconds)

```bash
# Check system health
curl http://localhost:8000/health

# Expected response:
# {"status": "healthy", "version": "2.0.0"}
```

### Step 5: Generate Your First Report (1 minute)

```bash
# Open a new terminal (keep the first one running)
cd mcx3d-financials/v2

# Generate a sample balance sheet
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet \
  --organization-id 1 \
  --date 2024-12-31 \
  --format json

# The report will be saved to reports/balance_sheet_2024.json
```

## 🎉 Success!

You now have:
- ✅ MCX3D Financial System running
- ✅ Database and Redis configured
- ✅ Your first financial report generated
- ✅ API documentation available

## 🔗 What's Next?

### Explore the System
```bash
# View API documentation
open http://localhost:8000/docs

# Generate different report types
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --help
docker-compose exec web python -m mcx3d_finance.cli.main generate cash-flow --help
```

### Connect to Xero (Optional)
```bash
# Set up Xero integration
python scripts/setup/xero_oauth_setup.py
```

### Learn More
- **[User Guide](../user-guide/README.md)** - Complete system documentation
- **[Basic Usage](../user-guide/basic-usage.md)** - Common workflows
- **[API Reference](../developer/api-reference.md)** - API integration

## 🔧 Common Commands

```bash
# Stop the system
docker-compose down

# Start the system (after initial setup)
docker-compose up

# View logs
docker-compose logs web

# Open a shell in the application container
docker-compose exec web bash

# Run tests
docker-compose exec web pytest
```

## 📊 Sample Data

The system includes sample financial data for testing:

```bash
# Generate reports for different years
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --date 2023-12-31
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --period 2023-Q4
docker-compose exec web python -m mcx3d_finance.cli.main generate cash-flow --period 2023

# Export to different formats
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --format pdf
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --format excel
```

## 🐛 Troubleshooting

### Port Already in Use
```bash
# Change the port in docker-compose.yml
# Edit the ports section: "8080:8000" instead of "8000:8000"
```

### Docker Permission Issues (Linux)
```bash
sudo usermod -aG docker $USER
# Log out and back in
```

### Database Connection Issues
```bash
# Reset the database
docker-compose down -v
docker-compose up --build
```

### Application Won't Start
```bash
# Check logs for errors
docker-compose logs web

# Rebuild containers
docker-compose build --no-cache
docker-compose up
```

## 📱 API Quick Test

Once the system is running, try these API calls:

```bash
# Get system health
curl http://localhost:8000/health

# List available organizations
curl http://localhost:8000/api/organizations

# Generate a balance sheet via API
curl -X POST "http://localhost:8000/api/reports/balance-sheet" \
  -H "Content-Type: application/json" \
  -d '{"organization_id": 1, "date": "2024-12-31"}'
```

## 🎯 Next Steps

Now that you have the system running:

1. **[Learn Basic Usage](../user-guide/basic-usage.md)** - Core workflows and features
2. **[Connect to Xero](../user-guide/xero-integration.md)** - Real data integration
3. **[Explore the API](../developer/api-reference.md)** - Build integrations
4. **[Deploy to Production](../operations/deployment.md)** - Production deployment

**Questions?** Check the [Troubleshooting Guide](../user-guide/troubleshooting.md) or [User Guide](../user-guide/README.md).