# MCX3D Financial Platform - Error Handling Guidelines

## Overview

This document provides guidelines for implementing consistent error handling across the MCX3D Financial Platform. All developers should follow these patterns to ensure reliable, debuggable, and secure error handling.

## Core Principles

1. **Fail Fast, Fail Explicitly**: Detect and report errors immediately with meaningful context
2. **Never Suppress Silently**: All errors must be logged, handled, or escalated appropriately
3. **Context Preservation**: Maintain full error context for debugging and analysis
4. **Recovery First**: Design systems with graceful degradation and automatic recovery

## Error Handling Patterns

### 1. Basic Error Handling with Error Boundary

Use the `error_boundary` context manager for all operations:

```python
from mcx3d_finance.exceptions.handlers import error_boundary
from mcx3d_finance.core.logging_factory import LoggerFactory

logger = LoggerFactory.get_logger(__name__, domain="your_domain")

def process_data(org_id: int, data: dict):
    with error_boundary(
        "process_data",
        organization_id=org_id,
        data_size=len(data),
        reraise=True  # Re-raise the exception after logging
    ):
        # Your operation here
        result = perform_processing(data)
        return result
```

### 2. External Service Calls with Recovery

All external service calls must use retry and circuit breaker patterns:

```python
from mcx3d_finance.exceptions.recovery import with_retry, with_circuit_breaker, RetryConfig

@with_retry(config=RetryConfig(
    max_attempts=3,
    initial_delay=1.0,
    max_delay=10.0,
    exponential_base=2,
    retryable_exceptions=(APIConnectionError, TimeoutError)
))
@with_circuit_breaker(name="external_api")
def call_external_api(endpoint: str, data: dict):
    with error_boundary("call_external_api", endpoint=endpoint):
        response = requests.post(endpoint, json=data, timeout=30)
        response.raise_for_status()
        return response.json()
```

### 3. Database Operations

Database operations should handle transient failures:

```python
@with_retry(config=RetryConfig(max_attempts=2))
def save_to_database(db: Session, entity: BaseModel):
    with error_boundary(
        "save_to_database",
        entity_type=type(entity).__name__,
        entity_id=getattr(entity, 'id', None)
    ):
        db.add(entity)
        db.commit()
        db.refresh(entity)
        return entity
```

### 4. Fallback Strategies

Implement fallback mechanisms for critical operations:

```python
def get_exchange_rate(from_currency: str, to_currency: str) -> float:
    # Primary: Live API
    try:
        return fetch_from_api(from_currency, to_currency)
    except (APIConnectionError, CircuitBreakerOpen) as e:
        logger.warning(
            "API unavailable, falling back to cache",
            error=str(e),
            from_currency=from_currency,
            to_currency=to_currency
        )
        
        # Secondary: Cache
        cached_rate = get_from_cache(from_currency, to_currency)
        if cached_rate:
            return cached_rate
            
        # Tertiary: Default rates
        return get_default_rate(from_currency, to_currency)
```

## Exception Hierarchy

### Creating Custom Exceptions

Always inherit from the appropriate base exception:

```python
from mcx3d_finance.exceptions.base import MCX3DException
from mcx3d_finance.exceptions.integration import IntegrationError

class CustomServiceError(IntegrationError):
    """Raised when custom service operations fail."""
    
    def __init__(self, service_name: str, operation: str, details: dict):
        super().__init__(
            message=f"{service_name} failed during {operation}",
            user_message="Service temporarily unavailable. Please try again.",
            error_code=f"CUSTOM_SERVICE_{operation.upper()}_ERROR",
            context={
                "service": service_name,
                "operation": operation,
                **details
            }
        )
```

## Logging Standards

### Structured Logging

Always use structured logging with appropriate context:

```python
logger.error(
    "Operation failed",
    correlation_id=correlation_id,
    operation="data_import",
    organization_id=org_id,
    error_type=type(e).__name__,
    error_details=str(e),
    duration_ms=elapsed_time
)
```

### Log Levels

- **DEBUG**: Detailed information for diagnosing problems
- **INFO**: General informational messages
- **WARNING**: Warning messages for recoverable issues
- **ERROR**: Error messages for failures requiring attention
- **CRITICAL**: Critical failures requiring immediate action

## Security Considerations

### Error Messages

1. **Never expose sensitive data** in error messages:
   ```python
   # BAD
   raise ValueError(f"Invalid password: {password}")
   
   # GOOD
   raise InvalidCredentialsError("Invalid credentials provided")
   ```

2. **Use environment-aware error details**:
   ```python
   if settings.ENVIRONMENT == "production":
       user_message = "An error occurred. Please contact support."
   else:
       user_message = f"Database connection failed: {str(e)}"
   ```

### Audit Logging

Security-related errors must be logged to audit logs:

```python
if isinstance(error, SecurityException):
    audit_logger.error(
        "Security violation detected",
        user_id=user_id,
        action=action,
        resource=resource,
        ip_address=request.client.host
    )
```

## Testing Error Scenarios

### Unit Tests

Test both success and failure paths:

```python
def test_process_data_handles_errors():
    with pytest.raises(DataProcessingError) as exc_info:
        process_data(invalid_data)
    
    assert exc_info.value.error_code == "INVALID_DATA_FORMAT"
    assert "correlation_id" in exc_info.value.context
```

### Integration Tests

Test recovery mechanisms:

```python
def test_api_client_retries_on_failure(mock_api):
    mock_api.side_effect = [
        ConnectionError("Network error"),
        ConnectionError("Network error"),
        {"status": "success"}
    ]
    
    result = api_client.fetch_data()
    assert result["status"] == "success"
    assert mock_api.call_count == 3
```

## Monitoring and Alerting

### Error Metrics

Monitor these key metrics:

1. **Error Rate**: Errors per minute/hour
2. **Error Types**: Distribution of error types
3. **Recovery Success**: Successful retries vs failures
4. **Circuit Breaker State**: Open/closed states
5. **Response Times**: Including retry delays

### Alert Thresholds

Configure alerts for:

- Error rate > 1% of requests
- Circuit breaker open for > 5 minutes
- Repeated errors from same operation
- Security-related errors

## Common Patterns

### Batch Operations

Handle partial failures in batch operations:

```python
def process_batch(items: List[dict]) -> dict:
    results = {"successful": [], "failed": []}
    
    for item in items:
        with error_boundary(
            "process_item",
            item_id=item.get("id"),
            reraise=False
        ):
            result = process_single_item(item)
            results["successful"].append(result)
        except MCX3DException as e:
            results["failed"].append({
                "item": item,
                "error": e.to_dict()
            })
    
    return results
```

### Async Error Handling

For async operations:

```python
async def async_operation(data: dict):
    async with error_boundary(
        "async_operation",
        data_size=len(data)
    ):
        result = await external_async_call(data)
        return result
```

## Migration Guide

### Updating Existing Code

1. **Replace basic try/except** with error_boundary:
   ```python
   # OLD
   try:
       result = operation()
   except Exception as e:
       logger.error(f"Failed: {e}")
       raise
   
   # NEW
   with error_boundary("operation"):
       result = operation()
   ```

2. **Add recovery patterns** to external calls:
   ```python
   # OLD
   def call_api():
       return requests.get(url)
   
   # NEW
   @with_retry(config=RetryConfig(max_attempts=3))
   @with_circuit_breaker(name="api")
   def call_api():
       with error_boundary("call_api", url=url):
           return requests.get(url)
   ```

## Checklist

Before submitting code, ensure:

- [ ] All operations use error_boundary
- [ ] External calls have retry and circuit breaker
- [ ] Errors include correlation IDs
- [ ] Sensitive data is not exposed
- [ ] Fallback strategies exist for critical operations
- [ ] Error scenarios are tested
- [ ] Logging uses structured format
- [ ] Custom exceptions inherit from correct base class

## Support

For questions about error handling patterns, contact the platform team or refer to the example implementations in the codebase.