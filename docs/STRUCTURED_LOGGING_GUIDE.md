# MCX3D Financials - Structured Logging Guide

## Overview

The MCX3D Financials platform implements comprehensive structured logging using JSON format with correlation IDs, domain-specific loggers, and automatic security redaction. This guide covers the logging architecture, implementation patterns, and best practices.

## Architecture

### Core Components

1. **Structured Logger** (`mcx3d_finance/monitoring/structured_logger.py`)
   - Base implementation using `structlog`
   - Correlation ID management
   - Business event tracking
   - Performance metrics
   - Security event logging

2. **Logging Configuration** (`mcx3d_finance/utils/logging_config.py`)
   - Centralized configuration
   - Domain-specific logger factory
   - Security filtering
   - Performance monitoring
   - Context enrichment

3. **Logging Adapter** (`mcx3d_finance/utils/logging_adapter.py`)
   - Drop-in replacement for Python's standard logging
   - Automatic domain detection
   - Backward compatibility

4. **Domain-Specific Utilities**
   - API Logging (`mcx3d_finance/utils/api_logging.py`)
   - Database Logging (`mcx3d_finance/utils/db_logging.py`)

## Implementation Patterns

### Basic Usage

```python
# Import the appropriate logger
from mcx3d_finance.utils.logging_config import get_api_logger
logger = get_api_logger("auth")

# Or use the adapter for backward compatibility
from mcx3d_finance.utils.logging_adapter import get_logger
logger = get_logger(__name__)
```

### Business Events

```python
# Log business-critical events
logger.log_business_event(
    "report_generated",
    report_type="income_statement",
    organization_id=org_id,
    duration_ms=1234.5,
    file_size_bytes=102400
)

# Log financial transactions
logger.log_financial_transaction(
    transaction_type="invoice_payment",
    amount=1500.00,
    currency="USD",
    invoice_id="INV-001",
    organization_id=org_id
)
```

### Performance Logging

```python
# Using decorator
from mcx3d_finance.utils.logging_config import log_performance

@log_performance("generate_report")
def generate_report(org_id: int):
    # Your code here
    pass

# Manual performance logging
logger.log_performance_metric(
    operation="database_query",
    duration_ms=45.2,
    success=True,
    query_type="aggregate",
    row_count=1500
)
```

### Security Events

```python
# Authentication events
from mcx3d_finance.utils.api_logging import log_authentication_event

log_authentication_event(
    "login_success",
    user_id=user_id,
    success=True,
    method="password",
    session_id=session_id
)

# Authorization events
from mcx3d_finance.utils.api_logging import log_authorization_event

log_authorization_event(
    resource="financial_report",
    action="read",
    user_id=user_id,
    allowed=True,
    organization_id=org_id
)

# Security threats
logger.log_security_event(
    event_type="suspicious_activity",
    severity="high",
    user_id=user_id,
    details={"reason": "multiple_failed_logins", "count": 5}
)
```

### Error Logging

```python
try:
    process_data()
except Exception as e:
    logger.log_error(
        e,
        operation="data_processing",
        organization_id=org_id,
        data_type="transactions",
        batch_size=100
    )
```

### API Request/Response Logging

```python
# Automatic with middleware
from mcx3d_finance.utils.api_logging import APILoggingMiddleware
app.add_middleware(APILoggingMiddleware)

# Manual endpoint logging
from mcx3d_finance.utils.api_logging import log_api_endpoint

@log_api_endpoint("create_report")
async def create_report(request: Request):
    # Your endpoint code
    pass
```

### Database Operations

```python
# Query logging
from mcx3d_finance.utils.db_logging import setup_database_logging
setup_database_logging(engine)

# Transaction logging
from mcx3d_finance.utils.db_logging import log_db_transaction

@log_db_transaction("update_transactions")
def update_transactions(org_id: int):
    # Your database operations
    pass

# Bulk operations
from mcx3d_finance.utils.db_logging import log_bulk_operation

with log_bulk_operation("import_invoices", record_count=1000):
    # Your bulk import code
    pass
```

## Log Structure

### Standard Fields

Every log entry includes:
```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "correlation_id": "550e8400-e29b-41d4-a716-446655440000",
  "service": "mcx3d-finance",
  "environment": "production",
  "level": "info",
  "message": "business_event",
  "event_type": "report_generated",
  "component": "api:reports"
}
```

### Business Event Fields

```json
{
  "event_type": "report_generated",
  "organization_id": 123,
  "user_id": "456",
  "report_type": "income_statement",
  "duration_ms": 1234.5,
  "success": true,
  "output_format": "pdf",
  "file_size_bytes": 102400
}
```

### Performance Metric Fields

```json
{
  "operation": "generate_report",
  "duration_ms": 1234.5,
  "success": true,
  "performance_warning": "SLOW_OPERATION",
  "threshold_exceeded_by_ms": 234.5,
  "resource_usage": {
    "memory_mb": 150,
    "cpu_percent": 45
  }
}
```

### Security Event Fields

```json
{
  "event_type": "login_failed",
  "severity": "medium",
  "user_id": "123",
  "ip_address": "*************",
  "reason": "invalid_password",
  "requires_attention": false
}
```

## Domain-Specific Loggers

### API Logger
```python
from mcx3d_finance.utils.logging_config import get_api_logger
logger = get_api_logger("auth")
```
Use for: HTTP requests, responses, authentication, authorization

### Core Logger
```python
from mcx3d_finance.utils.logging_config import get_core_logger
logger = get_core_logger("financial_calculator")
```
Use for: Business logic, calculations, financial operations

### Integration Logger
```python
from mcx3d_finance.utils.logging_config import get_integration_logger
logger = get_integration_logger("xero_client")
```
Use for: External service calls, API integrations, webhooks

### Database Logger
```python
from mcx3d_finance.utils.logging_config import get_db_logger
logger = get_db_logger("query_monitor")
```
Use for: Database queries, transactions, connection pooling

### Task Logger
```python
from mcx3d_finance.utils.logging_config import get_task_logger
logger = get_task_logger("report_generation")
```
Use for: Background tasks, Celery jobs, async operations

### Security Logger
```python
from mcx3d_finance.utils.logging_config import get_security_logger
logger = get_security_logger("access_control")
```
Use for: Security events, authentication, authorization, threats

## Security and Compliance

### Automatic Redaction

Sensitive fields are automatically redacted:
```python
# These fields will be logged as '[REDACTED]'
REDACTED_FIELDS = {
    'password', 'secret', 'token', 'api_key', 'private_key',
    'access_token', 'refresh_token', 'authorization',
    'ssn', 'social_security_number', 'credit_card',
    'cvv', 'pin', 'account_number', 'routing_number'
}
```

### Audit Logging

```python
# Financial compliance logging
logger.log_financial_transaction(
    transaction_type="payment_received",
    amount=5000.00,
    currency="USD",
    audit_trail=True,
    compliance_check="passed"
)

# User activity tracking
logger.log_user_activity(
    action="export_financial_data",
    user_id=user_id,
    resource="balance_sheet",
    organization_id=org_id,
    ip_address=request.client.host
)
```

## Performance Considerations

### Log Levels by Environment

- **Development**: DEBUG - All logs including detailed debugging
- **Testing**: INFO - Business events and important operations
- **Staging**: INFO - Same as testing
- **Production**: WARNING - Only warnings, errors, and critical events

### Performance Optimization

1. **Lazy Evaluation**: Use lambdas for expensive operations
   ```python
   logger.debug("Complex data", 
                data=lambda: expensive_serialization())
   ```

2. **Batch Logging**: Use context managers for related operations
   ```python
   with logger.correlation_context(correlation_id=request_id):
       # All logs within this context share the correlation ID
       process_request()
   ```

3. **Sampling**: For high-volume operations
   ```python
   if random.random() < 0.1:  # Log 10% of requests
       logger.log_performance_metric(...)
   ```

## Integration with Monitoring

### Prometheus Metrics

Logs are automatically exported as metrics:
- `mcx3d_business_events_total` - Counter of business events
- `mcx3d_operation_duration_seconds` - Histogram of operation durations
- `mcx3d_security_events_total` - Counter of security events by severity

### Log Aggregation

Logs are structured for easy aggregation:
```json
{
  "service": "mcx3d-finance",
  "component": "api:reports",
  "organization_id": 123,
  "user_id": "456"
}
```

Query examples:
```
# Find all logs for an organization
organization_id=123

# Find all API errors
component="api:*" AND level="error"

# Track user activity
user_id="456" AND event_type="user_activity"
```

## Migration Guide

### From Standard Logging

```python
# Old
import logging
logger = logging.getLogger(__name__)
logger.info(f"User {user_id} logged in")

# New
from mcx3d_finance.utils.logging_adapter import get_logger
logger = get_logger(__name__)
logger.log_business_event(
    "user_login",
    user_id=user_id,
    success=True
)
```

### From Print Statements

```python
# Old
print(f"Processing {count} records")

# New
logger.info("Processing records", count=count)
```

### Running Migration Script

```bash
# Analyze codebase for logging patterns
python -m mcx3d_finance.utils.migrate_logging --analyze

# View migration guide
python -m mcx3d_finance.utils.migrate_logging --guide
```

## Best Practices

1. **Always include context**: Organization ID, user ID, operation type
2. **Use structured data**: Pass data as keyword arguments, not in strings
3. **Log at boundaries**: API entry/exit, external calls, database operations
4. **Include business meaning**: Use business event types, not just technical details
5. **Performance awareness**: Log duration for operations >100ms
6. **Security first**: Never log sensitive data, use security event logging for threats
7. **Correlation tracking**: Use correlation IDs for request tracing
8. **Error context**: Include operation, input data (sanitized), and recovery actions
9. **Use appropriate levels**: DEBUG for development, INFO for business events, WARNING for issues
10. **Monitor and alert**: Set up alerts for ERROR and CRITICAL logs

## Troubleshooting

### Common Issues

1. **Missing correlation ID**: Ensure you're within a correlation context
2. **Performance impact**: Check log level settings in production
3. **Large log files**: Configure rotation and retention policies
4. **Missing logs**: Verify logger configuration and level settings

### Debug Logging

```python
# Enable debug logging for specific component
import logging
logging.getLogger('mcx3d_finance.core.financial_calculator').setLevel(logging.DEBUG)
```

### Log Analysis Tools

- **Local Development**: `tail -f logs/mcx3d-finance.log | jq`
- **Production**: Use log aggregation service queries
- **Correlation Tracking**: Filter by correlation_id to trace full request flow

## Future Enhancements

1. **Distributed Tracing**: OpenTelemetry integration
2. **ML-based Anomaly Detection**: Automatic detection of unusual patterns
3. **Real-time Dashboards**: Business metrics visualization
4. **Compliance Reporting**: Automated audit log reports
5. **Performance Profiling**: Automatic slow operation detection