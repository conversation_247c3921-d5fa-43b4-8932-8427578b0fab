# Investor Report Sandbox - Deep Context

## Purpose & Overview
**Financial analysis system for MCX3D LTD's investor reporting** - generates comprehensive, data-driven financial reports from Xero accounting data for fundraising activities.

## Core Architecture

### System Design Philosophy
- **100% Data-Driven**: No hardcoded values, all calculations from actual Xero data
- **Single Source of Truth**: Unified calculation engine in `analysis_unified/core/calculations.py`
- **UK FRS 102 Compliant**: All financial calculations follow UK accounting standards
- **Multi-Format Output**: PDF reports, JSON data, visualizations, and Jupyter notebooks

### Data Flow Pipeline
1. **Xero Integration** → OAuth 2.0 authentication → Fetch JSON data
2. **Data Loading** → Validation and normalization in `data_loader.py`
3. **Calculation Engine** → Unified metrics calculation in `calculations.py`
4. **Analysis Modules** → Specialized analyzers for revenue, expenses, unit economics
5. **Validation** → Multi-tier validation including balance sheet reconciliation
6. **Visualization** → 8 different financial charts using matplotlib/seaborn
7. **Report Generation** → Professional PDF reports with ReportLab + Jupyter notebooks

## Key Components

### Main Orchestrator
- `UnifiedFinancialAnalyzer` class in `financial_analyzer.py`
- Manages entire analysis workflow
- Coordinates between data loading, calculations, analysis, and reporting

### Core Modules (`analysis_unified/core/`)
1. **data_loader.py**: Loads and validates Xero JSON files
2. **calculations.py**: Core financial calculations (revenue, expenses, margins, SaaS metrics)
3. **validator.py**: Multi-tier validation system
4. **visualizations.py**: Chart generation engine

### Specialized Analyzers (`analysis_unified/analyzers/`)
1. **revenue_analyzer.py**: Customer concentration, growth trends, multi-currency
2. **expense_analyzer.py**: Burn rate, category breakdown, efficiency metrics
3. **unit_economics.py**: CAC, LTV, payback period, churn calculations
4. **cashflow_analyzer.py**: Cash runway and flow analysis
5. **projections.py**: Growth scenario modeling

### Key Business Logic & Metrics

#### Financial Calculations
- **Revenue Recognition**: Uses P&L statement total (includes unbilled revenue)
- **Bank Transactions**: Primary source for revenue timeline and customer analysis
- **Multi-Currency**: Supports multiple currencies with GBP as primary
- **Gross Margin**: 100% for software business model

#### SaaS Metrics
- **CAC**: £35 (CEO-led sales model, minimal marketing spend)
- **LTV**: Average revenue per customer × customer lifetime
- **LTV/CAC Ratio**: 131.4:1 (exceptional unit economics)
- **Churn Rate**: 69.7% (calculated with April 2025 cutoff)
- **Payback Period**: CAC / (MRR × Gross Margin)

#### Business Decisions
- **Accounting Basis**: Accrual accounting
- **Balance Sheet Treatment**: Directors' loan = equity, Wise accounts = assets
- **Churn Calculation**: Last payment date = service end date
- **Revenue Source**: P&L statement for total revenue (captures unbilled clients)

## Data Requirements

### Required Xero JSON Files
- `invoices_latest.json`: Invoice data for customer analysis
- `profit_loss_latest.json`: P&L statement (primary revenue source)
- `balance_sheet_latest.json`: Balance sheet for validation
- `bank_transactions_latest.json`: Transaction history for timeline
- `contacts_latest.json`: Customer information
- `accounts_latest.json`: Chart of accounts
- `trial_balance_latest.json`: Trial balance
- `organization_latest.json`: Organization details

### Output Structure
```
analysis_output/
├── financial_analysis.json          # Complete analysis results
├── reports/
│   └── modular_cx_financial_report.pdf
└── graphs/
    ├── revenue_forecast.png
    ├── expense_breakdown.png
    ├── cash_flow_waterfall.png
    ├── unit_economics_dashboard.png
    ├── customer_concentration.png
    ├── cash_runway_analysis.png
    ├── scenario_comparison.png
    └── cash_efficiency_dashboard.png
```

## Validation & Quality Assurance

### Multi-Tier Validation
1. **Data Completeness**: Ensures all required files are present
2. **Balance Sheet Reconciliation**: Assets = Liabilities + Equity
3. **Revenue Consistency**: Invoice revenue matches P&L statement
4. **Unit Economics Sanity**: Ratios within reasonable bounds
5. **Cash Position**: Validates cash flow calculations

### Error Handling
- Graceful handling of missing data
- Comprehensive logging to `financial_analysis.log`
- Validation reports with specific error details

## Integration Points

### Xero Authentication Flow
1. `xero_auth_step1.py`: Generate OAuth URL
2. `xero_auth_step2.py`: Complete authentication
3. `xero_fetch_data.py`: Fetch latest data

### Jupyter Notebook Integration
- `notebook_data_bridge.py`: Provides calculated metrics to notebooks
- Professional investor presentation with historical analysis
- Interactive data exploration capabilities

## Usage Patterns

### Command Line Interface
```bash
# Full analysis
python analyze.py --full

# Specific analyses
python analyze.py --revenue
python analyze.py --expenses
python analyze.py --unit-economics
python analyze.py --validate

# Options
--no-pdf       # Skip PDF generation
--no-charts    # Skip chart generation
--format json  # JSON output format
```

## Important Notes
- Virtual environment: `venv_investor/`
- Never commit `xero_token.json` or credentials
- `analysis_archive/` contains deprecated code - DO NOT USE
- All calculations must be data-driven (no hardcoding)