# MCX3D Financials V2 - Project Context

## Architecture Overview
**Multi-tenant UK financial analytics platform** with production monitoring capabilities.

### Core Application Structure
```
mcx3d_finance/
├── main.py                    # Basic FastAPI development entry point
├── main_with_monitoring.py    # Production app with full monitoring stack
├── api/                       # FastAPI REST endpoints
├── core/                      # Business logic layer
├── db/                        # Data models and database configuration
├── integrations/             # External service integrations (Xero, MCP)
├── monitoring/               # Production monitoring & observability
├── security/                 # Authentication, encryption, middleware
├── tasks/                    # Celery background task processing
├── validation/               # Multi-tier data validation
└── utils/                    # Shared utilities
```

### Key Technologies
- **Framework**: FastAPI with Pydantic for API layer
- **Database**: PostgreSQL with SQLAlchemy ORM, Redis for caching
- **Queue**: Celery for background processing
- **Monitoring**: Prometheus metrics, structured logging, Grafana dashboards
- **Security**: JWT auth, field-level encryption, comprehensive middleware
- **Testing**: pytest with >85% coverage requirement

## Business Domain
**UK FRS 102 compliant financial platform** specializing in:
- Financial statement generation (P&L, Balance Sheet, Cash Flow)
- SaaS KPI calculations and valuations
- Xero integration with real-time synchronization
- Multi-format report generation (PDF, Excel, HTML, JSON)

### Core Financial Components
- `financial_calculators.py`: UK-compliant financial calculations
- `account_classifications.py`: UK GAAP account mapping
- `valuation/`: DCF, multiples, and SaaS-specific valuation models
- `data_validation.py`: Multi-tier financial data validation

## Integration Architecture
- **Xero OAuth 2.0**: Real-time data sync with intelligent rate limiting
- **MCP Protocol**: Enhanced data processing capabilities
- **Multi-tenant**: Organization-scoped data isolation
- **Webhook Processing**: Real-time external event handling

## Development Environment
- **Commands**: `docker-compose up --build` for full stack
- **Testing**: `docker-compose exec web pytest --cov-fail-under=85`
- **Database**: Alembic migrations, health monitoring
- **Quality**: black, flake8, mypy for code standards

## Configuration Management
Environment-specific configs in `config/` directory:
- Auto-detection via ENVIRONMENT/ENV/APP_ENV variables
- Secure secret management via environment variables
- Production-safe defaults with validation

## Production Features
- Comprehensive monitoring with metrics server on port 8001
- Business intelligence dashboard with KPI collection
- Multi-channel alerting (email, Slack, PagerDuty)
- Audit trails and structured logging
- Health checks at multiple levels (basic/comprehensive/business)