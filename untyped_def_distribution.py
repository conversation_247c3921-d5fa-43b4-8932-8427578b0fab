import re
from collections import Counter

def untyped_def_distribution(file_path):
    """
    Parses a mypy error file and counts the number of [no-untyped-def]
    errors per file.

    Args:
        file_path (str): The path to the mypy error file.

    Returns:
        dict: A dictionary with the error counts per file.
    """
    error_pattern = re.compile(r"^(.*?):.*: error: .* \[no-untyped-def\]$")
    error_counts = Counter()

    with open(file_path, "r") as f:
        for line in f:
            match = error_pattern.match(line.strip())
            if match:
                file = match.group(1)
                error_counts[file] += 1

    return error_counts

if __name__ == "__main__":
    error_counts = untyped_def_distribution("mypy_errors.txt")
    for file, count in error_counts.most_common():
        print(f"{file}: {count}")