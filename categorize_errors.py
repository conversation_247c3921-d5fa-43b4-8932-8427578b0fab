import re
from collections import Counter

def categorize_mypy_errors(file_path):
    """
    Parses a mypy error file and categorizes the errors by error code.

    Args:
        file_path (str): The path to the mypy error file.

    Returns:
        dict: A dictionary with the error counts.
    """
    error_pattern = re.compile(r"\[([a-z-]+)\]$")
    error_counts = Counter()

    with open(file_path, "r") as f:
        for line in f:
            match = error_pattern.search(line.strip())
            if match:
                error_code = match.group(1)
                error_counts[error_code] += 1

    return error_counts

if __name__ == "__main__":
    error_counts = categorize_mypy_errors("mypy_errors.txt")
    for error_code, count in error_counts.most_common():
        print(f"{error_code}: {count}")