services:
  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - ./reports:/app/reports
    env_file:
      - .env
    environment:
      - DATABASE_URL=**********************************/mcx3d_db
      - REDIS_URL=redis://redis:6379/0
      - "SECRET_KEY=C7VKA43ZDuTxUKLf#S2RNdcFJ^embDF-9kp_y5n3I2M-*DZCHOw@sy0mf2hm1IVe"
      - ENVIRONMENT=development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  db:
    image: postgres:13
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=mcx3d_db
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d mcx3d_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  worker:
    build: .
    command: celery -A mcx3d_finance.tasks.celery_app worker --loglevel=info
    volumes:
      - .:/app
      - ./reports:/app/reports
    env_file:
      - .env.development
    environment:
      - DATABASE_URL=**********************************/mcx3d_db
      - REDIS_URL=redis://redis:6379/0
      - "SECRET_KEY=C7VKA43ZDuTxUKLf#S2RNdcFJ^embDF-9kp_y5n3I2M-*DZCHOw@sy0mf2hm1IVe"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "celery", "-A", "mcx3d_finance.tasks.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:
