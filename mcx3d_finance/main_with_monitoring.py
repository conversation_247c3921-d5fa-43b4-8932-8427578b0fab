"""
MCX3D Financial Application with Comprehensive Production Monitoring

This file demonstrates how to integrate the full monitoring stack with the
MCX3D Financial application for production deployment.
"""

import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging
from mcx3d_finance.core.logging_factory import LoggerFactory

# Import monitoring components
from mcx3d_finance.monitoring import (
    # Core monitoring
    initialize_metrics,
    start_metrics_server,
    main_logger,
    
    # Alerting
    alert_manager,
    AlertSeverity,
    
    # Business Intelligence
    BusinessIntelligenceCollector,
    
    # Health checking
    HealthChecker,
    
    # Audit trails
    audit_manager,
    audit_user_login
)

# Import API middleware
from mcx3d_finance.api.middleware.monitoring import add_monitoring_middleware

# Import existing API components
from mcx3d_finance.api import health, reports, auth_routes, metrics, dashboard
from mcx3d_finance.db.session import get_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = LoggerFactory.get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan with monitoring initialization."""
    
    # Startup
    logger.info("Starting MCX3D Financial Application with Production Monitoring...")
    
    try:
        # Initialize monitoring systems
        main_logger.log_business_event(
            'application_startup',
            service='mcx3d-finance',
            version='2.0.0',
            monitoring_enabled=True
        )
        
        # Initialize Prometheus metrics
        initialize_metrics()
        logger.info("Prometheus metrics initialized")
        
        # Start metrics server on separate port
        metrics_port = int(os.getenv('METRICS_PORT', '8001'))
        start_metrics_server(metrics_port)
        logger.info(f"Metrics server started on port {metrics_port}")
        
        # Initialize business intelligence collector
        db = next(get_db())
        bi_collector = BusinessIntelligenceCollector(db_session=db)
        app.state.bi_collector = bi_collector
        logger.info("Business Intelligence collector initialized")
        
        # Initialize health checker
        health_checker = HealthChecker(db_session=db)
        app.state.health_checker = health_checker
        logger.info("Health checker initialized")
        
        # Test alert system
        await alert_manager.trigger_alert(
            AlertSeverity.INFO,
            "MCX3D Financial Application Started",
            "Application successfully started with full monitoring enabled",
            {
                'component': 'application',
                'startup_time': 'success',
                'monitoring_version': '1.0.0'
            },
            'app_startup'
        )
        
        logger.info("MCX3D Financial Application startup complete with monitoring enabled")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize monitoring: {e}")
        # Continue startup even if monitoring fails
        yield
    
    # Shutdown
    logger.info("Shutting down MCX3D Financial Application...")
    
    main_logger.log_business_event(
        'application_shutdown',
        service='mcx3d-finance',
        graceful_shutdown=True
    )

def create_monitored_app() -> FastAPI:
    """Create FastAPI application with comprehensive monitoring."""
    
    app = FastAPI(
        title="MCX3D Financial Platform",
        description="Professional Financial Valuation and Reporting Platform with Production Monitoring",
        version="2.0.0",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add comprehensive monitoring middleware
    add_monitoring_middleware(app, full_monitoring=True)
    
    # Include API routers
    app.include_router(health.router)
    app.include_router(reports.router)
    app.include_router(auth_routes.router)
    app.include_router(metrics.router)
    app.include_router(dashboard.router)
    
    # Add custom monitoring endpoints
    @app.get("/monitoring/status")
    async def monitoring_status():
        """Get monitoring system status."""
        return {
            "monitoring_enabled": True,
            "components": {
                "structured_logging": True,
                "prometheus_metrics": True,
                "alerting_system": True,
                "business_intelligence": True,
                "health_monitoring": True,
                "audit_trails": True
            },
            "metrics_endpoint": "/metrics",
            "health_endpoints": [
                "/health/",
                "/health/detailed", 
                "/health/comprehensive",
                "/health/business",
                "/health/readiness",
                "/health/liveness"
            ]
        }
    
    @app.get("/monitoring/business-dashboard")
    async def business_dashboard():
        """Get business intelligence dashboard data."""
        try:
            bi_collector = app.state.bi_collector
            
            # Collect current KPIs
            kpis = await bi_collector.collect_daily_kpis()
            
            # Detect anomalies
            anomalies = await bi_collector.detect_anomalies(kpis)
            
            # Generate health score
            health_score = await bi_collector.generate_business_health_score(kpis)
            
            return {
                "timestamp": "2024-01-01T00:00:00Z",  # Current timestamp
                "health_score": health_score,
                "kpis": {name: {
                    "name": kpi.name,
                    "value": kpi.value,
                    "target": kpi.target,
                    "unit": kpi.unit,
                    "trend": kpi.trend.value,
                    "is_healthy": kpi.is_healthy
                } for name, kpi in kpis.items()},
                "anomalies": len(anomalies),
                "recommendations": health_score.get('insights', [])
            }
            
        except Exception as e:
            logger.error(f"Business dashboard failed: {e}")
            return {"error": "Business dashboard temporarily unavailable"}
    
    @app.post("/monitoring/test-alert")
    async def test_alert(request: Request):
        """Test alert system (for development/testing)."""
        try:
            body = await request.json()
            severity = AlertSeverity(body.get('severity', 'info'))
            
            await alert_manager.trigger_alert(
                severity,
                body.get('title', 'Test Alert'),
                body.get('message', 'This is a test alert'),
                body.get('context', {'component': 'test'}),
                'test_alert'
            )
            
            return {"status": "Alert sent successfully"}
            
        except Exception as e:
            logger.error(f"Test alert failed: {e}")
            return {"error": str(e)}
    
    # Add middleware for audit logging
    @app.middleware("http")
    async def audit_middleware(request: Request, call_next):
        """Middleware for audit logging."""
        response = await call_next(request)
        
        # Log user activities for audit
        if hasattr(request.state, 'user_id') and request.state.user_id:
            # This would be called for significant user actions
            if request.url.path.startswith('/api/reports/') and request.method == 'POST':
                await audit_manager.log_report_generation(
                    {
                        'report_type': 'api_generated',
                        'endpoint': request.url.path,
                        'method': request.method,
                        'status_code': response.status_code
                    },
                    request.state.user_id
                )
        
        return response
    
    return app

def run_production_server():
    """Run the application server with production monitoring."""
    
    # Configuration
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '8000'))
    workers = int(os.getenv('WORKERS', '1'))
    log_level = os.getenv('LOG_LEVEL', 'info')
    
    # Create monitored application
    app = create_monitored_app()
    
    logger.info(f"Starting MCX3D Financial server on {host}:{port} with monitoring")
    
    # Run with uvicorn
    uvicorn.run(
        app,
        host=host,
        port=port,
        workers=workers,
        log_level=log_level,
        access_log=True,
        loop="uvloop",  # High-performance event loop
        http="httptools"  # High-performance HTTP parser
    )

if __name__ == "__main__":
    # Example usage and testing
    import asyncio
    
    async def test_monitoring_integration():
        """Test monitoring system integration."""
        print("Testing MCX3D Financial Monitoring Integration...")
        
        # Test structured logging
        main_logger.log_business_event(
            'test_event',
            test_parameter='test_value',
            integration_test=True
        )
        print("✓ Structured logging test passed")
        
        # Test alerting
        await alert_manager.trigger_alert(
            AlertSeverity.INFO,
            "Integration Test Alert",
            "Testing alert system integration",
            {'component': 'integration_test'},
            'integration_test'
        )
        print("✓ Alerting system test passed")
        
        # Test audit trails
        await audit_manager.log_user_authentication(
            'test_user',
            'integration_test',
            True,
            '127.0.0.1',
            'test-agent'
        )
        print("✓ Audit trails test passed")
        
        print("All monitoring integration tests passed!")
    
    # Run tests
    asyncio.run(test_monitoring_integration())
    
    # Run production server
    run_production_server()