import asyncio
import typer
from mcx3d_finance.reporting.comprehensive_report import ComprehensiveReportGenerator

app = typer.Typer()

@app.command()
def generate_historic_reports(
    organization_id: int = 2,
    start_year: int = 2019,
    end_year: int = 2024,
):
    """
    Generates historic comprehensive financial reports for a given organization.
    """
    for year in range(start_year, end_year + 1):
        try:
            typer.echo(f"Generating report for year {year}...")
            generator = ComprehensiveReportGenerator(organization_id, year, use_xero_trial_balance=True)
            report = generator.generate()
            generator.save_reports(report)
            typer.echo(f"Successfully generated report for year {year}")
        except Exception as e:
            typer.echo(f"Failed to generate report for year {year}: {e}")

if __name__ == "__main__":
    app()