"""
MCX3D LTD Company Configuration Module

This module contains company-specific information, branding, and accounting policies
for MCX3D LTD (Modular CX).
"""

from mcx3d_finance.company.company_info import COMPANY_INFO
from mcx3d_finance.company.branding import BRANDING_CONFIG
from mcx3d_finance.company.accounting_policies import ACCOUNTING_POLICIES

__all__ = ["COMPANY_INFO", "BRANDING_CONFIG", "ACCOUNTING_POLICIES"]
