"""
MCX3D LTD Accounting Policies

Defines the accounting policies and methods used for financial reporting.
Compliant with UK GAAP and FRS 102.
"""

from typing import Dict, Any, List

ACCOUNTING_POLICIES: Dict[str, Any] = {
    # General Information
    "reporting_framework": "FRS 102",
    "reporting_standard": (
        "The Financial Reporting Standard applicable in the UK and Republic of Ireland"
    ),
    "functional_currency": "GBP",
    "presentation_currency": "GBP",
    # Basis of Preparation
    "basis_of_preparation": {
        "title": "Basis of Preparation",
        "policy": (
            "These financial statements have been prepared in accordance with applicable "
            "United Kingdom accounting standards, including Financial Reporting Standard 102 "
            "'The Financial Reporting Standard applicable in the UK and Republic of Ireland' "
            "(FRS 102) and the Companies Act 2006. The financial statements have been prepared "
            "under the historical cost convention."
        ),
        "going_concern": (
            "The directors have prepared the financial statements on a going concern basis "
            "after reviewing the company's forecasts and projections, taking into account "
            "possible changes in trading performance."
        ),
    },
    # Revenue Recognition
    "revenue_recognition": {
        "title": "Revenue Recognition",
        "policy": (
            "Revenue is recognised when the significant risks and rewards of ownership have "
            "been transferred to the customer, recovery of the consideration is probable, "
            "the associated costs can be estimated reliably, and the amount of revenue can "
            "be measured reliably."
        ),
        "specific_policies": {
            "software_licenses": (
                "Revenue from software licenses is recognised at the point of delivery "
                "when control passes to the customer."
            ),
            "saas_subscriptions": (
                "Subscription revenue is recognised on a straight-line basis over the "
                "subscription period as the performance obligations are satisfied over time."
            ),
            "professional_services": (
                "Revenue from professional services and implementation is recognised "
                "based on the stage of completion of the contract."
            ),
            "support_maintenance": (
                "Support and maintenance revenue is recognised on a straight-line basis "
                "over the period of the support contract."
            ),
        },
    },
    # Property, Plant and Equipment
    "tangible_assets": {
        "title": "Property, Plant and Equipment",
        "policy": (
            "Property, plant and equipment are stated at cost less accumulated depreciation "
            "and any accumulated impairment losses. Cost includes the original purchase price "
            "of the asset and the costs attributable to bringing the asset to its working "
            "condition for its intended use."
        ),
        "depreciation_rates": {
            "computer_equipment": {"rate": 0.33, "method": "straight-line", "years": 3},
            "office_equipment": {"rate": 0.25, "method": "straight-line", "years": 4},
            "furniture_fixtures": {"rate": 0.20, "method": "straight-line", "years": 5},
        },
        "capitalisation_threshold": 1000,  # GBP
    },
    # Intangible Assets
    "intangible_assets": {
        "title": "Intangible Assets",
        "policy": (
            "Intangible assets are stated at cost less accumulated amortisation and "
            "impairment losses. Intangible assets are recognised when it is probable "
            "that future economic benefits will flow to the company."
        ),
        "specific_policies": {
            "software_development": (
                "Costs associated with maintaining software are recognised as an expense "
                "as incurred. Development costs that are directly attributable to the design "
                "and testing of identifiable and unique software products controlled by the "
                "company are recognised as intangible assets when specific criteria are met."
            ),
            "intellectual_property": (
                "Acquired intellectual property rights are capitalised on the basis of "
                "the costs incurred to acquire and bring to use the specific rights."
            ),
        },
        "amortisation_rates": {
            "software": {"rate": 0.20, "method": "straight-line", "years": 5},
            "ip_rights": {"rate": 0.10, "method": "straight-line", "years": 10},
        },
    },
    # Research and Development
    "research_development": {
        "title": "Research and Development",
        "policy": (
            "Research expenditure is written off as incurred. Development expenditure is "
            "capitalised only when the company can demonstrate all of the following: "
            "technical feasibility, intention to complete, ability to use or sell, "
            "generation of probable future economic benefits, availability of resources, "
            "and ability to measure costs reliably."
        ),
        "r_and_d_tax_credits": (
            "Research and Development tax credits are recognised when there is reasonable "
            "assurance that they will be received and are treated as a credit against "
            "corporation tax or as other operating income."
        ),
    },
    # Financial Instruments
    "financial_instruments": {
        "title": "Financial Instruments",
        "policy": (
            "The company only enters into basic financial instrument transactions that "
            "result in the recognition of financial assets and liabilities like trade "
            "and other accounts receivable and payable, loans from banks and other "
            "third parties."
        ),
        "measurement": {
            "initial": "At transaction price including transaction costs",
            "subsequent": "At amortised cost using the effective interest method",
        },
        "derecognition": (
            "Financial assets are derecognised when the contractual rights to the "
            "cash flows expire or are settled."
        ),
    },
    # Accounts Receivable
    "accounts_receivable": {
        "title": "Trade and Other Debtors",
        "policy": (
            "Trade and other debtors are recognised initially at transaction price "
            "less attributable transaction costs. Subsequent to initial recognition "
            "they are measured at amortised cost, less any impairment losses."
        ),
        "bad_debt_provision": (
            "The company provides for doubtful debts based on estimated irrecoverable "
            "amounts determined by reference to past default experience and the current "
            "economic environment. The company applies the simplified approach to "
            "providing for expected credit losses."
        ),
        "aging_categories": {
            "current": {"days": "0-30", "provision_rate": 0.01},
            "overdue_1": {"days": "31-60", "provision_rate": 0.05},
            "overdue_2": {"days": "61-90", "provision_rate": 0.10},
            "overdue_3": {"days": "91-120", "provision_rate": 0.25},
            "overdue_4": {"days": "120+", "provision_rate": 0.50},
        },
    },
    # Foreign Currency
    "foreign_currency": {
        "title": "Foreign Currency Translation",
        "policy": (
            "Transactions in foreign currencies are recorded at the rate of exchange "
            "at the date of the transaction. Monetary assets and liabilities denominated "
            "in foreign currencies at the balance sheet date are reported at the rates "
            "of exchange prevailing at that date."
        ),
        "exchange_differences": (
            "Exchange differences are recognised in profit or loss in the period "
            "in which they arise."
        ),
    },
    # Taxation
    "taxation": {
        "title": "Taxation",
        "current_tax": (
            "Current tax is recognised for the amount of income tax payable in respect "
            "of the taxable profit for the current or past reporting periods using the "
            "tax rates and laws that have been enacted or substantively enacted by the "
            "reporting date."
        ),
        "deferred_tax": (
            "Deferred tax is recognised in respect of all timing differences at the "
            "reporting date, except as otherwise indicated. Deferred tax assets are "
            "only recognised to the extent that it is probable that they will be "
            "recovered against the reversal of deferred tax liabilities or other "
            "future taxable profits."
        ),
    },
    # Leases
    "leases": {
        "title": "Leases",
        "operating_leases": (
            "Rentals payable under operating leases are charged to profit or loss on "
            "a straight-line basis over the lease term. Benefits received and receivable "
            "as an incentive to sign an operating lease are recognised on a straight-line "
            "basis over the lease term."
        ),
    },
    # Employee Benefits
    "employee_benefits": {
        "title": "Employee Benefits",
        "short_term": (
            "Short-term employee benefits are recognised as an expense in the period "
            "in which the employees render the related service."
        ),
        "pension": (
            "The company operates a defined contribution pension scheme. Contributions "
            "are charged to profit or loss as they become payable in accordance with "
            "the rules of the scheme."
        ),
        "share_based_payments": (
            "The company operates equity-settled share-based payment arrangements. "
            "The fair value of the employee services received is recognised as an "
            "expense over the vesting period."
        ),
    },
    # Critical Judgements and Estimates
    "critical_judgements": {
        "title": "Critical Accounting Judgements and Estimation Uncertainty",
        "areas": [
            {
                "area": "Revenue Recognition",
                "description": (
                    "Determining the timing of revenue recognition for complex "
                    "multi-element arrangements requires judgement."
                ),
            },
            {
                "area": "Development Costs",
                "description": (
                    "Judgement is required in determining whether development costs "
                    "meet the criteria for capitalisation."
                ),
            },
            {
                "area": "Impairment",
                "description": (
                    "Assessment of impairment indicators and calculation of "
                    "recoverable amounts requires estimation."
                ),
            },
            {
                "area": "Useful Economic Lives",
                "description": (
                    "The useful economic lives of tangible and intangible assets "
                    "are based on management's judgement and experience."
                ),
            },
        ],
    },
}

# Notes disclosure order
NOTES_ORDER: List[str] = [
    "basis_of_preparation",
    "revenue_recognition",
    "tangible_assets",
    "intangible_assets",
    "research_development",
    "financial_instruments",
    "accounts_receivable",
    "foreign_currency",
    "taxation",
    "leases",
    "employee_benefits",
    "critical_judgements",
]
