from datetime import datetime
from typing import Any, Dict, List, Optional

from xero_python.accounting import AccountingApi
from xero_python.api_client import ApiClient, Configuration
from xero_python.exceptions import AccountingBadRequestException

from mcx3d_finance.core.config import get_xero_config

from mcx3d_finance.exceptions.recovery import RetryConfig, with_circuit_breaker, with_retry


class XeroClient:
    """
    Enhanced Xero API client with structured logging, error handling,
    and resilience patterns.
    """

    def __init__(self, organization_id: int):
        from mcx3d_finance.core.logging_factory import LoggerFactory
        self.logger = LoggerFactory.get_logger(__name__, domain='integration')
        self.organization_id = organization_id
        self.api_client: Optional[ApiClient] = None
        self.accounting_api: Optional[AccountingApi] = None
        self.tenant_id: Optional[str] = None

        from mcx3d_finance.exceptions.handlers import error_boundary, integration_error
        self.error_boundary = error_boundary
        self.integration_error = integration_error
        with self.error_boundary(
            "xero_client_init", organization_id=self.organization_id
        ):
            self.organization = self._get_organization()
            if not self.organization or not self.organization.xero_token:
                self.logger.warning(
                    "Organization not found or Xero token is missing.",
                    organization_id=self.organization_id,
                )
                return

            self.tenant_id = self.organization.xero_tenant_id
            self.api_client = self._create_api_client()
            self.accounting_api = AccountingApi(self.api_client)
            self.logger.info(
                "XeroClient initialized successfully.",
                organization_id=self.organization_id,
                xero_tenant_id=self.tenant_id,
            )

    def _get_organization(self) -> Optional["Organization"]:
        from mcx3d_finance.db.models import Organization
        from mcx3d_finance.db.session import SessionLocal
        db = SessionLocal()
        try:
            return db.query(Organization).get(self.organization_id)
        finally:
            db.close()

    def _token_refreshed_cb(self, token: Dict[str, Any]):
        """Callback to update the token in the database after a refresh."""
        from mcx3d_finance.db.models import Organization
        from mcx3d_finance.db.session import SessionLocal
        db = SessionLocal()
        try:
            org = db.query(Organization).get(self.organization_id)
            if org:
                org.xero_token = token
                db.commit()
                self.logger.info(
                    "Xero token refreshed and updated in DB.",
                    organization_id=self.organization_id,
                )
        finally:
            db.close()

    def _create_api_client(self) -> ApiClient:
        """Creates and configures the Xero API client."""
        xero_config = get_xero_config()
        config = Configuration()
        config.access_token = self.organization.xero_token["access_token"]

        return ApiClient(
            config,
            oauth2_token_updater=self._token_refreshed_cb,
            oauth2_token=self.organization.xero_token,
            client_id=xero_config["client_id"],
            client_secret=xero_config["client_secret"],
        )

    def _get_api(self) -> AccountingApi:
        """Ensures the accounting_api is available."""
        if not self.accounting_api or not self.tenant_id:
            self.logger.error(
                "Xero client not properly initialized.",
                organization_id=self.organization_id,
            )
            raise self.integration_error(
                "xero", "Xero client not initialized.", organization_id=self.organization_id
            )
        return self.accounting_api

    def _format_if_present(self, dt: Optional[datetime]) -> Optional[str]:
        """Formats a datetime object for Xero API if it exists."""
        return dt.strftime("%Y-%m-%d") if dt else None


    @with_retry(config=RetryConfig(max_attempts=3))
    @with_circuit_breaker(name="xero_api")
    def get_chart_of_accounts(self) -> List[Dict[str, Any]]:
        """Retrieves the chart of accounts from Xero."""
        with self.error_boundary("get_chart_of_accounts", organization_id=self.organization_id):
            api = self._get_api()
            self.logger.info("Fetching chart of accounts from Xero.", organization_id=self.organization_id)
            accounts = api.get_accounts(self.tenant_id)
            result = [acc.to_dict() for acc in accounts.accounts]
            self.logger.info("Successfully fetched chart of accounts.", organization_id=self.organization_id, account_count=len(result))
            return result


    @with_retry(config=RetryConfig(max_attempts=3))
    @with_circuit_breaker(name="xero_api")
    def get_trial_balance(self, date: Optional[datetime] = None) -> Dict[str, Any]:
        """Retrieves the trial balance from Xero."""
        with self.error_boundary("get_trial_balance", organization_id=self.organization_id):
            api = self._get_api()
            report_date = self._format_if_present(date)
            self.logger.info("Fetching trial balance from Xero.", organization_id=self.organization_id, report_date=report_date)
            trial_balance = api.get_report_trial_balance(self.tenant_id, date=report_date)
            result = trial_balance.to_dict()
            self.logger.info("Successfully fetched trial balance.", organization_id=self.organization_id)
            return result


    @with_retry(config=RetryConfig(max_attempts=3))
    @with_circuit_breaker(name="xero_api")
    def get_bank_transactions(
        self, account_id: str, from_date: datetime, to_date: datetime
    ) -> List[Dict[str, Any]]:
        """Retrieves bank transactions for a specific account and period."""
        with self.error_boundary(
            "get_bank_transactions",
            organization_id=self.organization_id,
            account_id=account_id,
        ):
            api = self._get_api()
            where_filter = (
                f'AccountID == Guid("{account_id}") && '
                f'Date >= DateTime({from_date.year}, {from_date.month}, {from_date.day}) && '
                f'Date <= DateTime({to_date.year}, {to_date.month}, {to_date.day})'
            )
            self.logger.info(
                "Fetching bank transactions from Xero.",
                organization_id=self.organization_id,
                account_id=account_id,
            )
            transactions = api.get_bank_transactions(
                self.tenant_id, where=where_filter, order="Date"
            )
            result = [tx.to_dict() for tx in transactions.bank_transactions]
            self.logger.info("Successfully fetched bank transactions.", organization_id=self.organization_id, transaction_count=len(result))
            return result


    @with_retry(config=RetryConfig(max_attempts=3))
    @with_circuit_breaker(name="xero_api")
    def get_invoices(self, from_date: datetime, to_date: datetime) -> List[Dict[str, Any]]:
        """Retrieves invoices for a specific period."""
        with self.error_boundary("get_invoices", organization_id=self.organization_id):
            api = self._get_api()
            where_filter = (
                f'Date >= DateTime({from_date.year}, {from_date.month}, {from_date.day}) && '
                f'Date <= DateTime({to_date.year}, {to_date.month}, {to_date.day})'
            )
            self.logger.info("Fetching invoices from Xero.", organization_id=self.organization_id)
            invoices = api.get_invoices(self.tenant_id, where=where_filter, order="Date")
            result = [inv.to_dict() for inv in invoices.invoices]
            self.logger.info("Successfully fetched invoices.", organization_id=self.organization_id, invoice_count=len(result))
            return result


    @with_retry(config=RetryConfig(max_attempts=3))
    @with_circuit_breaker(name="xero_api")
    def get_contacts(self) -> List[Dict[str, Any]]:
        """Retrieves all contacts from Xero."""
        with self.error_boundary("get_contacts", organization_id=self.organization_id):
            api = self._get_api()
            self.logger.info("Fetching contacts from Xero.", organization_id=self.organization_id)
            try:
                contacts = api.get_contacts(self.tenant_id)
                result = [c.to_dict() for c in contacts.contacts]
                self.logger.info("Successfully fetched contacts.", organization_id=self.organization_id, contact_count=len(result))
                return result
            except AccountingBadRequestException as e:
                self.logger.error("Bad request to Xero get_contacts.", details=e.body, organization_id=self.organization_id)
                raise self.integration_error("xero", "Failed to get contacts.", details=e.body, organization_id=self.organization_id)
