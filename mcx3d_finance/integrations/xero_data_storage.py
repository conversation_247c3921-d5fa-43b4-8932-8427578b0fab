"""
Xero Data Storage Service

Service to store imported Xero data into the database with proper handling of duplicates,
updates, and data integrity.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import and_
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from mcx3d_finance.db.models import (
    Organization,
    Account,
    Contact,
    Transaction,
    Invoice,
    BankTransaction,
    SyncStatus,
)
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.integrations.xero_data_import import XeroDataImportService

logger = LoggerFactory.get_logger(__name__, domain='integration')


class XeroDataStorageService:
    """Service to store Xero data in the database with transaction safety."""

    def __init__(self, db: Session, optimize: bool = True):
        self.db = db
        self.storage_stats = {
            "accounts": {"created": 0, "updated": 0, "skipped": 0},
            "contacts": {"created": 0, "updated": 0, "skipped": 0},
            "invoices": {"created": 0, "updated": 0, "skipped": 0},
            "bank_transactions": {"created": 0, "updated": 0, "skipped": 0},
            "tracking_categories": {"created": 0, "updated": 0, "skipped": 0},
        }
        # Optimization features
        self.optimize = optimize
        if optimize:
            self._contact_cache: Dict[str, int] = {}
            self._account_cache: Dict[str, int] = {}
            self._batch_size = 1000  # Configurable batch size for bulk operations

    def _parse_xero_date(self, date_str: Any) -> Optional[datetime]:
        """Parse Xero date format /Date(milliseconds+timezone)/ to datetime."""
        if not date_str:
            return None

        try:
            if isinstance(date_str, str) and date_str.startswith("/Date("):
                # Extract milliseconds from /Date(*************+0000)/
                # Find the closing parenthesis and extract just the milliseconds
                close_paren = date_str.index(")")
                ms_str = date_str[6:close_paren]
                # Remove timezone offset if present (e.g., "+0000")
                if "+" in ms_str or "-" in ms_str:
                    ms_str = ms_str.split("+")[0].split("-")[0]
                ms = int(ms_str)
                return datetime.fromtimestamp(ms / 1000, tz=timezone.utc)
            elif isinstance(date_str, str):
                # Handle ISO format dates
                return datetime.fromisoformat(date_str.replace("Z", "+00:00"))
            elif isinstance(date_str, datetime):
                return date_str
        except Exception as e:
            logger.warning(f"Failed to parse date {date_str}: {e}")

        return None

    def _preload_contacts(
        self, organization_id: int, xero_contact_ids: Set[str]
    ) -> None:
        """Preload all contacts for the organization to avoid N+1 queries."""
        if not self.optimize or not xero_contact_ids:
            return

        # Clear cache for new batch
        self._contact_cache.clear()

        # Bulk load all contacts
        contacts = (
            self.db.query(Contact)
            .filter(
                and_(
                    Contact.organization_id == organization_id,
                    Contact.xero_contact_id.in_(list(xero_contact_ids)),
                )
            )
            .all()
        )

        # Build cache
        self._contact_cache = {
            contact.xero_contact_id: contact.id for contact in contacts
        }

        logger.info(f"Preloaded {len(self._contact_cache)} contacts into cache")

    def _preload_accounts(
        self, organization_id: int, xero_account_ids: Set[str]
    ) -> None:
        """Preload all accounts for the organization."""
        if not self.optimize or not xero_account_ids:
            return

        self._account_cache.clear()

        accounts = (
            self.db.query(Account)
            .filter(
                and_(
                    Account.organization_id == organization_id,
                    Account.xero_account_id.in_(list(xero_account_ids)),
                )
            )
            .all()
        )

        self._account_cache = {
            account.xero_account_id: account.id for account in accounts
        }

        logger.info(f"Preloaded {len(self._account_cache)} accounts into cache")

    def store_all_data(
        self,
        organization_id: int,
        imported_data: Dict[str, Any],
        progress_callback=None,
    ) -> Dict[str, Any]:
        """
        Store all imported data in the database.

        Args:
            organization_id: Organization ID
            imported_data: Data from XeroDataImportService
            progress_callback: Optional callback for progress updates

        Returns:
            Dict with storage results and statistics
        """
        try:
            logger.info(f"Starting data storage for organization {organization_id}")

            # Update organization details
            self._update_progress(
                progress_callback, 5, "Updating organization details..."
            )
            org_details = imported_data.get("organization", {})
            if org_details:
                self._update_organization(organization_id, org_details)

            # Store accounts
            self._update_progress(progress_callback, 20, "Storing accounts...")
            accounts = imported_data.get("accounts", [])
            self._store_accounts(organization_id, accounts)

            # Store contacts
            self._update_progress(progress_callback, 40, "Storing contacts...")
            contacts = imported_data.get("contacts", [])
            self._store_contacts(organization_id, contacts)

            # Store invoices
            self._update_progress(progress_callback, 60, "Storing invoices...")
            invoices = imported_data.get("invoices", [])
            self._store_invoices(organization_id, invoices)

            # Store bank transactions
            self._update_progress(progress_callback, 80, "Storing bank transactions...")
            transactions = imported_data.get("transactions", [])
            self._store_bank_transactions(organization_id, transactions)

            # Commit all changes
            self._update_progress(progress_callback, 95, "Finalizing data storage...")
            self.db.commit()

            # Update sync status
            self._update_sync_status(organization_id, "completed")

            self._update_progress(progress_callback, 100, "Storage completed!")

            return {
                "success": True,
                "stats": self.storage_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            logger.error(f"Error during data storage: {e}")
            self.db.rollback()
            self._update_sync_status(organization_id, "failed", str(e))
            return {
                "success": False,
                "error": str(e),
                "stats": self.storage_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    def _update_progress(self, callback, progress: int, message: str):
        """Update progress if callback provided."""
        if callback:
            callback(progress, message)
        logger.info(f"Storage progress: {progress}% - {message}")

    def _update_organization(self, organization_id: int, org_details: Dict[str, Any]):
        """Update organization details."""
        try:
            org = self.db.query(Organization).get(organization_id)
            if org:
                # Update organization details
                if org_details.get("Name"):
                    org.name = org_details["Name"]
                if org_details.get("BaseCurrency"):
                    org.base_currency = org_details["BaseCurrency"]

                org.updated_at = datetime.now(timezone.utc)
                logger.info(f"Updated organization: {org.name}")
        except Exception as e:
            logger.error(f"Error updating organization: {e}")

    def _store_accounts(self, organization_id: int, accounts: List[Dict[str, Any]]):
        """Store or update accounts."""
        for account_data in accounts:
            try:
                xero_id = account_data.get("xero_account_id")
                if not xero_id:
                    continue

                # Convert date fields
                if "updated_date_utc" in account_data:
                    account_data["updated_date_utc"] = self._parse_xero_date(
                        account_data["updated_date_utc"]
                    )

                # Check if account exists
                existing = (
                    self.db.query(Account)
                    .filter_by(xero_account_id=xero_id, organization_id=organization_id)
                    .first()
                )

                if existing:
                    # Update existing account
                    for key, value in account_data.items():
                        if hasattr(existing, key) and key not in ["id", "created_at"]:
                            setattr(existing, key, value)
                    existing.updated_at = datetime.now(timezone.utc)
                    self.storage_stats["accounts"]["updated"] += 1
                else:
                    # Create new account - filter out invalid fields
                    valid_fields = {
                        k: v
                        for k, v in account_data.items()
                        if hasattr(Account, k) or k == "organization_id"
                    }
                    account = Account(organization_id=organization_id, **valid_fields)
                    self.db.add(account)
                    self.storage_stats["accounts"]["created"] += 1

            except Exception as e:
                logger.error(f"Error storing account {account_data.get('name')}: {e}")
                self.storage_stats["accounts"]["skipped"] += 1

    def _store_contacts(self, organization_id: int, contacts: List[Dict[str, Any]]):
        """Store or update contacts."""
        for contact_data in contacts:
            try:
                xero_id = contact_data.get("xero_contact_id")
                if not xero_id:
                    continue

                # Convert date fields
                if "updated_date_utc" in contact_data:
                    contact_data["updated_date_utc"] = self._parse_xero_date(
                        contact_data["updated_date_utc"]
                    )

                # Check if contact exists
                existing = (
                    self.db.query(Contact)
                    .filter_by(xero_contact_id=xero_id, organization_id=organization_id)
                    .first()
                )

                if existing:
                    # Update existing contact
                    for key, value in contact_data.items():
                        if hasattr(existing, key) and key not in ["id", "created_at"]:
                            setattr(existing, key, value)
                    existing.updated_at = datetime.now(timezone.utc)
                    self.storage_stats["contacts"]["updated"] += 1
                else:
                    # Create new contact - filter out invalid fields
                    valid_fields = {
                        k: v
                        for k, v in contact_data.items()
                        if hasattr(Contact, k) or k == "organization_id"
                    }
                    contact = Contact(organization_id=organization_id, **valid_fields)
                    self.db.add(contact)
                    self.storage_stats["contacts"]["created"] += 1

            except Exception as e:
                logger.error(f"Error storing contact {contact_data.get('name')}: {e}")
                self.storage_stats["contacts"]["skipped"] += 1

    def _store_invoices(self, organization_id: int, invoices: List[Dict[str, Any]]):
        """Store or update invoices with optimized batch loading."""
        if not invoices:
            return

        # If optimization is enabled, preload contacts
        if self.optimize:
            # Extract all unique contact IDs
            xero_contact_ids = {
                inv.get("contact_id") for inv in invoices if inv.get("contact_id")
            }

            # Preload all contacts at once
            self._preload_contacts(organization_id, xero_contact_ids)

            # Get all existing invoice IDs in one query
            xero_invoice_ids = [
                inv.get("xero_invoice_id")
                for inv in invoices
                if inv.get("xero_invoice_id")
            ]
            existing_invoices = (
                self.db.query(Invoice)
                .filter(
                    and_(
                        Invoice.organization_id == organization_id,
                        Invoice.xero_invoice_id.in_(xero_invoice_ids),
                    )
                )
                .all()
            )

            existing_map = {inv.xero_invoice_id: inv for inv in existing_invoices}

            # Prepare batch operations
            to_insert = []

            for invoice_data in invoices:
                try:
                    xero_id = invoice_data.get("xero_invoice_id")
                    if not xero_id:
                        continue

                    # Use cached contact lookup (no database query!)
                    xero_contact_id = invoice_data.get("contact_id")
                    if xero_contact_id:
                        invoice_data["contact_id"] = self._contact_cache.get(
                            xero_contact_id
                        )

                    # Convert date strings to datetime
                    for date_field in [
                        "date",
                        "due_date",
                        "expected_payment_date",
                        "planned_payment_date",
                        "updated_date_utc",
                    ]:
                        if date_field in invoice_data:
                            invoice_data[date_field] = self._parse_xero_date(
                                invoice_data[date_field]
                            )

                    # Check if exists
                    existing = existing_map.get(xero_id)

                    if existing:
                        # Update existing invoice
                        for key, value in invoice_data.items():
                            if hasattr(existing, key) and key not in [
                                "id",
                                "created_at",
                            ]:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self.storage_stats["invoices"]["updated"] += 1
                    else:
                        # Queue for batch insert
                        invoice = Invoice(
                            organization_id=organization_id, **invoice_data
                        )
                        to_insert.append(invoice)

                except Exception as e:
                    logger.error(
                        f"Error preparing invoice {invoice_data.get('invoice_number')}: {e}"
                    )
                    self.storage_stats["invoices"]["skipped"] += 1

            # Bulk insert new invoices
            if to_insert:
                # Process in batches to avoid memory issues
                for i in range(0, len(to_insert), self._batch_size):
                    batch = to_insert[i : i + self._batch_size]
                    self.db.bulk_save_objects(batch)
                    self.storage_stats["invoices"]["created"] += len(batch)
        else:
            # Original non-optimized implementation
            for invoice_data in invoices:
                try:
                    xero_id = invoice_data.get("xero_invoice_id")
                    if not xero_id:
                        continue

                    # Convert contact ID from Xero to internal ID
                    xero_contact_id = invoice_data.get("contact_id")
                    if xero_contact_id:
                        contact = (
                            self.db.query(Contact)
                            .filter_by(
                                xero_contact_id=xero_contact_id,
                                organization_id=organization_id,
                            )
                            .first()
                        )
                        if contact:
                            invoice_data["contact_id"] = contact.id
                        else:
                            invoice_data["contact_id"] = None

                    # Convert date strings to datetime
                    for date_field in [
                        "date",
                        "due_date",
                        "expected_payment_date",
                        "planned_payment_date",
                        "updated_date_utc",
                    ]:
                        if date_field in invoice_data:
                            invoice_data[date_field] = self._parse_xero_date(
                                invoice_data[date_field]
                            )

                    # Check if invoice exists
                    existing = (
                        self.db.query(Invoice)
                        .filter_by(
                            xero_invoice_id=xero_id, organization_id=organization_id
                        )
                        .first()
                    )

                    if existing:
                        # Update existing invoice
                        for key, value in invoice_data.items():
                            if hasattr(existing, key) and key not in [
                                "id",
                                "created_at",
                            ]:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self.storage_stats["invoices"]["updated"] += 1
                    else:
                        # Create new invoice
                        invoice = Invoice(
                            organization_id=organization_id, **invoice_data
                        )
                        self.db.add(invoice)
                        self.storage_stats["invoices"]["created"] += 1

                except Exception as e:
                    logger.error(
                        f"Error storing invoice {invoice_data.get('invoice_number')}: {e}"
                    )
                    self.storage_stats["invoices"]["skipped"] += 1

    def _store_bank_transactions(
        self, organization_id: int, transactions: List[Dict[str, Any]]
    ):
        """Store or update bank transactions with optimized batch loading."""
        if not transactions:
            return

        # If optimization is enabled, preload contacts
        if self.optimize:
            # Extract all unique contact IDs
            xero_contact_ids = {
                trans.get("contact_id")
                for trans in transactions
                if trans.get("contact_id")
            }

            # Preload all contacts at once
            self._preload_contacts(organization_id, xero_contact_ids)

            # Get all existing transaction IDs in one query
            xero_trans_ids = [
                t.get("xero_transaction_id")
                for t in transactions
                if t.get("xero_transaction_id")
            ]

            # Process in batches for large datasets
            existing_map = {}
            for i in range(0, len(xero_trans_ids), self._batch_size):
                batch_ids = xero_trans_ids[i : i + self._batch_size]
                batch_existing = (
                    self.db.query(BankTransaction)
                    .filter(
                        and_(
                            BankTransaction.organization_id == organization_id,
                            BankTransaction.xero_transaction_id.in_(batch_ids),
                        )
                    )
                    .all()
                )
                existing_map.update({t.xero_transaction_id: t for t in batch_existing})

            # Prepare batch operations
            to_insert = []

            for trans_data in transactions:
                try:
                    xero_id = trans_data.get("xero_transaction_id")
                    if not xero_id:
                        continue

                    # Use cached contact lookup (no database query!)
                    xero_contact_id = trans_data.get("contact_id")
                    if xero_contact_id:
                        trans_data["contact_id"] = self._contact_cache.get(
                            xero_contact_id
                        )

                    # Convert date strings to datetime
                    for date_field in ["date", "updated_date_utc"]:
                        if date_field in trans_data:
                            trans_data[date_field] = self._parse_xero_date(
                                trans_data[date_field]
                            )

                    # Check if exists
                    existing = existing_map.get(xero_id)

                    if existing:
                        # Update existing transaction
                        for key, value in trans_data.items():
                            if hasattr(existing, key) and key not in [
                                "id",
                                "created_at",
                            ]:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self.storage_stats["bank_transactions"]["updated"] += 1
                    else:
                        # Queue for batch insert
                        transaction = BankTransaction(
                            organization_id=organization_id, **trans_data
                        )
                        to_insert.append(transaction)

                except Exception as e:
                    logger.error(f"Error preparing bank transaction: {e}")
                    self.storage_stats["bank_transactions"]["skipped"] += 1

            # Bulk insert new transactions
            if to_insert:
                # Process in batches to avoid memory issues
                for i in range(0, len(to_insert), self._batch_size):
                    batch = to_insert[i : i + self._batch_size]
                    self.db.bulk_save_objects(batch)
                    self.storage_stats["bank_transactions"]["created"] += len(batch)
        else:
            # Original non-optimized implementation
            for trans_data in transactions:
                try:
                    xero_id = trans_data.get("xero_transaction_id")
                    if not xero_id:
                        continue

                    # Convert contact ID from Xero to internal ID
                    xero_contact_id = trans_data.get("contact_id")
                    if xero_contact_id:
                        contact = (
                            self.db.query(Contact)
                            .filter_by(
                                xero_contact_id=xero_contact_id,
                                organization_id=organization_id,
                            )
                            .first()
                        )
                        if contact:
                            trans_data["contact_id"] = contact.id
                        else:
                            trans_data["contact_id"] = None

                    # Convert date strings to datetime
                    for date_field in ["date", "updated_date_utc"]:
                        if date_field in trans_data:
                            trans_data[date_field] = self._parse_xero_date(
                                trans_data[date_field]
                            )

                    # Check if transaction exists
                    existing = (
                        self.db.query(BankTransaction)
                        .filter_by(
                            xero_transaction_id=xero_id, organization_id=organization_id
                        )
                        .first()
                    )

                    if existing:
                        # Update existing transaction
                        for key, value in trans_data.items():
                            if hasattr(existing, key) and key not in [
                                "id",
                                "created_at",
                            ]:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self.storage_stats["bank_transactions"]["updated"] += 1
                    else:
                        # Create new transaction
                        transaction = BankTransaction(
                            organization_id=organization_id, **trans_data
                        )
                        self.db.add(transaction)
                        self.storage_stats["bank_transactions"]["created"] += 1

                except Exception as e:
                    logger.error(f"Error storing bank transaction: {e}")
                    self.storage_stats["bank_transactions"]["skipped"] += 1

    def _update_sync_status(
        self, organization_id: int, status: str, error_message: str = None
    ):
        """Update sync status for the organization."""
        try:
            # Update organization sync status
            org = self.db.query(Organization).get(organization_id)
            if org:
                org.sync_status = status
                if status == "completed":
                    org.last_sync_at = datetime.now(timezone.utc)
                org.updated_at = datetime.now(timezone.utc)

            # Create sync status record
            sync_status = SyncStatus(
                organization_id=organization_id,
                sync_type="full",
                status=status,
                records_synced=self.storage_stats,
                started_at=datetime.now(timezone.utc),
                completed_at=(
                    datetime.now(timezone.utc) if status == "completed" else None
                ),
                error_message=error_message,
                created_at=datetime.now(timezone.utc),
            )
            self.db.add(sync_status)
            self.db.commit()

        except Exception as e:
            logger.error(f"Error updating sync status: {e}")

    def store_incremental_data(
        self, organization_id: int, imported_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Store incremental data updates."""
        try:
            logger.info(
                f"Starting incremental data storage for organization {organization_id}"
            )

            # Only store invoices and transactions for incremental sync
            invoices = imported_data.get("invoices", [])
            if invoices:
                self._store_invoices(organization_id, invoices)

            transactions = imported_data.get("transactions", [])
            if transactions:
                self._store_bank_transactions(organization_id, transactions)

            self.db.commit()
            self._update_sync_status(organization_id, "completed")

            return {
                "success": True,
                "stats": self.storage_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            logger.error(f"Error during incremental storage: {e}")
            self.db.rollback()
            self._update_sync_status(organization_id, "failed", str(e))
            return {
                "success": False,
                "error": str(e),
                "stats": self.storage_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }


def test_storage_service():
    """Test function for data storage service."""
    print("Testing Xero Data Storage Service...")

    # First import data
    import_service = XeroDataImportService(organization_id=2)
    import_result = import_service.import_all_data()

    if import_result["success"]:
        # Now store the data
        db = SessionLocal()
        try:
            storage_service = XeroDataStorageService(db)

            def progress_callback(progress, message):
                print(f"[{progress}%] {message}")

            storage_result = storage_service.store_all_data(
                organization_id=2,
                imported_data=import_result["processed_data"],
                progress_callback=progress_callback,
            )

            if storage_result["success"]:
                print("\n✅ Storage completed successfully!")
                print(f"\nStorage Statistics:")
                for entity, stats in storage_result["stats"].items():
                    print(f"  {entity}:")
                    for action, count in stats.items():
                        print(f"    {action}: {count}")
            else:
                print(f"\n❌ Storage failed: {storage_result['error']}")

        finally:
            db.close()
    else:
        print("❌ Import failed, cannot test storage")

    return storage_result if "storage_result" in locals() else None


if __name__ == "__main__":
    test_storage_service()
