"""
MCP Data Validation Service

This service uses the MCP Xero client to validate and cross-check financial data
between your processed data and live Xero data for accuracy and consistency.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from mcx3d_finance.integrations.mcp_xero_client import MCPXeroClient

logger = LoggerFactory.get_logger(__name__, domain='integration')


@dataclass
class ValidationResult:
    """Result of a data validation check."""

    category: str
    check_name: str
    status: str  # 'pass', 'fail', 'warning', 'error'
    message: str
    local_value: Any = None
    mcp_value: Any = None
    difference: Any = None


class MCPDataValidator:
    """Data validation service using MCP Xero integration."""

    def __init__(self, organization_id: int = 2):
        self.organization_id = organization_id
        self.mcp_client = MCPXeroClient(organization_id)
        self.results: List[ValidationResult] = []

    def validate_organization_info(self) -> List[ValidationResult]:
        """Validate organization information."""
        results = []

        try:
            org_details = self.mcp_client.get_organization_details()

            results.append(
                ValidationResult(
                    category="Organization",
                    check_name="Connection Test",
                    status="pass",
                    message=f"Successfully connected to {org_details.get('Name', 'Unknown')}",
                    mcp_value=org_details,
                )
            )

            # Validate currency
            currency = org_details.get("BaseCurrency", "Unknown")
            if currency == "GBP":
                results.append(
                    ValidationResult(
                        category="Organization",
                        check_name="Currency Check",
                        status="pass",
                        message=f"Currency is correctly set to {currency}",
                        mcp_value=currency,
                    )
                )
            else:
                results.append(
                    ValidationResult(
                        category="Organization",
                        check_name="Currency Check",
                        status="warning",
                        message=f"Unexpected currency: {currency}",
                        mcp_value=currency,
                    )
                )

        except Exception as e:
            results.append(
                ValidationResult(
                    category="Organization",
                    check_name="Connection Test",
                    status="error",
                    message=f"Failed to retrieve organization details: {e}",
                )
            )

        return results

    def validate_accounts_structure(self) -> List[ValidationResult]:
        """Validate chart of accounts structure."""
        results = []

        try:
            accounts = self.mcp_client.get_accounts()

            results.append(
                ValidationResult(
                    category="Accounts",
                    check_name="Account Count",
                    status="pass",
                    message=f"Retrieved {len(accounts)} accounts from Xero",
                    mcp_value=len(accounts),
                )
            )

            # Validate account types distribution
            account_types = {}
            for account in accounts:
                acc_type = account.get("Type", "UNKNOWN")
                account_types[acc_type] = account_types.get(acc_type, 0) + 1

            # Check for essential account types
            essential_types = ["BANK", "REVENUE", "EXPENSE", "CURRENT", "FIXED"]
            for acc_type in essential_types:
                if acc_type in account_types:
                    results.append(
                        ValidationResult(
                            category="Accounts",
                            check_name=f"{acc_type} Accounts",
                            status="pass",
                            message=f"Found {account_types[acc_type]} {acc_type} accounts",
                            mcp_value=account_types[acc_type],
                        )
                    )
                else:
                    results.append(
                        ValidationResult(
                            category="Accounts",
                            check_name=f"{acc_type} Accounts",
                            status="warning",
                            message=f"No {acc_type} accounts found",
                            mcp_value=0,
                        )
                    )

        except Exception as e:
            results.append(
                ValidationResult(
                    category="Accounts",
                    check_name="Account Structure",
                    status="error",
                    message=f"Failed to validate accounts: {e}",
                )
            )

        return results

    def validate_recent_data(self) -> List[ValidationResult]:
        """Validate recent transaction and invoice data."""
        results = []

        try:
            # Test invoices
            invoices = self.mcp_client.get_invoices(page=1)

            results.append(
                ValidationResult(
                    category="Transactions",
                    check_name="Recent Invoices",
                    status="pass",
                    message=f"Retrieved {len(invoices)} recent invoices",
                    mcp_value=len(invoices),
                )
            )

            # Analyze invoice statuses
            if invoices:
                statuses = {}
                total_amount = 0

                for invoice in invoices[:10]:  # Analyze first 10
                    status = invoice.get("Status", "UNKNOWN")
                    statuses[status] = statuses.get(status, 0) + 1

                    try:
                        amount = float(invoice.get("Total", 0))
                        total_amount += amount
                    except:
                        pass

                results.append(
                    ValidationResult(
                        category="Transactions",
                        check_name="Invoice Analysis",
                        status="pass",
                        message=f"Invoice statuses: {dict(statuses)}, Total: £{total_amount:,.2f}",
                        mcp_value={"statuses": statuses, "total_amount": total_amount},
                    )
                )

        except Exception as e:
            results.append(
                ValidationResult(
                    category="Transactions",
                    check_name="Recent Data",
                    status="error",
                    message=f"Failed to validate recent data: {e}",
                )
            )

        return results

    def validate_contacts(self) -> List[ValidationResult]:
        """Validate contacts/customers data."""
        results = []

        try:
            contacts = self.mcp_client.get_contacts()

            results.append(
                ValidationResult(
                    category="Contacts",
                    check_name="Contact Count",
                    status="pass",
                    message=f"Retrieved {len(contacts)} contacts from Xero",
                    mcp_value=len(contacts),
                )
            )

            # Analyze contact types
            if contacts:
                customers = sum(1 for c in contacts[:10] if c.get("IsCustomer"))
                suppliers = sum(1 for c in contacts[:10] if c.get("IsSupplier"))

                results.append(
                    ValidationResult(
                        category="Contacts",
                        check_name="Contact Types",
                        status="pass",
                        message=f"Sample: {customers} customers, {suppliers} suppliers",
                        mcp_value={"customers": customers, "suppliers": suppliers},
                    )
                )

        except Exception as e:
            results.append(
                ValidationResult(
                    category="Contacts",
                    check_name="Contact Validation",
                    status="error",
                    message=f"Failed to validate contacts: {e}",
                )
            )

        return results

    def run_full_validation(self) -> Dict[str, Any]:
        """Run complete validation suite."""
        logger.info("Starting full MCP data validation...")

        self.results = []

        # Run all validation checks
        validation_methods = [
            self.validate_organization_info,
            self.validate_accounts_structure,
            self.validate_recent_data,
            self.validate_contacts,
        ]

        for method in validation_methods:
            try:
                method_results = method()
                self.results.extend(method_results)
            except Exception as e:
                self.results.append(
                    ValidationResult(
                        category="System",
                        check_name=method.__name__,
                        status="error",
                        message=f"Validation method failed: {e}",
                    )
                )

        # Compile summary
        summary = self._compile_summary()

        logger.info(
            f"Validation complete: {summary['total_checks']} checks, "
            f"{summary['passed']} passed, {summary['failed']} failed, "
            f"{summary['warnings']} warnings, {summary['errors']} errors"
        )

        return {
            "summary": summary,
            "results": self.results,
            "timestamp": datetime.now().isoformat(),
            "organization_id": self.organization_id,
        }

    def _compile_summary(self) -> Dict[str, Any]:
        """Compile validation summary statistics."""
        summary = {
            "total_checks": len(self.results),
            "passed": 0,
            "failed": 0,
            "warnings": 0,
            "errors": 0,
            "categories": {},
        }

        for result in self.results:
            # Count by status
            if result.status == "pass":
                summary["passed"] += 1
            elif result.status == "fail":
                summary["failed"] += 1
            elif result.status == "warning":
                summary["warnings"] += 1
            elif result.status == "error":
                summary["errors"] += 1

            # Count by category
            category = result.category
            if category not in summary["categories"]:
                summary["categories"][category] = {
                    "total": 0,
                    "passed": 0,
                    "failed": 0,
                    "warnings": 0,
                    "errors": 0,
                }

            summary["categories"][category]["total"] += 1
            if result.status in summary["categories"][category]:
                summary["categories"][category][result.status] += 1

        # Calculate success rate
        if summary["total_checks"] > 0:
            summary["success_rate"] = (
                summary["passed"] / summary["total_checks"]
            ) * 100
        else:
            summary["success_rate"] = 0

        return summary

    def get_failed_checks(self) -> List[ValidationResult]:
        """Get list of failed validation checks."""
        return [r for r in self.results if r.status in ["fail", "error"]]

    def get_warnings(self) -> List[ValidationResult]:
        """Get list of validation warnings."""
        return [r for r in self.results if r.status == "warning"]

    def print_report(self) -> None:
        """Print a formatted validation report."""
        if not self.results:
            print("No validation results available. Run validation first.")
            return

        summary = self._compile_summary()

        print("🔍 MCP Data Validation Report")
        print("=" * 50)
        print(f"Organization ID: {self.organization_id}")
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        print("📊 Summary:")
        print(f"   Total Checks: {summary['total_checks']}")
        print(f"   ✅ Passed: {summary['passed']}")
        print(f"   ❌ Failed: {summary['failed']}")
        print(f"   ⚠️  Warnings: {summary['warnings']}")
        print(f"   🚨 Errors: {summary['errors']}")
        print(f"   📈 Success Rate: {summary['success_rate']:.1f}%")
        print()

        # Print by category
        for category, stats in summary["categories"].items():
            print(f"📁 {category}:")
            print(
                f"   Total: {stats['total']}, Passed: {stats['passed']}, "
                f"Failed: {stats['failed']}, Warnings: {stats['warnings']}, "
                f"Errors: {stats['errors']}"
            )
        print()

        # Print failed checks
        failed_checks = self.get_failed_checks()
        if failed_checks:
            print("❌ Failed Checks:")
            for result in failed_checks:
                print(f"   {result.category}/{result.check_name}: {result.message}")
            print()

        # Print warnings
        warnings = self.get_warnings()
        if warnings:
            print("⚠️  Warnings:")
            for result in warnings:
                print(f"   {result.category}/{result.check_name}: {result.message}")


def create_validator(organization_id: int = 2) -> MCPDataValidator:
    """Factory function to create data validator."""
    return MCPDataValidator(organization_id)


def test_validation() -> Dict[str, Any]:
    """Test the MCP data validation service."""
    print("🧪 Testing MCP Data Validation Service")
    print("=" * 50)

    validator = create_validator()
    validation_results = validator.run_full_validation()

    # Print the report
    validator.print_report()

    return validation_results


if __name__ == "__main__":
    test_validation()
