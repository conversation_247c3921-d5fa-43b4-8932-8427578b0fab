"""
Xero Data Persistence Service

Handles saving imported and transformed Xero data to the database.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from decimal import Decimal
import json

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, Account, Contact, Invoice, BankTransaction
from mcx3d_finance.utils.date_parser import parse_date_safe
from mcx3d_finance.exceptions.base import MCX3DException
from mcx3d_finance.exceptions.integration import DataPersistenceError
from mcx3d_finance.exceptions.handlers import error_boundary
from mcx3d_finance.exceptions.recovery import with_retry, RetryConfig

logger = LoggerFactory.get_logger(__name__, domain='integration')


class XeroDataPersistenceService:
    """Service to persist imported Xero data to the database."""

    def __init__(self, organization_id: int):
        self.organization_id = organization_id
        self.stats = {
            "accounts": {"saved": 0, "updated": 0, "errors": 0},
            "contacts": {"saved": 0, "updated": 0, "errors": 0},
            "invoices": {"saved": 0, "updated": 0, "errors": 0},
            "bank_transactions": {"saved": 0, "updated": 0, "errors": 0},
        }

    @with_retry(config=RetryConfig(max_attempts=2, retryable_exceptions=(DataPersistenceError,)))
    def persist_all_data(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Persist all imported data to the database.

        Args:
            processed_data: Dictionary containing processed data from import service

        Returns:
            Dictionary with persistence results and statistics
        """
        with error_boundary(
            "persist_all_data",
            organization_id=self.organization_id,
            reraise=False,
            default_return={
                "success": False,
                "error": "Failed to persist data",
                "stats": self.stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        ) as error_context:
            logger.info(
                "Starting data persistence",
                correlation_id=error_context.correlation_id,
                organization_id=self.organization_id,
                data_types=list(processed_data.keys())
            )

            with SessionLocal() as db:
                # Persist accounts first (needed for foreign key relationships)
                if "accounts" in processed_data:
                    self._persist_accounts(db, processed_data["accounts"])

                # Persist contacts
                if "contacts" in processed_data:
                    self._persist_contacts(db, processed_data["contacts"])

                # Persist invoices
                if "invoices" in processed_data:
                    self._persist_invoices(db, processed_data["invoices"])

                # Persist bank transactions
                if "transactions" in processed_data:
                    self._persist_bank_transactions(db, processed_data["transactions"])

                # Commit all changes
                db.commit()
                logger.info(
                    "All data persisted successfully",
                    correlation_id=error_context.correlation_id,
                    organization_id=self.organization_id,
                    stats=self.stats
                )

            return {
                "success": True,
                "stats": self.stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    def _persist_accounts(
        self, db: Session, accounts_data: List[Dict[str, Any]]
    ) -> None:
        """Persist accounts to database."""
        logger.info(f"Persisting {len(accounts_data)} accounts")

        for account_data in accounts_data:
            with error_boundary(
                "persist_account",
                account_name=account_data.get("name"),
                xero_account_id=account_data.get("xero_account_id"),
                organization_id=self.organization_id,
                reraise=False
            ):
                # Check if account already exists
                existing_account = (
                    db.query(Account)
                    .filter_by(xero_account_id=account_data.get("xero_account_id"))
                    .first()
                )

                if existing_account:
                    # Update existing account
                    self._update_account(existing_account, account_data)
                    self.stats["accounts"]["updated"] += 1
                else:
                    # Create new account
                    account = Account(
                        xero_account_id=account_data.get("xero_account_id"),
                        code=account_data.get("code", ""),
                        name=account_data.get("name", ""),
                        type=account_data.get("type", ""),
                        tax_type=account_data.get("tax_type", ""),
                        description=account_data.get("description", ""),
                        class_type=account_data.get("class_type", ""),
                        status=account_data.get("status", "ACTIVE"),
                        show_in_expense_claims=account_data.get(
                            "show_in_expense_claims", False
                        ),
                        bank_account_number=account_data.get("bank_account_number", ""),
                        bank_account_type=account_data.get("bank_account_type", ""),
                        currency_code=account_data.get("currency_code", "GBP"),
                        reporting_code=account_data.get("reporting_code", ""),
                        reporting_code_name=account_data.get("reporting_code_name", ""),
                        has_attachments=account_data.get("has_attachments", False),
                        updated_date_utc=self._parse_datetime(
                            account_data.get("updated_date_utc")
                        ),
                        add_to_watchlist=account_data.get("add_to_watchlist", False),
                        gaap_classification=account_data.get("gaap_classification", ""),
                        organization_id=self.organization_id,
                    )
                    db.add(account)
                    self.stats["accounts"]["saved"] += 1
            except MCX3DException:
                # Error already logged by error_boundary
                self.stats["accounts"]["errors"] += 1

    def _persist_contacts(
        self, db: Session, contacts_data: List[Dict[str, Any]]
    ) -> None:
        """Persist contacts to database."""
        logger.info(f"Persisting {len(contacts_data)} contacts")

        for contact_data in contacts_data:
            try:
                # Check if contact already exists
                existing_contact = (
                    db.query(Contact)
                    .filter_by(xero_contact_id=contact_data.get("xero_contact_id"))
                    .first()
                )

                if existing_contact:
                    # Update existing contact
                    self._update_contact(existing_contact, contact_data)
                    self.stats["contacts"]["updated"] += 1
                else:
                    # Create new contact
                    contact = Contact(
                        xero_contact_id=contact_data.get("xero_contact_id"),
                        contact_number=contact_data.get("contact_number", ""),
                        account_number=contact_data.get("account_number", ""),
                        contact_status=contact_data.get("contact_status", "ACTIVE"),
                        name=contact_data.get("name", ""),
                        first_name=contact_data.get("first_name", ""),
                        last_name=contact_data.get("last_name", ""),
                        email_address=contact_data.get("email_address", ""),
                        bank_account_details=contact_data.get(
                            "bank_account_details", ""
                        ),
                        tax_number=contact_data.get("tax_number", ""),
                        accounts_receivable_tax_type=contact_data.get(
                            "accounts_receivable_tax_type", ""
                        ),
                        accounts_payable_tax_type=contact_data.get(
                            "accounts_payable_tax_type", ""
                        ),
                        is_supplier=contact_data.get("is_supplier", False),
                        is_customer=contact_data.get("is_customer", False),
                        default_currency=contact_data.get("default_currency", "GBP"),
                        updated_date_utc=self._parse_datetime(
                            contact_data.get("updated_date_utc")
                        ),
                        has_attachments=contact_data.get("has_attachments", False),
                        has_validation_errors=contact_data.get(
                            "has_validation_errors", False
                        ),
                        # Phone numbers
                        phone_default=contact_data.get("phone_default", ""),
                        phone_mobile=contact_data.get("phone_mobile", ""),
                        phone_fax=contact_data.get("phone_fax", ""),
                        # Addresses
                        address_street=self._parse_json_safe(
                            contact_data.get("address_street")
                        ),
                        address_postal=self._parse_json_safe(
                            contact_data.get("address_postal")
                        ),
                        organization_id=self.organization_id,
                    )
                    db.add(contact)
                    self.stats["contacts"]["saved"] += 1

            except Exception as e:
                logger.error(
                    f"Error persisting contact {contact_data.get('name')}: {e}"
                )
                self.stats["contacts"]["errors"] += 1

    def _persist_invoices(
        self, db: Session, invoices_data: List[Dict[str, Any]]
    ) -> None:
        """Persist invoices to database."""
        logger.info(f"Persisting {len(invoices_data)} invoices")

        for invoice_data in invoices_data:
            try:
                # Check if invoice already exists
                existing_invoice = (
                    db.query(Invoice)
                    .filter_by(xero_invoice_id=invoice_data.get("xero_invoice_id"))
                    .first()
                )

                # Get contact ID from contact_id field
                contact_id = self._get_contact_db_id(db, invoice_data.get("contact_id"))

                if existing_invoice:
                    # Update existing invoice
                    self._update_invoice(existing_invoice, invoice_data, contact_id)
                    self.stats["invoices"]["updated"] += 1
                else:
                    # Create new invoice
                    invoice = Invoice(
                        xero_invoice_id=invoice_data.get("xero_invoice_id"),
                        type=invoice_data.get("type", ""),
                        contact_id=contact_id,
                        date=self._parse_datetime(invoice_data.get("date")),
                        due_date=self._parse_datetime(invoice_data.get("due_date")),
                        line_amount_types=invoice_data.get("line_amount_types", ""),
                        invoice_number=invoice_data.get("invoice_number", ""),
                        reference=invoice_data.get("reference", ""),
                        branding_theme_id=invoice_data.get("branding_theme_id", ""),
                        url=invoice_data.get("url", ""),
                        currency_code=invoice_data.get("currency_code", "GBP"),
                        currency_rate=float(invoice_data.get("currency_rate", 1.0)),
                        status=invoice_data.get("status", ""),
                        sent_to_contact=invoice_data.get("sent_to_contact", False),
                        expected_payment_date=self._parse_datetime(
                            invoice_data.get("expected_payment_date")
                        ),
                        planned_payment_date=self._parse_datetime(
                            invoice_data.get("planned_payment_date")
                        ),
                        sub_total=float(invoice_data.get("sub_total", 0)),
                        total_tax=float(invoice_data.get("total_tax", 0)),
                        total=float(invoice_data.get("total", 0)),
                        total_discount=float(invoice_data.get("total_discount", 0)),
                        has_attachments=invoice_data.get("has_attachments", False),
                        has_errors=invoice_data.get("has_errors", False),
                        is_discounted=invoice_data.get("is_discounted", False),
                        payments=self._parse_json_safe(
                            invoice_data.get("payments", [])
                        ),
                        amount_due=float(invoice_data.get("amount_due", 0)),
                        amount_paid=float(invoice_data.get("amount_paid", 0)),
                        amount_credited=float(invoice_data.get("amount_credited", 0)),
                        updated_date_utc=self._parse_datetime(
                            invoice_data.get("updated_date_utc")
                        ),
                        line_items=self._parse_json_safe(
                            invoice_data.get("line_items", [])
                        ),
                        organization_id=self.organization_id,
                    )
                    db.add(invoice)
                    self.stats["invoices"]["saved"] += 1

            except Exception as e:
                logger.error(
                    f"Error persisting invoice {invoice_data.get('invoice_number')}: {e}"
                )
                self.stats["invoices"]["errors"] += 1

    def _persist_bank_transactions(
        self, db: Session, transactions_data: List[Dict[str, Any]]
    ) -> None:
        """Persist bank transactions to database."""
        logger.info(f"Persisting {len(transactions_data)} bank transactions")

        for transaction_data in transactions_data:
            try:
                # Check if transaction already exists
                existing_transaction = (
                    db.query(BankTransaction)
                    .filter_by(
                        xero_transaction_id=transaction_data.get("xero_transaction_id")
                    )
                    .first()
                )

                # Get contact ID if specified
                contact_id = self._get_contact_db_id(
                    db, transaction_data.get("contact_id")
                )

                if existing_transaction:
                    # Update existing transaction
                    self._update_bank_transaction(
                        existing_transaction, transaction_data, contact_id
                    )
                    self.stats["bank_transactions"]["updated"] += 1
                else:
                    # Create new bank transaction
                    bank_transaction = BankTransaction(
                        xero_transaction_id=transaction_data.get("xero_transaction_id"),
                        type=transaction_data.get("type", ""),
                        contact_id=contact_id,
                        line_items=self._parse_json_safe(
                            transaction_data.get("line_items", [])
                        ),
                        bank_account=self._parse_json_safe(
                            transaction_data.get("bank_account", {})
                        ),
                        is_reconciled=transaction_data.get("is_reconciled", False),
                        date=self._parse_datetime(transaction_data.get("date")),
                        reference=transaction_data.get("reference", ""),
                        currency_code=transaction_data.get("currency_code", "GBP"),
                        currency_rate=float(transaction_data.get("currency_rate", 1.0)),
                        url=transaction_data.get("url", ""),
                        status=transaction_data.get("status", ""),
                        line_amount_types=transaction_data.get("line_amount_types", ""),
                        sub_total=float(transaction_data.get("sub_total", 0)),
                        total_tax=float(transaction_data.get("total_tax", 0)),
                        total=float(transaction_data.get("total", 0)),
                        updated_date_utc=self._parse_datetime(
                            transaction_data.get("updated_date_utc")
                        ),
                        has_attachments=transaction_data.get("has_attachments", False),
                        organization_id=self.organization_id,
                    )
                    db.add(bank_transaction)
                    self.stats["bank_transactions"]["saved"] += 1

            except Exception as e:
                logger.error(
                    f"Error persisting bank transaction {transaction_data.get('xero_transaction_id')}: {e}"
                )
                self.stats["bank_transactions"]["errors"] += 1

    def _update_account(self, account: Account, account_data: Dict[str, Any]) -> None:
        """Update existing account with new data."""
        account.code = account_data.get("code", account.code)
        account.name = account_data.get("name", account.name)
        account.type = account_data.get("type", account.type)
        account.tax_type = account_data.get("tax_type", account.tax_type)
        account.description = account_data.get("description", account.description)
        account.class_type = account_data.get("class_type", account.class_type)
        account.status = account_data.get("status", account.status)
        account.show_in_expense_claims = account_data.get(
            "show_in_expense_claims", account.show_in_expense_claims
        )
        account.bank_account_number = account_data.get(
            "bank_account_number", account.bank_account_number
        )
        account.bank_account_type = account_data.get(
            "bank_account_type", account.bank_account_type
        )
        account.currency_code = account_data.get("currency_code", account.currency_code)
        account.reporting_code = account_data.get(
            "reporting_code", account.reporting_code
        )
        account.reporting_code_name = account_data.get(
            "reporting_code_name", account.reporting_code_name
        )
        account.has_attachments = account_data.get(
            "has_attachments", account.has_attachments
        )
        account.updated_date_utc = (
            self._parse_datetime(account_data.get("updated_date_utc"))
            or account.updated_date_utc
        )
        account.add_to_watchlist = account_data.get(
            "add_to_watchlist", account.add_to_watchlist
        )
        account.gaap_classification = account_data.get(
            "gaap_classification", account.gaap_classification
        )
        account.updated_at = datetime.now(timezone.utc)

    def _update_contact(self, contact: Contact, contact_data: Dict[str, Any]) -> None:
        """Update existing contact with new data."""
        contact.contact_number = contact_data.get(
            "contact_number", contact.contact_number
        )
        contact.account_number = contact_data.get(
            "account_number", contact.account_number
        )
        contact.contact_status = contact_data.get(
            "contact_status", contact.contact_status
        )
        contact.name = contact_data.get("name", contact.name)
        contact.first_name = contact_data.get("first_name", contact.first_name)
        contact.last_name = contact_data.get("last_name", contact.last_name)
        contact.email_address = contact_data.get("email_address", contact.email_address)
        contact.bank_account_details = contact_data.get(
            "bank_account_details", contact.bank_account_details
        )
        contact.tax_number = contact_data.get("tax_number", contact.tax_number)
        contact.accounts_receivable_tax_type = contact_data.get(
            "accounts_receivable_tax_type", contact.accounts_receivable_tax_type
        )
        contact.accounts_payable_tax_type = contact_data.get(
            "accounts_payable_tax_type", contact.accounts_payable_tax_type
        )
        contact.is_supplier = contact_data.get("is_supplier", contact.is_supplier)
        contact.is_customer = contact_data.get("is_customer", contact.is_customer)
        contact.default_currency = contact_data.get(
            "default_currency", contact.default_currency
        )
        contact.updated_date_utc = (
            self._parse_datetime(contact_data.get("updated_date_utc"))
            or contact.updated_date_utc
        )
        contact.has_attachments = contact_data.get(
            "has_attachments", contact.has_attachments
        )
        contact.has_validation_errors = contact_data.get(
            "has_validation_errors", contact.has_validation_errors
        )
        contact.phone_default = contact_data.get("phone_default", contact.phone_default)
        contact.phone_mobile = contact_data.get("phone_mobile", contact.phone_mobile)
        contact.phone_fax = contact_data.get("phone_fax", contact.phone_fax)
        contact.address_street = (
            self._parse_json_safe(contact_data.get("address_street"))
            or contact.address_street
        )
        contact.address_postal = (
            self._parse_json_safe(contact_data.get("address_postal"))
            or contact.address_postal
        )
        contact.updated_at = datetime.now(timezone.utc)

    def _update_invoice(
        self, invoice: Invoice, invoice_data: Dict[str, Any], contact_id: Optional[int]
    ) -> None:
        """Update existing invoice with new data."""
        invoice.type = invoice_data.get("type", invoice.type)
        invoice.contact_id = contact_id or invoice.contact_id
        invoice.date = self._parse_datetime(invoice_data.get("date")) or invoice.date
        invoice.due_date = (
            self._parse_datetime(invoice_data.get("due_date")) or invoice.due_date
        )
        invoice.line_amount_types = invoice_data.get(
            "line_amount_types", invoice.line_amount_types
        )
        invoice.invoice_number = invoice_data.get(
            "invoice_number", invoice.invoice_number
        )
        invoice.reference = invoice_data.get("reference", invoice.reference)
        invoice.branding_theme_id = invoice_data.get(
            "branding_theme_id", invoice.branding_theme_id
        )
        invoice.url = invoice_data.get("url", invoice.url)
        invoice.currency_code = invoice_data.get("currency_code", invoice.currency_code)
        invoice.currency_rate = float(
            invoice_data.get("currency_rate", invoice.currency_rate)
        )
        invoice.status = invoice_data.get("status", invoice.status)
        invoice.sent_to_contact = invoice_data.get(
            "sent_to_contact", invoice.sent_to_contact
        )
        invoice.expected_payment_date = (
            self._parse_datetime(invoice_data.get("expected_payment_date"))
            or invoice.expected_payment_date
        )
        invoice.planned_payment_date = (
            self._parse_datetime(invoice_data.get("planned_payment_date"))
            or invoice.planned_payment_date
        )
        invoice.sub_total = float(invoice_data.get("sub_total", invoice.sub_total))
        invoice.total_tax = float(invoice_data.get("total_tax", invoice.total_tax))
        invoice.total = float(invoice_data.get("total", invoice.total))
        invoice.total_discount = float(
            invoice_data.get("total_discount", invoice.total_discount)
        )
        invoice.has_attachments = invoice_data.get(
            "has_attachments", invoice.has_attachments
        )
        invoice.has_errors = invoice_data.get("has_errors", invoice.has_errors)
        invoice.is_discounted = invoice_data.get("is_discounted", invoice.is_discounted)
        invoice.payments = (
            self._parse_json_safe(invoice_data.get("payments", [])) or invoice.payments
        )
        invoice.amount_due = float(invoice_data.get("amount_due", invoice.amount_due))
        invoice.amount_paid = float(
            invoice_data.get("amount_paid", invoice.amount_paid)
        )
        invoice.amount_credited = float(
            invoice_data.get("amount_credited", invoice.amount_credited)
        )
        invoice.updated_date_utc = (
            self._parse_datetime(invoice_data.get("updated_date_utc"))
            or invoice.updated_date_utc
        )
        invoice.line_items = (
            self._parse_json_safe(invoice_data.get("line_items", []))
            or invoice.line_items
        )
        invoice.updated_at = datetime.now(timezone.utc)

    def _update_bank_transaction(
        self,
        bank_transaction: BankTransaction,
        transaction_data: Dict[str, Any],
        contact_id: Optional[int],
    ) -> None:
        """Update existing bank transaction with new data."""
        bank_transaction.type = transaction_data.get("type", bank_transaction.type)
        bank_transaction.contact_id = contact_id or bank_transaction.contact_id
        bank_transaction.line_items = (
            self._parse_json_safe(transaction_data.get("line_items", []))
            or bank_transaction.line_items
        )
        bank_transaction.bank_account = (
            self._parse_json_safe(transaction_data.get("bank_account", {}))
            or bank_transaction.bank_account
        )
        bank_transaction.is_reconciled = transaction_data.get(
            "is_reconciled", bank_transaction.is_reconciled
        )
        bank_transaction.date = (
            self._parse_datetime(transaction_data.get("date")) or bank_transaction.date
        )
        bank_transaction.reference = transaction_data.get(
            "reference", bank_transaction.reference
        )
        bank_transaction.currency_code = transaction_data.get(
            "currency_code", bank_transaction.currency_code
        )
        bank_transaction.currency_rate = float(
            transaction_data.get("currency_rate", bank_transaction.currency_rate)
        )
        bank_transaction.url = transaction_data.get("url", bank_transaction.url)
        bank_transaction.status = transaction_data.get(
            "status", bank_transaction.status
        )
        bank_transaction.line_amount_types = transaction_data.get(
            "line_amount_types", bank_transaction.line_amount_types
        )
        bank_transaction.sub_total = float(
            transaction_data.get("sub_total", bank_transaction.sub_total)
        )
        bank_transaction.total_tax = float(
            transaction_data.get("total_tax", bank_transaction.total_tax)
        )
        bank_transaction.total = float(
            transaction_data.get("total", bank_transaction.total)
        )
        bank_transaction.updated_date_utc = (
            self._parse_datetime(transaction_data.get("updated_date_utc"))
            or bank_transaction.updated_date_utc
        )
        bank_transaction.has_attachments = transaction_data.get(
            "has_attachments", bank_transaction.has_attachments
        )
        bank_transaction.updated_at = datetime.now(timezone.utc)

    def _get_contact_db_id(
        self, db: Session, xero_contact_id: Optional[str]
    ) -> Optional[int]:
        """Get database contact ID from Xero contact ID."""
        if not xero_contact_id:
            return None

        contact = (
            db.query(Contact)
            .filter_by(
                xero_contact_id=xero_contact_id, organization_id=self.organization_id
            )
            .first()
        )

        return contact.id if contact else None

    def _parse_datetime(self, date_string: Optional[str]) -> Optional[datetime]:
        """Parse datetime string safely."""
        if not date_string:
            return None

        try:
            # Try to parse various datetime formats
            return parse_date_safe(date_string)
        except Exception as e:
            logger.warning(f"Could not parse datetime '{date_string}': {e}")
            return None

    def _parse_json_safe(self, json_data: Any) -> Any:
        """Parse JSON data safely."""
        if json_data is None:
            return None

        if isinstance(json_data, str):
            try:
                return json.loads(json_data)
            except json.JSONDecodeError:
                return json_data

        return json_data
