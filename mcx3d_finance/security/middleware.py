"""
Security middleware for MCX3D Financials.
"""
import time
import secrets
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.cors import CORSMiddleware
from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.security.config import SecurityConfig


logger = LoggerFactory.get_logger(__name__, domain='security')


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to all responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        for header, value in SecurityConfig.SECURITY_HEADERS.items():
            response.headers[header] = value
        
        return response


class CSRFMiddleware(BaseHTTPMiddleware):
    """CSRF protection middleware."""
    
    SAFE_METHODS = {"GET", "HEAD", "OPTIONS", "TRACE"}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not SecurityConfig.CSRF_ENABLED:
            return await call_next(request)
        
        # Skip CSRF for safe methods
        if request.method in self.SAFE_METHODS:
            return await call_next(request)
        
        # Skip CSRF for API key authenticated requests
        if request.headers.get(SecurityConfig.API_KEY_HEADER):
            return await call_next(request)
        
        # Get CSRF token from header
        csrf_token = request.headers.get(SecurityConfig.CSRF_HEADER_NAME)
        if not csrf_token:
            return JSONResponse(
                status_code=403,
                content={"detail": "CSRF token missing"}
            )
        
        # Get CSRF token from cookie
        cookie_token = request.cookies.get(SecurityConfig.CSRF_COOKIE_NAME)
        if not cookie_token:
            return JSONResponse(
                status_code=403,
                content={"detail": "CSRF cookie missing"}
            )
        
        # Validate tokens match
        if not secrets.compare_digest(csrf_token, cookie_token):
            return JSONResponse(
                status_code=403,
                content={"detail": "CSRF token invalid"}
            )
        
        response = await call_next(request)
        
        # Set new CSRF token for GET requests
        if request.method == "GET" and request.url.path.startswith("/api"):
            new_token = secrets.token_urlsafe(SecurityConfig.CSRF_TOKEN_LENGTH)
            response.set_cookie(
                key=SecurityConfig.CSRF_COOKIE_NAME,
                value=new_token,
                secure=SecurityConfig.CSRF_COOKIE_SECURE,
                httponly=SecurityConfig.CSRF_COOKIE_HTTPONLY,
                samesite=SecurityConfig.SESSION_COOKIE_SAMESITE.lower(),
                max_age=SecurityConfig.SESSION_EXPIRE_MINUTES * 60
            )
            response.headers["X-CSRF-Token"] = new_token
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Log all API requests for security auditing."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not SecurityConfig.LOG_API_REQUESTS:
            return await call_next(request)
        
        start_time = time.time()
        
        # Get request details
        client_ip = get_remote_address(request)
        user_agent = request.headers.get("user-agent", "unknown")
        
        # Log request
        logger.info(
            f"API Request: {request.method} {request.url.path} "
            f"from {client_ip} (User-Agent: {user_agent})"
        )
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"API Response: {request.method} {request.url.path} "
            f"Status: {response.status_code} "
            f"Duration: {process_time:.3f}s"
        )
        
        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class RateLimitMiddleware:
    """Rate limiting middleware wrapper."""
    
    def __init__(self):
        self.limiter = Limiter(
            key_func=get_remote_address,
            default_limits=[SecurityConfig.RATE_LIMIT_DEFAULT] if SecurityConfig.RATE_LIMIT_ENABLED else [],
            storage_uri=SecurityConfig.RATE_LIMIT_STORAGE_URL if SecurityConfig.RATE_LIMIT_ENABLED else None,
            enabled=SecurityConfig.RATE_LIMIT_ENABLED
        )
    
    def __call__(self, app):
        """Apply rate limiting to the app."""
        app.state.limiter = self.limiter
        app.add_exception_handler(RateLimitExceeded, self._rate_limit_handler)
        return app
    
    async def _rate_limit_handler(self, request: Request, exc: RateLimitExceeded):
        """Handle rate limit exceeded errors."""
        response = JSONResponse(
            status_code=429,
            content={"detail": f"Rate limit exceeded: {exc.detail}"}
        )
        response.headers["Retry-After"] = str(exc.retry_after)
        return response


def setup_security_middleware(app):
    """Setup all security middleware for the application."""
    # Validate configuration
    SecurityConfig.validate()
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        **SecurityConfig.get_cors_config()
    )
    
    # Security headers
    app.add_middleware(SecurityHeadersMiddleware)
    
    # CSRF protection
    app.add_middleware(CSRFMiddleware)
    
    # Request logging
    app.add_middleware(RequestLoggingMiddleware)
    
    # Rate limiting
    if SecurityConfig.RATE_LIMIT_ENABLED:
        rate_limiter = RateLimitMiddleware()
        rate_limiter(app)
    
    logger.info("Security middleware configured successfully")