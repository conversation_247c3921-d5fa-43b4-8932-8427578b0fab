"""
Database error handling utilities for MCX3D Finance.

This module provides standardized error handling for database operations,
including transaction management, deadlock recovery, and connection pooling.
"""

import functools
from mcx3d_finance.core.logging_factory import LoggerFactory
import time
from contextlib import contextmanager
from typing import Any, Callable, Optional, Type, TypeVar, Union

from sqlalchemy.exc import (
    DBAPIError,
    DataError,
    DatabaseError,
    IntegrityError,
    InvalidRequestError,
    OperationalError,
    ProgrammingError,
    SQLAlchemyError,
)
from sqlalchemy.orm import Session

from mcx3d_finance.exceptions.base import MCX3DException
from mcx3d_finance.exceptions.integration import DataPersistenceError
from mcx3d_finance.exceptions.validation import DataIntegrityError
from mcx3d_finance.exceptions.handlers import (
    error_boundary,
    persistence_error,
)
from mcx3d_finance.utils.error_logging import error_logger, get_correlation_id

logger = LoggerFactory.get_logger(__name__, domain='db')

T = TypeVar('T')


class DatabaseError(MCX3DException):
    """Base class for database-specific errors."""
    pass


class DeadlockError(DatabaseError):
    """Raised when a database deadlock is detected."""
    pass


class ConnectionPoolExhaustedError(DatabaseError):
    """Raised when the connection pool is exhausted."""
    pass


def convert_db_error(error: Exception) -> MCX3DException:
    """
    Convert SQLAlchemy exceptions to MCX3D exceptions.
    
    Args:
        error: The original database exception
        
    Returns:
        MCX3D exception with appropriate context
    """
    error_message = str(error)
    
    # Handle specific SQLAlchemy exceptions
    if isinstance(error, IntegrityError):
        # Parse common integrity violations
        if "duplicate key" in error_message.lower():
            return DataIntegrityError(
                message=f"Duplicate key violation: {error_message}",
                user_message="A record with this identifier already exists",
                context={
                    "error_type": "duplicate_key",
                    "original_error": error_message,
                },
                original_exception=error
            )
        elif "foreign key" in error_message.lower():
            return DataIntegrityError(
                message=f"Foreign key violation: {error_message}",
                user_message="Referenced record does not exist or cannot be deleted",
                context={
                    "error_type": "foreign_key",
                    "original_error": error_message,
                },
                original_exception=error
            )
        elif "not null" in error_message.lower():
            return DataIntegrityError(
                message=f"Not null violation: {error_message}",
                user_message="Required field is missing",
                context={
                    "error_type": "not_null",
                    "original_error": error_message,
                },
                original_exception=error
            )
        else:
            return DataIntegrityError(
                message=f"Data integrity violation: {error_message}",
                user_message="Data validation failed",
                original_exception=error
            )
    
    elif isinstance(error, OperationalError):
        # Handle operational errors
        if "deadlock" in error_message.lower():
            return DeadlockError(
                message=f"Database deadlock detected: {error_message}",
                user_message="The operation could not be completed due to concurrent access. Please try again.",
                context={
                    "error_type": "deadlock",
                    "retryable": True,
                },
                original_exception=error
            )
        elif "connection" in error_message.lower():
            return ConnectionPoolExhaustedError(
                message=f"Database connection error: {error_message}",
                user_message="Unable to connect to the database. Please try again later.",
                context={
                    "error_type": "connection",
                    "retryable": True,
                },
                original_exception=error
            )
        else:
            return DatabaseError(
                message=f"Database operational error: {error_message}",
                user_message="A database error occurred. Please try again.",
                original_exception=error
            )
    
    elif isinstance(error, DataError):
        return DataPersistenceError(
            message=f"Invalid data for database: {error_message}",
            user_message="The provided data is invalid or incompatible",
            original_exception=error
        )
    
    elif isinstance(error, ProgrammingError):
        return DatabaseError(
            message=f"Database programming error: {error_message}",
            user_message="A database configuration error occurred",
            severity="CRITICAL",
            original_exception=error
        )
    
    elif isinstance(error, SQLAlchemyError):
        return DataPersistenceError(
            message=f"Database error: {error_message}",
            user_message="A database error occurred while processing your request",
            original_exception=error
        )
    
    else:
        # Unknown database error
        return DataPersistenceError(
            message=f"Unexpected database error: {error_message}",
            user_message="An unexpected error occurred. Please contact support.",
            original_exception=error
        )


@contextmanager
def database_transaction(
    session: Session,
    operation: str,
    entity_type: Optional[str] = None,
    entity_id: Optional[Union[str, int]] = None,
    read_only: bool = False
):
    """
    Context manager for database transactions with standardized error handling.
    
    Args:
        session: SQLAlchemy session
        operation: Operation being performed (for logging)
        entity_type: Type of entity being operated on
        entity_id: ID of the entity
        read_only: Whether this is a read-only transaction
        
    Example:
        with database_transaction(session, "update", "Organization", org_id):
            org = session.query(Organization).get(org_id)
            org.name = new_name
            session.flush()
    """
    correlation_id = get_correlation_id()
    start_time = time.time()
    
    try:
        # Begin transaction if not already in one
        if not session.in_transaction():
            session.begin()
        
        yield session
        
        # Commit if not read-only
        if not read_only:
            session.commit()
            
        # Log successful transaction
        duration_ms = (time.time() - start_time) * 1000
        logger.info(
            f"Database transaction completed: {operation}",
            extra={
                "operation": operation,
                "entity_type": entity_type,
                "entity_id": entity_id,
                "duration_ms": duration_ms,
                "correlation_id": correlation_id,
            }
        )
        
    except Exception as e:
        # Rollback on any error
        session.rollback()
        
        # Convert to MCX3D exception
        mcx3d_error = convert_db_error(e)
        mcx3d_error.add_context("operation", operation)
        if entity_type:
            mcx3d_error.add_context("entity_type", entity_type)
        if entity_id:
            mcx3d_error.add_context("entity_id", entity_id)
        mcx3d_error.add_context("correlation_id", correlation_id)
        
        # Log the error
        duration_ms = (time.time() - start_time) * 1000
        error_logger.log_error(
            mcx3d_error,
            f"Database transaction failed: {operation}",
            extra_context={
                "duration_ms": duration_ms,
            },
            correlation_id=correlation_id
        )
        
        raise mcx3d_error
    finally:
        # Ensure session is cleaned up
        session.close()


def with_database_retry(
    max_retries: int = 3,
    retry_on: Optional[tuple] = None,
    backoff_factor: float = 1.0
):
    """
    Decorator for database operations with retry logic.
    
    Args:
        max_retries: Maximum number of retry attempts
        retry_on: Tuple of exception types to retry on
        backoff_factor: Exponential backoff factor
        
    Example:
        @with_database_retry(max_retries=3)
        def update_organization(session, org_id, data):
            # Database operation that might fail transiently
            pass
    """
    if retry_on is None:
        retry_on = (DeadlockError, ConnectionPoolExhaustedError, OperationalError)
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except retry_on as e:
                    last_exception = e
                    
                    if attempt == max_retries - 1:
                        # Last attempt, don't retry
                        raise
                    
                    # Calculate backoff delay
                    delay = backoff_factor * (2 ** attempt)
                    
                    logger.warning(
                        f"Database operation failed (attempt {attempt + 1}/{max_retries}): {e}. "
                        f"Retrying in {delay}s...",
                        extra={
                            "attempt": attempt + 1,
                            "max_retries": max_retries,
                            "delay": delay,
                            "error_type": type(e).__name__,
                        }
                    )
                    
                    time.sleep(delay)
            
            # This shouldn't be reached, but just in case
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator


def handle_db_errors(
    operation: Optional[str] = None,
    entity_type: Optional[str] = None,
    default_return: Any = None
):
    """
    Decorator for standardized database error handling.
    
    Args:
        operation: Operation name (defaults to function name)
        entity_type: Type of entity being operated on
        default_return: Default return value on error
        
    Example:
        @handle_db_errors(operation="fetch_organizations", entity_type="Organization")
        def get_all_organizations(session):
            return session.query(Organization).all()
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            op_name = operation or func.__name__
            
            try:
                return func(*args, **kwargs)
            except SQLAlchemyError as e:
                # Convert to MCX3D exception
                mcx3d_error = convert_db_error(e)
                mcx3d_error.add_context("operation", op_name)
                if entity_type:
                    mcx3d_error.add_context("entity_type", entity_type)
                
                # Log the error
                error_logger.log_error(
                    mcx3d_error,
                    f"Database operation failed: {op_name}"
                )
                
                if default_return is not None:
                    return default_return
                raise mcx3d_error
            except Exception as e:
                # Handle non-database exceptions
                error = persistence_error(
                    operation=op_name,
                    entity_type=entity_type or "unknown",
                    message=str(e)
                )
                
                error_logger.log_error(
                    error,
                    f"Unexpected error in database operation: {op_name}"
                )
                
                if default_return is not None:
                    return default_return
                raise error
                
        return wrapper
    return decorator


class DatabaseConnectionManager:
    """
    Manages database connections with health checking and recovery.
    """
    
    def __init__(self, session_factory: Callable[[], Session]):
        self.session_factory = session_factory
        self._health_check_interval = 60  # seconds
        self._last_health_check = 0
    
    @contextmanager
    def get_session(self) -> Session:
        """
        Get a database session with automatic health checking.
        
        Yields:
            Database session
        """
        # Check connection health periodically
        if time.time() - self._last_health_check > self._health_check_interval:
            self._check_connection_health()
            self._last_health_check = time.time()
        
        session = None
        try:
            session = self.session_factory()
            yield session
        except Exception as e:
            if session:
                session.rollback()
            raise convert_db_error(e)
        finally:
            if session:
                session.close()
    
    def _check_connection_health(self):
        """Check database connection health."""
        try:
            with self.session_factory() as session:
                # Simple query to check connection
                session.execute("SELECT 1")
                logger.debug("Database connection health check passed")
        except Exception as e:
            logger.error(f"Database connection health check failed: {e}")
            raise ConnectionPoolExhaustedError(
                message="Database connection health check failed",
                user_message="Database connection is unavailable",
                original_exception=e
            )