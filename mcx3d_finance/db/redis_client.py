"""
Redis connection management for the MCX3D financial system.

Provides a singleton Redis client with connection pooling to avoid
recreation of Redis connections and improve performance.
"""

import os
import redis
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Optional
from redis.connection import ConnectionPool
from mcx3d_finance.core.config import detect_environment, load_config

logger = LoggerFactory.get_logger(__name__, domain='db')


class RedisClientSingleton:
    """Singleton Redis client with connection pooling."""
    
    _instance = None
    _client = None
    _pool = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisClientSingleton, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._client is None:
            self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Redis client with connection pooling."""
        try:
            environment = detect_environment()
            config = load_config()
            redis_config = config.get('redis', {})
            
            # Get Redis URL from environment or config
            redis_url = os.getenv('REDIS_URL') or redis_config.get('url', 'redis://localhost:6379/0')
            
            # Connection pool configuration based on environment
            pool_kwargs = {
                'max_connections': redis_config.get('max_connections', 20),
                'retry_on_timeout': redis_config.get('retry_on_timeout', True),
                'socket_timeout': redis_config.get('socket_timeout', 5),
                'socket_connect_timeout': redis_config.get('socket_connect_timeout', 5),
                'health_check_interval': redis_config.get('health_check_interval', 30)
            }
            
            # Environment-specific optimizations
            if environment == 'production':
                pool_kwargs.update({
                    'max_connections': 30,
                    'socket_timeout': 5,
                    'socket_connect_timeout': 5,
                    'retry_on_timeout': True,
                    'health_check_interval': 30
                })
            elif environment == 'development':
                pool_kwargs.update({
                    'max_connections': 10,
                    'socket_timeout': 3,
                    'socket_connect_timeout': 3,
                    'health_check_interval': 60
                })
            elif environment == 'testing':
                pool_kwargs.update({
                    'max_connections': 5,
                    'socket_timeout': 1,
                    'socket_connect_timeout': 1,
                    'retry_on_timeout': False,
                    'health_check_interval': 120
                })
            
            # Create connection pool
            self._pool = ConnectionPool.from_url(redis_url, **pool_kwargs)
            
            # Create Redis client with the pool
            self._client = redis.Redis(connection_pool=self._pool)
            
            # Test connection
            self._client.ping()
            
            logger.info(f"Redis client initialized for {environment} environment "
                       f"with {pool_kwargs['max_connections']} max connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis client: {e}")
            # Create a basic client as fallback
            self._client = redis.Redis.from_url(
                os.getenv('REDIS_URL', 'redis://localhost:6379/0')
            )
    
    @property
    def client(self) -> redis.Redis:
        """Get the Redis client instance."""
        if self._client is None:
            self._initialize_client()
        return self._client
    
    def get_connection_info(self) -> dict:
        """Get connection pool information for monitoring."""
        if self._pool is None:
            return {'status': 'not_initialized'}
        
        try:
            info = self._client.info('clients')
            
            # Safely get pool connection info
            pool_info = {}
            if hasattr(self._pool, 'max_connections'):
                pool_info['pool_max_connections'] = self._pool.max_connections
            
            if hasattr(self._pool, '_created_connections'):
                created_conns = self._pool._created_connections
                pool_info['pool_created_connections'] = len(created_conns) if hasattr(created_conns, '__len__') else 0
            
            if hasattr(self._pool, '_available_connections'):
                available_conns = self._pool._available_connections
                pool_info['pool_available_connections'] = len(available_conns) if hasattr(available_conns, '__len__') else 0
            
            return {
                'status': 'connected',
                **pool_info,
                'connected_clients': info.get('connected_clients', 0),
                'blocked_clients': info.get('blocked_clients', 0),
                'tracking_clients': info.get('tracking_clients', 0)
            }
        except Exception as e:
            logger.error(f"Failed to get Redis connection info: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def health_check(self) -> dict:
        """Perform health check on Redis connection."""
        try:
            start_time = time.time()
            result = self._client.ping()
            response_time = (time.time() - start_time) * 1000
            
            if result:
                return {
                    'status': 'healthy',
                    'response_time_ms': round(response_time, 2),
                    'connection_info': self.get_connection_info()
                }
            else:
                return {
                    'status': 'unhealthy',
                    'error': 'Ping failed'
                }
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def close(self):
        """Close Redis connections (for cleanup)."""
        if self._client:
            self._client.close()
        if self._pool:
            self._pool.disconnect()
        self._client = None
        self._pool = None
        logger.info("Redis client connections closed")


# Global singleton instance
redis_client_singleton = RedisClientSingleton()


def get_redis_client() -> redis.Redis:
    """Get the singleton Redis client."""
    return redis_client_singleton.client


def get_redis_connection_info() -> dict:
    """Get Redis connection pool information."""
    return redis_client_singleton.get_connection_info()


def redis_health_check() -> dict:
    """Perform Redis health check."""
    return redis_client_singleton.health_check()


# Import time for health check
import time