from typing import List, Optional, Any, Dict
from sqlalchemy import <PERSON>umn, Integer, String, ForeignKey, DateTime, Float, Text, Boolean, JSON
from sqlalchemy.orm import relationship, Mapped, mapped_column
from datetime import datetime, timezone
from mcx3d_finance.db.session import Base


def utc_now() -> datetime:
    """Return timezone-aware UTC datetime."""
    return datetime.now(timezone.utc)


class Organization(Base):
    __tablename__ = "organizations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[Optional[str]] = mapped_column(String, index=True)
    xero_tenant_id: Mapped[str] = mapped_column(String, unique=True, index=True)
    xero_tenant_type: Mapped[Optional[str]] = mapped_column(String)
    xero_token: Mapped[Optional[str]] = mapped_column(Text)
    token_expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    base_currency: Mapped[str] = mapped_column(String, default="USD")
    last_sync_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    sync_status: Mapped[Optional[str]] = mapped_column(String)
    created_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    accounts: Mapped[List["Account"]] = relationship("Account", back_populates="organization")
    contacts: Mapped[List["Contact"]] = relationship("Contact", back_populates="organization")
    transactions: Mapped[List["Transaction"]] = relationship("Transaction", back_populates="organization")
    invoices: Mapped[List["Invoice"]] = relationship("Invoice", back_populates="organization")
    bank_transactions: Mapped[List["BankTransaction"]] = relationship("BankTransaction", back_populates="organization")
    users: Mapped[List["User"]] = relationship("User", secondary="user_organizations", back_populates="organizations")
    user_associations: Mapped[List["UserOrganization"]] = relationship("UserOrganization", back_populates="organization", cascade="all, delete-orphan", overlaps="organizations,users")


class Account(Base):
    __tablename__ = "accounts"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_account_id: Mapped[str] = mapped_column(String, unique=True, index=True)
    code: Mapped[Optional[str]] = mapped_column(String, index=True)
    name: Mapped[Optional[str]] = mapped_column(String, index=True)
    type: Mapped[Optional[str]] = mapped_column(String)
    tax_type: Mapped[Optional[str]] = mapped_column(String)
    description: Mapped[Optional[str]] = mapped_column(Text)
    class_type: Mapped[Optional[str]] = mapped_column(String)
    status: Mapped[Optional[str]] = mapped_column(String)
    show_in_expense_claims: Mapped[bool] = mapped_column(Boolean, default=False)
    bank_account_number: Mapped[Optional[str]] = mapped_column(String)
    bank_account_type: Mapped[Optional[str]] = mapped_column(String)
    currency_code: Mapped[str] = mapped_column(String, default="GBP")
    reporting_code: Mapped[Optional[str]] = mapped_column(String)
    reporting_code_name: Mapped[Optional[str]] = mapped_column(String)
    has_attachments: Mapped[bool] = mapped_column(Boolean, default=False)
    updated_date_utc: Mapped[Optional[datetime]] = mapped_column(DateTime)
    add_to_watchlist: Mapped[bool] = mapped_column(Boolean, default=False)
    gaap_classification: Mapped[Optional[str]] = mapped_column(String)
    organization_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("organizations.id"))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now)

    organization: Mapped["Organization"] = relationship("Organization", back_populates="accounts")
    transactions: Mapped[List["Transaction"]] = relationship("Transaction", back_populates="account")


class Contact(Base):
    __tablename__ = "contacts"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_contact_id: Mapped[str] = mapped_column(String, unique=True, index=True)
    contact_number: Mapped[Optional[str]] = mapped_column(String)
    account_number: Mapped[Optional[str]] = mapped_column(String)
    contact_status: Mapped[str] = mapped_column(String, default="ACTIVE")
    name: Mapped[Optional[str]] = mapped_column(String, index=True)
    first_name: Mapped[Optional[str]] = mapped_column(String)
    last_name: Mapped[Optional[str]] = mapped_column(String)
    email_address: Mapped[Optional[str]] = mapped_column(String, index=True)
    bank_account_details: Mapped[Optional[str]] = mapped_column(String)
    tax_number: Mapped[Optional[str]] = mapped_column(String)
    accounts_receivable_tax_type: Mapped[Optional[str]] = mapped_column(String)
    accounts_payable_tax_type: Mapped[Optional[str]] = mapped_column(String)
    is_supplier: Mapped[bool] = mapped_column(Boolean, default=False)
    is_customer: Mapped[bool] = mapped_column(Boolean, default=False)
    default_currency: Mapped[str] = mapped_column(String, default="GBP")
    updated_date_utc: Mapped[Optional[datetime]] = mapped_column(DateTime)
    has_attachments: Mapped[bool] = mapped_column(Boolean, default=False)
    has_validation_errors: Mapped[bool] = mapped_column(Boolean, default=False)
    phone_default: Mapped[Optional[str]] = mapped_column(String)
    phone_mobile: Mapped[Optional[str]] = mapped_column(String)
    phone_fax: Mapped[Optional[str]] = mapped_column(String)
    address_street: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    address_postal: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    organization_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("organizations.id"))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now)

    organization: Mapped["Organization"] = relationship("Organization", back_populates="contacts")
    invoices: Mapped[List["Invoice"]] = relationship("Invoice", back_populates="contact")
    bank_transactions: Mapped[List["BankTransaction"]] = relationship("BankTransaction", back_populates="contact")


class Transaction(Base):
    __tablename__ = "transactions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_transaction_id: Mapped[str] = mapped_column(String, unique=True, index=True)
    type: Mapped[Optional[str]] = mapped_column(String)
    date: Mapped[Optional[datetime]] = mapped_column(DateTime, index=True)
    amount: Mapped[Optional[float]] = mapped_column(Float)
    description: Mapped[Optional[str]] = mapped_column(Text)
    reference: Mapped[Optional[str]] = mapped_column(String)
    is_reconciled: Mapped[bool] = mapped_column(Boolean, default=False)
    currency_code: Mapped[str] = mapped_column(String, default="GBP")
    currency_rate: Mapped[float] = mapped_column(Float, default=1.0)
    sub_total: Mapped[Optional[float]] = mapped_column(Float)
    total_tax: Mapped[Optional[float]] = mapped_column(Float)
    total: Mapped[Optional[float]] = mapped_column(Float)
    line_items: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(JSON)
    status: Mapped[Optional[str]] = mapped_column(String)
    updated_date_utc: Mapped[Optional[datetime]] = mapped_column(DateTime)
    has_attachments: Mapped[bool] = mapped_column(Boolean, default=False)
    contact_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("contacts.id"))
    account_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("accounts.id"))
    organization_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("organizations.id"))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now)

    organization: Mapped["Organization"] = relationship("Organization", back_populates="transactions")
    account: Mapped["Account"] = relationship("Account", back_populates="transactions")
    contact: Mapped["Contact"] = relationship("Contact")


class Invoice(Base):
    __tablename__ = "invoices"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_invoice_id: Mapped[str] = mapped_column(String, unique=True, index=True)
    type: Mapped[Optional[str]] = mapped_column(String)
    contact_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("contacts.id"))
    date: Mapped[Optional[datetime]] = mapped_column(DateTime, index=True)
    due_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    line_amount_types: Mapped[Optional[str]] = mapped_column(String)
    invoice_number: Mapped[Optional[str]] = mapped_column(String, index=True)
    reference: Mapped[Optional[str]] = mapped_column(String)
    branding_theme_id: Mapped[Optional[str]] = mapped_column(String)
    url: Mapped[Optional[str]] = mapped_column(String)
    currency_code: Mapped[str] = mapped_column(String, default="GBP")
    currency_rate: Mapped[float] = mapped_column(Float, default=1.0)
    status: Mapped[Optional[str]] = mapped_column(String, index=True)
    sent_to_contact: Mapped[bool] = mapped_column(Boolean, default=False)
    expected_payment_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    planned_payment_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    sub_total: Mapped[Optional[float]] = mapped_column(Float)
    total_tax: Mapped[Optional[float]] = mapped_column(Float)
    total: Mapped[Optional[float]] = mapped_column(Float)
    total_discount: Mapped[Optional[float]] = mapped_column(Float)
    has_attachments: Mapped[bool] = mapped_column(Boolean, default=False)
    has_errors: Mapped[bool] = mapped_column(Boolean, default=False)
    is_discounted: Mapped[bool] = mapped_column(Boolean, default=False)
    payments: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(JSON)
    amount_due: Mapped[Optional[float]] = mapped_column(Float)
    amount_paid: Mapped[Optional[float]] = mapped_column(Float)
    amount_credited: Mapped[Optional[float]] = mapped_column(Float)
    updated_date_utc: Mapped[Optional[datetime]] = mapped_column(DateTime)
    line_items: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(JSON)
    organization_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("organizations.id"))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now)

    organization: Mapped["Organization"] = relationship("Organization")
    contact: Mapped["Contact"] = relationship("Contact", back_populates="invoices")


class BankTransaction(Base):
    __tablename__ = "bank_transactions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_transaction_id: Mapped[str] = mapped_column(String, unique=True, index=True)
    type: Mapped[Optional[str]] = mapped_column(String)
    contact_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("contacts.id"))
    line_items: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(JSON)
    bank_account: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    is_reconciled: Mapped[bool] = mapped_column(Boolean, default=False)
    date: Mapped[Optional[datetime]] = mapped_column(DateTime, index=True)
    reference: Mapped[Optional[str]] = mapped_column(String)
    currency_code: Mapped[str] = mapped_column(String, default="GBP")
    currency_rate: Mapped[float] = mapped_column(Float, default=1.0)
    url: Mapped[Optional[str]] = mapped_column(String)
    status: Mapped[Optional[str]] = mapped_column(String)
    line_amount_types: Mapped[Optional[str]] = mapped_column(String)
    sub_total: Mapped[Optional[float]] = mapped_column(Float)
    total_tax: Mapped[Optional[float]] = mapped_column(Float)
    total: Mapped[Optional[float]] = mapped_column(Float)
    updated_date_utc: Mapped[Optional[datetime]] = mapped_column(DateTime)
    has_attachments: Mapped[bool] = mapped_column(Boolean, default=False)
    organization_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("organizations.id"))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now)

    organization: Mapped["Organization"] = relationship("Organization")
    contact: Mapped["Contact"] = relationship("Contact", back_populates="bank_transactions")


class BankReconciliation(Base):
    """Track bank reconciliation history and matched transactions."""
    __tablename__ = "bank_reconciliations"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    organization_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("organizations.id"))
    bank_account_id: Mapped[Optional[str]] = mapped_column(String)
    start_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    end_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    status: Mapped[Optional[str]] = mapped_column(String)
    
    total_transactions: Mapped[int] = mapped_column(Integer, default=0)
    matched_transactions_count: Mapped[int] = mapped_column("matched_transactions", Integer, default=0)
    unmatched_transactions: Mapped[int] = mapped_column(Integer, default=0)
    total_amount: Mapped[float] = mapped_column(Float, default=0.0)
    matched_amount: Mapped[float] = mapped_column(Float, default=0.0)
    unmatched_amount: Mapped[float] = mapped_column(Float, default=0.0)
    
    results: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    errors: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(JSON)
    
    user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"))
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    organization: Mapped["Organization"] = relationship("Organization")
    user: Mapped["User"] = relationship("User")
    matched_transactions: Mapped[List["ReconciliationMatch"]] = relationship("ReconciliationMatch", back_populates="reconciliation")


class ReconciliationMatch(Base):
    """Individual transaction matches in a reconciliation."""
    __tablename__ = "reconciliation_matches"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    reconciliation_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("bank_reconciliations.id"))
    bank_transaction_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("bank_transactions.id"))
    
    statement_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    statement_description: Mapped[Optional[str]] = mapped_column(String)
    statement_amount: Mapped[Optional[float]] = mapped_column(Float)
    statement_reference: Mapped[Optional[str]] = mapped_column(String)
    
    match_type: Mapped[Optional[str]] = mapped_column(String)
    match_confidence: Mapped[Optional[float]] = mapped_column(Float)
    match_criteria: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    
    reconciliation: Mapped["BankReconciliation"] = relationship("BankReconciliation", back_populates="matched_transactions")
    bank_transaction: Mapped["BankTransaction"] = relationship("BankTransaction")


class SyncStatus(Base):
    __tablename__ = "sync_status"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    organization_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("organizations.id"))
    sync_type: Mapped[Optional[str]] = mapped_column(String)
    status: Mapped[Optional[str]] = mapped_column(String)
    records_synced: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    organization: Mapped["Organization"] = relationship("Organization")


class User(Base):
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    email: Mapped[str] = mapped_column(String, unique=True, index=True)
    hashed_password: Mapped[str] = mapped_column(String)
    full_name: Mapped[Optional[str]] = mapped_column(String)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    
    organizations: Mapped[List["Organization"]] = relationship("Organization", secondary="user_organizations", back_populates="users")
    organization_associations: Mapped[List["UserOrganization"]] = relationship("UserOrganization", back_populates="user", cascade="all, delete-orphan", overlaps="organizations,users")


class UserOrganization(Base):
    __tablename__ = "user_organizations"
    
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), primary_key=True)
    organization_id: Mapped[int] = mapped_column(Integer, ForeignKey("organizations.id"), primary_key=True)
    role: Mapped[Optional[str]] = mapped_column(String)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    
    user: Mapped["User"] = relationship("User", back_populates="organization_associations", overlaps="organizations,users")
    organization: Mapped["Organization"] = relationship("Organization", back_populates="user_associations", overlaps="organizations,users")
