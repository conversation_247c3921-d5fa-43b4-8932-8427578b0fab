"""
OAuth Security Enhancements for Xero Integration
"""

import re
import time
from typing import Dict, List, Optional, Set, Union
from redis import Redis
from urllib.parse import urlparse
from threading import Lock
from mcx3d_finance.core.config import settings
from mcx3d_finance.utils.redis_client import get_redis_client
from mcx3d_finance.exceptions.auth import XeroAuthorizationError
from mcx3d_finance.core.logging_factory import LoggerFactory

logger = LoggerFactory.get_logger(__name__, domain='auth')


class RedirectURIValidator:
    """
    Enhanced redirect URI validation with security whitelisting.
    """
    
    def __init__(self) -> None:
        self.development_patterns = [
            r'^http://localhost:8000/api/auth/xero/callback$',
            r'^http://127\.0\.0\.1:8000/api/auth/xero/callback$',
        ]
        
        self.production_patterns = [
            r'^https://[a-zA-Z0-9-]+\.mcx3d\.com/api/auth/xero/callback$',
            r'^https://app\.mcx3d\.com/api/auth/xero/callback$',
        ]
    
    def validate_redirect_uri(self, redirect_uri: str) -> bool:
        """
        Validate redirect URI against environment-appropriate whitelist.
        
        Args:
            redirect_uri: The redirect URI to validate
            
        Returns:
            True if valid, False otherwise
            
        Raises:
            XeroAuthorizationError: If URI is invalid
        """
        if not redirect_uri:
            raise XeroAuthorizationError("Redirect URI cannot be empty")
        
        # Parse URI for basic validation
        try:
            parsed = urlparse(redirect_uri)
        except Exception as e:
            raise XeroAuthorizationError(f"Invalid redirect URI format: {e}")
        
        # Environment-specific validation
        if settings.debug or settings.environment in ['development', 'testing']:
            patterns = self.development_patterns
        else:
            patterns = self.production_patterns
        
        # Check against allowed patterns
        for pattern in patterns:
            if re.match(pattern, redirect_uri):
                logger.debug(f"Redirect URI validated: {redirect_uri}")
                return True
        
        # Log security violation
        logger.error(f"SECURITY: Invalid redirect URI attempted: {redirect_uri}")
        raise XeroAuthorizationError(
            f"Redirect URI not allowed: {redirect_uri}. "
            f"Please use an approved redirect URI for {settings.environment} environment."
        )
    
    def get_allowed_redirect_uris(self) -> List[str]:
        """Get list of allowed redirect URIs for current environment."""
        if settings.debug or settings.environment in ['development', 'testing']:
            return [
                'http://localhost:8000/api/auth/xero/callback',
                'http://127.0.0.1:8000/api/auth/xero/callback',
            ]
        else:
            return [
                'https://app.mcx3d.com/api/auth/xero/callback',
                'https://prod.mcx3d.com/api/auth/xero/callback',
            ]


class OAuthRateLimiter:
    """
    Rate limiter for OAuth endpoints to prevent abuse.
    """
    
    def __init__(self) -> None:
        self.redis_available = True
        self.memory_store: Dict[str, List[float]] = {}
        self.memory_lock = Lock()
        self.redis_client: Optional[Redis] = None
        
        try:
            self.redis_client = get_redis_client()
            self.redis_client.ping()
        except Exception as e:
            logger.warning(f"Redis unavailable for rate limiting: {e}. Using memory fallback.")
            self.redis_available = False
            self.redis_client = None
    
    def check_rate_limit(self, 
                        identifier: str, 
                        endpoint: str = "oauth", 
                        max_requests: int = 5, 
                        window_seconds: int = 300) -> bool:
        """
        Check if request is within rate limits.
        
        Args:
            identifier: IP address or user identifier
            endpoint: OAuth endpoint name ('initiate', 'callback', etc.)
            max_requests: Maximum requests allowed in window
            window_seconds: Time window in seconds
            
        Returns:
            True if request is allowed, False if rate limited
        """
        key = f"oauth_rate_limit:{endpoint}:{identifier}"
        current_time = time.time()
        window_start = current_time - window_seconds
        
        if self.redis_available and self.redis_client:
            return self._check_redis_rate_limit(key, current_time, window_start, max_requests)
        else:
            return self._check_memory_rate_limit(key, current_time, window_start, max_requests)
    
    def _check_redis_rate_limit(self, key: str, current_time: float, 
                               window_start: float, max_requests: int) -> bool:
        """Check rate limit using Redis sliding window."""
        try:
            if not self.redis_client:
                return True
            pipe = self.redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current entries
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiry
            pipe.expire(key, int(window_start) + 1)
            
            results = pipe.execute()
            current_count = results[1]
            
            if current_count >= max_requests:
                logger.warning(f"Rate limit exceeded for key: {key}, count: {current_count}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Redis rate limiting failed: {e}")
            self.redis_available = False
            return True  # Fail open for availability
    
    def _check_memory_rate_limit(self, key: str, current_time: float,
                                window_start: float, max_requests: int) -> bool:
        """Check rate limit using in-memory sliding window."""
        with self.memory_lock:
            # Initialize if not exists
            if key not in self.memory_store:
                self.memory_store[key] = []
            
            # Remove old entries
            self.memory_store[key] = [
                t for t in self.memory_store[key] if t > window_start
            ]
            
            # Check limit
            if len(self.memory_store[key]) >= max_requests:
                logger.warning(f"Memory rate limit exceeded for key: {key}")
                return False
            
            # Add current request
            self.memory_store[key].append(current_time)
            return True
    
    def get_rate_limit_status(self, identifier: str, endpoint: str = "oauth") -> Dict[str, int]:
        """Get current rate limit status for identifier."""
        key = f"oauth_rate_limit:{endpoint}:{identifier}"
        current_time = time.time()
        window_start = current_time - 300  # 5 minute window
        
        if self.redis_available and self.redis_client:
            try:
                count = self.redis_client.zcount(key, window_start, current_time)
                # Handle async/sync Redis responses
                if hasattr(count, '__await__'):
                    # This is an async response, use 0 as fallback
                    count_int = 0
                else:
                    count_int = int(count) if count is not None else 0
                return {
                    "current_requests": count_int,
                    "limit": 5,
                    "window_seconds": 300,
                    "reset_time": int(current_time + 300)
                }
            except Exception:
                pass
        
        # Memory fallback
        with self.memory_lock:
            if key in self.memory_store:
                valid_requests = [t for t in self.memory_store[key] if t > window_start]
                return {
                    "current_requests": len(valid_requests),
                    "limit": 5,
                    "window_seconds": 300,
                    "reset_time": int(current_time + 300)
                }
        
        return {"current_requests": 0, "limit": 5, "window_seconds": 300, "reset_time": int(current_time + 300)}


class OAuthAuditLogger:
    """
    Audit logging for OAuth operations.
    """
    
    def __init__(self) -> None:
        # Create dedicated OAuth audit logger
        self.audit_logger = LoggerFactory.get_logger("oauth_audit", domain='auth')
    
    def log_oauth_event(self, 
                       event_type: str, 
                       status: str, 
                       details: Dict, 
                       client_ip: Optional[str] = None,
                       user_agent: Optional[str] = None):
        """
        Log OAuth security events.
        
        Args:
            event_type: Type of event ('auth_initiated', 'callback_received', etc.)
            status: Status ('success', 'failure', 'security_violation')
            details: Event details dictionary
            client_ip: Client IP address
            user_agent: Client user agent
        """
        audit_entry = {
            "event_type": event_type,
            "status": status,
            "timestamp": time.time(),
            "client_ip": client_ip,
            "user_agent": user_agent,
            **details
        }
        
        # Log at appropriate level
        if status == "security_violation":
            self.audit_logger.error(f"SECURITY_VIOLATION: {audit_entry}")
        elif status == "failure":
            self.audit_logger.warning(f"OAUTH_FAILURE: {audit_entry}")
        else:
            self.audit_logger.info(f"OAUTH_EVENT: {audit_entry}")


# Global instances for reuse
redirect_validator = RedirectURIValidator()
oauth_rate_limiter = OAuthRateLimiter()
oauth_audit_logger = OAuthAuditLogger()


def validate_oauth_request(redirect_uri: str, client_ip: str, endpoint: str = "oauth") -> bool:
    """
    Comprehensive OAuth request validation.
    
    Args:
        redirect_uri: Redirect URI to validate
        client_ip: Client IP address for rate limiting
        endpoint: OAuth endpoint name
        
    Returns:
        True if request is valid and allowed
        
    Raises:
        XeroAuthorizationError: If validation fails
    """
    # Rate limiting check
    if not oauth_rate_limiter.check_rate_limit(client_ip, endpoint):
        oauth_audit_logger.log_oauth_event(
            event_type="rate_limit_exceeded",
            status="security_violation",
            details={"endpoint": endpoint, "client_ip": client_ip}
        )
        raise XeroAuthorizationError("Rate limit exceeded. Please try again later.")
    
    # Redirect URI validation
    redirect_validator.validate_redirect_uri(redirect_uri)
    
    # Log successful validation
    oauth_audit_logger.log_oauth_event(
        event_type="oauth_validation",
        status="success",
        details={"endpoint": endpoint, "redirect_uri": redirect_uri},
        client_ip=client_ip
    )
    
    return True