# Standard library imports
import base64
import hashlib
import json
import os
import secrets
import time
from datetime import datetime, timedelta, timezone
from threading import Lock
from typing import Any, Dict, Optional, Tuple, Union
from redis import Redis
from urllib.parse import parse_qs, urlparse

# Third party imports
import requests
from requests_oauthlib import OAuth2Session

# Relative imports
from mcx3d_finance.core.config import get_xero_config, settings
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.db.models import Organization
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.exceptions.auth import (
    InvalidStateError,
    XeroAuthError,
    XeroAuthorizationError,
    XeroTokenRefreshError,
)
from mcx3d_finance.utils.distributed_lock import TokenRefreshLock, distributed_lock
from mcx3d_finance.utils.redis_client import get_redis_client
from mcx3d_finance.auth.oauth_security import validate_oauth_request, oauth_audit_logger

logger = LoggerFactory.get_logger(__name__, domain="auth")


class StateStorage:
    """
    Enhanced state storage with Redis primary and in-memory fallback.
    Provides graceful degradation if Redis is unavailable with security improvements.
    """
    
    # Class-level singleton instance
    _instance = None
    _lock = Lock()
    _initialized: bool = False
    
    def __new__(cls) -> 'StateStorage':
        """Singleton pattern for state storage."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(StateStorage, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self) -> None:
        if self._initialized:
            return
            
        self._memory_store: Dict[str, Tuple[str, float]] = {}
        self._memory_lock = Lock()
        self._redis_available = True
        self._namespace = "mcx3d:oauth"  # Security: Namespaced keys
        self._last_cleanup = 0.0
        self._cleanup_interval = 300  # 5 minutes
        self._redis_client: Optional[Redis] = None
        
        try:
            self._redis_client = get_redis_client()
            # Test Redis connection
            self._redis_client.ping()
            logger.info("Redis connection successful for OAuth state storage")
        except Exception as e:
            logger.warning(f"Redis unavailable for OAuth state storage: {e}. Using in-memory fallback.")
            self._redis_available = False
            self._redis_client = None
            
        self._initialized = True
    
    def set(self, key: str, value: str, ttl_seconds: int) -> bool:
        """Store a value with TTL. Returns True if successful."""
        # Security: Use namespaced keys
        namespaced_key = f"{self._namespace}:{key}"
        
        # Try Redis first
        if self._redis_available and self._redis_client:
            try:
                self._redis_client.setex(namespaced_key, ttl_seconds, value)
                return True
            except Exception as e:
                logger.warning(f"Redis storage failed: {e}. Falling back to in-memory storage.")
                self._redis_available = False
        
        # Fallback to in-memory storage
        with self._memory_lock:
            expiry_time = time.time() + ttl_seconds
            self._memory_store[namespaced_key] = (value, expiry_time)
            # Clean up expired entries periodically
            self._periodic_cleanup()
            logger.debug(f"Stored {key} in memory with {ttl_seconds}s TTL")
        
        return True
    
    def get(self, key: str) -> Optional[str]:
        """Retrieve a value. Returns None if not found or expired."""
        # Security: Use namespaced keys
        namespaced_key = f"{self._namespace}:{key}"
        
        # Try Redis first
        if self._redis_available and self._redis_client:
            try:
                value = self._redis_client.get(namespaced_key)
                if value:
                    return value.decode('utf-8') if isinstance(value, bytes) else str(value)
            except Exception as e:
                logger.warning(f"Redis retrieval failed: {e}. Falling back to in-memory storage.")
                self._redis_available = False
        
        # Fallback to in-memory storage
        with self._memory_lock:
            if namespaced_key in self._memory_store:
                value, expiry_time = self._memory_store[namespaced_key]
                if time.time() < expiry_time:
                    return value
                else:
                    # Expired, remove it
                    del self._memory_store[namespaced_key]
        
        return None
    
    def delete(self, key: str) -> bool:
        """Delete a value. Returns True if the key existed."""
        # Security: Use namespaced keys
        namespaced_key = f"{self._namespace}:{key}"
        deleted = False
        
        # Try Redis first
        if self._redis_available and self._redis_client:
            try:
                deleted = bool(self._redis_client.delete(namespaced_key))
            except Exception as e:
                logger.warning(f"Redis deletion failed: {e}")
                self._redis_available = False
        
        # Also delete from in-memory storage
        with self._memory_lock:
            if namespaced_key in self._memory_store:
                del self._memory_store[namespaced_key]
                deleted = True
        
        return deleted
    
    def _periodic_cleanup(self) -> None:
        """Periodically remove expired entries from in-memory storage."""
        current_time = time.time()
        
        # Only cleanup every 5 minutes to avoid overhead
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
            
        expired_keys = [
            k for k, (_, expiry) in self._memory_store.items()
            if current_time >= expiry
        ]
        
        for key in expired_keys:
            del self._memory_store[key]
            
        self._last_cleanup = current_time
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired state entries")
    
    def _cleanup_expired(self) -> None:
        """Remove expired entries from in-memory storage (legacy method)."""
        self._periodic_cleanup()


class XeroAuthManager:
    """Complete Xero OAuth 2.0 authentication manager with proper OAuth2Session implementation."""

    def __init__(self) -> None:
        self.config = get_xero_config()
        self.state_storage = StateStorage()
        
        # Xero OAuth2 endpoints
        self.authorization_base_url = "https://login.xero.com/identity/connect/authorize"
        self.token_url = "https://identity.xero.com/connect/token"

    def _generate_code_verifier(self) -> str:
        """
        Generate a cryptographically random code_verifier for PKCE.
        
        Returns:
            Base64url-encoded random string (43-128 characters)
        """
        # Generate 32 random bytes and encode as base64url (43 characters)
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

    def _generate_code_challenge(self, code_verifier: str) -> str:
        """
        Generate code_challenge from code_verifier using SHA256.
        
        Args:
            code_verifier: The code verifier string
            
        Returns:
            Base64url-encoded SHA256 hash of the code verifier
        """
        # Create SHA256 hash of code_verifier
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        # Encode as base64url without padding
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')

    def generate_auth_url(self, state: Optional[str] = None, client_ip: Optional[str] = None) -> Tuple[Optional[str], Optional[str]]:
        """
        Generate Xero authorization URL with PKCE support and enhanced security.

        Args:
            state: Optional state parameter for CSRF protection
            client_ip: Client IP address for rate limiting and audit logging

        Returns:
            Tuple of (Authorization URL, state) or (None, None) if error
        """
        try:
            # Security: Validate OAuth request with rate limiting and redirect URI check
            if client_ip:
                validate_oauth_request(self.config["redirect_uri"], client_ip, "auth_initiate")
            
            # Generate state for CSRF protection if not provided
            if not state:
                state = secrets.token_urlsafe(32)

            # Generate PKCE parameters
            code_verifier = self._generate_code_verifier()
            code_challenge = self._generate_code_challenge(code_verifier)

            # Store state and code_verifier with 10-minute expiration
            self.state_storage.set(f"xero_oauth_state:{state}", "valid", 600)
            self.state_storage.set(f"xero_oauth_verifier:{state}", code_verifier, 600)

            # Create OAuth2Session with conditional insecure transport for development only

            # Only allow insecure transport in development mode with explicit warning
            if settings.debug and self.config["redirect_uri"].startswith("http://localhost"):
                logger.warning(
                    "OAUTHLIB_INSECURE_TRANSPORT enabled for localhost development only. "
                    "This MUST NOT be used in production!"
                )
                os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
            else:
                # Ensure HTTPS is enforced in production
                os.environ.pop('OAUTHLIB_INSECURE_TRANSPORT', None)
            
            oauth = OAuth2Session(
                client_id=self.config["client_id"],
                redirect_uri=self.config["redirect_uri"],
                scope=self.config["scopes"].split(),
                state=state
            )

            # Generate authorization URL with PKCE parameters
            authorization_url, oauth_state = oauth.authorization_url(
                self.authorization_base_url,
                state=state,
                code_challenge=code_challenge,
                code_challenge_method="S256"
            )

            # Audit logging for successful auth URL generation
            oauth_audit_logger.log_oauth_event(
                event_type="auth_url_generated",
                status="success",
                details={
                    "state": state[:8] + "...",  # Only log prefix for security
                    "redirect_uri": self.config["redirect_uri"],
                    "scopes": self.config["scopes"]
                },
                client_ip=client_ip
            )
            
            logger.info(f"Generated Xero auth URL with PKCE - state: {state}")
            return authorization_url, state

        except (ValueError, KeyError) as e:
            # Audit logging for configuration errors
            oauth_audit_logger.log_oauth_event(
                event_type="auth_url_generation",
                status="failure",
                details={"error": str(e), "error_type": "configuration"},
                client_ip=client_ip
            )
            logger.error(f"Configuration error generating Xero auth URL: {e}")
            raise XeroAuthorizationError(f"Invalid Xero configuration: {e}")
        except XeroAuthorizationError:
            # Re-raise security violations (already logged by validate_oauth_request)
            raise
        except Exception as e:
            # Audit logging for unexpected errors
            oauth_audit_logger.log_oauth_event(
                event_type="auth_url_generation",
                status="failure",
                details={"error": str(e), "error_type": "unexpected"},
                client_ip=client_ip
            )
            logger.error(f"Unexpected error generating Xero auth URL: {e}")
            raise XeroAuthorizationError(f"Failed to generate authorization URL: {e}")

    def handle_callback(self, authorization_response_url: str, client_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Handle OAuth callback and exchange authorization code for tokens with enhanced security.

        Args:
            authorization_response_url: Full callback URL with authorization code
            client_ip: Client IP address for rate limiting and audit logging

        Returns:
            Dictionary with success status and token information
        """
        try:
            logger.info("Processing Xero OAuth callback")

            # Security: Validate callback request with rate limiting
            if client_ip:
                validate_oauth_request(self.config["redirect_uri"], client_ip, "callback")

            # Parse the callback URL
            parsed_url = urlparse(authorization_response_url)
            query_params = parse_qs(parsed_url.query)

            # Extract parameters
            code = query_params.get("code", [None])[0]
            state = query_params.get("state", [None])[0]
            error = query_params.get("error", [None])[0]

            if error:
                # Audit logging for OAuth errors from Xero
                oauth_audit_logger.log_oauth_event(
                    event_type="oauth_callback",
                    status="failure",
                    details={
                        "error": error,
                        "error_description": query_params.get("error_description", [""])[0],
                        "source": "xero"
                    },
                    client_ip=client_ip
                )
                logger.error(f"OAuth error from Xero: {error}")
                return {
                    "success": False,
                    "error": f"Authorization failed: {error}",
                    "error_description": query_params.get("error_description", [""])[0]
                }

            if not code or not state:
                logger.error("Missing authorization code or state in callback")
                return {
                    "success": False,
                    "error": "Missing authorization code or state parameter"
                }

            # Validate state
            stored_state = self.state_storage.get(f"xero_oauth_state:{state}")
            if not stored_state:
                logger.error("Invalid or expired state parameter")
                raise InvalidStateError("Invalid or expired authorization state")

            # Get code_verifier for PKCE
            code_verifier = self.state_storage.get(f"xero_oauth_verifier:{state}")
            if not code_verifier:
                logger.error("Missing code_verifier for PKCE flow")
                raise InvalidStateError("Invalid or expired PKCE verification data")

            # Clean up state and verifier
            self.state_storage.delete(f"xero_oauth_state:{state}")
            self.state_storage.delete(f"xero_oauth_verifier:{state}")

            # Create OAuth2Session for token exchange with conditional insecure transport

            # Only allow insecure transport in development mode with explicit warning
            if settings.debug and self.config["redirect_uri"].startswith("http://localhost"):
                logger.warning(
                    "OAUTHLIB_INSECURE_TRANSPORT enabled for localhost development only. "
                    "This MUST NOT be used in production!"
                )
                os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
            else:
                # Ensure HTTPS is enforced in production
                os.environ.pop('OAUTHLIB_INSECURE_TRANSPORT', None)
            
            oauth = OAuth2Session(
                client_id=self.config["client_id"],
                redirect_uri=self.config["redirect_uri"],
                state=state
            )

            # Exchange authorization code for tokens with PKCE
            token = oauth.fetch_token(
                self.token_url,
                authorization_response=authorization_response_url,
                client_secret=self.config["client_secret"],
                code_verifier=code_verifier,
                include_client_id=True
            )

            logger.info("Successfully exchanged authorization code for tokens")

            # Audit logging for successful token exchange
            oauth_audit_logger.log_oauth_event(
                event_type="token_exchange",
                status="success",
                details={
                    "expires_in": token.get("expires_in", 1800),
                    "token_type": token.get("token_type", "Bearer")
                },
                client_ip=client_ip
            )

            # Get tenant information
            tenant_info = self._get_tenant_info(token["access_token"])

            if not tenant_info:
                raise XeroAuthError("Failed to retrieve tenant information")

            # Store token information
            result = self._store_token_info(token, tenant_info)

            # Audit logging for successful organization setup
            oauth_audit_logger.log_oauth_event(
                event_type="organization_setup",
                status="success",
                details={
                    "organization_id": result.get("organization_id"),
                    "tenant_name": result.get("tenant_name"),
                    "action": result.get("action")
                },
                client_ip=client_ip
            )
            
            return {
                "success": True,
                "message": "Authorization successful",
                "organization_id": result.get("organization_id"),
                "tenant_name": result.get("tenant_name"),
                "token_expires_in": token.get("expires_in", 1800)
            }

        except InvalidStateError as e:
            logger.error(f"Invalid state error in OAuth callback: {e}")
            return {
                "success": False,
                "error": str(e)
            }
        except XeroAuthError as e:
            logger.error(f"Xero auth error in OAuth callback: {e}")
            return {
                "success": False,
                "error": str(e)
            }
        except Exception as e:
            logger.error(f"Unexpected error processing OAuth callback: {e}")
            return {
                "success": False,
                "error": f"Callback processing failed: {str(e)}"
            }

    def _get_tenant_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """
        Get tenant information from Xero using access token.

        Args:
            access_token: Valid Xero access token

        Returns:
            Tenant information or None if failed
        """
        try:
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            response = requests.get(
                "https://api.xero.com/connections",
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                connections = response.json()
                if connections and isinstance(connections, list):
                    # Return the first tenant (most common case)
                    tenant = connections[0]
                    if isinstance(tenant, dict):
                        logger.info(f"Retrieved tenant info: {tenant.get('tenantName', 'Unknown')}")
                        return tenant
                else:
                    logger.error("No tenant connections found")
                    return None
            else:
                logger.error(f"Failed to get tenant info: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            logger.error(f"Network error getting tenant info: {e}")
            return None
        except (KeyError, ValueError) as e:
            logger.error(f"Data parsing error getting tenant info: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting tenant info: {e}")
            return None

    def _store_token_info(self, token: Dict[str, Any], tenant_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Store token and tenant information in database.

        Args:
            token: OAuth token information
            tenant_info: Xero tenant information

        Returns:
            Dictionary with storage result
        """
        try:
            db = SessionLocal()
            tenant_id = tenant_info.get("tenantId")
            
            # Use distributed lock for tenant ID to prevent race conditions
            redis_client = get_redis_client()
            
            with distributed_lock(redis_client, f"xero_tenant_setup:{tenant_id}", timeout=30):
                # Check if organization already exists
                existing_org = db.query(Organization).filter(
                    Organization.xero_tenant_id == tenant_id
                ).first()

                if existing_org:
                    # Update existing organization
                    existing_org.name = tenant_info.get("tenantName", "Unknown Organization")
                    existing_org.xero_token = json.dumps(token)
                    existing_org.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=token.get("expires_in", 1800))
                    existing_org.updated_at = datetime.now(timezone.utc)

                    db.commit()
                    db.refresh(existing_org)

                    logger.info(f"Updated existing organization: {existing_org.name}")
                    return {
                        "organization_id": existing_org.id,
                        "tenant_name": existing_org.name,
                        "action": "updated"
                    }

                else:
                    # Create new organization
                    new_org = Organization(
                        name=tenant_info.get("tenantName", "Unknown Organization"),
                        xero_tenant_id=tenant_id,
                        xero_tenant_type=tenant_info.get("tenantType", "ORGANISATION"),
                        xero_token=json.dumps(token),
                        token_expires_at=datetime.now(timezone.utc) + timedelta(seconds=token.get("expires_in", 1800)),
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                        is_active=True
                    )

                    db.add(new_org)
                    db.commit()
                    db.refresh(new_org)

                    logger.info(f"Created new organization: {new_org.name}")
                    return {
                        "organization_id": new_org.id,
                        "tenant_name": new_org.name,
                        "action": "created"
                    }

        except Exception as e:
            logger.error(f"Error storing token info: {e}")
            if 'db' in locals():
                db.rollback()
            raise XeroAuthError(f"Failed to store token information: {e}")
        finally:
            if 'db' in locals():
                db.close()

    def get_valid_token(self, organization_id: int) -> Optional[str]:
        """
        Get valid access token for organization, refreshing if necessary.

        Args:
            organization_id: Organization ID

        Returns:
            Valid access token or None if failed
        """
        try:
            db = SessionLocal()
            organization = db.query(Organization).get(organization_id)

            if not organization or not organization.xero_token:
                logger.error(f"No token found for organization {organization_id}")
                return None

            token_data = json.loads(organization.xero_token)

            # Check if token is still valid
            if organization.token_expires_at:
                # Ensure both datetimes are timezone-aware
                expires_at = organization.token_expires_at
                if expires_at.tzinfo is None:
                    logger.warning(f"Found naive datetime in token expiry for org {organization_id}, converting to UTC: {expires_at}")
                    expires_at = expires_at.replace(tzinfo=timezone.utc)
                
                # Add 5-minute buffer to prevent edge cases
                if expires_at > datetime.now(timezone.utc) + timedelta(minutes=5):
                    access_token = token_data.get("access_token")
                    return access_token if isinstance(access_token, str) else None

            # Token expired or about to expire, acquire lock and refresh
            logger.info(f"Token expired or expiring soon for org {organization_id}, attempting refresh")
            
            # Use distributed lock to prevent race conditions
            redis_client = get_redis_client()
            with TokenRefreshLock(redis_client, organization_id):
                # Re-check token validity after acquiring lock
                # Another process might have already refreshed it
                db.refresh(organization)
                if organization.xero_token:
                    fresh_token_data = json.loads(organization.xero_token)
                    if organization.token_expires_at:
                        expires_at = organization.token_expires_at
                        if expires_at.tzinfo is None:
                            logger.warning(f"Found naive datetime in token expiry during refresh check for org {organization_id}, converting to UTC: {expires_at}")
                            expires_at = expires_at.replace(tzinfo=timezone.utc)
                        
                        if expires_at > datetime.now(timezone.utc) + timedelta(minutes=5):
                            logger.info(f"Token already refreshed by another process for org {organization_id}")
                            access_token = fresh_token_data.get("access_token")
                            return access_token if isinstance(access_token, str) else None
                
                # Perform the actual refresh
                refreshed_token = self._refresh_token(token_data)

                if refreshed_token:
                    # Update organization with new token
                    organization.xero_token = json.dumps(refreshed_token)
                    organization.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=refreshed_token.get("expires_in", 1800))
                    organization.updated_at = datetime.now(timezone.utc)

                    db.commit()
                    logger.info(f"Token refreshed successfully for org {organization_id}")
                    access_token = refreshed_token.get("access_token")
                    return access_token if isinstance(access_token, str) else None
                else:
                    logger.error(f"Token refresh failed for org {organization_id}")
                    return None

        except XeroTokenRefreshError as e:
            logger.error(f"Token refresh failed for org {organization_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting valid token for org {organization_id}: {e}")
            return None
        finally:
            if 'db' in locals():
                db.close()

    def _refresh_token(self, token_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Refresh access token using refresh token.

        Args:
            token_data: Current token data including refresh token

        Returns:
            New token data or None if failed
        """
        try:
            refresh_token = token_data.get("refresh_token")
            if not refresh_token:
                logger.error("No refresh token available")
                return None

            # Create OAuth2Session for token refresh
            oauth = OAuth2Session(
                client_id=self.config["client_id"],
                token=token_data
            )

            # Refresh the token
            new_token = oauth.refresh_token(
                self.token_url,
                refresh_token=refresh_token,
                client_id=self.config["client_id"],
                client_secret=self.config["client_secret"]
            )

            logger.info("Token refreshed successfully")
            return new_token if isinstance(new_token, dict) else None

        except requests.RequestException as e:
            logger.error(f"Network error refreshing token: {e}")
            raise XeroTokenRefreshError(f"Network error during token refresh: {e}")
        except (KeyError, ValueError) as e:
            logger.error(f"Token data error during refresh: {e}")
            raise XeroTokenRefreshError(f"Invalid token data: {e}")
        except Exception as e:
            logger.error(f"Unexpected error refreshing token: {e}")
            raise XeroTokenRefreshError(f"Token refresh failed: {e}")

    def revoke_token(self, organization_id: int) -> bool:
        """
        Revoke tokens for organization.

        Args:
            organization_id: Organization ID

        Returns:
            True if successful, False otherwise
        """
        try:
            db = SessionLocal()
            organization = db.query(Organization).get(organization_id)

            if not organization:
                logger.error(f"Organization {organization_id} not found")
                return False

            # Clear token data
            organization.xero_token = None
            organization.token_expires_at = None
            organization.updated_at = datetime.now(timezone.utc)

            db.commit()
            logger.info(f"Tokens revoked for organization {organization_id}")
            return True

        except Exception as e:
            logger.error(f"Unexpected error revoking tokens: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()