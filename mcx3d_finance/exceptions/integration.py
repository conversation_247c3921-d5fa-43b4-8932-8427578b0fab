"""
Integration exceptions for the MCX3D financial system.

These exceptions handle errors in external service integrations,
API communications, and data synchronization operations.
"""

from typing import Optional
from mcx3d_finance.exceptions.base import MCX3DException


class IntegrationError(MCX3DException):
    """Base exception for all integration-related errors."""

    def _get_default_user_message(self) -> str:
        return "An error occurred while communicating with external services. Please try again later."


class XeroIntegrationError(IntegrationError):
    """
    Exception raised for Xero API integration errors.

    Used for Xero-specific API errors, authentication issues,
    and data synchronization problems.
    """

    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        xero_error_code: Optional[str] = None,
        xero_error_message: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize Xero integration error.

        Args:
            message: Technical error message
            operation: Xero operation that failed (optional)
            xero_error_code: Xero API error code (optional)
            xero_error_message: Xero API error message (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.operation = operation
        self.xero_error_code = xero_error_code
        self.xero_error_message = xero_error_message

        if operation:
            self.add_context("xero_operation", operation)
        if xero_error_code:
            self.add_context("xero_error_code", xero_error_code)
        if xero_error_message:
            self.add_context("xero_error_message", xero_error_message)

    def _get_default_user_message(self) -> str:
        if self.operation:
            return f"Xero integration error during {self.operation}. Please check your Xero connection and try again."
        return "Xero integration error. Please verify your Xero connection and permissions."


class APIConnectionError(IntegrationError):
    """
    Exception raised for API connection failures.

    Used for network connectivity issues, timeouts, and
    general API communication failures.
    """

    def __init__(
        self,
        message: str,
        service_name: str,
        endpoint: Optional[str] = None,
        status_code: Optional[int] = None,
        response_body: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize API connection error.

        Args:
            message: Technical error message
            service_name: Name of the external service
            endpoint: API endpoint that failed (optional)
            status_code: HTTP status code (optional)
            response_body: Response body content (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.endpoint = endpoint
        self.status_code = status_code
        self.response_body = response_body

        self.add_context("service_name", service_name)
        if endpoint:
            self.add_context("endpoint", endpoint)
        if status_code:
            self.add_context("status_code", status_code)
        if response_body:
            # Truncate response body to avoid logging large payloads
            truncated_body = (
                response_body[:500] + "..."
                if len(response_body) > 500
                else response_body
            )
            self.add_context("response_body", truncated_body)

    def _get_default_user_message(self) -> str:
        if self.status_code:
            return f"Connection error with {self.service_name} (HTTP {self.status_code}). Please try again later."
        return f"Unable to connect to {self.service_name}. Please check your internet connection and try again."


class DataSyncError(IntegrationError):
    """
    Exception raised during data synchronization operations.

    Used for errors during data import, export, or synchronization
    between MCX3D and external systems.
    """

    def __init__(
        self,
        message: str,
        sync_operation: str,
        data_type: Optional[str] = None,
        records_processed: Optional[int] = None,
        records_failed: Optional[int] = None,
        **kwargs,
    ):
        """
        Initialize data sync error.

        Args:
            message: Technical error message
            sync_operation: Type of sync operation (import, export, update)
            data_type: Type of data being synchronized (optional)
            records_processed: Number of records processed before failure (optional)
            records_failed: Number of records that failed (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.sync_operation = sync_operation
        self.data_type = data_type
        self.records_processed = records_processed
        self.records_failed = records_failed

        self.add_context("sync_operation", sync_operation)
        if data_type:
            self.add_context("data_type", data_type)
        if records_processed is not None:
            self.add_context("records_processed", records_processed)
        if records_failed is not None:
            self.add_context("records_failed", records_failed)

    def _get_default_user_message(self) -> str:
        data_desc = f" {self.data_type}" if self.data_type else ""
        if self.records_processed is not None:
            return f"Data synchronization failed during{data_desc} {self.sync_operation} after processing {self.records_processed} record(s)."
        return f"Data synchronization error during{data_desc} {self.sync_operation}. Please try again."


class AuthenticationError(IntegrationError):
    """
    Exception raised for authentication and authorization errors.

    Used for OAuth failures, token expiration, insufficient permissions,
    and other authentication-related issues.
    """

    def __init__(
        self,
        message: str,
        auth_type: str,
        service_name: Optional[str] = None,
        auth_stage: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize authentication error.

        Args:
            message: Technical error message
            auth_type: Type of authentication (OAuth, API key, etc.)
            service_name: Name of the service requiring authentication (optional)
            auth_stage: Stage of authentication where error occurred (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.auth_type = auth_type
        self.service_name = service_name
        self.auth_stage = auth_stage

        self.add_context("auth_type", auth_type)
        if service_name:
            self.add_context("service_name", service_name)
        if auth_stage:
            self.add_context("auth_stage", auth_stage)

    def _get_default_user_message(self) -> str:
        service_desc = f" with {self.service_name}" if self.service_name else ""
        return f"Authentication failed{service_desc}. Please re-authenticate and try again."


class RateLimitError(IntegrationError):
    """
    Exception raised when API rate limits are exceeded.

    Used for rate limiting errors from external APIs.
    """

    def __init__(
        self,
        message: str,
        service_name: str,
        limit_type: Optional[str] = None,
        retry_after: Optional[int] = None,
        current_usage: Optional[int] = None,
        limit_value: Optional[int] = None,
        **kwargs,
    ):
        """
        Initialize rate limit error.

        Args:
            message: Technical error message
            service_name: Name of the service that rate limited us
            limit_type: Type of rate limit (requests, data, etc.)
            retry_after: Seconds to wait before retrying (optional)
            current_usage: Current usage count (optional)
            limit_value: Rate limit threshold (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.limit_type = limit_type
        self.retry_after = retry_after
        self.current_usage = current_usage
        self.limit_value = limit_value

        self.add_context("service_name", service_name)
        if limit_type:
            self.add_context("limit_type", limit_type)
        if retry_after:
            self.add_context("retry_after_seconds", retry_after)
        if current_usage is not None:
            self.add_context("current_usage", current_usage)
        if limit_value is not None:
            self.add_context("limit_value", limit_value)

    def _get_default_user_message(self) -> str:
        if self.retry_after:
            return f"Rate limit exceeded for {self.service_name}. Please wait {self.retry_after} seconds before trying again."
        return f"Rate limit exceeded for {self.service_name}. Please wait before making more requests."


class WebhookError(IntegrationError):
    """
    Exception raised during webhook processing.

    Used for webhook validation, processing, and response errors.
    """

    def __init__(
        self,
        message: str,
        webhook_source: str,
        event_type: Optional[str] = None,
        payload_size: Optional[int] = None,
        **kwargs,
    ):
        """
        Initialize webhook error.

        Args:
            message: Technical error message
            webhook_source: Source of the webhook (Xero, etc.)
            event_type: Type of webhook event (optional)
            payload_size: Size of the webhook payload in bytes (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.webhook_source = webhook_source
        self.event_type = event_type
        self.payload_size = payload_size

        self.add_context("webhook_source", webhook_source)
        if event_type:
            self.add_context("event_type", event_type)
        if payload_size is not None:
            self.add_context("payload_size", payload_size)

    def _get_default_user_message(self) -> str:
        return f"Webhook processing error from {self.webhook_source}. The system will retry automatically."


class DataMappingError(IntegrationError):
    """
    Exception raised during data mapping and transformation.

    Used for errors when converting data between different formats
    or systems during integration.
    """

    def __init__(
        self,
        message: str,
        source_system: str,
        target_system: str,
        mapping_type: Optional[str] = None,
        unmapped_fields: Optional[list] = None,
        **kwargs,
    ):
        """
        Initialize data mapping error.

        Args:
            message: Technical error message
            source_system: System providing the data
            target_system: System receiving the data
            mapping_type: Type of mapping (account, transaction, etc.)
            unmapped_fields: List of fields that couldn't be mapped (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.source_system = source_system
        self.target_system = target_system
        self.mapping_type = mapping_type
        self.unmapped_fields = unmapped_fields

        self.add_context("source_system", source_system)
        self.add_context("target_system", target_system)
        if mapping_type:
            self.add_context("mapping_type", mapping_type)
        if unmapped_fields:
            self.add_context("unmapped_fields", unmapped_fields)

    def _get_default_user_message(self) -> str:
        mapping_desc = f" ({self.mapping_type})" if self.mapping_type else ""
        return f"Data mapping error{mapping_desc} between {self.source_system} and {self.target_system}. Please check field mappings."


class ServiceUnavailableError(IntegrationError):
    """
    Exception raised when external services are unavailable.

    Used for planned maintenance, service outages, or degraded performance
    of external services.
    """

    def __init__(
        self,
        message: str,
        service_name: str,
        outage_type: Optional[str] = None,
        estimated_recovery: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize service unavailable error.

        Args:
            message: Technical error message
            service_name: Name of the unavailable service
            outage_type: Type of outage (maintenance, incident, etc.)
            estimated_recovery: Estimated recovery time (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.outage_type = outage_type
        self.estimated_recovery = estimated_recovery

        self.add_context("service_name", service_name)
        if outage_type:
            self.add_context("outage_type", outage_type)
        if estimated_recovery:
            self.add_context("estimated_recovery", estimated_recovery)

    def _get_default_user_message(self) -> str:
        recovery_info = (
            f" (estimated recovery: {self.estimated_recovery})"
            if self.estimated_recovery
            else ""
        )
        return f"{self.service_name} is currently unavailable{recovery_info}. Please try again later."


class DataPersistenceError(IntegrationError):
    """
    Exception raised during data persistence operations.

    Used for database errors, constraint violations, and other
    data storage issues during integration.
    """

    def __init__(
        self,
        message: str,
        operation: str,
        data_type: Optional[str] = None,
        record_id: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize data persistence error.

        Args:
            message: Technical error message
            operation: Type of persistence operation (save, update, delete)
            data_type: Type of data being persisted (optional)
            record_id: ID of the record that failed (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.operation = operation
        self.data_type = data_type
        self.record_id = record_id

        self.add_context("persistence_operation", operation)
        if data_type:
            self.add_context("data_type", data_type)
        if record_id:
            self.add_context("record_id", record_id)

    def _get_default_user_message(self) -> str:
        data_desc = f" {self.data_type}" if self.data_type else ""
        return f"Database error during{data_desc} {self.operation} operation. Please try again."
