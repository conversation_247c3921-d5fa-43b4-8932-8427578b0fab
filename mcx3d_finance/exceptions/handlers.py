"""
Standardized error handling utilities for MCX3D Finance.

This module provides consistent error handling patterns, decorators, and utilities
to ensure uniform error management across the application.
"""

import functools
from mcx3d_finance.core.logging_factory import LoggerFactory
import traceback
from contextlib import contextmanager
from datetime import datetime, timezone
from typing import Any, Callable, Dict, List, Optional, Type, Union
from uuid import uuid4

from mcx3d_finance.exceptions.base import MCX3<PERSON>xception
from mcx3d_finance.exceptions.financial import (
    FinancialCalculationError,
    ValuationError,
)
from mcx3d_finance.exceptions.integration import (
    DataPersistenceError,
    IntegrationError,
    ServiceUnavailableError,
)
from mcx3d_finance.exceptions.validation import ValidationError

logger = LoggerFactory.get_logger(__name__)


class ErrorContext:
    """Thread-safe context manager for maintaining error context."""

    def __init__(self):
        self._context: Dict[str, Any] = {}
        self.correlation_id: Optional[str] = None
        self.operation: Optional[str] = None
        self.user_id: Optional[str] = None
        self.organization_id: Optional[str] = None

    def set_correlation_id(self, correlation_id: str) -> None:
        """Set the correlation ID for error tracking."""
        self.correlation_id = correlation_id
        self._context['correlation_id'] = correlation_id

    def add_context(self, key: str, value: Any) -> None:
        """Add context information."""
        self._context[key] = value

    def get_context(self) -> Dict[str, Any]:
        """Get the current context."""
        return self._context.copy()

    def clear(self) -> None:
        """Clear the context."""
        self._context.clear()
        self.correlation_id = None
        self.operation = None
        self.user_id = None
        self.organization_id = None


# Global error context
error_context = ErrorContext()


@contextmanager
def error_boundary(
    operation: str,
    reraise: bool = True,
    default_return: Any = None,
    correlation_id: Optional[str] = None,
    **context_kwargs
):
    """
    Context manager for consistent error handling.

    Args:
        operation: Name of the operation being performed
        reraise: Whether to re-raise the exception after handling
        default_return: Default value to return if exception is caught and not re-raised
        correlation_id: Optional correlation ID for tracking
        **context_kwargs: Additional context to add to errors

    Example:
        with error_boundary("calculate_valuation", organization_id=org_id):
            return calculate_dcf_valuation(data)
    """
    if not correlation_id:
        correlation_id = str(uuid4())

    error_context.set_correlation_id(correlation_id)
    error_context.operation = operation

    # Add any additional context
    for key, value in context_kwargs.items():
        error_context.add_context(key, value)

    try:
        yield error_context
    except MCX3DException as e:
        # MCX3D exceptions already have context, just add operation context
        e.add_context("operation", operation)
        e.add_context("correlation_id", correlation_id)
        for key, value in context_kwargs.items():
            e.add_context(key, value)

        logger.error(
            f"Error in {operation}: {e.message}",
            extra={
                "error_details": e.to_dict(),
                "correlation_id": correlation_id,
            }
        )

        if reraise:
            raise
        return default_return
    except Exception as e:
        # Convert non-MCX3D exceptions to MCX3DException
        mcx3d_error = MCX3DException(
            message=f"Unexpected error in {operation}: {str(e)}",
            user_message=f"An error occurred during {operation}. Please try again or contact support.",
            error_code=f"UNHANDLED_{operation.upper()}_ERROR",
            context={
                "operation": operation,
                "correlation_id": correlation_id,
                "original_error_type": type(e).__name__,
                **context_kwargs
            },
            original_exception=e,
            severity="ERROR"
        )

        logger.error(
            f"Unhandled error in {operation}: {str(e)}",
            extra={
                "error_details": mcx3d_error.to_dict(),
                "correlation_id": correlation_id,
                "traceback": traceback.format_exc()
            }
        )

        if reraise:
            raise mcx3d_error from e
        return default_return


def handle_errors(
    operation: Optional[str] = None,
    exceptions: Optional[List[Type[Exception]]] = None,
    default_return: Any = None,
    reraise: bool = True,
    log_level: str = "ERROR",
    include_traceback: bool = True,
):
    """
    Decorator for consistent error handling.

    Args:
        operation: Operation name (defaults to function name)
        exceptions: List of exceptions to specifically handle
        default_return: Default return value if exception is caught
        reraise: Whether to re-raise exceptions
        log_level: Logging level for errors
        include_traceback: Whether to include traceback in logs

    Example:
        @handle_errors(operation="calculate_mrr", exceptions=[ValueError])
        def calculate_monthly_recurring_revenue(data):
            return sum(data['subscription_values'])
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation or func.__name__
            correlation_id = str(uuid4())

            # Extract context from function arguments if available
            context = {}
            if args and hasattr(args[0], '__dict__'):
                # If first argument is self/cls, try to extract useful context
                obj = args[0]
                if hasattr(obj, 'organization_id'):
                    context['organization_id'] = obj.organization_id
                if hasattr(obj, 'user_id'):
                    context['user_id'] = obj.user_id

            with error_boundary(
                op_name,
                reraise=reraise,
                default_return=default_return,
                correlation_id=correlation_id,
                **context
            ):
                return func(*args, **kwargs)

        return wrapper
    return decorator


def convert_exception(
    exception: Exception,
    target_type: Type[MCX3DException] = MCX3DException,
    operation: Optional[str] = None,
    **context_kwargs
) -> MCX3DException:
    """
    Convert any exception to an MCX3D exception with proper context.

    Args:
        exception: The original exception
        target_type: Target MCX3D exception type
        operation: Operation that caused the error
        **context_kwargs: Additional context

    Returns:
        MCX3D exception with full context
    """
    if isinstance(exception, MCX3DException):
        # Already an MCX3D exception, just add context
        for key, value in context_kwargs.items():
            exception.add_context(key, value)
        if operation:
            exception.add_context("operation", operation)
        return exception

    # Create new MCX3D exception
    return target_type(
        message=str(exception),
        original_exception=exception,
        context={
            "original_type": type(exception).__name__,
            "operation": operation,
            **context_kwargs
        }
    )


class ErrorRecoveryStrategy:
    """Base class for error recovery strategies."""

    def can_recover(self, exception: Exception) -> bool:
        """Check if this strategy can handle the exception."""
        raise NotImplementedError

    def recover(self, exception: Exception, context: Dict[str, Any]) -> Any:
        """Attempt to recover from the exception."""
        raise NotImplementedError


class RetryStrategy(ErrorRecoveryStrategy):
    """Retry strategy for transient errors."""

    def __init__(
        self,
        max_retries: int = 3,
        backoff_factor: float = 2.0,
        retryable_exceptions: Optional[List[Type[Exception]]] = None
    ):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.retryable_exceptions = retryable_exceptions or [
            ServiceUnavailableError,
            ConnectionError,
            TimeoutError,
        ]

    def can_recover(self, exception: Exception) -> bool:
        return any(
            isinstance(exception, exc_type)
            for exc_type in self.retryable_exceptions
        )

    def recover(self, exception: Exception, context: Dict[str, Any]) -> Any:
        # Recovery logic is implemented in retry decorators
        raise NotImplementedError("Use retry decorators for retry logic")


class FallbackStrategy(ErrorRecoveryStrategy):
    """Fallback to alternative implementation."""

    def __init__(self, fallback_func: Callable):
        self.fallback_func = fallback_func

    def can_recover(self, exception: Exception) -> bool:
        return True  # Can always fallback

    def recover(self, exception: Exception, context: Dict[str, Any]) -> Any:
        logger.warning(
            f"Using fallback strategy due to error: {exception}",
            extra={"context": context}
        )
        return self.fallback_func(**context)


class CacheStrategy(ErrorRecoveryStrategy):
    """Use cached results when service is unavailable."""

    def __init__(self, cache_client):
        self.cache_client = cache_client

    def can_recover(self, exception: Exception) -> bool:
        return isinstance(exception, (ServiceUnavailableError, ConnectionError))

    def recover(self, exception: Exception, context: Dict[str, Any]) -> Any:
        cache_key = context.get('cache_key')
        if cache_key:
            cached_value = self.cache_client.get(cache_key)
            if cached_value:
                logger.warning(
                    f"Using cached value due to error: {exception}",
                    extra={"cache_key": cache_key, "context": context}
                )
                return cached_value
        raise exception


def aggregate_errors(errors: List[Exception]) -> MCX3DException:
    """
    Aggregate multiple errors into a single exception.

    Args:
        errors: List of exceptions to aggregate

    Returns:
        Single MCX3D exception containing all error information
    """
    if not errors:
        return MCX3DException("No errors to aggregate")

    if len(errors) == 1:
        return convert_exception(errors[0])

    error_details = []
    for idx, error in enumerate(errors):
        if isinstance(error, MCX3DException):
            error_details.append(error.to_dict())
        else:
            error_details.append({
                "index": idx,
                "type": type(error).__name__,
                "message": str(error),
                "traceback": traceback.format_exception(type(error), error, error.__traceback__)
            })

    return MCX3DException(
        message=f"Multiple errors occurred ({len(errors)} total)",
        user_message="Multiple errors occurred during processing. Please check the logs for details.",
        context={
            "error_count": len(errors),
            "errors": error_details,
            "timestamp": datetime.now(timezone.utc).isoformat()
        },
        severity="ERROR"
    )


# Convenience functions for common error scenarios
def validation_error(
    field: str,
    value: Any,
    message: str,
    **context
) -> ValidationError:
    """Create a standardized validation error."""
    return ValidationError(
        message=f"Validation failed for field '{field}': {message}",
        user_message=f"Invalid value for {field}: {message}",
        context={
            "field": field,
            "value": str(value),
            "validation_message": message,
            **context
        }
    )


def calculation_error(
    calculation_type: str,
    message: str,
    input_data: Optional[Dict[str, Any]] = None,
    **context
) -> FinancialCalculationError:
    """Create a standardized financial calculation error."""
    return FinancialCalculationError(
        message=f"{calculation_type} calculation failed: {message}",
        user_message=f"Unable to complete {calculation_type} calculation. Please verify your input data.",
        context={
            "calculation_type": calculation_type,
            "input_data": input_data,
            **context
        }
    )


def persistence_error(
    operation: str,
    entity_type: str,
    entity_id: Optional[str] = None,
    message: Optional[str] = None,
    **context
) -> DataPersistenceError:
    """Create a standardized data persistence error."""
    error_message = message or f"Failed to {operation} {entity_type}"
    if entity_id:
        error_message += f" with ID: {entity_id}"

    return DataPersistenceError(
        message=error_message,
        user_message=f"Unable to {operation} {entity_type}. Please try again.",
        context={
            "operation": operation,
            "entity_type": entity_type,
            "entity_id": entity_id,
            **context
        }
    )


def integration_error(
    service_name: str,
    message: str,
    **context
) -> IntegrationError:
    """Create a standardized integration error."""
    return IntegrationError(
        message=f"Integration with {service_name} failed: {message}",
        user_message=f"Could not connect to {service_name}. Please try again later.",
        context={
            "service_name": service_name,
            **context
        }
    )
