"""
Data validation exceptions for the MCX3D financial system.

These exceptions handle validation errors for input data, business rules,
configuration, and data integrity checks.
"""

from typing import Dict, Any, Optional, List
from mcx3d_finance.exceptions.base import MCX3DException


class MCX3DValidationException(MCX3DException):
    """Base exception for all validation-related errors."""

    def _get_default_user_message(self) -> str:
        return "Data validation failed. Please review and correct the input data."


class ValidationError(MCX3DValidationException):
    """
    General validation error for input data validation failures.

    Used when input data doesn't meet validation criteria.
    """

    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        validation_rule: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize validation error.

        Args:
            message: Technical error message
            field_name: Name of the field that failed validation (optional)
            field_value: Value that failed validation (optional)
            validation_rule: Rule that was violated (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.field_name = field_name
        self.field_value = field_value
        self.validation_rule = validation_rule

        if field_name:
            self.add_context("field_name", field_name)
        if field_value is not None:
            self.add_context("field_value", str(field_value))
        if validation_rule:
            self.add_context("validation_rule", validation_rule)

    def _get_default_user_message(self) -> str:
        if self.field_name:
            return f"Validation failed for field '{self.field_name}'. Please check the value and try again."
        return "Input validation failed. Please check your data and try again."


class DataIntegrityError(MCX3DValidationException):
    """
    Exception raised when data integrity checks fail.

    Used for referential integrity, consistency checks, and
    cross-field validation failures.
    """

    def __init__(
        self,
        message: str,
        integrity_type: str,
        affected_fields: Optional[List[str]] = None,
        expected_relationship: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize data integrity error.

        Args:
            message: Technical error message
            integrity_type: Type of integrity check (referential, consistency, etc.)
            affected_fields: Fields involved in the integrity check (optional)
            expected_relationship: Description of expected relationship (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.integrity_type = integrity_type
        self.affected_fields = affected_fields or []
        self.expected_relationship = expected_relationship

        self.add_context("integrity_type", integrity_type)
        if self.affected_fields:
            self.add_context("affected_fields", self.affected_fields)
        if expected_relationship:
            self.add_context("expected_relationship", expected_relationship)

    def _get_default_user_message(self) -> str:
        if self.affected_fields:
            fields = ", ".join(self.affected_fields)
            return f"Data integrity error involving fields: {fields}. Please ensure data consistency."
        return f"Data integrity error ({self.integrity_type}). Please verify data relationships and consistency."


class SchemaValidationError(MCX3DValidationException):
    """
    Exception raised when data doesn't match expected schema.

    Used for JSON schema validation, data structure validation,
    and format validation failures.
    """

    def __init__(
        self,
        message: str,
        schema_name: str,
        validation_errors: List[Dict[str, Any]],
        **kwargs,
    ):
        """
        Initialize schema validation error.

        Args:
            message: Technical error message
            schema_name: Name of the schema being validated against
            validation_errors: List of detailed validation error information
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.schema_name = schema_name
        self.validation_errors = validation_errors

        self.add_context("schema_name", schema_name)
        self.add_context("error_count", len(validation_errors))
        self.add_context("validation_errors", validation_errors)

    def _get_default_user_message(self) -> str:
        error_count = len(self.validation_errors)
        return f"Data format validation failed for {self.schema_name} with {error_count} error(s). Please check data structure."

    def get_validation_summary(self) -> str:
        """Get a formatted summary of schema validation errors."""
        if not self.validation_errors:
            return f"Schema validation passed for {self.schema_name}"

        summary = f"Schema validation failed for {self.schema_name} with {len(self.validation_errors)} error(s):\n"

        for i, error in enumerate(self.validation_errors, 1):
            field = error.get("field", "unknown")
            error_type = error.get("type", "validation")
            message = error.get("message", "Validation failed")
            summary += f"{i}. {field}: {error_type} - {message}\n"

        return summary.strip()


class BusinessRuleValidationError(MCX3DValidationException):
    """
    Exception raised when business rule validation fails.

    Used for domain-specific business logic validation,
    such as financial constraints and business process rules.
    """

    def __init__(
        self,
        message: str,
        rule_name: str,
        rule_description: Optional[str] = None,
        violated_constraints: Optional[List[str]] = None,
        **kwargs,
    ):
        """
        Initialize business rule validation error.

        Args:
            message: Technical error message
            rule_name: Name of the business rule that was violated
            rule_description: Human-readable description of the rule (optional)
            violated_constraints: List of specific constraints that were violated (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.rule_name = rule_name
        self.rule_description = rule_description
        self.violated_constraints = violated_constraints or []

        self.add_context("rule_name", rule_name)
        if rule_description:
            self.add_context("rule_description", rule_description)
        if self.violated_constraints:
            self.add_context("violated_constraints", self.violated_constraints)

    def _get_default_user_message(self) -> str:
        base_msg = f"Business rule validation failed: {self.rule_name}"
        if self.rule_description:
            base_msg += f" ({self.rule_description})"
        return (
            base_msg + ". Please adjust your data to comply with business requirements."
        )


class ConfigurationValidationError(MCX3DValidationException):
    """
    Exception raised when configuration validation fails.

    Used for system configuration, user settings, and
    application parameter validation failures.
    """

    def __init__(
        self,
        message: str,
        config_section: str,
        config_parameter: Optional[str] = None,
        expected_type: Optional[str] = None,
        expected_values: Optional[List[Any]] = None,
        **kwargs,
    ):
        """
        Initialize configuration validation error.

        Args:
            message: Technical error message
            config_section: Configuration section where error occurred
            config_parameter: Specific parameter that failed validation (optional)
            expected_type: Expected data type (optional)
            expected_values: List of expected values (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.config_section = config_section
        self.config_parameter = config_parameter
        self.expected_type = expected_type
        self.expected_values = expected_values

        self.add_context("config_section", config_section)
        if config_parameter:
            self.add_context("config_parameter", config_parameter)
        if expected_type:
            self.add_context("expected_type", expected_type)
        if expected_values:
            self.add_context("expected_values", expected_values)

    def _get_default_user_message(self) -> str:
        param_desc = f".{self.config_parameter}" if self.config_parameter else ""
        return f"Configuration validation error in {self.config_section}{param_desc}. Please check your settings."


class RequiredFieldError(ValidationError):
    """
    Exception raised when required fields are missing.

    Specialized validation error for missing required data fields.
    """

    def __init__(
        self,
        message: str,
        missing_fields: List[str],
        data_context: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize required field error.

        Args:
            message: Technical error message
            missing_fields: List of missing required field names
            data_context: Context where fields are missing (optional)
            **kwargs: Additional arguments for base exception
        """
        field_list = ", ".join(missing_fields)
        super().__init__(
            message, field_name=field_list, validation_rule="required", **kwargs
        )
        self.missing_fields = missing_fields
        self.data_context = data_context

        self.add_context("missing_fields", missing_fields)
        if data_context:
            self.add_context("data_context", data_context)

    def _get_default_user_message(self) -> str:
        field_count = len(self.missing_fields)
        if field_count == 1:
            field_desc = f"field '{self.missing_fields[0]}'"
        else:
            field_desc = f"{field_count} fields: {', '.join(self.missing_fields)}"

        context_desc = f" in {self.data_context}" if self.data_context else ""
        return f"Missing required {field_desc}{context_desc}. Please provide all required information."


class DataTypeValidationError(ValidationError):
    """
    Exception raised when data types don't match expectations.

    Specialized validation error for type conversion and
    data type validation failures.
    """

    def __init__(
        self,
        message: str,
        field_name: str,
        provided_type: str,
        expected_type: str,
        provided_value: Optional[Any] = None,
        **kwargs,
    ):
        """
        Initialize data type validation error.

        Args:
            message: Technical error message
            field_name: Name of the field with type error
            provided_type: Type that was provided
            expected_type: Type that was expected
            provided_value: The value that failed type validation (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(
            message,
            field_name=field_name,
            field_value=provided_value,
            validation_rule=f"type:{expected_type}",
            **kwargs,
        )
        self.provided_type = provided_type
        self.expected_type = expected_type

        self.add_context("provided_type", provided_type)
        self.add_context("expected_type", expected_type)

    def _get_default_user_message(self) -> str:
        return f"Invalid data type for '{self.field_name}': expected {self.expected_type}, got {self.provided_type}."


class ValidationWarning(MCX3DValidationException):
    """
    Exception raised for validation warnings (non-critical validation issues).

    Used for data quality issues that don't prevent processing
    but should be brought to user attention.
    """

    def __init__(
        self,
        message: str,
        warning_type: str,
        field_name: Optional[str] = None,
        suggested_action: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize validation warning.

        Args:
            message: Technical warning message
            warning_type: Type of warning (quality, format, etc.)
            field_name: Field that triggered the warning (optional)
            suggested_action: Suggested corrective action (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, severity="WARNING", **kwargs)
        self.warning_type = warning_type
        self.field_name = field_name
        self.suggested_action = suggested_action

        self.add_context("warning_type", warning_type)
        if field_name:
            self.add_context("field_name", field_name)
        if suggested_action:
            self.add_context("suggested_action", suggested_action)

    def _get_default_user_message(self) -> str:
        field_desc = f" for '{self.field_name}'" if self.field_name else ""
        action_desc = f" {self.suggested_action}" if self.suggested_action else ""
        return f"Validation warning{field_desc}: {self.warning_type}.{action_desc}"
