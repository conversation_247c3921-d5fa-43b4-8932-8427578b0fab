"""
MCX3D Finance Exception Hierarchy

This module provides comprehensive exception handling for the MCX3D financial
valuation system. All exceptions inherit from MCX3DException to enable
consistent error handling patterns across the application.

Usage:
    from mcx3d_finance.exceptions import (
        ReportGenerationError,
        FinancialCalculationError,
        ValidationError
    )
"""

from mcx3d_finance.exceptions.base import (
    MCX3DException,
    MCX3DConfigurationError,
    MCX3DSystemError,
    MCX3DResourceError,
    MCX3DTimeoutError,
)

from mcx3d_finance.exceptions.reporting import (
    MCX3DReportingException,
    ReportGenerationError,
    ReportDataValidationError,
    ReportOutputError,
    ChartGenerationError,
    ReportTemplateError,
    ReportFormatError,
    ReportMemoryError,
)

from mcx3d_finance.exceptions.financial import (
    MCX3DFinancialException,
    FinancialCalculationError,
    FinancialDataError,
    ValuationError,
    ProjectionError,
    DCFCalculationError,
    MultiplesValuationError,
    SaaSValuationError,
    FinancialRangeError,
    NumericalStabilityError,
)

from mcx3d_finance.exceptions.validation import (
    MCX3DValidationException,
    ValidationError,
    DataIntegrityError,
    SchemaValidationError,
    BusinessRuleValidationError,
    ConfigurationValidationError,
    RequiredFieldError,
    DataTypeValidationError,
)

from mcx3d_finance.exceptions.auth import (
    MCX3DAuthenticationError,
    InvalidCredentialsError,
    AccountLockedError,
    MFARequiredError,
    InvalidMFATokenError,
    TokenExpiredError,
    TokenInvalidError,
    SessionError,
    SessionExpiredError,
    SessionNotFoundError,
    XeroAuthError,
    XeroTokenRefreshError,
    XeroAuthorizationError,
    InvalidStateError,
)

from mcx3d_finance.exceptions.integration import (
    IntegrationError,
    XeroIntegrationError,
    APIConnectionError,
    DataSyncError,
    AuthenticationError as IntegrationAuthenticationError,
    RateLimitError,
)

__all__ = [
    # Base exceptions
    "MCX3DException",
    "MCX3DConfigurationError",
    "MCX3DSystemError",
    "MCX3DResourceError",
    "MCX3DTimeoutError",
    # Reporting exceptions
    "MCX3DReportingException",
    "ReportGenerationError",
    "ReportDataValidationError",
    "ReportOutputError",
    "ChartGenerationError",
    "ReportTemplateError",
    "ReportFormatError",
    "ReportMemoryError",
    # Financial exceptions
    "MCX3DFinancialException",
    "FinancialCalculationError",
    "FinancialDataError",
    "ValuationError",
    "ProjectionError",
    "DCFCalculationError",
    "MultiplesValuationError",
    "SaaSValuationError",
    "FinancialRangeError",
    "NumericalStabilityError",
    # Validation exceptions
    "MCX3DValidationException",
    "ValidationError",
    "DataIntegrityError",
    "SchemaValidationError",
    "BusinessRuleValidationError",
    "ConfigurationValidationError",
    "RequiredFieldError",
    "DataTypeValidationError",
    # Authentication exceptions
    "MCX3DAuthenticationError",
    "InvalidCredentialsError",
    "AccountLockedError",
    "MFARequiredError",
    "InvalidMFATokenError",
    "TokenExpiredError",
    "TokenInvalidError",
    "SessionError",
    "SessionExpiredError",
    "SessionNotFoundError",
    "XeroAuthError",
    "XeroTokenRefreshError",
    "XeroAuthorizationError",
    "InvalidStateError",
    # Integration exceptions
    "IntegrationError",
    "XeroIntegrationError",
    "APIConnectionError",
    "DataSyncError",
    "IntegrationAuthenticationError",
    "RateLimitError",
]
