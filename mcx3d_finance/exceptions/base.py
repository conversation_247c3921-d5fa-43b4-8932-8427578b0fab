"""
Base exception classes for the MCX3D financial system.

These exceptions provide the foundation for all error handling within MCX3D,
including structured error context, user-friendly messages, and debugging support.
"""

import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Union

from mcx3d_finance.core.logging_factory import LoggerFactory

logger = LoggerFactory.get_logger(__name__)


class MCX3DException(Exception):
    """
    Base exception for all MCX3D financial system errors.

    Provides structured error context, user-friendly messages, and debugging support.
    All MCX3D exceptions should inherit from this class to ensure consistent
    error handling patterns.
    """

    def __init__(
        self,
        message: str,
        user_message: Optional[str] = None,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
        severity: str = "ERROR",
    ):
        """
        Initialize MCX3D base exception.

        Args:
            message: Technical error message for logging
            user_message: User-friendly error message (optional)
            error_code: Unique error code for tracking (optional)
            context: Additional context data (optional)
            original_exception: Original exception that caused this error (optional)
            severity: Error severity level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        super().__init__(message)

        self.message = message
        self.user_message = user_message or self._get_default_user_message()
        self.error_code = error_code or self._generate_error_code()
        self.context = context or {}
        self.original_exception = original_exception
        self.severity = severity
        self.timestamp = datetime.now(timezone.utc).isoformat()
        self.traceback_info = traceback.format_exc()

        # Log the exception when created
        self._log_exception()

    def _get_default_user_message(self) -> str:
        """Generate a default user-friendly message."""
        return "An error occurred while processing your request. Please contact support if the issue persists."

    def _generate_error_code(self) -> str:
        """Generate a unique error code for this exception type."""
        class_name = self.__class__.__name__
        timestamp_short = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        return f"MCX3D_{class_name.upper()}_{timestamp_short}"

    def _log_exception(self):
        """Log the exception with appropriate level based on severity."""
        log_data = {
            "error_code": self.error_code,
            "message": self.message,
            "user_message": self.user_message,
            "context": self.context,
            "timestamp": self.timestamp,
            "exception_type": self.__class__.__name__,
        }

        if self.original_exception:
            log_data["original_exception"] = str(self.original_exception)

        log_level = getattr(logging, self.severity, logging.ERROR)
        logger.log(log_level, f"MCX3D Exception: {self.message}", extra=log_data)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert exception to dictionary for structured logging or API responses.

        Returns:
            Dictionary representation of the exception
        """
        return {
            "error_code": self.error_code,
            "message": self.message,
            "user_message": self.user_message,
            "context": self.context,
            "severity": self.severity,
            "timestamp": self.timestamp,
            "exception_type": self.__class__.__name__,
            "original_exception": (
                str(self.original_exception) if self.original_exception else None
            ),
        }

    def add_context(self, key: str, value: Any) -> "MCX3DException":
        """
        Add context information to the exception.

        Args:
            key: Context key
            value: Context value

        Returns:
            Self for method chaining
        """
        self.context[key] = value
        return self

    def get_user_message(self) -> str:
        """Get the user-friendly error message."""
        return self.user_message

    def get_technical_details(self) -> Dict[str, Any]:
        """Get technical details for debugging."""
        return {
            "error_code": self.error_code,
            "technical_message": self.message,
            "context": self.context,
            "traceback": self.traceback_info,
            "original_exception": (
                str(self.original_exception) if self.original_exception else None
            ),
            "timestamp": self.timestamp,
        }


class MCX3DConfigurationError(MCX3DException):
    """
    Exception raised for configuration-related errors.

    Used when system configuration is invalid, missing, or incompatible.
    """

    def _get_default_user_message(self) -> str:
        return "System configuration error. Please check your settings and try again."


class MCX3DSystemError(MCX3DException):
    """
    Exception raised for system-level errors.

    Used for infrastructure issues, resource constraints, or environmental problems.
    """

    def _get_default_user_message(self) -> str:
        return "A system error occurred. Please try again later or contact support."


class MCX3DResourceError(MCX3DException):
    """
    Exception raised for resource-related errors.

    Used when system resources (memory, disk, network) are unavailable or exhausted.
    """

    def __init__(
        self,
        message: str,
        resource_type: str,
        current_usage: Optional[Union[int, float]] = None,
        limit: Optional[Union[int, float]] = None,
        **kwargs,
    ):
        """
        Initialize resource error.

        Args:
            message: Technical error message
            resource_type: Type of resource (memory, disk, network, etc.)
            current_usage: Current resource usage
            limit: Resource limit that was exceeded
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.resource_type = resource_type
        self.current_usage = current_usage
        self.limit = limit

        # Add resource info to context
        self.add_context("resource_type", resource_type)
        if current_usage is not None:
            self.add_context("current_usage", current_usage)
        if limit is not None:
            self.add_context("limit", limit)

    def _get_default_user_message(self) -> str:
        return f"System resources ({self.resource_type}) are currently unavailable. Please try again later."


class MCX3DTimeoutError(MCX3DException):
    """
    Exception raised when operations exceed time limits.

    Used for operations that take too long to complete.
    """

    def __init__(self, message: str, operation: str, timeout_seconds: float, **kwargs):
        """
        Initialize timeout error.

        Args:
            message: Technical error message
            operation: Name of the operation that timed out
            timeout_seconds: Timeout duration in seconds
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.operation = operation
        self.timeout_seconds = timeout_seconds

        # Add timeout info to context
        self.add_context("operation", operation)
        self.add_context("timeout_seconds", timeout_seconds)

    def _get_default_user_message(self) -> str:
        return f"The operation ({self.operation}) is taking longer than expected. Please try again or contact support."
