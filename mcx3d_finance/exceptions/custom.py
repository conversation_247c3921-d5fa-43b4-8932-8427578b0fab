"""
Custom exceptions for the MCX3D financial system.
"""

from typing import List, Optional
from mcx3d_finance.exceptions.base import MCX3DException


class ReportValidationError(MCX3DException):
    """
    Exception raised when input data for report generation fails validation.

    Used when the data provided for report generation is invalid, incomplete,
    or doesn't meet business rules.
    """

    def __init__(
        self,
        message: str,
        validation_errors: Optional[List[str]] = None,
        data_type: Optional[str] = None,
        **kwargs,
    ) -> None:
        """
        Initialize report data validation error.

        Args:
            message: Technical error message
            validation_errors: List of specific validation error messages
            data_type: Type of data being validated (DCF, financial statements, etc.)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.validation_errors = validation_errors or []
        self.data_type = data_type

        self.add_context("validation_errors", self.validation_errors)
        if data_type:
            self.add_context("data_type", data_type)

    def _get_default_user_message(self) -> str:
        error_count = len(self.validation_errors)
        return f"Report data validation failed with {error_count} error(s). Please correct the data and try again."

    def get_validation_summary(self) -> str:
        """Get a formatted summary of validation errors."""
        if not self.validation_errors:
            return "No validation errors"

        summary = f"Validation failed with {len(self.validation_errors)} error(s):\n"
        for i, error in enumerate(self.validation_errors, 1):
            summary += f"{i}. {error}\n"

        return summary.strip()
