"""
Error recovery strategies and circuit breaker implementations for MCX3D Finance.

This module provides fault tolerance patterns including circuit breakers,
retry mechanisms, and graceful degradation strategies.
"""

import asyncio
import functools
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Type, Union
from uuid import uuid4

from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.exceptions.base import MCX3DException
from mcx3d_finance.exceptions.integration import (
    RateLimitError,
    ServiceUnavailableError,
    APIConnectionError,
)

logger = LoggerFactory.get_logger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"  # Normal operation
    OPEN = "open"      # Failing, rejecting calls
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker behavior."""
    failure_threshold: int = 5
    success_threshold: int = 2
    recovery_timeout: float = 60.0  # seconds
    expected_exception_types: Set[Type[Exception]] = field(
        default_factory=lambda: {
            ServiceUnavailableError,
            APIConnectionError,
            ConnectionError,
            TimeoutError,
        }
    )
    exclude_exception_types: Set[Type[Exception]] = field(
        default_factory=lambda: {
            ValueError,
            TypeError,
            KeyError,
        }
    )


class CircuitBreaker:
    """
    Circuit breaker implementation for fault tolerance.
    
    The circuit breaker prevents cascading failures by monitoring
    service health and temporarily blocking calls to failing services.
    """
    
    def __init__(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[float] = None
        self.last_success_time: Optional[float] = None
        self._state_change_callbacks: List[Callable] = []
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function through circuit breaker.
        
        Args:
            func: Function to execute
            *args, **kwargs: Function arguments
            
        Returns:
            Function result
            
        Raises:
            ServiceUnavailableError: If circuit is open
            Original exception: If function fails and circuit allows
        """
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self._transition_to_half_open()
            else:
                raise ServiceUnavailableError(
                    message=f"Circuit breaker '{self.name}' is OPEN",
                    service_name=self.name,
                    context={
                        "failure_count": self.failure_count,
                        "last_failure": datetime.fromtimestamp(
                            self.last_failure_time, tz=timezone.utc
                        ).isoformat() if self.last_failure_time else None,
                        "recovery_timeout": self.config.recovery_timeout,
                    }
                )
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure(e)
            raise
    
    async def call_async(self, func: Callable, *args, **kwargs) -> Any:
        """Async version of call method."""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self._transition_to_half_open()
            else:
                raise ServiceUnavailableError(
                    message=f"Circuit breaker '{self.name}' is OPEN",
                    service_name=self.name,
                    context={
                        "failure_count": self.failure_count,
                        "last_failure": datetime.fromtimestamp(
                            self.last_failure_time, tz=timezone.utc
                        ).isoformat() if self.last_failure_time else None,
                        "recovery_timeout": self.config.recovery_timeout,
                    }
                )
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure(e)
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        return (
            self.last_failure_time is not None
            and time.time() - self.last_failure_time >= self.config.recovery_timeout
        )
    
    def _on_success(self):
        """Handle successful call."""
        self.last_success_time = time.time()
        
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self._transition_to_closed()
        else:
            self.failure_count = 0
            self.success_count = 0
    
    def _on_failure(self, exception: Exception):
        """Handle failed call."""
        # Check if this exception should trip the circuit
        if not self._should_count_failure(exception):
            return
        
        self.last_failure_time = time.time()
        self.failure_count += 1
        self.success_count = 0
        
        if self.state == CircuitState.HALF_OPEN:
            self._transition_to_open()
        elif (
            self.state == CircuitState.CLOSED
            and self.failure_count >= self.config.failure_threshold
        ):
            self._transition_to_open()
        
        # Log the failure
        logger.warning(
            f"Circuit breaker '{self.name}' recorded failure {self.failure_count}: {exception}"
        )
    
    def _should_count_failure(self, exception: Exception) -> bool:
        """Determine if exception should count as circuit failure."""
        # Excluded exceptions don't count
        if type(exception) in self.config.exclude_exception_types:
            return False
        
        # If expected exceptions are defined, only count those
        if self.config.expected_exception_types:
            return type(exception) in self.config.expected_exception_types
        
        # Otherwise count all exceptions
        return True
    
    def _transition_to_open(self):
        """Transition to OPEN state."""
        self.state = CircuitState.OPEN
        logger.error(
            f"Circuit breaker '{self.name}' transitioned to OPEN after "
            f"{self.failure_count} failures"
        )
        self._notify_state_change(CircuitState.OPEN)
    
    def _transition_to_closed(self):
        """Transition to CLOSED state."""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        logger.info(f"Circuit breaker '{self.name}' transitioned to CLOSED")
        self._notify_state_change(CircuitState.CLOSED)
    
    def _transition_to_half_open(self):
        """Transition to HALF_OPEN state."""
        self.state = CircuitState.HALF_OPEN
        self.success_count = 0
        logger.info(f"Circuit breaker '{self.name}' transitioned to HALF_OPEN")
        self._notify_state_change(CircuitState.HALF_OPEN)
    
    def _notify_state_change(self, new_state: CircuitState):
        """Notify callbacks of state change."""
        for callback in self._state_change_callbacks:
            try:
                callback(self.name, new_state)
            except Exception as e:
                logger.error(f"Error in circuit breaker callback: {e}")
    
    def add_state_change_callback(self, callback: Callable[[str, CircuitState], None]):
        """Add callback for state changes."""
        self._state_change_callbacks.append(callback)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current circuit breaker status."""
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure": datetime.fromtimestamp(
                self.last_failure_time, tz=timezone.utc
            ).isoformat() if self.last_failure_time else None,
            "last_success": datetime.fromtimestamp(
                self.last_success_time, tz=timezone.utc
            ).isoformat() if self.last_success_time else None,
            "config": {
                "failure_threshold": self.config.failure_threshold,
                "success_threshold": self.config.success_threshold,
                "recovery_timeout": self.config.recovery_timeout,
            }
        }
    
    def reset(self):
        """Manually reset the circuit breaker."""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        logger.info(f"Circuit breaker '{self.name}' manually reset")


class CircuitBreakerRegistry:
    """Registry for managing multiple circuit breakers."""
    
    _instance = None
    _breakers: Dict[str, CircuitBreaker] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def get_or_create(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ) -> CircuitBreaker:
        """Get existing or create new circuit breaker."""
        if name not in self._breakers:
            self._breakers[name] = CircuitBreaker(name, config)
        return self._breakers[name]
    
    def get_all_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all circuit breakers."""
        return {
            name: breaker.get_status()
            for name, breaker in self._breakers.items()
        }
    
    def reset_all(self):
        """Reset all circuit breakers."""
        for breaker in self._breakers.values():
            breaker.reset()


# Global registry instance
circuit_registry = CircuitBreakerRegistry()


def with_circuit_breaker(
    name: Optional[str] = None,
    config: Optional[CircuitBreakerConfig] = None
):
    """
    Decorator to add circuit breaker protection to functions.
    
    Args:
        name: Circuit breaker name (defaults to function name)
        config: Circuit breaker configuration
    
    Example:
        @with_circuit_breaker(name="external_api")
        def call_external_api():
            return requests.get("https://api.example.com/data")
    """
    def decorator(func: Callable) -> Callable:
        breaker_name = name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            breaker = circuit_registry.get_or_create(breaker_name, config)
            return breaker.call(func, *args, **kwargs)
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            breaker = circuit_registry.get_or_create(breaker_name, config)
            return await breaker.call_async(func, *args, **kwargs)
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class RetryConfig:
    """Configuration for retry behavior."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        initial_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        retryable_exceptions: Optional[Set[Type[Exception]]] = None
    ):
        self.max_attempts = max_attempts
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.retryable_exceptions = retryable_exceptions or {
            ServiceUnavailableError,
            APIConnectionError,
            RateLimitError,
            ConnectionError,
            TimeoutError,
        }


def calculate_backoff_delay(
    attempt: int,
    config: RetryConfig
) -> float:
    """Calculate exponential backoff delay with optional jitter."""
    delay = min(
        config.initial_delay * (config.exponential_base ** (attempt - 1)),
        config.max_delay
    )
    
    if config.jitter:
        import random
        # Add jitter: 0.5 to 1.5 times the delay
        delay *= (0.5 + random.random())
    
    return delay


def with_retry(
    config: Optional[RetryConfig] = None,
    circuit_breaker_name: Optional[str] = None
):
    """
    Decorator to add retry logic to functions.
    
    Args:
        config: Retry configuration
        circuit_breaker_name: Optional circuit breaker to use
    
    Example:
        @with_retry(config=RetryConfig(max_attempts=5))
        def unstable_operation():
            # Operation that might fail transiently
            pass
    """
    retry_config = config or RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, retry_config.max_attempts + 1):
                try:
                    # Use circuit breaker if specified
                    if circuit_breaker_name:
                        breaker = circuit_registry.get_or_create(circuit_breaker_name)
                        return breaker.call(func, *args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                        
                except Exception as e:
                    last_exception = e
                    
                    # Check if exception is retryable
                    if not any(
                        isinstance(e, exc_type)
                        for exc_type in retry_config.retryable_exceptions
                    ):
                        raise
                    
                    # Don't retry on last attempt
                    if attempt == retry_config.max_attempts:
                        raise
                    
                    # Calculate and apply backoff
                    delay = calculate_backoff_delay(attempt, retry_config)
                    logger.warning(
                        f"Retry {attempt}/{retry_config.max_attempts} for "
                        f"{func.__name__} after {e.__class__.__name__}. "
                        f"Waiting {delay:.2f}s..."
                    )
                    time.sleep(delay)
            
            # This should not be reached, but just in case
            if last_exception:
                raise last_exception
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, retry_config.max_attempts + 1):
                try:
                    # Use circuit breaker if specified
                    if circuit_breaker_name:
                        breaker = circuit_registry.get_or_create(circuit_breaker_name)
                        return await breaker.call_async(func, *args, **kwargs)
                    else:
                        return await func(*args, **kwargs)
                        
                except Exception as e:
                    last_exception = e
                    
                    # Check if exception is retryable
                    if not any(
                        isinstance(e, exc_type)
                        for exc_type in retry_config.retryable_exceptions
                    ):
                        raise
                    
                    # Don't retry on last attempt
                    if attempt == retry_config.max_attempts:
                        raise
                    
                    # Calculate and apply backoff
                    delay = calculate_backoff_delay(attempt, retry_config)
                    logger.warning(
                        f"Retry {attempt}/{retry_config.max_attempts} for "
                        f"{func.__name__} after {e.__class__.__name__}. "
                        f"Waiting {delay:.2f}s..."
                    )
                    await asyncio.sleep(delay)
            
            # This should not be reached, but just in case
            if last_exception:
                raise last_exception
        
        # Return appropriate wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class GracefulDegradation:
    """
    Provides graceful degradation strategies for failing services.
    """
    
    def __init__(self, cache_client=None):
        self.cache_client = cache_client
        self.fallback_strategies: Dict[str, Callable] = {}
    
    def register_fallback(self, operation: str, fallback_func: Callable):
        """Register a fallback function for an operation."""
        self.fallback_strategies[operation] = fallback_func
    
    def with_fallback(
        self,
        operation: str,
        cache_key: Optional[str] = None,
        cache_ttl: int = 3600
    ):
        """
        Decorator to add fallback behavior to functions.
        
        Args:
            operation: Operation identifier
            cache_key: Optional cache key for results
            cache_ttl: Cache TTL in seconds
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    result = func(*args, **kwargs)
                    
                    # Cache successful result if cache is available
                    if self.cache_client and cache_key:
                        try:
                            self.cache_client.set(cache_key, result, ttl=cache_ttl)
                        except Exception as e:
                            logger.warning(f"Failed to cache result: {e}")
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"Operation {operation} failed: {e}")
                    
                    # Try cache first
                    if self.cache_client and cache_key:
                        try:
                            cached_result = self.cache_client.get(cache_key)
                            if cached_result is not None:
                                logger.info(f"Using cached result for {operation}")
                                return cached_result
                        except Exception as cache_error:
                            logger.warning(f"Cache retrieval failed: {cache_error}")
                    
                    # Try registered fallback
                    if operation in self.fallback_strategies:
                        try:
                            logger.info(f"Using fallback strategy for {operation}")
                            return self.fallback_strategies[operation](*args, **kwargs)
                        except Exception as fallback_error:
                            logger.error(f"Fallback also failed: {fallback_error}")
                    
                    # Re-raise original exception if no fallback worked
                    raise
            
            return wrapper
        return decorator