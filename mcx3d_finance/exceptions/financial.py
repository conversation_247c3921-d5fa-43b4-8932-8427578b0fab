"""
Financial calculation exceptions for the MCX3D financial system.

These exceptions handle errors in financial calculations, valuations,
projections, and financial data processing.
"""

from typing import Dict, Any, Optional, List, Union
from mcx3d_finance.exceptions.base import MCX3DException


class MCX3DFinancialException(MCX3DException):
    """Base exception for all financial calculation and data errors."""

    def _get_default_user_message(self) -> str:
        return "An error occurred during financial calculations. Please verify your data and try again."


class FinancialCalculationError(MCX3DFinancialException):
    """
    Exception raised when financial calculations fail.

    Used for mathematical errors, invalid assumptions, or calculation
    failures in financial models.
    """

    def __init__(
        self,
        message: str,
        calculation_type: str,
        input_data: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        """
        Initialize financial calculation error.

        Args:
            message: Technical error message
            calculation_type: Type of calculation (DCF, NPV, IRR, etc.)
            input_data: Input data that caused the error (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.calculation_type = calculation_type
        self.input_data = input_data

        self.add_context("calculation_type", calculation_type)
        if input_data:
            # Only include keys to avoid logging sensitive data
            self.add_context(
                "input_data_keys",
                (
                    list(input_data.keys())
                    if isinstance(input_data, dict)
                    else str(type(input_data))
                ),
            )

    def _get_default_user_message(self) -> str:
        return f"Financial calculation error in {self.calculation_type}. Please check your input values and assumptions."


class FinancialDataError(MCX3DFinancialException):
    """
    Exception raised when financial data is invalid or incomplete.

    Used for data quality issues, missing required fields, or
    inconsistent financial data.
    """

    def __init__(
        self,
        message: str,
        data_type: str,
        missing_fields: Optional[List[str]] = None,
        invalid_values: Optional[Dict[str, str]] = None,
        **kwargs,
    ):
        """
        Initialize financial data error.

        Args:
            message: Technical error message
            data_type: Type of financial data (revenue, expenses, etc.)
            missing_fields: List of missing required fields (optional)
            invalid_values: Dictionary of invalid values and reasons (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.data_type = data_type
        self.missing_fields = missing_fields or []
        self.invalid_values = invalid_values or {}

        self.add_context("data_type", data_type)
        if self.missing_fields:
            self.add_context("missing_fields", self.missing_fields)
        if self.invalid_values:
            self.add_context("invalid_values", self.invalid_values)

    def _get_default_user_message(self) -> str:
        issues = []
        if self.missing_fields:
            issues.append(f"{len(self.missing_fields)} missing field(s)")
        if self.invalid_values:
            issues.append(f"{len(self.invalid_values)} invalid value(s)")

        issue_desc = " and ".join(issues) if issues else "data quality issues"
        return f"Financial data error in {self.data_type}: {issue_desc}. Please review and correct the data."


class ValuationError(MCX3DFinancialException):
    """
    Exception raised during company valuation calculations.

    Used for errors in DCF, multiples, or other valuation methodologies.
    """

    def __init__(
        self,
        message: str,
        valuation_method: str,
        valuation_stage: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize valuation error.

        Args:
            message: Technical error message
            valuation_method: Valuation methodology (DCF, multiples, etc.)
            valuation_stage: Stage where error occurred (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.valuation_method = valuation_method
        self.valuation_stage = valuation_stage

        self.add_context("valuation_method", valuation_method)
        if valuation_stage:
            self.add_context("valuation_stage", valuation_stage)

    def _get_default_user_message(self) -> str:
        return f"Valuation calculation error using {self.valuation_method} method. Please verify assumptions and try again."


class ProjectionError(MCX3DFinancialException):
    """
    Exception raised during financial projection calculations.

    Used for errors in revenue projections, expense forecasting,
    or cash flow projections.
    """

    def __init__(
        self,
        message: str,
        projection_type: str,
        projection_period: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize projection error.

        Args:
            message: Technical error message
            projection_type: Type of projection (revenue, expenses, cash flow)
            projection_period: Period for projections (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.projection_type = projection_type
        self.projection_period = projection_period

        self.add_context("projection_type", projection_type)
        if projection_period:
            self.add_context("projection_period", projection_period)

    def _get_default_user_message(self) -> str:
        return f"Error generating {self.projection_type} projections. Please check historical data and growth assumptions."


class DCFCalculationError(ValuationError):
    """
    Exception raised during DCF (Discounted Cash Flow) calculations.

    Specialized exception for DCF-specific errors including discount rate,
    terminal value, and cash flow projection issues.
    """

    def __init__(
        self,
        message: str,
        discount_rate: Optional[float] = None,
        terminal_growth_rate: Optional[float] = None,
        cash_flows: Optional[List[float]] = None,
        **kwargs,
    ):
        """
        Initialize DCF calculation error.

        Args:
            message: Technical error message
            discount_rate: Discount rate used (optional)
            terminal_growth_rate: Terminal growth rate used (optional)
            cash_flows: Cash flows being discounted (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, valuation_method="DCF", **kwargs)
        self.discount_rate = discount_rate
        self.terminal_growth_rate = terminal_growth_rate
        self.cash_flows = cash_flows

        if discount_rate is not None:
            self.add_context("discount_rate", discount_rate)
        if terminal_growth_rate is not None:
            self.add_context("terminal_growth_rate", terminal_growth_rate)
        if cash_flows is not None:
            self.add_context("cash_flow_count", len(cash_flows))
            self.add_context(
                "total_cash_flow", sum(cf for cf in cash_flows if cf is not None)
            )

    def _get_default_user_message(self) -> str:
        return "DCF valuation calculation error. Please verify discount rate, growth assumptions, and cash flow projections."


class MultiplesValuationError(ValuationError):
    """
    Exception raised during multiples-based valuation calculations.

    Specialized exception for multiples valuation errors including
    comparable company issues and multiple calculation problems.
    """

    def __init__(
        self,
        message: str,
        multiple_type: Optional[str] = None,
        comparable_count: Optional[int] = None,
        **kwargs,
    ):
        """
        Initialize multiples valuation error.

        Args:
            message: Technical error message
            multiple_type: Type of multiple (P/E, EV/Revenue, etc.)
            comparable_count: Number of comparable companies (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, valuation_method="Multiples", **kwargs)
        self.multiple_type = multiple_type
        self.comparable_count = comparable_count

        if multiple_type:
            self.add_context("multiple_type", multiple_type)
        if comparable_count is not None:
            self.add_context("comparable_count", comparable_count)

    def _get_default_user_message(self) -> str:
        return "Multiples valuation calculation error. Please verify comparable companies and financial metrics."


class SaaSValuationError(ValuationError):
    """
    Exception raised during SaaS-specific valuation calculations.

    Specialized exception for SaaS metrics and valuation errors including
    ARR, MRR, and unit economics calculations.
    """

    def __init__(
        self,
        message: str,
        metric_type: Optional[str] = None,
        metric_value: Optional[Union[int, float]] = None,
        **kwargs,
    ):
        """
        Initialize SaaS valuation error.

        Args:
            message: Technical error message
            metric_type: Type of SaaS metric (ARR, MRR, CAC, LTV, etc.)
            metric_value: Value of the metric (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, valuation_method="SaaS", **kwargs)
        self.metric_type = metric_type
        self.metric_value = metric_value

        if metric_type:
            self.add_context("metric_type", metric_type)
        if metric_value is not None:
            self.add_context("metric_value", metric_value)

    def _get_default_user_message(self) -> str:
        return "SaaS valuation calculation error. Please verify recurring revenue metrics and unit economics data."


class NumericalStabilityError(FinancialCalculationError):
    """
    Exception raised when numerical calculations become unstable.

    Used for division by zero, overflow, underflow, or other
    numerical stability issues in financial calculations.
    """

    def __init__(
        self,
        message: str,
        operation: str,
        operands: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        """
        Initialize numerical stability error.

        Args:
            message: Technical error message
            operation: Mathematical operation that failed
            operands: Values involved in the operation (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, calculation_type=f"Numerical_{operation}", **kwargs)
        self.operation = operation
        self.operands = operands

        self.add_context("numerical_operation", operation)
        if operands:
            self.add_context(
                "operand_types", {k: type(v).__name__ for k, v in operands.items()}
            )

    def _get_default_user_message(self) -> str:
        return f"Numerical calculation error in {self.operation}. Please check for zero values or extreme numbers in your data."


class FinancialRangeError(FinancialDataError):
    """
    Exception raised when financial values are outside acceptable ranges.

    Used for validation of financial assumptions, rates, and other
    values that must fall within reasonable business ranges.
    """

    def __init__(
        self,
        message: str,
        field_name: str,
        value: Union[int, float],
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None,
        **kwargs,
    ):
        """
        Initialize financial range error.

        Args:
            message: Technical error message
            field_name: Name of the field with invalid range
            value: The invalid value
            min_value: Minimum acceptable value (optional)
            max_value: Maximum acceptable value (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, data_type=field_name, **kwargs)
        self.field_name = field_name
        self.value = value
        self.min_value = min_value
        self.max_value = max_value

        self.add_context("field_name", field_name)
        self.add_context("invalid_value", value)
        if min_value is not None:
            self.add_context("min_value", min_value)
        if max_value is not None:
            self.add_context("max_value", max_value)

    def _get_default_user_message(self) -> str:
        range_desc = ""
        if self.min_value is not None and self.max_value is not None:
            range_desc = f" (expected: {self.min_value} - {self.max_value})"
        elif self.min_value is not None:
            range_desc = f" (minimum: {self.min_value})"
        elif self.max_value is not None:
            range_desc = f" (maximum: {self.max_value})"

        return f"Invalid value for {self.field_name}: {self.value}{range_desc}. Please enter a valid value."
