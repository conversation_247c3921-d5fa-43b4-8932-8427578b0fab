"""Authentication-specific exceptions for the MCX3D finance application."""

from typing import <PERSON>tional
from mcx3d_finance.exceptions.base import MCX3DException


class MCX3DAuthenticationError(MCX3DException):
    """Base class for authentication-related errors."""

    def __init__(self, message: str, error_code: Optional[str] = None, **kwargs):
        super().__init__(message, error_code=error_code, **kwargs)
        if error_code:
            self.add_context("auth_error_code", error_code)

    def _get_default_user_message(self) -> str:
        return "Authentication failed. Please check your credentials and try again."


class InvalidCredentialsError(MCX3DAuthenticationError):
    """Raised when provided credentials are invalid."""

    pass


class AccountLockedError(MCX3DAuthenticationError):
    """Raised when an account is temporarily locked due to failed attempts."""

    def __init__(self, message: str, remaining_minutes: int):
        super().__init__(message)
        self.remaining_minutes = remaining_minutes


class MFARequiredError(MCX3DAuthenticationError):
    """Raised when MFA is required but not provided."""

    def __init__(self, message: str, challenge: Optional[str] = None):
        super().__init__(message)
        self.challenge = challenge


class InvalidMFATokenError(MCX3DAuthenticationError):
    """Raised when provided MFA token is invalid."""

    pass


class TokenExpiredError(MCX3DAuthenticationError):
    """Raised when a token has expired."""

    pass


class TokenInvalidError(MCX3DAuthenticationError):
    """Raised when a token is malformed or invalid."""

    pass


class SessionError(MCX3DAuthenticationError):
    """Base class for session-related errors."""

    pass


class SessionExpiredError(SessionError):
    """Raised when a session has expired."""

    pass


class SessionNotFoundError(SessionError):
    """Raised when a session cannot be found."""

    pass


class XeroAuthError(MCX3DAuthenticationError):
    """Base class for Xero OAuth-related errors."""

    pass


class XeroTokenRefreshError(XeroAuthError):
    """Raised when Xero token refresh fails."""

    pass


class XeroAuthorizationError(XeroAuthError):
    """Raised when Xero authorization fails."""

    pass


class InvalidStateError(XeroAuthError):
    """Raised when OAuth state parameter is invalid or expired."""

    pass
