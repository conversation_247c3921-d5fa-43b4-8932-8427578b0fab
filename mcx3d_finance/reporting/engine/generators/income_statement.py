"""
Production-ready Income Statement Generator.

Wraps the existing UKProfitAndLossGenerator with unified interface
and enhanced features for the consolidated reporting system.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional
from datetime import datetime
from decimal import Decimal

from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator
from mcx3d_finance.db.session import <PERSON>Local
from mcx3d_finance.db.models import Organization
from mcx3d_finance.exceptions.reporting import ReportGenerationError
from mcx3d_finance.utils.audit_logger import AuditLogger

logger = LoggerFactory.get_logger(__name__)


class IncomeStatementGenerator:
    """
    Production-ready Income Statement generator with UK FRS 102 compliance.
    
    Provides unified interface while leveraging existing UK-compliant
    profit & loss generation with enhanced error handling and features.
    """

    def __init__(self, organization_id: int) -> None:
        self.organization_id = organization_id
        self.audit_logger = AuditLogger()
        
        # Initialize the existing UK generator
        self.uk_generator = UKProfitAndLossGenerator(organization_id)
        
        # Validate organization exists
        self._validate_organization()

    def generate(
        self,
        dates: Dict[str, datetime],
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate comprehensive income statement.
        
        Args:
            dates: Dictionary containing period_start and period_end
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            **kwargs: Additional parameters
            
        Returns:
            Complete income statement data
            
        Raises:
            ReportGenerationError: If generation fails
        """
        try:
            period_start = dates["period_start"]
            period_end = dates["period_end"]
            
            logger.info(
                f"Generating income statement for org {self.organization_id} "
                f"from {period_start.date()} to {period_end.date()}"
            )
            
            # Audit log the generation request
            self.audit_logger.log_event(
                event_type="report_generation_started",
                user_id=None,  # Will be set by calling context
                organization_id=self.organization_id,
                details={
                    "report_type": "income_statement",
                    "period_start": period_start.isoformat(),
                    "period_end": period_end.isoformat(),
                    "comparative_period": comparative_period
                }
            )
            
            # Generate current period P&L using existing UK generator
            current_period_data = self.uk_generator.generate_profit_and_loss(
                from_date=period_start,
                to_date=period_end
            )
            
            # Generate comparative period if requested
            comparative_data = None
            if comparative_period:
                comparative_data = self._generate_comparative_period(
                    period_start, period_end
                )
            
            # Build comprehensive income statement
            income_statement = self._build_comprehensive_statement(
                current_data=current_period_data,
                comparative_data=comparative_data,
                period_start=period_start,
                period_end=period_end,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            # Validate the generated data
            self._validate_generated_data(income_statement)
            
            # Audit log successful generation
            self.audit_logger.log_event(
                event_type="report_generation_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "income_statement",
                    "total_revenue": income_statement.get("summary", {}).get("total_revenue", 0),
                    "net_profit": income_statement.get("summary", {}).get("net_profit", 0)
                }
            )
            
            logger.info(
                f"Successfully generated income statement for org {self.organization_id}"
            )
            
            return income_statement
            
        except Exception as e:
            # Audit log the error
            self.audit_logger.log_event(
                event_type="report_generation_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "income_statement",
                    "error": str(e)
                }
            )
            
            logger.error(f"Error generating income statement: {e}")
            raise ReportGenerationError(
                f"Failed to generate income statement: {e}",
                report_type="income_statement",
                organization_id=self.organization_id
            )

    def generate_income_statement(
        self,
        dates: Dict[str, datetime],
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Alias for generate method for backwards compatibility.
        """
        return self.generate(
            dates=dates,
            comparative_period=comparative_period,
            include_notes=include_notes,
            include_ratios=include_ratios,
            **kwargs
        )

    def _generate_comparative_period(
        self,
        current_start: datetime,
        current_end: datetime
    ) -> Optional[Dict[str, Any]]:
        """Generate comparative period data (previous year)."""
        try:
            # Calculate previous year dates
            prev_start = datetime(current_start.year - 1, current_start.month, current_start.day)
            prev_end = datetime(current_end.year - 1, current_end.month, current_end.day)
            
            # Generate previous year P&L
            comparative_data = self.uk_generator.generate_profit_and_loss(
                from_date=prev_start,
                to_date=prev_end
            )
            
            return comparative_data
            
        except Exception as e:
            logger.warning(f"Could not generate comparative period: {e}")
            return None

    def _build_comprehensive_statement(
        self,
        current_data: Dict[str, Any],
        comparative_data: Optional[Dict[str, Any]],
        period_start: datetime,
        period_end: datetime,
        include_notes: bool,
        include_ratios: bool
    ) -> Dict[str, Any]:
        """Build comprehensive income statement with all components."""
        
        # Get organization info
        db = SessionLocal()
        try:
            organization = db.query(Organization).filter(
                Organization.id == self.organization_id
            ).first()
            
            if not organization:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} not found"
                )
            
            # Build the comprehensive statement
            statement = {
                "header": {
                    "organization_id": self.organization_id,
                    "organization_name": organization.name or f"Organization {self.organization_id}",
                    "report_type": "Income Statement",
                    "period": {
                        "start_date": period_start.date().isoformat(),
                        "end_date": period_end.date().isoformat(),
                        "description": f"Year ended {period_end.date().strftime('%d %B %Y')}"
                    },
                    "generated_at": datetime.now().isoformat(),
                    "currency": organization.base_currency or "GBP",
                    "compliance": "FRS 102 (UK GAAP)",
                    "basis_of_preparation": "Accruals basis"
                },
                "financial_data": current_data,
                "summary": self._extract_summary_metrics(current_data)
            }
            
            # Add comparative data if available
            if comparative_data:
                statement["comparative"] = {
                    "financial_data": comparative_data,
                    "summary": self._extract_summary_metrics(comparative_data),
                    "variance_analysis": self._calculate_variances(current_data, comparative_data)
                }
            
            # Add notes if requested
            if include_notes:
                statement["notes"] = self._generate_explanatory_notes(
                    current_data, comparative_data
                )
            
            # Add financial ratios if requested
            if include_ratios:
                statement["ratios"] = self._calculate_financial_ratios(current_data)
            
            # Add statutory information for UK compliance
            statement["statutory_info"] = self._get_statutory_information(organization)
            
            return statement
            
        finally:
            db.close()

    def _extract_summary_metrics(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key summary metrics from financial data."""
        try:
            # Extract totals from the UK generator format
            revenues = financial_data.get("revenues", {})
            expenses = financial_data.get("expenses", {})
            
            total_revenue = sum(
                self._safe_decimal(value) for value in revenues.values()
            )
            
            total_expenses = sum(
                self._safe_decimal(value) for value in expenses.values()
            )
            
            gross_profit = total_revenue - self._safe_decimal(
                expenses.get("cost_of_sales", 0)
            )
            
            net_profit = total_revenue - total_expenses
            
            # Calculate margins
            gross_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0
            net_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
            
            return {
                "total_revenue": float(total_revenue),
                "total_expenses": float(total_expenses),
                "gross_profit": float(gross_profit),
                "net_profit": float(net_profit),
                "gross_margin_percent": round(float(gross_margin), 2),
                "net_margin_percent": round(float(net_margin), 2)
            }
            
        except Exception as e:
            logger.warning(f"Error extracting summary metrics: {e}")
            return {
                "total_revenue": 0.0,
                "total_expenses": 0.0,
                "gross_profit": 0.0,
                "net_profit": 0.0,
                "gross_margin_percent": 0.0,
                "net_margin_percent": 0.0
            }

    def _calculate_variances(
        self,
        current_data: Dict[str, Any],
        comparative_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate variances between current and comparative periods."""
        current_summary = self._extract_summary_metrics(current_data)
        comparative_summary = self._extract_summary_metrics(comparative_data)
        
        variances = {}
        
        for key in current_summary:
            current_value = current_summary[key]
            comparative_value = comparative_summary[key]
            
            variance_amount = current_value - comparative_value
            variance_percent = (
                (variance_amount / comparative_value * 100) 
                if comparative_value != 0 else 0
            )
            
            variances[key] = {
                "current": current_value,
                "comparative": comparative_value,
                "variance_amount": variance_amount,
                "variance_percent": round(variance_percent, 2)
            }
        
        return variances

    def _generate_explanatory_notes(
        self,
        current_data: Dict[str, Any],
        comparative_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate explanatory notes for the income statement."""
        notes = {
            "accounting_policies": [
                "The financial statements have been prepared under the historical cost convention.",
                "Revenue is recognised when the significant risks and rewards of ownership have been transferred.",
                "Expenses are recognised on an accruals basis.",
            ],
            "significant_items": [],
            "subsequent_events": []
        }
        
        # Add notes about significant variances if comparative data available
        if comparative_data:
            variances = self._calculate_variances(current_data, comparative_data)
            
            for key, variance in variances.items():
                if abs(variance["variance_percent"]) > 10:  # Significant variance threshold
                    notes["significant_items"].append(
                        f"{key.replace('_', ' ').title()}: "
                        f"{variance['variance_percent']:+.1f}% change from prior year"
                    )
        
        return notes

    def _calculate_financial_ratios(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate key financial ratios."""
        summary = self._extract_summary_metrics(financial_data)
        
        return {
            "profitability": {
                "gross_margin": summary["gross_margin_percent"],
                "net_margin": summary["net_margin_percent"],
                "return_on_sales": summary["net_margin_percent"]
            },
            "efficiency": {
                "revenue_per_employee": None,  # Would need employee count
                "expense_ratio": (
                    summary["total_expenses"] / summary["total_revenue"] * 100
                    if summary["total_revenue"] > 0 else 0
                )
            }
        }

    def _get_statutory_information(self, organization: Organization) -> Dict[str, Any]:
        """Get statutory information for UK compliance."""
        return {
            "company_number": getattr(organization, 'company_number', None),
            "registered_office": getattr(organization, 'registered_office', None),
            "directors": getattr(organization, 'directors', []),
            "auditors": getattr(organization, 'auditors', None),
            "accounting_reference_date": getattr(organization, 'year_end_date', None)
        }

    def _validate_organization(self) -> None:
        """Validate that organization exists and is accessible."""
        db = SessionLocal()
        try:
            organization = db.query(Organization).filter(
                Organization.id == self.organization_id
            ).first()
            
            if not organization:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} not found",
                    organization_id=self.organization_id
                )
            
            if not organization.is_active:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} is not active",
                    organization_id=self.organization_id
                )
                
        finally:
            db.close()

    def _validate_generated_data(self, income_statement: Dict[str, Any]) -> None:
        """Validate the generated income statement data."""
        required_sections = ["header", "financial_data", "summary"]
        
        for section in required_sections:
            if section not in income_statement:
                raise ReportGenerationError(
                    f"Missing required section: {section}",
                    report_type="income_statement"
                )
        
        # Validate summary metrics
        summary = income_statement.get("summary", {})
        if not isinstance(summary.get("total_revenue"), (int, float)):
            raise ReportGenerationError(
                "Invalid total_revenue in summary",
                report_type="income_statement"
            )

    def _safe_decimal(self, value: Any) -> Decimal:
        """Safely convert value to Decimal."""
        if value is None:
            return Decimal("0")
        try:
            return Decimal(str(value))
        except (ValueError, TypeError):
            return Decimal("0")

    def __del__(self) -> None:
        """Cleanup resources."""
        if hasattr(self, 'uk_generator') and hasattr(self.uk_generator, 'db'):
            try:
                self.uk_generator.db.close()
            except:
                pass