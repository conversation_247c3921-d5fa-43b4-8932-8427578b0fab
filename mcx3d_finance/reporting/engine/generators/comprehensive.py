"""
Production-ready Comprehensive Report Generator.

Generates complete financial statement packages including Income Statement,
Balance Sheet, Cash Flow Statement, and additional analyses.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from decimal import Decimal
import os

from mcx3d_finance.reporting.engine.generators.income_statement import IncomeStatementGenerator
from mcx3d_finance.reporting.engine.generators.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.reporting.engine.generators.cash_flow import CashFlowGenerator
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
from mcx3d_finance.exceptions.reporting import ReportGenerationError
from mcx3d_finance.utils.audit_logger import AuditLogger

logger = LoggerFactory.get_logger(__name__)


class ComprehensiveReportGenerator:
    """
    Production-ready Comprehensive Report generator.
    
    Generates complete financial statement packages with all three core statements
    plus additional analyses, ratios, and management information.
    """

    def __init__(self, organization_id: int) -> None:
        self.organization_id = organization_id
        self.audit_logger = AuditLogger()
        
        # Initialize individual generators
        self.income_generator = IncomeStatementGenerator(organization_id)
        self.balance_generator = BalanceSheetGenerator(organization_id)
        self.cash_flow_generator = CashFlowGenerator(organization_id)
        
        # Validate organization exists
        self._validate_organization()

    def generate(
        self,
        dates: Dict[str, datetime],
        formats: Optional[List[str]] = None,
        include_notes: bool = True,
        include_ratios: bool = True,
        include_management_analysis: bool = True,
        output_directory: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate comprehensive financial report package.
        
        Args:
            dates: Dictionary containing period_start, period_end, and as_of_date
            formats: List of output formats to generate
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios and analysis
            include_management_analysis: Include management discussion & analysis
            output_directory: Custom output directory
            **kwargs: Additional parameters
            
        Returns:
            Complete comprehensive report data with all statements
            
        Raises:
            ReportGenerationError: If generation fails
        """
        try:
            period_start = dates.get("period_start")
            period_end = dates.get("period_end") 
            as_of_date = dates.get("as_of_date", period_end)
            
            if not period_start or not period_end:
                raise ReportGenerationError(
                    "Both period_start and period_end are required for comprehensive reports"
                )
            
            logger.info(
                f"Generating comprehensive report for org {self.organization_id} "
                f"from {period_start.date()} to {period_end.date()}"
            )
            
            # Audit log the generation request
            self.audit_logger.log_event(
                event_type="report_generation_started",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "comprehensive",
                    "period_start": period_start.isoformat(),
                    "period_end": period_end.isoformat(),
                    "formats": formats or ["json"],
                    "include_management_analysis": include_management_analysis
                }
            )
            
            # Generate all three core financial statements
            financial_statements = self._generate_core_statements(
                period_start, period_end, as_of_date, include_notes, include_ratios
            )
            
            # Generate comprehensive analysis
            comprehensive_analysis = self._generate_comprehensive_analysis(
                financial_statements, include_management_analysis
            )
            
            # Build the complete package
            comprehensive_report = self._build_comprehensive_package(
                financial_statements=financial_statements,
                comprehensive_analysis=comprehensive_analysis,
                period_start=period_start,
                period_end=period_end,
                formats=formats or ["json"],
                output_directory=output_directory
            )
            
            # Validate the generated data
            self._validate_generated_data(comprehensive_report)
            
            # Audit log successful generation
            self.audit_logger.log_event(
                event_type="report_generation_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "comprehensive",
                    "statements_generated": list(financial_statements.keys()),
                    "analysis_included": bool(comprehensive_analysis),
                    "formats": formats or ["json"]
                }
            )
            
            logger.info(
                f"Successfully generated comprehensive report for org {self.organization_id}"
            )
            
            return comprehensive_report
            
        except Exception as e:
            # Audit log the error
            self.audit_logger.log_event(
                event_type="report_generation_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "comprehensive",
                    "error": str(e)
                }
            )
            
            logger.error(f"Error generating comprehensive report: {e}")
            raise ReportGenerationError(
                f"Failed to generate comprehensive report: {e}",
                report_type="comprehensive",
                organization_id=self.organization_id
            )

    def _generate_core_statements(
        self,
        period_start: datetime,
        period_end: datetime,
        as_of_date: datetime,
        include_notes: bool,
        include_ratios: bool
    ) -> Dict[str, Dict[str, Any]]:
        """Generate all three core financial statements."""
        
        statements = {}
        
        # Generate Income Statement
        try:
            statements["income_statement"] = self.income_generator.generate(
                dates={
                    "period_start": period_start,
                    "period_end": period_end
                },
                comparative_period=True,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            logger.info("Income statement generated successfully")
        except Exception as e:
            logger.error(f"Failed to generate income statement: {e}")
            statements["income_statement"] = {"error": str(e)}
        
        # Generate Balance Sheet
        try:
            statements["balance_sheet"] = self.balance_generator.generate(
                dates={
                    "as_of_date": as_of_date
                },
                comparative_period=True,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            logger.info("Balance sheet generated successfully")
        except Exception as e:
            logger.error(f"Failed to generate balance sheet: {e}")
            statements["balance_sheet"] = {"error": str(e)}
        
        # Generate Cash Flow Statement
        try:
            statements["cash_flow"] = self.cash_flow_generator.generate(
                dates={
                    "period_start": period_start,
                    "period_end": period_end
                },
                comparative_period=True,
                include_notes=include_notes,
                include_ratios=include_ratios,
                method="indirect"  # Default to indirect method
            )
            logger.info("Cash flow statement generated successfully")
        except Exception as e:
            logger.error(f"Failed to generate cash flow statement: {e}")
            statements["cash_flow"] = {"error": str(e)}
        
        return statements

    def _generate_comprehensive_analysis(
        self,
        financial_statements: Dict[str, Dict[str, Any]],
        include_management_analysis: bool
    ) -> Dict[str, Any]:
        """Generate comprehensive financial analysis across all statements."""
        
        analysis = {
            "cross_statement_ratios": self._calculate_cross_statement_ratios(financial_statements),
            "trend_analysis": self._perform_trend_analysis(financial_statements),
            "financial_health_assessment": self._assess_financial_health(financial_statements),
            "risk_analysis": self._perform_risk_analysis(financial_statements)
        }
        
        if include_management_analysis:
            analysis["management_discussion"] = self._generate_management_discussion(
                financial_statements, analysis
            )
        
        return analysis

    def _calculate_cross_statement_ratios(
        self,
        statements: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate ratios that require data from multiple statements."""
        
        # Extract summaries safely
        income_summary = statements.get("income_statement", {}).get("summary", {})
        balance_summary = statements.get("balance_sheet", {}).get("summary", {})
        cash_flow_summary = statements.get("cash_flow", {}).get("summary", {})
        
        # Financial Performance Ratios
        revenue = income_summary.get("total_revenue", 0)
        net_profit = income_summary.get("net_profit", 0)
        total_assets = balance_summary.get("total_assets", 0)
        total_equity = balance_summary.get("total_equity", 0)
        operating_cash_flow = cash_flow_summary.get("net_cash_operating", 0)
        
        ratios = {
            "profitability": {
                "return_on_assets": (net_profit / total_assets * 100) if total_assets > 0 else 0,
                "return_on_equity": (net_profit / total_equity * 100) if total_equity > 0 else 0,
                "asset_turnover": (revenue / total_assets) if total_assets > 0 else 0
            },
            "cash_flow_quality": {
                "operating_cf_to_revenue": (operating_cash_flow / revenue * 100) if revenue > 0 else 0,
                "operating_cf_to_net_income": (operating_cash_flow / net_profit) if net_profit > 0 else 0,
                "cash_flow_margin": (operating_cash_flow / revenue * 100) if revenue > 0 else 0
            },
            "efficiency": {
                "revenue_per_total_assets": (revenue / total_assets) if total_assets > 0 else 0,
                "cash_conversion_efficiency": (operating_cash_flow / net_profit) if net_profit > 0 else 0
            }
        }
        
        # Round all ratios to 2 decimal places
        return self._round_nested_dict(ratios, 2)

    def _perform_trend_analysis(
        self,
        statements: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Perform trend analysis using comparative data."""
        
        trends = {
            "income_trends": {},
            "balance_sheet_trends": {},
            "cash_flow_trends": {},
            "overall_performance": {}
        }
        
        # Analyze income statement trends
        income_stmt = statements.get("income_statement", {})
        if "comparative" in income_stmt:
            variance_analysis = income_stmt["comparative"].get("variance_analysis", {})
            trends["income_trends"] = {
                "revenue_growth": variance_analysis.get("total_revenue", {}).get("variance_percent", 0),
                "profit_growth": variance_analysis.get("net_profit", {}).get("variance_percent", 0),
                "margin_trends": self._analyze_margin_trends(income_stmt)
            }
        
        # Analyze balance sheet trends
        balance_sheet = statements.get("balance_sheet", {})
        if "comparative" in balance_sheet:
            variance_analysis = balance_sheet["comparative"].get("variance_analysis", {})
            trends["balance_sheet_trends"] = {
                "asset_growth": variance_analysis.get("total_assets", {}).get("variance_percent", 0),
                "equity_growth": variance_analysis.get("total_equity", {}).get("variance_percent", 0),
                "working_capital_trend": variance_analysis.get("working_capital", {}).get("variance_percent", 0)
            }
        
        # Analyze cash flow trends
        cash_flow = statements.get("cash_flow", {})
        if "comparative" in cash_flow:
            variance_analysis = cash_flow["comparative"].get("variance_analysis", {})
            trends["cash_flow_trends"] = {
                "operating_cf_growth": variance_analysis.get("net_cash_operating", {}).get("variance_percent", 0),
                "investing_cf_change": variance_analysis.get("net_cash_investing", {}).get("variance_percent", 0),
                "financing_cf_change": variance_analysis.get("net_cash_financing", {}).get("variance_percent", 0)
            }
        
        # Overall performance assessment
        trends["overall_performance"] = self._assess_overall_performance_trends(trends)
        
        return trends

    def _assess_financial_health(
        self,
        statements: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Assess overall financial health using multiple indicators."""
        
        # Extract data safely
        income_summary = statements.get("income_statement", {}).get("summary", {})
        balance_summary = statements.get("balance_sheet", {}).get("summary", {})
        cash_flow_summary = statements.get("cash_flow", {}).get("summary", {})
        
        # Health indicators
        health_indicators = []
        health_score = 0
        max_score = 100
        
        # Profitability check (20 points)
        net_margin = income_summary.get("net_margin_percent", 0)
        if net_margin > 15:
            health_score += 20
            health_indicators.append("Strong profitability")
        elif net_margin > 5:
            health_score += 15
            health_indicators.append("Adequate profitability")
        elif net_margin > 0:
            health_score += 10
            health_indicators.append("Marginal profitability")
        else:
            health_indicators.append("Loss-making operations")
        
        # Liquidity check (20 points)
        current_assets = balance_summary.get("current_assets", 0)
        current_liabilities = balance_summary.get("current_liabilities", 0)
        current_ratio = (current_assets / current_liabilities) if current_liabilities > 0 else 0
        
        if current_ratio > 2.0:
            health_score += 20
            health_indicators.append("Strong liquidity position")
        elif current_ratio > 1.5:
            health_score += 15
            health_indicators.append("Adequate liquidity")
        elif current_ratio > 1.0:
            health_score += 10
            health_indicators.append("Acceptable liquidity")
        else:
            health_indicators.append("Liquidity concerns")
        
        # Solvency check (20 points)
        total_equity = balance_summary.get("total_equity", 0)
        total_assets = balance_summary.get("total_assets", 0)
        equity_ratio = (total_equity / total_assets * 100) if total_assets > 0 else 0
        
        if equity_ratio > 50:
            health_score += 20
            health_indicators.append("Strong equity position")
        elif equity_ratio > 30:
            health_score += 15
            health_indicators.append("Adequate equity base")
        elif equity_ratio > 0:
            health_score += 10
            health_indicators.append("Limited equity base")
        else:
            health_indicators.append("Negative equity - insolvency risk")
        
        # Cash flow check (20 points)
        operating_cf = cash_flow_summary.get("net_cash_operating", 0)
        if operating_cf > 0:
            health_score += 20
            health_indicators.append("Positive operating cash flow")
        else:
            health_indicators.append("Negative operating cash flow")
        
        # Growth check (20 points) - would need comparative data
        # For now, assume neutral
        health_score += 10
        health_indicators.append("Growth assessment requires comparative data")
        
        # Determine overall health rating
        if health_score >= 80:
            health_rating = "Excellent"
        elif health_score >= 60:
            health_rating = "Good"
        elif health_score >= 40:
            health_rating = "Fair"
        elif health_score >= 20:
            health_rating = "Poor"
        else:
            health_rating = "Critical"
        
        return {
            "overall_rating": health_rating,
            "health_score": health_score,
            "max_score": max_score,
            "health_percentage": round((health_score / max_score) * 100, 1),
            "indicators": health_indicators,
            "recommendations": self._generate_health_recommendations(health_score, health_indicators)
        }

    def _perform_risk_analysis(
        self,
        statements: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Perform comprehensive risk analysis."""
        
        risks = {
            "liquidity_risk": self._assess_liquidity_risk(statements),
            "credit_risk": self._assess_credit_risk(statements),
            "operational_risk": self._assess_operational_risk(statements),
            "financial_risk": self._assess_financial_risk(statements)
        }
        
        # Overall risk score (lower is better)
        risk_scores = [risk.get("risk_score", 0) for risk in risks.values()]
        overall_risk_score = sum(risk_scores) / len(risk_scores) if risk_scores else 0
        
        if overall_risk_score <= 2:
            overall_risk_level = "Low"
        elif overall_risk_score <= 3:
            overall_risk_level = "Medium"
        else:
            overall_risk_level = "High"
        
        return {
            "overall_risk_level": overall_risk_level,
            "overall_risk_score": round(overall_risk_score, 2),
            "risk_breakdown": risks,
            "mitigation_recommendations": self._generate_risk_mitigation_recommendations(risks)
        }

    def _generate_management_discussion(
        self,
        statements: Dict[str, Dict[str, Any]],
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate management discussion and analysis section."""
        
        return {
            "executive_summary": self._generate_executive_summary(statements, analysis),
            "key_performance_indicators": self._extract_key_kpis(statements, analysis),
            "significant_events": self._identify_significant_events(statements),
            "outlook_and_prospects": self._generate_outlook(statements, analysis),
            "critical_accounting_policies": self._list_critical_accounting_policies()
        }

    def _build_comprehensive_package(
        self,
        financial_statements: Dict[str, Dict[str, Any]],
        comprehensive_analysis: Dict[str, Any],
        period_start: datetime,
        period_end: datetime,
        formats: List[str],
        output_directory: Optional[str]
    ) -> Dict[str, Any]:
        """Build the complete comprehensive report package."""
        
        # Get organization info
        db = SessionLocal()
        try:
            organization = db.query(Organization).filter(
                Organization.id == self.organization_id
            ).first()
            
            if not organization:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} not found"
                )
            
            # Build the comprehensive package
            package = {
                "header": {
                    "organization_id": self.organization_id,
                    "organization_name": organization.name or f"Organization {self.organization_id}",
                    "report_type": "Comprehensive Financial Report",
                    "period": {
                        "start_date": period_start.date().isoformat(),
                        "end_date": period_end.date().isoformat(),
                        "description": f"Year ended {period_end.date().strftime('%d %B %Y')}"
                    },
                    "generated_at": datetime.now().isoformat(),
                    "currency": organization.base_currency or "GBP",
                    "compliance": "FRS 102 (UK GAAP)",
                    "formats_generated": formats
                },
                "financial_statements": financial_statements,
                "comprehensive_analysis": comprehensive_analysis,
                "package_summary": self._generate_package_summary(financial_statements, comprehensive_analysis),
                "statutory_info": self._get_statutory_information(organization)
            }
            
            # Add output information if directory specified
            if output_directory:
                package["output_info"] = {
                    "output_directory": output_directory,
                    "generated_files": self._list_generated_files(output_directory, formats)
                }
            
            return package
            
        finally:
            db.close()

    def _generate_package_summary(
        self,
        statements: Dict[str, Dict[str, Any]],
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate executive summary of the entire package."""
        
        # Extract key metrics from each statement
        income_summary = statements.get("income_statement", {}).get("summary", {})
        balance_summary = statements.get("balance_sheet", {}).get("summary", {})
        cash_flow_summary = statements.get("cash_flow", {}).get("summary", {})
        
        return {
            "key_metrics": {
                "total_revenue": income_summary.get("total_revenue", 0),
                "net_profit": income_summary.get("net_profit", 0),
                "total_assets": balance_summary.get("total_assets", 0),
                "total_equity": balance_summary.get("total_equity", 0),
                "operating_cash_flow": cash_flow_summary.get("net_cash_operating", 0)
            },
            "financial_health": analysis.get("financial_health_assessment", {}).get("overall_rating", "Unknown"),
            "risk_level": analysis.get("risk_analysis", {}).get("overall_risk_level", "Unknown"),
            "statements_generated": len([s for s in statements.values() if "error" not in s]),
            "statements_failed": len([s for s in statements.values() if "error" in s])
        }

    # Helper methods for analysis components
    def _analyze_margin_trends(self, income_stmt: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze margin trends from income statement."""
        current_summary = income_stmt.get("summary", {})
        comparative = income_stmt.get("comparative", {})
        
        if not comparative:
            return {"note": "Comparative data not available"}
        
        comparative_summary = comparative.get("summary", {})
        
        return {
            "gross_margin_change": (
                current_summary.get("gross_margin_percent", 0) - 
                comparative_summary.get("gross_margin_percent", 0)
            ),
            "net_margin_change": (
                current_summary.get("net_margin_percent", 0) - 
                comparative_summary.get("net_margin_percent", 0)
            )
        }

    def _assess_overall_performance_trends(self, trends: Dict[str, Any]) -> Dict[str, str]:
        """Assess overall performance based on trends."""
        assessments = []
        
        # Check revenue growth
        revenue_growth = trends.get("income_trends", {}).get("revenue_growth", 0)
        if revenue_growth > 10:
            assessments.append("Strong revenue growth")
        elif revenue_growth > 0:
            assessments.append("Positive revenue growth")
        else:
            assessments.append("Revenue decline")
        
        # Check asset growth
        asset_growth = trends.get("balance_sheet_trends", {}).get("asset_growth", 0)
        if asset_growth > 0:
            assessments.append("Growing asset base")
        else:
            assessments.append("Declining assets")
        
        return {
            "overall_trend": "Positive" if revenue_growth > 0 and asset_growth > 0 else "Mixed",
            "key_observations": assessments
        }

    def _assess_liquidity_risk(self, statements: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Assess liquidity risk."""
        balance_summary = statements.get("balance_sheet", {}).get("summary", {})
        
        current_ratio = (
            balance_summary.get("current_assets", 0) / balance_summary.get("current_liabilities", 1)
        )
        
        if current_ratio > 2.0:
            risk_level = "Low"
            risk_score = 1
        elif current_ratio > 1.5:
            risk_level = "Low-Medium"
            risk_score = 2
        elif current_ratio > 1.0:
            risk_level = "Medium"
            risk_score = 3
        else:
            risk_level = "High"
            risk_score = 4
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "current_ratio": round(current_ratio, 2),
            "assessment": f"Current ratio of {current_ratio:.2f} indicates {risk_level.lower()} liquidity risk"
        }

    def _assess_credit_risk(self, statements: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Assess credit risk."""
        # Simplified assessment - would need more detailed receivables analysis
        return {
            "risk_level": "Medium",
            "risk_score": 2,
            "assessment": "Credit risk assessment requires detailed receivables aging analysis"
        }

    def _assess_operational_risk(self, statements: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Assess operational risk."""
        income_summary = statements.get("income_statement", {}).get("summary", {})
        net_margin = income_summary.get("net_margin_percent", 0)
        
        if net_margin > 15:
            risk_level = "Low"
            risk_score = 1
        elif net_margin > 5:
            risk_level = "Medium"
            risk_score = 2
        elif net_margin > 0:
            risk_level = "Medium-High"
            risk_score = 3
        else:
            risk_level = "High"
            risk_score = 4
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "net_margin": net_margin,
            "assessment": f"Net margin of {net_margin:.1f}% indicates {risk_level.lower()} operational risk"
        }

    def _assess_financial_risk(self, statements: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Assess financial/leverage risk."""
        balance_summary = statements.get("balance_sheet", {}).get("summary", {})
        
        debt_to_equity = (
            balance_summary.get("total_liabilities", 0) / 
            max(balance_summary.get("total_equity", 1), 1)
        )
        
        if debt_to_equity < 0.5:
            risk_level = "Low"
            risk_score = 1
        elif debt_to_equity < 1.0:
            risk_level = "Medium"
            risk_score = 2
        elif debt_to_equity < 2.0:
            risk_level = "Medium-High"
            risk_score = 3
        else:
            risk_level = "High"
            risk_score = 4
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "debt_to_equity": round(debt_to_equity, 2),
            "assessment": f"Debt-to-equity ratio of {debt_to_equity:.2f} indicates {risk_level.lower()} financial risk"
        }

    def _generate_health_recommendations(
        self,
        health_score: int,
        indicators: List[str]
    ) -> List[str]:
        """Generate health improvement recommendations."""
        recommendations = []
        
        if health_score < 40:
            recommendations.extend([
                "Urgent review of business operations required",
                "Consider debt restructuring or additional capital injection",
                "Implement immediate cost reduction measures"
            ])
        elif health_score < 60:
            recommendations.extend([
                "Focus on improving operational efficiency",
                "Review and optimize working capital management",
                "Consider strategic initiatives to boost profitability"
            ])
        elif health_score < 80:
            recommendations.extend([
                "Maintain current positive trends",
                "Look for growth opportunities",
                "Continue monitoring key financial metrics"
            ])
        
        return recommendations

    def _generate_risk_mitigation_recommendations(
        self,
        risks: Dict[str, Any]
    ) -> List[str]:
        """Generate risk mitigation recommendations."""
        recommendations = []
        
        for risk_type, risk_data in risks.items():
            risk_level = risk_data.get("risk_level", "Unknown")
            if risk_level in ["High", "Medium-High"]:
                if risk_type == "liquidity_risk":
                    recommendations.append("Improve working capital management and maintain adequate cash reserves")
                elif risk_type == "financial_risk":
                    recommendations.append("Consider debt reduction or equity strengthening initiatives")
                elif risk_type == "operational_risk":
                    recommendations.append("Focus on operational efficiency and margin improvement")
        
        return recommendations

    def _generate_executive_summary(
        self,
        statements: Dict[str, Dict[str, Any]],
        analysis: Dict[str, Any]
    ) -> str:
        """Generate executive summary text."""
        health_rating = analysis.get("financial_health_assessment", {}).get("overall_rating", "Unknown")
        risk_level = analysis.get("risk_analysis", {}).get("overall_risk_level", "Unknown")
        
        return (
            f"This comprehensive financial report presents a complete analysis of the organization's "
            f"financial position and performance. The overall financial health is assessed as '{health_rating}' "
            f"with a '{risk_level}' risk profile. The analysis includes detailed examination of all three "
            f"core financial statements along with comprehensive ratio analysis and risk assessment."
        )

    def _extract_key_kpis(
        self,
        statements: Dict[str, Dict[str, Any]],
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract key performance indicators."""
        income_summary = statements.get("income_statement", {}).get("summary", {})
        balance_summary = statements.get("balance_sheet", {}).get("summary", {})
        
        return {
            "revenue": income_summary.get("total_revenue", 0),
            "net_profit_margin": income_summary.get("net_margin_percent", 0),
            "return_on_assets": analysis.get("cross_statement_ratios", {}).get("profitability", {}).get("return_on_assets", 0),
            "current_ratio": (
                balance_summary.get("current_assets", 0) / 
                max(balance_summary.get("current_liabilities", 1), 1)
            ),
            "debt_to_equity": (
                balance_summary.get("total_liabilities", 0) / 
                max(balance_summary.get("total_equity", 1), 1)
            )
        }

    def _identify_significant_events(self, statements: Dict[str, Dict[str, Any]]) -> List[str]:
        """Identify significant events from the financial data."""
        events = []
        
        # Check for significant variances
        for statement_name, statement_data in statements.items():
            if "comparative" in statement_data:
                variance_analysis = statement_data["comparative"].get("variance_analysis", {})
                for metric, variance in variance_analysis.items():
                    if abs(variance.get("variance_percent", 0)) > 25:
                        events.append(
                            f"Significant change in {metric}: {variance['variance_percent']:+.1f}%"
                        )
        
        return events

    def _generate_outlook(
        self,
        statements: Dict[str, Dict[str, Any]],
        analysis: Dict[str, Any]
    ) -> str:
        """Generate outlook and prospects."""
        health_rating = analysis.get("financial_health_assessment", {}).get("overall_rating", "Unknown")
        
        if health_rating in ["Excellent", "Good"]:
            return "The organization shows strong financial fundamentals with positive prospects for continued growth and stability."
        elif health_rating == "Fair":
            return "The organization demonstrates adequate financial performance with opportunities for improvement and growth."
        else:
            return "The organization faces financial challenges that require immediate attention and strategic action."

    def _list_critical_accounting_policies(self) -> List[str]:
        """List critical accounting policies."""
        return [
            "Revenue recognition: Revenue is recognised when significant risks and rewards of ownership are transferred",
            "Depreciation: Fixed assets are depreciated using the straight-line method over their estimated useful lives",
            "Inventory valuation: Inventory is valued at the lower of cost and net realisable value",
            "Provisions: Provisions are recognised when there is a present obligation resulting from past events"
        ]

    def _get_statutory_information(self, organization: Organization) -> Dict[str, Any]:
        """Get statutory information for UK compliance."""
        return {
            "company_number": getattr(organization, 'company_number', None),
            "registered_office": getattr(organization, 'registered_office', None),
            "directors": getattr(organization, 'directors', []),
            "auditors": getattr(organization, 'auditors', None),
            "accounting_reference_date": getattr(organization, 'year_end_date', None)
        }

    def _list_generated_files(self, output_directory: str, formats: List[str]) -> List[str]:
        """List files that would be generated in the output directory."""
        files = []
        base_name = f"comprehensive_report_{datetime.now().strftime('%Y%m%d')}"
        
        for format_type in formats:
            if format_type.lower() == "pdf":
                files.append(f"{base_name}.pdf")
            elif format_type.lower() == "excel":
                files.append(f"{base_name}.xlsx")
            elif format_type.lower() == "json":
                files.append(f"{base_name}.json")
            elif format_type.lower() == "html":
                files.append(f"{base_name}.html")
        
        return files

    def _round_nested_dict(self, data: Dict[str, Any], decimals: int) -> Dict[str, Any]:
        """Round all numeric values in nested dictionary."""
        result = {}
        for key, value in data.items():
            if isinstance(value, dict):
                result[key] = self._round_nested_dict(value, decimals)
            elif isinstance(value, (int, float)):
                result[key] = round(value, decimals)
            else:
                result[key] = value
        return result

    def _validate_organization(self) -> None:
        """Validate that organization exists and is accessible."""
        db = SessionLocal()
        try:
            organization = db.query(Organization).filter(
                Organization.id == self.organization_id
            ).first()
            
            if not organization:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} not found",
                    organization_id=self.organization_id
                )
            
            if not organization.is_active:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} is not active",
                    organization_id=self.organization_id
                )
                
        finally:
            db.close()

    def _validate_generated_data(self, comprehensive_report: Dict[str, Any]) -> None:
        """Validate the generated comprehensive report data."""
        required_sections = ["header", "financial_statements", "comprehensive_analysis", "package_summary"]
        
        for section in required_sections:
            if section not in comprehensive_report:
                raise ReportGenerationError(
                    f"Missing required section: {section}",
                    report_type="comprehensive"
                )
        
        # Validate that at least one financial statement was generated successfully
        statements = comprehensive_report.get("financial_statements", {})
        successful_statements = [s for s in statements.values() if "error" not in s]
        
        if not successful_statements:
            raise ReportGenerationError(
                "No financial statements were generated successfully",
                report_type="comprehensive"
            )

    def __del__(self) -> None:
        """Cleanup resources."""
        # Individual generators will handle their own cleanup
        pass