"""
Production-ready Balance Sheet Generator.

Wraps the existing UKBalanceSheetGenerator with unified interface
and enhanced features for the consolidated reporting system.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

from mcx3d_finance.core.financials.balance_sheet import UKBalanceSheetGenerator
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
from mcx3d_finance.exceptions.reporting import ReportGenerationError
from mcx3d_finance.utils.audit_logger import AuditLogger

logger = LoggerFactory.get_logger(__name__)


class BalanceSheetGenerator:
    """
    Production-ready Balance Sheet generator with UK FRS 102 compliance.
    
    Provides unified interface while leveraging existing UK-compliant
    balance sheet generation with enhanced error handling and features.
    """

    def __init__(self, organization_id: int) -> None:
        self.organization_id = organization_id
        self.audit_logger = AuditLogger()
        
        # Initialize the existing UK generator
        self.uk_generator = UKBalanceSheetGenerator(organization_id)
        
        # Validate organization exists
        self._validate_organization()

    def generate(
        self,
        dates: Dict[str, datetime],
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate comprehensive balance sheet.
        
        Args:
            dates: Dictionary containing as_of_date
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            **kwargs: Additional parameters
            
        Returns:
            Complete balance sheet data
            
        Raises:
            ReportGenerationError: If generation fails
        """
        try:
            as_of_date = dates["as_of_date"]
            
            logger.info(
                f"Generating balance sheet for org {self.organization_id} "
                f"as of {as_of_date.date()}"
            )
            
            # Audit log the generation request
            self.audit_logger.log_event(
                event_type="report_generation_started",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "balance_sheet",
                    "as_of_date": as_of_date.isoformat(),
                    "comparative_period": comparative_period
                }
            )
            
            # Generate current period balance sheet using existing UK generator
            current_period_data = self.uk_generator.generate_balance_sheet(
                as_of_date=as_of_date
            )
            
            # Generate comparative period if requested
            comparative_data = None
            if comparative_period:
                comparative_data = self._generate_comparative_period(as_of_date)
            
            # Build comprehensive balance sheet
            balance_sheet = self._build_comprehensive_statement(
                current_data=current_period_data,
                comparative_data=comparative_data,
                as_of_date=as_of_date,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            # Validate the generated data
            self._validate_generated_data(balance_sheet)
            
            # Audit log successful generation
            self.audit_logger.log_event(
                event_type="report_generation_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "balance_sheet",
                    "total_assets": balance_sheet.get("summary", {}).get("total_assets", 0),
                    "total_liabilities": balance_sheet.get("summary", {}).get("total_liabilities", 0),
                    "total_equity": balance_sheet.get("summary", {}).get("total_equity", 0)
                }
            )
            
            logger.info(
                f"Successfully generated balance sheet for org {self.organization_id}"
            )
            
            return balance_sheet
            
        except Exception as e:
            # Audit log the error
            self.audit_logger.log_event(
                event_type="report_generation_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "report_type": "balance_sheet",
                    "error": str(e)
                }
            )
            
            logger.error(f"Error generating balance sheet: {e}")
            raise ReportGenerationError(
                f"Failed to generate balance sheet: {e}",
                report_type="balance_sheet",
                organization_id=self.organization_id
            )

    def generate_balance_sheet(
        self,
        dates: Dict[str, datetime],
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Alias for generate method for backwards compatibility.
        """
        return self.generate(
            dates=dates,
            comparative_period=comparative_period,
            include_notes=include_notes,
            include_ratios=include_ratios,
            **kwargs
        )

    def _generate_comparative_period(self, current_date: datetime) -> Optional[Dict[str, Any]]:
        """Generate comparative period data (previous year)."""
        try:
            # Calculate previous year date
            prev_date = datetime(current_date.year - 1, current_date.month, current_date.day)
            
            # Generate previous year balance sheet
            comparative_data = self.uk_generator.generate_balance_sheet(
                as_of_date=prev_date
            )
            
            return comparative_data
            
        except Exception as e:
            logger.warning(f"Could not generate comparative period: {e}")
            return None

    def _build_comprehensive_statement(
        self,
        current_data: Dict[str, Any],
        comparative_data: Optional[Dict[str, Any]],
        as_of_date: datetime,
        include_notes: bool,
        include_ratios: bool
    ) -> Dict[str, Any]:
        """Build comprehensive balance sheet with all components."""
        
        # Get organization info
        db = SessionLocal()
        try:
            organization = db.query(Organization).filter(
                Organization.id == self.organization_id
            ).first()
            
            if not organization:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} not found"
                )
            
            # Build the comprehensive statement
            statement = {
                "header": {
                    "organization_id": self.organization_id,
                    "organization_name": organization.name or f"Organization {self.organization_id}",
                    "report_type": "Balance Sheet",
                    "as_of_date": as_of_date.date().isoformat(),
                    "description": f"As at {as_of_date.date().strftime('%d %B %Y')}",
                    "generated_at": datetime.now().isoformat(),
                    "currency": organization.base_currency or "GBP",
                    "compliance": "FRS 102 (UK GAAP)",
                    "basis_of_preparation": "Historical cost basis"
                },
                "financial_data": current_data,
                "summary": self._extract_summary_metrics(current_data)
            }
            
            # Add comparative data if available
            if comparative_data:
                statement["comparative"] = {
                    "financial_data": comparative_data,
                    "summary": self._extract_summary_metrics(comparative_data),
                    "variance_analysis": self._calculate_variances(current_data, comparative_data)
                }
            
            # Add notes if requested
            if include_notes:
                statement["notes"] = self._generate_explanatory_notes(
                    current_data, comparative_data, as_of_date
                )
            
            # Add financial ratios if requested
            if include_ratios:
                statement["ratios"] = self._calculate_financial_ratios(current_data)
            
            # Add statutory information for UK compliance
            statement["statutory_info"] = self._get_statutory_information(organization)
            
            # Add balance sheet validation
            statement["validation"] = self._validate_balance_sheet_equation(current_data)
            
            return statement
            
        finally:
            db.close()

    def _extract_summary_metrics(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key summary metrics from balance sheet data."""
        try:
            # Extract totals from the UK generator format
            assets = financial_data.get("assets", {})
            liabilities = financial_data.get("liabilities", {})
            equity = financial_data.get("equity", {})
            
            # Calculate asset totals
            current_assets = sum(
                self._safe_decimal(value) 
                for key, value in assets.items() 
                if "current" in key.lower()
            )
            
            non_current_assets = sum(
                self._safe_decimal(value) 
                for key, value in assets.items() 
                if "non_current" in key.lower() or "fixed" in key.lower()
            )
            
            total_assets = sum(
                self._safe_decimal(value) for value in assets.values()
            )
            
            # Calculate liability totals
            current_liabilities = sum(
                self._safe_decimal(value) 
                for key, value in liabilities.items() 
                if "current" in key.lower()
            )
            
            non_current_liabilities = sum(
                self._safe_decimal(value) 
                for key, value in liabilities.items() 
                if "non_current" in key.lower() or "long_term" in key.lower()
            )
            
            total_liabilities = sum(
                self._safe_decimal(value) for value in liabilities.values()
            )
            
            # Calculate equity total
            total_equity = sum(
                self._safe_decimal(value) for value in equity.values()
            )
            
            # Calculate working capital
            working_capital = current_assets - current_liabilities
            
            # Calculate net assets
            net_assets = total_assets - total_liabilities
            
            return {
                "current_assets": float(current_assets),
                "non_current_assets": float(non_current_assets),
                "total_assets": float(total_assets),
                "current_liabilities": float(current_liabilities),
                "non_current_liabilities": float(non_current_liabilities),
                "total_liabilities": float(total_liabilities),
                "total_equity": float(total_equity),
                "working_capital": float(working_capital),
                "net_assets": float(net_assets)
            }
            
        except Exception as e:
            logger.warning(f"Error extracting summary metrics: {e}")
            return {
                "current_assets": 0.0,
                "non_current_assets": 0.0,
                "total_assets": 0.0,
                "current_liabilities": 0.0,
                "non_current_liabilities": 0.0,
                "total_liabilities": 0.0,
                "total_equity": 0.0,
                "working_capital": 0.0,
                "net_assets": 0.0
            }

    def _calculate_variances(
        self,
        current_data: Dict[str, Any],
        comparative_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate variances between current and comparative periods."""
        current_summary = self._extract_summary_metrics(current_data)
        comparative_summary = self._extract_summary_metrics(comparative_data)
        
        variances = {}
        
        for key in current_summary:
            current_value = current_summary[key]
            comparative_value = comparative_summary[key]
            
            variance_amount = current_value - comparative_value
            variance_percent = (
                (variance_amount / comparative_value * 100) 
                if comparative_value != 0 else 0
            )
            
            variances[key] = {
                "current": current_value,
                "comparative": comparative_value,
                "variance_amount": variance_amount,
                "variance_percent": round(variance_percent, 2)
            }
        
        return variances

    def _generate_explanatory_notes(
        self,
        current_data: Dict[str, Any],
        comparative_data: Optional[Dict[str, Any]],
        as_of_date: datetime
    ) -> Dict[str, Any]:
        """Generate explanatory notes for the balance sheet."""
        notes = {
            "accounting_policies": [
                "The balance sheet has been prepared under the historical cost convention.",
                "Fixed assets are stated at cost less accumulated depreciation.",
                "Current assets are stated at the lower of cost and net realisable value.",
                "Liabilities are stated at their settlement value."
            ],
            "significant_items": [],
            "subsequent_events": [],
            "going_concern": self._assess_going_concern(current_data),
            "contingencies": []
        }
        
        # Add notes about significant variances if comparative data available
        if comparative_data:
            variances = self._calculate_variances(current_data, comparative_data)
            
            for key, variance in variances.items():
                if abs(variance["variance_percent"]) > 15:  # Significant variance threshold
                    notes["significant_items"].append(
                        f"{key.replace('_', ' ').title()}: "
                        f"{variance['variance_percent']:+.1f}% change from prior year"
                    )
        
        # Add liquidity analysis
        summary = self._extract_summary_metrics(current_data)
        current_ratio = (
            summary["current_assets"] / summary["current_liabilities"]
            if summary["current_liabilities"] > 0 else 0
        )
        
        if current_ratio < 1.0:
            notes["significant_items"].append(
                f"Current ratio of {current_ratio:.2f} indicates potential liquidity concerns"
            )
        
        return notes

    def _calculate_financial_ratios(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate key financial ratios for balance sheet analysis."""
        summary = self._extract_summary_metrics(financial_data)
        
        # Liquidity ratios
        current_ratio = (
            summary["current_assets"] / summary["current_liabilities"]
            if summary["current_liabilities"] > 0 else 0
        )
        
        quick_ratio = (
            (summary["current_assets"] - summary.get("inventory", 0)) / summary["current_liabilities"]
            if summary["current_liabilities"] > 0 else 0
        )
        
        # Leverage ratios
        debt_to_equity = (
            summary["total_liabilities"] / summary["total_equity"]
            if summary["total_equity"] > 0 else 0
        )
        
        debt_to_assets = (
            summary["total_liabilities"] / summary["total_assets"]
            if summary["total_assets"] > 0 else 0
        )
        
        # Efficiency ratios
        asset_turnover = None  # Would need revenue data
        
        return {
            "liquidity": {
                "current_ratio": round(current_ratio, 2),
                "quick_ratio": round(quick_ratio, 2),
                "working_capital": summary["working_capital"]
            },
            "leverage": {
                "debt_to_equity": round(debt_to_equity, 2),
                "debt_to_assets": round(debt_to_assets * 100, 2),  # As percentage
                "equity_ratio": round((1 - debt_to_assets) * 100, 2)  # As percentage
            },
            "structure": {
                "current_assets_percent": round(
                    summary["current_assets"] / summary["total_assets"] * 100, 2
                ) if summary["total_assets"] > 0 else 0,
                "current_liabilities_percent": round(
                    summary["current_liabilities"] / summary["total_liabilities"] * 100, 2
                ) if summary["total_liabilities"] > 0 else 0
            }
        }

    def _assess_going_concern(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess going concern status based on balance sheet indicators."""
        summary = self._extract_summary_metrics(financial_data)
        
        indicators = []
        risk_score = 0
        
        # Check working capital
        if summary["working_capital"] < 0:
            indicators.append("Negative working capital")
            risk_score += 2
        
        # Check current ratio
        current_ratio = (
            summary["current_assets"] / summary["current_liabilities"]
            if summary["current_liabilities"] > 0 else 0
        )
        
        if current_ratio < 1.0:
            indicators.append(f"Low current ratio ({current_ratio:.2f})")
            risk_score += 1
        
        # Check equity position
        if summary["total_equity"] < 0:
            indicators.append("Negative equity position")
            risk_score += 3
        
        # Determine risk level
        if risk_score >= 4:
            risk_level = "high"
        elif risk_score >= 2:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "indicators": indicators,
            "assessment": (
                "No material uncertainties identified" if risk_level == "low"
                else f"{risk_level.title()} risk indicators present"
            )
        }

    def _validate_balance_sheet_equation(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that Assets = Liabilities + Equity."""
        summary = self._extract_summary_metrics(financial_data)
        
        assets = summary["total_assets"]
        liabilities_plus_equity = summary["total_liabilities"] + summary["total_equity"]
        
        difference = abs(assets - liabilities_plus_equity)
        tolerance = 0.01  # £0.01 tolerance for rounding
        
        is_balanced = difference <= tolerance
        
        return {
            "is_balanced": is_balanced,
            "assets": assets,
            "liabilities_plus_equity": liabilities_plus_equity,
            "difference": round(difference, 2),
            "status": "Balanced" if is_balanced else f"Unbalanced by £{difference:.2f}"
        }

    def _get_statutory_information(self, organization: Organization) -> Dict[str, Any]:
        """Get statutory information for UK compliance."""
        return {
            "company_number": getattr(organization, 'company_number', None),
            "registered_office": getattr(organization, 'registered_office', None),
            "directors": getattr(organization, 'directors', []),
            "auditors": getattr(organization, 'auditors', None),
            "accounting_reference_date": getattr(organization, 'year_end_date', None),
            "share_capital": getattr(organization, 'share_capital', None)
        }

    def _validate_organization(self) -> None:
        """Validate that organization exists and is accessible."""
        db = SessionLocal()
        try:
            organization = db.query(Organization).filter(
                Organization.id == self.organization_id
            ).first()
            
            if not organization:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} not found",
                    organization_id=self.organization_id
                )
            
            if not organization.is_active:
                raise ReportGenerationError(
                    f"Organization {self.organization_id} is not active",
                    organization_id=self.organization_id
                )
                
        finally:
            db.close()

    def _validate_generated_data(self, balance_sheet: Dict[str, Any]) -> None:
        """Validate the generated balance sheet data."""
        required_sections = ["header", "financial_data", "summary", "validation"]
        
        for section in required_sections:
            if section not in balance_sheet:
                raise ReportGenerationError(
                    f"Missing required section: {section}",
                    report_type="balance_sheet"
                )
        
        # Validate balance sheet equation
        validation = balance_sheet.get("validation", {})
        if not validation.get("is_balanced", False):
            logger.warning(
                f"Balance sheet is not balanced: {validation.get('status', 'Unknown error')}"
            )
        
        # Validate summary metrics
        summary = balance_sheet.get("summary", {})
        required_metrics = ["total_assets", "total_liabilities", "total_equity"]
        
        for metric in required_metrics:
            if not isinstance(summary.get(metric), (int, float)):
                raise ReportGenerationError(
                    f"Invalid {metric} in summary",
                    report_type="balance_sheet"
                )

    def _safe_decimal(self, value: Any) -> Decimal:
        """Safely convert value to Decimal."""
        if value is None:
            return Decimal("0")
        try:
            return Decimal(str(value))
        except (ValueError, TypeError):
            return Decimal("0")

    def __del__(self) -> None:
        """Cleanup resources."""
        if hasattr(self, 'uk_generator') and hasattr(self.uk_generator, 'db'):
            try:
                self.uk_generator.db.close()
            except:
                pass