"""
JSON formatter for financial reports.
"""

import json
from typing import Dict, Any
from datetime import datetime, date
from decimal import Decimal


class JSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for financial data types."""
    
    def default(self, obj: Any) -> Any:
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)


class JSONFormatter:
    """
    Formats financial reports as JSON.
    
    Handles special data types like Decimal and datetime objects.
    """
    
    def __init__(self) -> None:
        self.encoder = JSONEncoder
    
    def format(self, data: Dict[str, Any], **kwargs) -> str:
        """
        Format report data as JSON.
        
        Args:
            data: Report data dictionary
            **kwargs: Additional formatting options
            
        Returns:
            JSON string representation of the report
        """
        indent = kwargs.get('indent', 2)
        ensure_ascii = kwargs.get('ensure_ascii', False)
        
        return json.dumps(
            data,
            cls=self.encoder,
            indent=indent,
            ensure_ascii=ensure_ascii
        )