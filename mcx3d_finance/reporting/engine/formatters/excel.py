"""
Excel formatter for financial reports.
"""

import io
from typing import Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
from mcx3d_finance.core.logging_factory import LoggerFactory

try:
    import openpyxl
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    Workbook = None

logger = LoggerFactory.get_logger(__name__)


class ExcelFormatter:
    """
    Formats financial reports as Excel files.
    
    Generates structured Excel workbooks with formatting for professional presentation.
    """
    
    def __init__(self) -> None:
        if not EXCEL_AVAILABLE:
            raise ImportError("openpyxl is required for Excel formatting. Install with: pip install openpyxl")
        
        # Define styles
        self.header_font = Font(name='Arial', size=16, bold=True)
        self.subheader_font = Font(name='Arial', size=12, bold=True)
        self.section_font = Font(name='Arial', size=11, bold=True)
        self.normal_font = Font(name='Arial', size=10)
        
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.section_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
        self.total_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
        
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        self.center_alignment = Alignment(horizontal='center', vertical='center')
        self.right_alignment = Alignment(horizontal='right', vertical='center')
        self.left_alignment = Alignment(horizontal='left', vertical='center')
    
    def format(self, data: Dict[str, Any], **kwargs: Any) -> bytes:
        """
        Format report data as Excel file.
        
        Args:
            data: Report data dictionary
            **kwargs: Additional formatting options
            
        Returns:
            Excel file content as bytes
        """
        wb = Workbook()
        
        # Remove default sheet
        wb.remove(wb.active)
        
        # Create sheets based on report content
        if 'income_statement' in data:
            self._create_income_statement_sheet(wb, data['income_statement'], data.get('metadata', {}))
        
        if 'balance_sheet' in data:
            self._create_balance_sheet_sheet(wb, data['balance_sheet'], data.get('metadata', {}))
        
        if 'cash_flow' in data:
            self._create_cash_flow_sheet(wb, data['cash_flow'], data.get('metadata', {}))
        
        if 'kpis' in data:
            self._create_kpis_sheet(wb, data['kpis'], data.get('metadata', {}))
        
        # Save to bytes
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        return output.getvalue()
    
    def _create_income_statement_sheet(self, wb: Workbook, data: Dict[str, Any], metadata: Dict[str, Any]) -> None:
        """Create income statement worksheet."""
        ws = wb.create_sheet("Income Statement")
        
        # Add header
        row = self._add_header(ws, "Income Statement", metadata)
        
        # Revenue section
        row = self._add_section(ws, "Revenue", data.get('revenue', {}), row + 2, start_col=1)
        
        # Gross profit
        if 'gross_profit' in data:
            row = self._add_total_row(ws, "Gross Profit", data['gross_profit'], row + 1)
        
        # Operating expenses
        row = self._add_section(ws, "Operating Expenses", data.get('expenses', {}), row + 2, start_col=1)
        
        # Operating profit
        if 'operating_profit' in data:
            row = self._add_total_row(ws, "Operating Profit", data['operating_profit'], row + 1)
        
        # Net income
        if 'net_income' in data:
            row = self._add_total_row(ws, "Net Income", data['net_income'], row + 2)
        
        # Auto-fit columns
        self._autofit_columns(ws)
    
    def _create_balance_sheet_sheet(self, wb: Workbook, data: Dict[str, Any], metadata: Dict[str, Any]) -> None:
        """Create balance sheet worksheet."""
        ws = wb.create_sheet("Balance Sheet")
        
        # Add header
        row = self._add_header(ws, "Balance Sheet", metadata)
        
        # Assets section
        row = self._add_section(ws, "Assets", data.get('assets', {}), row + 2, start_col=1)
        
        # Total assets
        if 'total_assets' in data:
            row = self._add_total_row(ws, "Total Assets", data['total_assets'], row + 1)
        
        # Liabilities section
        row = self._add_section(ws, "Liabilities", data.get('liabilities', {}), row + 2, start_col=1)
        
        # Total liabilities
        if 'total_liabilities' in data:
            row = self._add_total_row(ws, "Total Liabilities", data['total_liabilities'], row + 1)
        
        # Equity section
        row = self._add_section(ws, "Equity", data.get('equity', {}), row + 2, start_col=1)
        
        # Total equity
        if 'total_equity' in data:
            row = self._add_total_row(ws, "Total Equity", data['total_equity'], row + 1)
        
        # Auto-fit columns
        self._autofit_columns(ws)
    
    def _create_cash_flow_sheet(self, wb: Workbook, data: Dict[str, Any], metadata: Dict[str, Any]) -> None:
        """Create cash flow statement worksheet."""
        ws = wb.create_sheet("Cash Flow")
        
        # Add header
        row = self._add_header(ws, "Cash Flow Statement", metadata)
        
        # Operating activities
        row = self._add_section(ws, "Operating Activities", data.get('operating', {}), row + 2, start_col=1)
        if 'net_operating' in data:
            row = self._add_total_row(ws, "Net Cash from Operating", data['net_operating'], row + 1)
        
        # Investing activities
        row = self._add_section(ws, "Investing Activities", data.get('investing', {}), row + 2, start_col=1)
        if 'net_investing' in data:
            row = self._add_total_row(ws, "Net Cash from Investing", data['net_investing'], row + 1)
        
        # Financing activities
        row = self._add_section(ws, "Financing Activities", data.get('financing', {}), row + 2, start_col=1)
        if 'net_financing' in data:
            row = self._add_total_row(ws, "Net Cash from Financing", data['net_financing'], row + 1)
        
        # Net change
        if 'net_change' in data:
            row = self._add_total_row(ws, "Net Change in Cash", data['net_change'], row + 2)
        
        # Auto-fit columns
        self._autofit_columns(ws)
    
    def _create_kpis_sheet(self, wb: Workbook, data: Dict[str, Any], metadata: Dict[str, Any]) -> None:
        """Create KPIs worksheet."""
        ws = wb.create_sheet("Key Metrics")
        
        # Add header
        row = self._add_header(ws, "Key Performance Indicators", metadata)
        
        # Add KPI sections
        for section_name, section_data in data.items():
            if isinstance(section_data, dict):
                row = self._add_section(ws, section_name.replace('_', ' ').title(), 
                                      section_data, row + 2, start_col=1)
        
        # Auto-fit columns
        self._autofit_columns(ws)
    
    def _add_header(self, ws: Any, title: str, metadata: Dict[str, Any]) -> int:
        """Add header section to worksheet."""
        # Organization name
        ws['A1'] = metadata.get('organization_name', '')
        ws['A1'].font = self.header_font
        ws['A1'].alignment = self.center_alignment
        ws.merge_cells('A1:C1')
        
        # Report title
        ws['A2'] = title
        ws['A2'].font = self.subheader_font
        ws['A2'].alignment = self.center_alignment
        ws.merge_cells('A2:C2')
        
        # Period
        ws['A3'] = f"Period: {metadata.get('period', '')}"
        ws['A3'].font = self.normal_font
        ws['A3'].alignment = self.center_alignment
        ws.merge_cells('A3:C3')
        
        # Generated date
        ws['A4'] = f"Generated: {metadata.get('generated_at', datetime.now().isoformat())}"
        ws['A4'].font = self.normal_font
        ws['A4'].alignment = self.center_alignment
        ws.merge_cells('A4:C4')
        
        return 5
    
    def _add_section(self, ws: Any, title: str, data: Dict[str, Any], 
                    start_row: int, start_col: int = 1, level: int = 0) -> int:
        """Add a section with line items to worksheet."""
        row = start_row
        
        # Section header
        cell = ws.cell(row=row, column=start_col)
        cell.value = title
        cell.font = self.section_font
        cell.fill = self.section_fill
        cell.border = self.thin_border
        
        # Merge header cells
        ws.merge_cells(start_row=row, start_column=start_col, 
                      end_row=row, end_column=start_col + 1)
        
        row += 1
        
        # Add line items
        for key, value in data.items():
            if isinstance(value, dict):
                # Nested section
                row = self._add_section(ws, key.replace('_', ' ').title(), 
                                      value, row, start_col + 1, level + 1)
            else:
                # Line item
                name_cell = ws.cell(row=row, column=start_col)
                name_cell.value = key.replace('_', ' ').title()
                name_cell.font = self.normal_font
                name_cell.border = self.thin_border
                
                value_cell = ws.cell(row=row, column=start_col + 1)
                value_cell.value = self._format_value(value)
                value_cell.font = self.normal_font
                value_cell.alignment = self.right_alignment
                value_cell.border = self.thin_border
                value_cell.number_format = '#,##0.00'
                
                row += 1
        
        return row
    
    def _add_total_row(self, ws: Any, label: str, value: Any, row: int) -> int:
        """Add a total row with special formatting."""
        name_cell = ws.cell(row=row, column=1)
        name_cell.value = label
        name_cell.font = Font(name='Arial', size=10, bold=True)
        name_cell.fill = self.total_fill
        name_cell.border = self.thin_border
        
        value_cell = ws.cell(row=row, column=2)
        value_cell.value = self._format_value(value)
        value_cell.font = Font(name='Arial', size=10, bold=True)
        value_cell.alignment = self.right_alignment
        value_cell.fill = self.total_fill
        value_cell.border = self.thin_border
        value_cell.number_format = '#,##0.00'
        
        return row + 1
    
    def _format_value(self, value: Any) -> Optional[float]:
        """Format values for Excel."""
        if isinstance(value, (int, float, Decimal)):
            return float(value)
        elif value is None:
            return 0
        else:
            try:
                return float(value)
            except (ValueError, TypeError):
                return 0
    
    def _autofit_columns(self, ws: Any) -> None:
        """Auto-fit column widths based on content."""
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width