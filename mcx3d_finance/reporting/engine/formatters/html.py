"""
HTML formatter for financial reports.
"""

from typing import Dict, Any
from datetime import datetime
from decimal import Decimal


class HTMLFormatter:
    """
    Formats financial reports as HTML.
    
    Generates structured HTML with CSS styling for professional presentation.
    """
    
    def __init__(self) -> None:
        self.css_style = """
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .report-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .report-date { font-size: 14px; color: #666; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .amount { text-align: right; }
            .total { font-weight: bold; background-color: #f9f9f9; }
            .section-header { font-weight: bold; background-color: #e0e0e0; }
        </style>
        """
    
    def format(self, data: Dict[str, Any], **kwargs: Any) -> str:
        """
        Format report data as HTML.
        
        Args:
            data: Report data dictionary
            **kwargs: Additional formatting options
            
        Returns:
            HTML string representation of the report
        """
        report_type = data.get('metadata', {}).get('report_type', 'Financial Report')
        organization_name = data.get('metadata', {}).get('organization_name', '')
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{report_type} - {organization_name}</title>
            {self.css_style}
        </head>
        <body>
        """
        
        # Add header
        html += self._format_header(data.get('metadata', {}))
        
        # Format the main content based on report type
        if 'income_statement' in data:
            html += self._format_income_statement(data['income_statement'])
        elif 'balance_sheet' in data:
            html += self._format_balance_sheet(data['balance_sheet'])
        elif 'cash_flow' in data:
            html += self._format_cash_flow(data['cash_flow'])
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def _format_header(self, metadata: Dict[str, Any]) -> str:
        """Format the report header."""
        report_type = metadata.get('report_type', 'Financial Report')
        organization_name = metadata.get('organization_name', '')
        period = metadata.get('period', '')
        generated_at = metadata.get('generated_at', datetime.now().isoformat())
        
        return f"""
        <div class="header">
            <div class="report-title">{organization_name}</div>
            <div class="report-title">{report_type}</div>
            <div class="report-date">Period: {period}</div>
            <div class="report-date">Generated: {generated_at}</div>
        </div>
        """
    
    def _format_income_statement(self, data: Dict[str, Any]) -> str:
        """Format income statement data."""
        html = "<h2>Income Statement</h2><table>"
        
        # Revenue section
        html += self._format_section("Revenue", data.get('revenue', {}))
        
        # Expenses section
        html += self._format_section("Operating Expenses", data.get('expenses', {}))
        
        # Totals
        html += f"""
        <tr class="total">
            <td>Net Income</td>
            <td class="amount">{self._format_amount(data.get('net_income', 0))}</td>
        </tr>
        """
        
        html += "</table>"
        return html
    
    def _format_balance_sheet(self, data: Dict[str, Any]) -> str:
        """Format balance sheet data."""
        html = "<h2>Balance Sheet</h2><table>"
        
        # Assets
        html += self._format_section("Assets", data.get('assets', {}))
        
        # Liabilities
        html += self._format_section("Liabilities", data.get('liabilities', {}))
        
        # Equity
        html += self._format_section("Equity", data.get('equity', {}))
        
        html += "</table>"
        return html
    
    def _format_cash_flow(self, data: Dict[str, Any]) -> str:
        """Format cash flow statement data."""
        html = "<h2>Cash Flow Statement</h2><table>"
        
        # Operating activities
        html += self._format_section("Operating Activities", data.get('operating', {}))
        
        # Investing activities
        html += self._format_section("Investing Activities", data.get('investing', {}))
        
        # Financing activities
        html += self._format_section("Financing Activities", data.get('financing', {}))
        
        # Net change
        html += f"""
        <tr class="total">
            <td>Net Change in Cash</td>
            <td class="amount">{self._format_amount(data.get('net_change', 0))}</td>
        </tr>
        """
        
        html += "</table>"
        return html
    
    def _format_section(self, title: str, items: Dict[str, Any]) -> str:
        """Format a section of line items."""
        html = f'<tr class="section-header"><td colspan="2">{title}</td></tr>'
        
        for key, value in items.items():
            if isinstance(value, dict):
                # Nested section
                html += self._format_section(key, value)
            else:
                # Line item
                html += f"""
                <tr>
                    <td>{key.replace('_', ' ').title()}</td>
                    <td class="amount">{self._format_amount(value)}</td>
                </tr>
                """
        
        return html
    
    def _format_amount(self, value: Any) -> str:
        """Format monetary amounts."""
        if isinstance(value, (int, float, Decimal)):
            return f"£{value:,.2f}"
        return str(value)