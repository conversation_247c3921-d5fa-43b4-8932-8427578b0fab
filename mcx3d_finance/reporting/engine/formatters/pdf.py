"""
PDF formatter for financial reports.
"""

import io
from typing import Dict, Any, List, Tuple
from datetime import datetime
from decimal import Decimal
from mcx3d_finance.core.logging_factory import LoggerFactory

try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
    from reportlab.platypus.tableofcontents import TableOfContents
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

logger = LoggerFactory.get_logger(__name__)


class PDFFormatter:
    """
    Formats financial reports as PDF files.
    
    Generates professional PDF documents with tables and formatting.
    """
    
    def __init__(self) -> None:
        if not PDF_AVAILABLE:
            raise ImportError("reportlab is required for PDF formatting. Install with: pip install reportlab")
        
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self) -> None:
        """Set up custom paragraph styles."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            textColor=colors.HexColor('#1f4788'),
            spaceAfter=30,
            alignment=1  # Center
        ))
        
        # Subtitle style
        self.styles.add(ParagraphStyle(
            name='Subtitle',
            parent=self.styles['Normal'],
            fontSize=14,
            textColor=colors.HexColor('#666666'),
            spaceAfter=12,
            alignment=1  # Center
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading1'],
            fontSize=16,
            textColor=colors.HexColor('#1f4788'),
            spaceAfter=12
        ))
        
        # Table header style
        self.styles.add(ParagraphStyle(
            name='TableHeader',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.white,
            alignment=1
        ))
    
    def format(self, data: Dict[str, Any], **kwargs: Any) -> bytes:
        """
        Format report data as PDF.
        
        Args:
            data: Report data dictionary
            **kwargs: Additional formatting options
            
        Returns:
            PDF file content as bytes
        """
        # Create PDF in memory
        buffer = io.BytesIO()
        
        # Get page size (default to A4)
        pagesize = kwargs.get('pagesize', A4)
        
        # Create document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=pagesize,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18,
        )
        
        # Build content
        story = []
        
        # Add header
        story.extend(self._create_header(data.get('metadata', {})))
        
        # Add content based on report type
        if 'income_statement' in data:
            story.extend(self._create_income_statement(data['income_statement']))
        
        if 'balance_sheet' in data:
            if story:  # Add page break if not first section
                story.append(PageBreak())
            story.extend(self._create_balance_sheet(data['balance_sheet']))
        
        if 'cash_flow' in data:
            if story:  # Add page break if not first section
                story.append(PageBreak())
            story.extend(self._create_cash_flow(data['cash_flow']))
        
        if 'kpis' in data:
            if story:  # Add page break if not first section
                story.append(PageBreak())
            story.extend(self._create_kpis(data['kpis']))
        
        # Build PDF
        doc.build(story)
        
        # Get PDF content
        buffer.seek(0)
        return buffer.getvalue()
    
    def _create_header(self, metadata: Dict[str, Any]) -> List[Any]:
        """Create document header."""
        story = []
        
        # Organization name
        if 'organization_name' in metadata:
            story.append(Paragraph(metadata['organization_name'], self.styles['CustomTitle']))
        
        # Report type
        report_type = metadata.get('report_type', 'Financial Report')
        story.append(Paragraph(report_type, self.styles['Subtitle']))
        
        # Period
        if 'period' in metadata:
            story.append(Paragraph(f"Period: {metadata['period']}", self.styles['Subtitle']))
        
        # Generated date
        generated_at = metadata.get('generated_at', datetime.now().isoformat())
        story.append(Paragraph(f"Generated: {generated_at}", self.styles['Subtitle']))
        
        story.append(Spacer(1, 0.5 * inch))
        
        return story
    
    def _create_income_statement(self, data: Dict[str, Any]) -> List[Any]:
        """Create income statement section."""
        story = []
        
        story.append(Paragraph("Income Statement", self.styles['SectionHeader']))
        story.append(Spacer(1, 0.2 * inch))
        
        # Create table data
        table_data = []
        
        # Revenue section
        table_data.extend(self._create_section_rows("Revenue", data.get('revenue', {})))
        
        # Gross profit
        if 'gross_profit' in data:
            table_data.append(['', ''])  # Empty row
            table_data.append(['Gross Profit', self._format_amount(data['gross_profit'])])
        
        # Operating expenses
        table_data.append(['', ''])  # Empty row
        table_data.extend(self._create_section_rows("Operating Expenses", data.get('expenses', {})))
        
        # Operating profit
        if 'operating_profit' in data:
            table_data.append(['', ''])  # Empty row
            table_data.append(['Operating Profit', self._format_amount(data['operating_profit'])])
        
        # Net income
        if 'net_income' in data:
            table_data.append(['', ''])  # Empty row
            table_data.append(['Net Income', self._format_amount(data['net_income'])])
        
        # Create table
        table = self._create_financial_table(table_data)
        story.append(table)
        
        return story
    
    def _create_balance_sheet(self, data: Dict[str, Any]) -> List[Any]:
        """Create balance sheet section."""
        story = []
        
        story.append(Paragraph("Balance Sheet", self.styles['SectionHeader']))
        story.append(Spacer(1, 0.2 * inch))
        
        # Create table data
        table_data = []
        
        # Assets section
        table_data.extend(self._create_section_rows("Assets", data.get('assets', {})))
        
        # Total assets
        if 'total_assets' in data:
            table_data.append(['', ''])  # Empty row
            table_data.append(['Total Assets', self._format_amount(data['total_assets'])])
        
        # Liabilities section
        table_data.append(['', ''])  # Empty row
        table_data.extend(self._create_section_rows("Liabilities", data.get('liabilities', {})))
        
        # Total liabilities
        if 'total_liabilities' in data:
            table_data.append(['', ''])  # Empty row
            table_data.append(['Total Liabilities', self._format_amount(data['total_liabilities'])])
        
        # Equity section
        table_data.append(['', ''])  # Empty row
        table_data.extend(self._create_section_rows("Equity", data.get('equity', {})))
        
        # Total equity
        if 'total_equity' in data:
            table_data.append(['', ''])  # Empty row
            table_data.append(['Total Equity', self._format_amount(data['total_equity'])])
        
        # Create table
        table = self._create_financial_table(table_data)
        story.append(table)
        
        return story
    
    def _create_cash_flow(self, data: Dict[str, Any]) -> List[Any]:
        """Create cash flow statement section."""
        story = []
        
        story.append(Paragraph("Cash Flow Statement", self.styles['SectionHeader']))
        story.append(Spacer(1, 0.2 * inch))
        
        # Create table data
        table_data = []
        
        # Operating activities
        table_data.extend(self._create_section_rows("Operating Activities", data.get('operating', {})))
        if 'net_operating' in data:
            table_data.append(['Net Cash from Operating', self._format_amount(data['net_operating'])])
        
        # Investing activities
        table_data.append(['', ''])  # Empty row
        table_data.extend(self._create_section_rows("Investing Activities", data.get('investing', {})))
        if 'net_investing' in data:
            table_data.append(['Net Cash from Investing', self._format_amount(data['net_investing'])])
        
        # Financing activities
        table_data.append(['', ''])  # Empty row
        table_data.extend(self._create_section_rows("Financing Activities", data.get('financing', {})))
        if 'net_financing' in data:
            table_data.append(['Net Cash from Financing', self._format_amount(data['net_financing'])])
        
        # Net change
        if 'net_change' in data:
            table_data.append(['', ''])  # Empty row
            table_data.append(['Net Change in Cash', self._format_amount(data['net_change'])])
        
        # Create table
        table = self._create_financial_table(table_data)
        story.append(table)
        
        return story
    
    def _create_kpis(self, data: Dict[str, Any]) -> List[Any]:
        """Create KPIs section."""
        story = []
        
        story.append(Paragraph("Key Performance Indicators", self.styles['SectionHeader']))
        story.append(Spacer(1, 0.2 * inch))
        
        # Create separate tables for each KPI section
        for section_name, section_data in data.items():
            if isinstance(section_data, dict):
                story.append(Paragraph(section_name.replace('_', ' ').title(), 
                                     self.styles['Heading2']))
                story.append(Spacer(1, 0.1 * inch))
                
                table_data = self._create_kpi_rows(section_data)
                table = self._create_kpi_table(table_data)
                story.append(table)
                story.append(Spacer(1, 0.2 * inch))
        
        return story
    
    def _create_section_rows(self, title: str, data: Dict[str, Any], 
                           indent_level: int = 0) -> List[List[str]]:
        """Create table rows for a section."""
        rows = []
        
        # Section header
        indent = "  " * indent_level
        rows.append([f"{indent}{title}", ""])
        
        # Line items
        for key, value in data.items():
            if isinstance(value, dict):
                # Nested section
                rows.extend(self._create_section_rows(
                    key.replace('_', ' ').title(), 
                    value, 
                    indent_level + 1
                ))
            else:
                # Line item
                indent = "  " * (indent_level + 1)
                rows.append([
                    f"{indent}{key.replace('_', ' ').title()}",
                    self._format_amount(value)
                ])
        
        return rows
    
    def _create_kpi_rows(self, data: Dict[str, Any]) -> List[List[str]]:
        """Create table rows for KPI data."""
        rows = []
        
        for key, value in data.items():
            label = key.replace('_', ' ').title()
            formatted_value = self._format_kpi_value(key, value)
            rows.append([label, formatted_value])
        
        return rows
    
    def _create_financial_table(self, data: List[List[str]]) -> Table:
        """Create a formatted financial table."""
        table = Table(data, colWidths=[4 * inch, 2 * inch])
        
        # Apply table style
        style = TableStyle([
            # Headers and section rows
            ('FONT', (0, 0), (-1, -1), 'Helvetica', 10),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            
            # Section headers (rows with empty second column)
            ('FONT', (0, 0), (0, 0), 'Helvetica-Bold', 11),
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#e0e0e0')),
            
            # Grid
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ])
        
        # Apply bold formatting to total rows
        for i, row in enumerate(data):
            if row[0] and ('Total' in row[0] or 'Net' in row[0] or 'Profit' in row[0] or 'Income' in row[0]):
                style.add('FONT', (0, i), (-1, i), 'Helvetica-Bold', 10)
                style.add('BACKGROUND', (0, i), (-1, i), colors.HexColor('#f0f0f0'))
        
        table.setStyle(style)
        return table
    
    def _create_kpi_table(self, data: List[List[str]]) -> Table:
        """Create a formatted KPI table."""
        table = Table(data, colWidths=[3 * inch, 3 * inch])
        
        # Apply table style
        style = TableStyle([
            ('FONT', (0, 0), (-1, -1), 'Helvetica', 10),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ])
        
        table.setStyle(style)
        return table
    
    def _format_amount(self, value: Any) -> str:
        """Format monetary amounts."""
        if isinstance(value, (int, float, Decimal)):
            # Format with thousands separator
            return f"£{value:,.2f}"
        elif value is None:
            return "£0.00"
        else:
            return str(value)
    
    def _format_kpi_value(self, key: str, value: Any) -> str:
        """Format KPI values based on their type."""
        if value is None:
            return "N/A"
        
        # Percentage values
        if any(term in key.lower() for term in ['rate', 'ratio', 'margin', 'growth']):
            if isinstance(value, (int, float, Decimal)):
                return f"{value:.2%}"
        
        # Monetary values
        elif any(term in key.lower() for term in ['revenue', 'cost', 'value', 'price']):
            if isinstance(value, (int, float, Decimal)):
                return f"£{value:,.2f}"
        
        # Count/quantity values
        elif any(term in key.lower() for term in ['count', 'number', 'days']):
            if isinstance(value, (int, float, Decimal)):
                return f"{value:,.0f}"
        
        # Default formatting
        else:
            if isinstance(value, (int, float, Decimal)):
                return f"{value:,.2f}"
            else:
                return str(value)