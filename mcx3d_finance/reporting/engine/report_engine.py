"""
Central Report Generation Engine

Orchestrates all financial report generation with unified interface, 
eliminating duplicated logic across CLI, API, and Task layers.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path
from enum import Enum

from mcx3d_finance.reporting.utils.period_parser import PeriodParser
from mcx3d_finance.reporting.utils.output_manager import OutputManager
from mcx3d_finance.reporting.utils.validators import ReportValidator
from mcx3d_finance.reporting.utils.cache_manager import CacheManager
from mcx3d_finance.reporting.config.settings import ReportSettings

logger = LoggerFactory.get_logger(__name__)


class ReportType(Enum):
    """Supported report types."""
    INCOME_STATEMENT = "income_statement"
    BALANCE_SHEET = "balance_sheet"
    CASH_FLOW = "cash_flow"
    COMPREHENSIVE = "comprehensive"


class OutputFormat(Enum):
    """Supported output formats."""
    PDF = "pdf"
    EXCEL = "excel"
    JSON = "json"
    HTML = "html"
    CSV = "csv"


class ReportEngine:
    """
    Central engine for all financial report generation.
    
    Provides unified interface that eliminates code duplication across
    CLI, API, and Task layers.
    """

    def __init__(
        self,
        organization_id: int,
        enable_caching: bool = True,
        enable_validation: bool = True,
        cache_ttl: int = 3600
    ):
        self.organization_id = organization_id
        self.period_parser = PeriodParser()
        self.output_manager = OutputManager()
        self.validator = ReportValidator() if enable_validation else None
        self.cache_manager = CacheManager(enabled=enable_caching, ttl=cache_ttl)
        self.settings = ReportSettings()
        
        # Lazy-loaded generators
        self._generators = {}
        self._formatters = {}

    def generate_report(
        self,
        report_type: Union[ReportType, str],
        format_type: Union[OutputFormat, str] = OutputFormat.JSON,
        period_start: Optional[Union[str, datetime]] = None,
        period_end: Optional[Union[str, datetime]] = None,
        as_of_date: Optional[Union[str, datetime]] = None,
        output_path: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate any financial report with unified interface.
        
        Args:
            report_type: Type of report to generate
            format_type: Output format
            period_start: Period start (for period-based reports)
            period_end: Period end (for period-based reports)
            as_of_date: As-of date (for point-in-time reports)
            output_path: Custom output path
            **kwargs: Additional generator-specific parameters
            
        Returns:
            Dictionary with generation results and metadata
        """
        try:
            # Normalize enums
            if isinstance(report_type, str):
                report_type = ReportType(report_type)
            if isinstance(format_type, str):
                format_type = OutputFormat(format_type)
            
            # Validate inputs
            if self.validator:
                self.validator.validate_request(
                    report_type=report_type,
                    format_type=format_type,
                    organization_id=self.organization_id,
                    period_start=period_start,
                    period_end=period_end,
                    as_of_date=as_of_date
                )
            
            # Parse dates
            dates = self._parse_dates(
                report_type=report_type,
                period_start=period_start,
                period_end=period_end,
                as_of_date=as_of_date
            )
            
            # Check cache
            cache_key = self._generate_cache_key(
                report_type, format_type, dates, kwargs
            )
            
            if format_type == OutputFormat.JSON:
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    logger.info(f"Returning cached {report_type.value} report")
                    return cached_result

            # Generate output path
            if not output_path:
                output_path = self.output_manager.generate_path(
                    organization_id=self.organization_id,
                    report_type=report_type,
                    format_type=format_type,
                    dates=dates
                )
            
            # Get generator and generate data
            generator = self._get_generator(report_type)
            report_data = generator.generate(dates=dates, **kwargs)
            
            # Format output
            formatter = self._get_formatter(format_type)
            result = formatter.format(
                data=report_data,
                output_path=output_path,
                report_type=report_type,
                organization_id=self.organization_id
            )
            
            # Cache result for JSON format
            if format_type == OutputFormat.JSON:
                self.cache_manager.set(cache_key, result)
            
            logger.info(
                f"Generated {report_type.value} report in {format_type.value} format: {output_path}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating {report_type.value} report: {e}")
            raise

    def _parse_dates(
        self,
        report_type: ReportType,
        period_start: Optional[Union[str, datetime]] = None,
        period_end: Optional[Union[str, datetime]] = None,
        as_of_date: Optional[Union[str, datetime]] = None
    ) -> Dict[str, datetime]:
        """Parse and validate dates based on report type."""
        if report_type == ReportType.BALANCE_SHEET:
            if as_of_date:
                return {"as_of_date": self.period_parser.parse_date(as_of_date)}
            else:
                return {"as_of_date": datetime.now()}
        else:
            # Period-based reports
            if period_start and period_end:
                return {
                    "period_start": self.period_parser.parse_date(period_start),
                    "period_end": self.period_parser.parse_date(period_end)
                }
            elif period_start:  # Assume single period string like "2023-Q4"
                start, end = self.period_parser.parse_period(period_start)
                return {"period_start": start, "period_end": end}
            else:
                # Default to current year
                return self.period_parser.get_current_year_period()

    def _generate_cache_key(
        self,
        report_type: ReportType,
        format_type: OutputFormat,
        dates: Dict[str, datetime],
        kwargs: Dict[str, Any]
    ) -> str:
        """Generate cache key for report."""
        import hashlib
        
        key_parts = [
            str(self.organization_id),
            report_type.value,
            format_type.value,
        ]
        
        # Add date components
        for date_key, date_val in dates.items():
            key_parts.append(f"{date_key}:{date_val.isoformat()}")
        
        # Add kwargs
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _get_generator(self, report_type: ReportType) -> Any:
        """Get report generator, lazy-loading as needed."""
        if report_type not in self._generators:
            if report_type == ReportType.INCOME_STATEMENT:
                from mcx3d_finance.reporting.engine.generators.income_statement import IncomeStatementGenerator
                self._generators[report_type] = IncomeStatementGenerator(self.organization_id)
            elif report_type == ReportType.BALANCE_SHEET:
                from mcx3d_finance.reporting.engine.generators.balance_sheet import BalanceSheetGenerator
                self._generators[report_type] = BalanceSheetGenerator(self.organization_id)
            elif report_type == ReportType.CASH_FLOW:
                from mcx3d_finance.reporting.engine.generators.cash_flow import CashFlowGenerator
                self._generators[report_type] = CashFlowGenerator(self.organization_id)
            elif report_type == ReportType.COMPREHENSIVE:
                from mcx3d_finance.reporting.engine.generators.comprehensive import ComprehensiveReportGenerator
                self._generators[report_type] = ComprehensiveReportGenerator(self.organization_id)
            else:
                raise ValueError(f"Unsupported report type: {report_type}")
        
        return self._generators[report_type]

    def _get_formatter(self, format_type: OutputFormat) -> Any:
        """Get output formatter, lazy-loading as needed."""
        if format_type not in self._formatters:
            if format_type == OutputFormat.PDF:
                from mcx3d_finance.reporting.engine.formatters.pdf import PDFFormatter
                self._formatters[format_type] = PDFFormatter()
            elif format_type == OutputFormat.EXCEL:
                from mcx3d_finance.reporting.engine.formatters.excel import ExcelFormatter
                self._formatters[format_type] = ExcelFormatter()
            elif format_type == OutputFormat.JSON:
                from mcx3d_finance.reporting.engine.formatters.json import JSONFormatter
                self._formatters[format_type] = JSONFormatter()
            elif format_type == OutputFormat.HTML:
                from mcx3d_finance.reporting.engine.formatters.html import HTMLFormatter
                self._formatters[format_type] = HTMLFormatter()
            else:
                raise ValueError(f"Unsupported format type: {format_type}")
        
        return self._formatters[format_type]

    def list_available_reports(self) -> List[str]:
        """List all available report types."""
        return [report_type.value for report_type in ReportType]

    def list_available_formats(self) -> List[str]:
        """List all available output formats."""
        return [format_type.value for format_type in OutputFormat]

    def clear_cache(self, report_type: Optional[ReportType] = None) -> None:
        """Clear report cache."""
        self.cache_manager.clear(pattern=f"*{self.organization_id}*{report_type.value if report_type else ''}*")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache usage statistics."""
        return self.cache_manager.get_stats()