"""
Generates a comprehensive financial report meeting stock market standards.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from datetime import datetime
from pathlib import Path
import json
from typing import Dict, Any, Union
from decimal import Decimal

from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator
from mcx3d_finance.core.financials.balance_sheet import UKBalanceSheetGenerator
from mcx3d_finance.core.financials.cash_flow import UKCashFlowGenerator
from mcx3d_finance.reporting.financial_ratios import calculate_financial_ratios
from mcx3d_finance.integrations.xero_client import XeroClient
from mcx3d_finance.company.company_info import COMPANY_INFO
import datetime

logger = LoggerFactory.get_logger(__name__)


def json_serial(obj: Any) -> Union[str, float]:
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    if isinstance(obj, Decimal):
        return float(obj)
    raise TypeError("Type %s not serializable" % type(obj))

class ComprehensiveReportGenerator:
    """
    Orchestrates the generation of a comprehensive financial report.
    """

    def __init__(self, organization_id: int, year: int, use_xero_trial_balance: bool = False) -> None:
        self.organization_id = organization_id
        self.year = year
        self.xero_client = XeroClient(organization_id)
        self.use_xero_trial_balance = use_xero_trial_balance

    def generate(self) -> Dict[str, Any]:
        """
        Generates the full comprehensive report.
        """
        # 1. Get Trial Balances
        end_date = datetime.datetime(self.year, 12, 31)
        previous_end_date = datetime.datetime(self.year - 1, 12, 31)

        # 2. Generate Financial Statements
        income_statement_generator = UKProfitAndLossGenerator(self.organization_id)
        balance_sheet_generator = UKBalanceSheetGenerator(self.organization_id)
        cash_flow_generator = UKCashFlowGenerator(self.organization_id)

        income_statement = income_statement_generator.generate_profit_and_loss(
            datetime.datetime(self.year, 1, 1), end_date
        )
        balance_sheet = balance_sheet_generator.generate_balance_sheet(end_date)
        cash_flow_statement = cash_flow_generator.generate_cash_flow_statement(
            datetime.datetime(self.year, 1, 1), end_date
        )
        
        previous_income_statement = income_statement_generator.generate_profit_and_loss(
            datetime.datetime(self.year - 1, 1, 1), previous_end_date
        )
        previous_balance_sheet = balance_sheet_generator.generate_balance_sheet(previous_end_date)

        # 3. Calculate Ratios
        financial_ratios = calculate_financial_ratios(
            income_statement, balance_sheet, cash_flow_statement
        )

        # 4. Assemble the report
        report = self._build_report_structure(
            income_statement,
            balance_sheet,
            cash_flow_statement,
            previous_income_statement,
            previous_balance_sheet,
            financial_ratios,
        )

        return report

    def _build_report_structure(
        self,
        income_statement,
        balance_sheet,
        cash_flow_statement,
        previous_income_statement,
        previous_balance_sheet,
        financial_ratios,
    ) -> Dict[str, Any]:
        """
        Builds the final comprehensive report JSON structure.
        """
        report = {
            "company_info": COMPANY_INFO,
            "reporting_period": {
                "year": self.year,
                "end_date": datetime.datetime(self.year, 12, 31).strftime("%Y-%m-%d"),
            },
            "financial_statements": {
                "income_statement": income_statement,
                "balance_sheet": balance_sheet,
                "cash_flow_statement": cash_flow_statement,
            },
            "comparative_data": {
                "previous_year": {
                    "income_statement": previous_income_statement,
                    "balance_sheet": previous_balance_sheet,
                }
            },
            "financial_ratios": financial_ratios,
            "executive_summary": self._generate_executive_summary(financial_ratios),
            "notes_to_statements": ["Placeholder for notes to financial statements."],
            "auditors_report": "Placeholder for auditor's report.",
        }
        return report

    def _generate_executive_summary(self, ratios: Dict[str, float]) -> str:
        """
        Generates a brief executive summary based on key ratios.
        """
        summary = f"Financial performance for {self.year}:\\n"
        summary += f"- Net Profit Margin: {ratios['net_profit_margin']:.2%}\\n"
        summary += f"- Return on Equity: {ratios['return_on_equity']:.2%}\\n"
        summary += f"- Debt-to-Equity Ratio: {ratios['debt_to_equity_ratio']:.2f}\\n"
        summary += f"- Current Ratio: {ratios['current_ratio']:.2f}\\n"
        return summary

    def save_reports(self, report: Dict[str, Any]) -> None:
        """
        Saves the comprehensive report and its components to disk.
        """
        output_dir = Path(f"reports/{self.year}")
        output_dir.mkdir(parents=True, exist_ok=True)

        # Save individual components
        with open(output_dir / f"income_statement_{self.year}.json", "w") as f:
            json.dump(report["financial_statements"]["income_statement"], f, indent=2, default=json_serial)
        with open(output_dir / f"balance_sheet_{self.year}.json", "w") as f:
            json.dump(report["financial_statements"]["balance_sheet"], f, indent=2, default=json_serial)
        with open(output_dir / f"cash_flow_statement_{self.year}.json", "w") as f:
            json.dump(report["financial_statements"]["cash_flow_statement"], f, indent=2, default=json_serial)
        with open(output_dir / f"executive_summary_{self.year}.json", "w") as f:
            json.dump({"summary": report["executive_summary"]}, f, indent=2, default=json_serial)

        # Save the main comprehensive report
        with open(output_dir / f"comprehensive_financial_report_{self.year}.json", "w") as f:
            json.dump(report, f, indent=2, default=json_serial)

        logger.info(f"Reports saved to {output_dir}")