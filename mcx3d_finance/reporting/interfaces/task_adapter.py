"""
Task adapter for the consolidated report generation engine.

Provides Celery task-compatible interface for async report generation
using the new unified engine internally.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional, List
from datetime import datetime
import os
import traceback

from mcx3d_finance.reporting.engine.report_engine import ReportEngine, ReportType, OutputFormat
from mcx3d_finance.exceptions.reporting import ReportGenerationError
from mcx3d_finance.utils.audit_logger import AuditLogger

logger = LoggerFactory.get_logger(__name__)


class TaskReportAdapter:
    """
    Adapter that provides Celery task-compatible interface to the unified report engine.
    
    This allows existing Celery tasks to be gradually migrated to use the new
    consolidated system while maintaining backward compatibility.
    """

    def __init__(self, organization_id: int, task_id: Optional[str] = None) -> None:
        self.organization_id = organization_id
        self.task_id = task_id
        self.audit_logger = AuditLogger()
        self.engine = ReportEngine(organization_id)

    def execute_income_statement_task(
        self,
        period_start: str,
        period_end: str,
        output_format: str = "pdf",
        output_path: Optional[str] = None,
        complexity: Optional[str] = None,
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute income statement generation task.
        
        Args:
            period_start: Period start date (YYYY-MM-DD or period string)
            period_end: Period end date (YYYY-MM-DD or empty for period parsing)
            output_format: Output format (pdf, excel, json, html)
            output_path: Custom output path
            complexity: Report complexity level
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            **kwargs: Additional task parameters
            
        Returns:
            Task execution result
        """
        try:
            logger.info(
                f"Task {self.task_id}: Starting income statement generation for org {self.organization_id}"
            )
            
            # Audit log task start
            self.audit_logger.log_event(
                event_type="task_started",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "income_statement_generation",
                    "period_start": period_start,
                    "period_end": period_end,
                    "output_format": output_format
                }
            )
            
            start_time = datetime.now()
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.INCOME_STATEMENT,
                format_type=OutputFormat(output_format.upper()),
                period_start=period_start,
                period_end=period_end if period_end else None,
                output_path=output_path,
                complexity=complexity,
                comparative_period=comparative_period,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Transform to task-compatible response
            task_result = self._transform_to_task_response(
                result, "income_statement", execution_time
            )
            
            # Audit log task completion
            self.audit_logger.log_event(
                event_type="task_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "income_statement_generation",
                    "execution_time": execution_time,
                    "output_file": result.get("output_path"),
                    "file_size": result.get("file_size_bytes", 0)
                }
            )
            
            logger.info(
                f"Task {self.task_id}: Successfully completed income statement generation "
                f"in {execution_time:.2f} seconds"
            )
            
            return task_result
            
        except Exception as e:
            # Audit log task failure
            self.audit_logger.log_event(
                event_type="task_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "income_statement_generation",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
            )
            
            logger.error(f"Task {self.task_id}: Income statement generation failed: {e}")
            raise self._transform_to_task_exception(e, "income_statement")

    def execute_balance_sheet_task(
        self,
        as_of_date: str,
        output_format: str = "pdf",
        output_path: Optional[str] = None,
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute balance sheet generation task.
        
        Args:
            as_of_date: Balance sheet date (YYYY-MM-DD)
            output_format: Output format (pdf, excel, json, html)
            output_path: Custom output path
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            **kwargs: Additional task parameters
            
        Returns:
            Task execution result
        """
        try:
            logger.info(
                f"Task {self.task_id}: Starting balance sheet generation for org {self.organization_id}"
            )
            
            # Audit log task start
            self.audit_logger.log_event(
                event_type="task_started",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "balance_sheet_generation",
                    "as_of_date": as_of_date,
                    "output_format": output_format
                }
            )
            
            start_time = datetime.now()
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.BALANCE_SHEET,
                format_type=OutputFormat(output_format.upper()),
                as_of_date=as_of_date,
                output_path=output_path,
                comparative_period=comparative_period,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Transform to task-compatible response
            task_result = self._transform_to_task_response(
                result, "balance_sheet", execution_time
            )
            
            # Audit log task completion
            self.audit_logger.log_event(
                event_type="task_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "balance_sheet_generation",
                    "execution_time": execution_time,
                    "output_file": result.get("output_path"),
                    "file_size": result.get("file_size_bytes", 0)
                }
            )
            
            logger.info(
                f"Task {self.task_id}: Successfully completed balance sheet generation "
                f"in {execution_time:.2f} seconds"
            )
            
            return task_result
            
        except Exception as e:
            # Audit log task failure
            self.audit_logger.log_event(
                event_type="task_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "balance_sheet_generation",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
            )
            
            logger.error(f"Task {self.task_id}: Balance sheet generation failed: {e}")
            raise self._transform_to_task_exception(e, "balance_sheet")

    def execute_cash_flow_task(
        self,
        period_start: str,
        period_end: str,
        output_format: str = "pdf",
        method: str = "indirect",
        output_path: Optional[str] = None,
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute cash flow statement generation task.
        
        Args:
            period_start: Period start date (YYYY-MM-DD or period string)
            period_end: Period end date (YYYY-MM-DD or empty for period parsing)
            output_format: Output format (pdf, excel, json, html)
            method: Cash flow method (indirect or direct)
            output_path: Custom output path
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include cash flow ratios
            **kwargs: Additional task parameters
            
        Returns:
            Task execution result
        """
        try:
            logger.info(
                f"Task {self.task_id}: Starting cash flow generation for org {self.organization_id}"
            )
            
            # Audit log task start
            self.audit_logger.log_event(
                event_type="task_started",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "cash_flow_generation",
                    "period_start": period_start,
                    "period_end": period_end,
                    "method": method,
                    "output_format": output_format
                }
            )
            
            start_time = datetime.now()
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.CASH_FLOW,
                format_type=OutputFormat(output_format.upper()),
                period_start=period_start,
                period_end=period_end if period_end else None,
                output_path=output_path,
                method=method,
                comparative_period=comparative_period,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Transform to task-compatible response
            task_result = self._transform_to_task_response(
                result, "cash_flow", execution_time
            )
            
            # Audit log task completion
            self.audit_logger.log_event(
                event_type="task_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "cash_flow_generation",
                    "execution_time": execution_time,
                    "output_file": result.get("output_path"),
                    "file_size": result.get("file_size_bytes", 0)
                }
            )
            
            logger.info(
                f"Task {self.task_id}: Successfully completed cash flow generation "
                f"in {execution_time:.2f} seconds"
            )
            
            return task_result
            
        except Exception as e:
            # Audit log task failure
            self.audit_logger.log_event(
                event_type="task_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "cash_flow_generation",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
            )
            
            logger.error(f"Task {self.task_id}: Cash flow generation failed: {e}")
            raise self._transform_to_task_exception(e, "cash_flow")

    def execute_comprehensive_report_task(
        self,
        period_start: str,
        period_end: str,
        formats: Optional[List[str]] = None,
        output_directory: Optional[str] = None,
        include_management_analysis: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute comprehensive report generation task.
        
        Args:
            period_start: Period start date (YYYY-MM-DD or period string)
            period_end: Period end date (YYYY-MM-DD or empty for period parsing)
            formats: List of output formats to generate
            output_directory: Custom output directory
            include_management_analysis: Include management discussion & analysis
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            **kwargs: Additional task parameters
            
        Returns:
            Task execution result
        """
        try:
            logger.info(
                f"Task {self.task_id}: Starting comprehensive report generation for org {self.organization_id}"
            )
            
            # Audit log task start
            self.audit_logger.log_event(
                event_type="task_started",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "comprehensive_report_generation",
                    "period_start": period_start,
                    "period_end": period_end,
                    "formats": formats or ["json"],
                    "include_management_analysis": include_management_analysis
                }
            )
            
            start_time = datetime.now()
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.COMPREHENSIVE,
                format_type=OutputFormat.JSON,  # Comprehensive always starts with JSON
                period_start=period_start,
                period_end=period_end if period_end else None,
                as_of_date=period_end if period_end else None,
                output_path=output_directory,
                formats=formats or ["json"],
                include_management_analysis=include_management_analysis,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Transform to task-compatible response
            task_result = self._transform_to_task_response(
                result, "comprehensive", execution_time
            )
            
            # Audit log task completion
            self.audit_logger.log_event(
                event_type="task_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "comprehensive_report_generation",
                    "execution_time": execution_time,
                    "output_directory": result.get("output_path"),
                    "formats_generated": formats or ["json"]
                }
            )
            
            logger.info(
                f"Task {self.task_id}: Successfully completed comprehensive report generation "
                f"in {execution_time:.2f} seconds"
            )
            
            return task_result
            
        except Exception as e:
            # Audit log task failure
            self.audit_logger.log_event(
                event_type="task_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "comprehensive_report_generation",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
            )
            
            logger.error(f"Task {self.task_id}: Comprehensive report generation failed: {e}")
            raise self._transform_to_task_exception(e, "comprehensive")

    def execute_bulk_report_task(
        self,
        report_configs: List[Dict[str, Any]],
        output_directory: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute bulk report generation task.
        
        Args:
            report_configs: List of report configuration dictionaries
            output_directory: Base output directory for all reports
            **kwargs: Additional task parameters
            
        Returns:
            Bulk task execution result
        """
        try:
            logger.info(
                f"Task {self.task_id}: Starting bulk report generation for org {self.organization_id} "
                f"({len(report_configs)} reports)"
            )
            
            # Audit log task start
            self.audit_logger.log_event(
                event_type="task_started",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "bulk_report_generation",
                    "report_count": len(report_configs),
                    "report_types": [config.get("report_type") for config in report_configs]
                }
            )
            
            start_time = datetime.now()
            results = []
            successful_reports = 0
            failed_reports = 0
            
            for i, config in enumerate(report_configs):
                try:
                    logger.info(f"Task {self.task_id}: Processing report {i+1}/{len(report_configs)}")
                    
                    # Extract report configuration
                    report_type = config.get("report_type")
                    
                    if report_type == "income_statement":
                        result = self.execute_income_statement_task(**config)
                    elif report_type == "balance_sheet":
                        result = self.execute_balance_sheet_task(**config)
                    elif report_type == "cash_flow":
                        result = self.execute_cash_flow_task(**config)
                    elif report_type == "comprehensive":
                        result = self.execute_comprehensive_report_task(**config)
                    else:
                        raise ValueError(f"Unsupported report type: {report_type}")
                    
                    results.append({
                        "report_index": i,
                        "report_type": report_type,
                        "status": "success",
                        "result": result
                    })
                    successful_reports += 1
                    
                except Exception as e:
                    logger.error(f"Task {self.task_id}: Failed to generate report {i+1}: {e}")
                    results.append({
                        "report_index": i,
                        "report_type": config.get("report_type", "unknown"),
                        "status": "failed",
                        "error": str(e)
                    })
                    failed_reports += 1
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Build bulk task result
            bulk_result = {
                "task_id": self.task_id,
                "organization_id": self.organization_id,
                "status": "completed",
                "execution_time": execution_time,
                "summary": {
                    "total_reports": len(report_configs),
                    "successful_reports": successful_reports,
                    "failed_reports": failed_reports,
                    "success_rate": (successful_reports / len(report_configs) * 100) if report_configs else 0
                },
                "results": results,
                "output_directory": output_directory
            }
            
            # Audit log task completion
            self.audit_logger.log_event(
                event_type="task_completed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "bulk_report_generation",
                    "execution_time": execution_time,
                    "successful_reports": successful_reports,
                    "failed_reports": failed_reports,
                    "output_directory": output_directory
                }
            )
            
            logger.info(
                f"Task {self.task_id}: Bulk report generation completed "
                f"({successful_reports}/{len(report_configs)} successful) in {execution_time:.2f} seconds"
            )
            
            return bulk_result
            
        except Exception as e:
            # Audit log task failure
            self.audit_logger.log_event(
                event_type="task_failed",
                user_id=None,
                organization_id=self.organization_id,
                details={
                    "task_id": self.task_id,
                    "task_type": "bulk_report_generation",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
            )
            
            logger.error(f"Task {self.task_id}: Bulk report generation failed: {e}")
            raise self._transform_to_task_exception(e, "bulk")

    def _transform_to_task_response(
        self,
        engine_result: Dict[str, Any],
        report_type: str,
        execution_time: float
    ) -> Dict[str, Any]:
        """
        Transform engine response to task-compatible format.
        
        Args:
            engine_result: Result from the report engine
            report_type: Name of the report type
            execution_time: Task execution time in seconds
            
        Returns:
            Task-compatible response dictionary
        """
        return {
            "task_id": self.task_id,
            "organization_id": self.organization_id,
            "report_type": report_type,
            "status": "completed",
            "execution_time": execution_time,
            "result": {
                "output_path": engine_result.get("output_path"),
                "filename": engine_result.get("filename"),
                "format": engine_result.get("format"),
                "file_size_bytes": engine_result.get("file_size_bytes", 0),
                "file_size_mb": round(engine_result.get("file_size_bytes", 0) / (1024 * 1024), 2),
                "generation_time": engine_result.get("generation_time", 0)
            },
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "engine_version": "consolidated",
                "report_data_available": bool(engine_result.get("report_data"))
            }
        }

    def _transform_to_task_exception(self, exception: Exception, report_type: str) -> Exception:
        """
        Transform exceptions to task-compatible format.
        
        Args:
            exception: Original exception
            report_type: Report type being generated
            
        Returns:
            Task-compatible exception
        """
        if isinstance(exception, ReportGenerationError):
            # Already a reporting-specific error
            return exception
        else:
            # Wrap in ReportGenerationError
            return ReportGenerationError(
                f"Task {self.task_id}: Failed to generate {report_type} report: {str(exception)}",
                report_type=report_type,
                organization_id=self.organization_id,
                task_id=self.task_id
            )

    def update_task_progress(self, current: int, total: int, description: str = "") -> None:
        """
        Update task progress (for Celery task progress tracking).
        
        Args:
            current: Current progress value
            total: Total progress value
            description: Progress description
        """
        try:
            # This would integrate with Celery's task.update_state method
            progress = {
                "current": current,
                "total": total,
                "percentage": round((current / total) * 100, 1) if total > 0 else 0,
                "description": description
            }
            
            logger.info(
                f"Task {self.task_id}: Progress {progress['percentage']}% - {description}"
            )
            
            # In a real Celery task, this would be:
            # self.update_state(state='PROGRESS', meta=progress)
            
        except Exception as e:
            logger.warning(f"Failed to update task progress: {e}")

    def get_task_info(self) -> Dict[str, Any]:
        """
        Get task information and metadata.
        
        Returns:
            Task information dictionary
        """
        return {
            "task_id": self.task_id,
            "organization_id": self.organization_id,
            "adapter_type": "TaskReportAdapter",
            "engine_type": "consolidated",
            "supported_reports": ["income_statement", "balance_sheet", "cash_flow", "comprehensive"],
            "supported_formats": ["pdf", "excel", "json", "html"],
            "capabilities": {
                "async_generation": True,
                "bulk_generation": True,
                "progress_tracking": True,
                "audit_logging": True,
                "error_handling": True
            }
        }