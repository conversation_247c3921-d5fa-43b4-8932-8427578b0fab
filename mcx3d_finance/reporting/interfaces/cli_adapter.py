"""
CLI adapter for the consolidated report generation engine.

Provides backward-compatible interface for existing CLI commands while
using the new unified engine internally.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional
from datetime import datetime

from mcx3d_finance.reporting.engine.report_engine import ReportEngine, ReportType, OutputFormat

logger = LoggerFactory.get_logger(__name__)


class CLIReportAdapter:
    """
    Adapter that provides CLI-compatible interface to the unified report engine.
    
    This allows existing CLI commands to be gradually migrated to use the new
    consolidated system without breaking changes.
    """

    def __init__(self, organization_id: int) -> None:
        self.organization_id = organization_id
        self.engine = ReportEngine(organization_id)

    def generate_income_statement(
        self,
        period: str,
        format_type: str = "pdf",
        output_path: Optional[str] = None,
        complexity: Optional[str] = None,
        async_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Generate income statement using CLI parameters.
        
        Args:
            period: Period string (YYYY-QN, YYYY-MM, or YYYY)
            format_type: Output format 
            output_path: Custom output path
            complexity: Report complexity level
            async_mode: Whether to generate asynchronously
            
        Returns:
            Generation result with CLI-compatible format
        """
        try:
            # Handle async mode
            if async_mode:
                return self._generate_async(
                    report_type=ReportType.INCOME_STATEMENT,
                    period=period,
                    format_type=format_type,
                    output_path=output_path,
                    complexity=complexity
                )
            
            # Synchronous generation using new engine
            result = self.engine.generate_report(
                report_type=ReportType.INCOME_STATEMENT,
                format_type=OutputFormat(format_type),
                period_start=period,  # Engine will parse this
                output_path=output_path,
                complexity=complexity
            )
            
            # Transform to CLI-compatible response
            return self._transform_to_cli_response(result, "income_statement")
            
        except Exception as e:
            logger.error(f"CLI adapter error generating income statement: {e}")
            return {
                "status": "error",
                "error": str(e),
                "organization_id": self.organization_id
            }

    def generate_balance_sheet(
        self,
        date: str,
        format_type: str = "pdf", 
        output_path: Optional[str] = None,
        async_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Generate balance sheet using CLI parameters.
        
        Args:
            date: As-of date (YYYY-MM-DD)
            format_type: Output format
            output_path: Custom output path
            async_mode: Whether to generate asynchronously
            
        Returns:
            Generation result with CLI-compatible format
        """
        try:
            # Handle async mode
            if async_mode:
                return self._generate_async(
                    report_type=ReportType.BALANCE_SHEET,
                    as_of_date=date,
                    format_type=format_type,
                    output_path=output_path
                )
            
            # Synchronous generation using new engine
            result = self.engine.generate_report(
                report_type=ReportType.BALANCE_SHEET,
                format_type=OutputFormat(format_type),
                as_of_date=date,
                output_path=output_path
            )
            
            # Transform to CLI-compatible response
            return self._transform_to_cli_response(result, "balance_sheet")
            
        except Exception as e:
            logger.error(f"CLI adapter error generating balance sheet: {e}")
            return {
                "status": "error",
                "error": str(e),
                "organization_id": self.organization_id
            }

    def generate_cash_flow(
        self,
        period: str,
        format_type: str = "pdf",
        output_path: Optional[str] = None,
        async_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Generate cash flow statement using CLI parameters.
        
        Args:
            period: Period string (YYYY-QN, YYYY-MM, or YYYY)
            format_type: Output format
            output_path: Custom output path
            async_mode: Whether to generate asynchronously
            
        Returns:
            Generation result with CLI-compatible format
        """
        try:
            # Handle async mode  
            if async_mode:
                return self._generate_async(
                    report_type=ReportType.CASH_FLOW,
                    period=period,
                    format_type=format_type,
                    output_path=output_path
                )
            
            # Synchronous generation using new engine
            result = self.engine.generate_report(
                report_type=ReportType.CASH_FLOW,
                format_type=OutputFormat(format_type),
                period_start=period,  # Engine will parse this
                output_path=output_path
            )
            
            # Transform to CLI-compatible response
            return self._transform_to_cli_response(result, "cash_flow")
            
        except Exception as e:
            logger.error(f"CLI adapter error generating cash flow: {e}")
            return {
                "status": "error", 
                "error": str(e),
                "organization_id": self.organization_id
            }

    def generate_comprehensive_report(
        self,
        year: int,
        formats: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive report using CLI parameters.
        
        Args:
            year: Report year
            formats: List of output formats
            
        Returns:
            Generation result with CLI-compatible format
        """
        try:
            period = str(year)  # Convert year to period string
            
            result = self.engine.generate_report(
                report_type=ReportType.COMPREHENSIVE,
                format_type=OutputFormat.JSON,  # Comprehensive always generates JSON first
                period_start=period,
                year=year,  # Pass as additional parameter
                formats=formats or ["json"]
            )
            
            return {
                "status": "success",
                "output_dir": f"reports/{year}",
                "organization_id": self.organization_id,
                "year": year
            }
            
        except Exception as e:
            logger.error(f"CLI adapter error generating comprehensive report: {e}")
            return {
                "status": "error",
                "error": str(e),
                "organization_id": self.organization_id
            }

    def _generate_async(self, **kwargs) -> Dict[str, Any]:
        """
        Handle async report generation.
        
        For now, delegates to the existing task system.
        In the future, this could be integrated into the engine.
        """
        report_type = kwargs.get("report_type")
        
        # Import the existing task functions
        if report_type == ReportType.INCOME_STATEMENT:
from mcx3d_finance.tasks.report_tasks import generate_income_statement_async
            task = generate_income_statement_async.delay(
                organization_id=self.organization_id,
                period_start=kwargs.get("period", ""),
                period_end="",  # Will be parsed from period
                output_format=kwargs.get("format_type", "pdf"),
                output_path=kwargs.get("output_path"),
                complexity=kwargs.get("complexity")
            )
        elif report_type == ReportType.BALANCE_SHEET:
from mcx3d_finance.tasks.report_tasks import generate_balance_sheet_async
            task = generate_balance_sheet_async.delay(
                organization_id=self.organization_id,
                as_of_date=kwargs.get("as_of_date"),
                output_format=kwargs.get("format_type", "pdf"),
                output_path=kwargs.get("output_path")
            )
        elif report_type == ReportType.CASH_FLOW:
from mcx3d_finance.tasks.report_tasks import generate_cash_flow_async
            task = generate_cash_flow_async.delay(
                organization_id=self.organization_id,
                period_start=kwargs.get("period", ""),
                period_end="",  # Will be parsed from period
                output_format=kwargs.get("format_type", "pdf"),
                output_path=kwargs.get("output_path")
            )
        
        return {
            "status": "async_started",
            "task_id": task.id,
            "organization_id": self.organization_id
        }

    def _transform_to_cli_response(
        self,
        engine_result: Dict[str, Any],
        report_name: str
    ) -> Dict[str, Any]:
        """
        Transform engine response to CLI-compatible format.
        
        Args:
            engine_result: Result from the report engine
            report_name: Name of the report type
            
        Returns:
            CLI-compatible response dictionary
        """
        return {
            "status": "success",
            "output": engine_result.get("output_path"),
            "format": engine_result.get("format", ""),
            "organization_id": self.organization_id,
            "report_type": report_name,
            "generation_time": engine_result.get("generation_time", 0),
            "file_size_mb": engine_result.get("file_size_mb", 0)
        }