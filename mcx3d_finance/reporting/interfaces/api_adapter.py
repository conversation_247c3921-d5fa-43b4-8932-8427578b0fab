"""
API adapter for the consolidated report generation engine.

Provides FastAPI-compatible interface for existing API endpoints while
using the new unified engine internally.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPException
from starlette.responses import FileResponse

from mcx3d_finance.reporting.engine.report_engine import ReportEngine, ReportType, OutputFormat

logger = LoggerFactory.get_logger(__name__)


class APIReportAdapter:
    """
    Adapter that provides FastAPI-compatible interface to the unified report engine.
    
    This allows existing API endpoints to be gradually migrated to use the new
    consolidated system without breaking changes to the API contract.
    """

    def __init__(self, organization_id: int) -> None:
        self.organization_id = organization_id
        self.engine = ReportEngine(organization_id)

    async def get_income_statement(
        self,
        start_date: str,
        end_date: str,
        format_type: str = "json",
        output_path: Optional[str] = None,
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True
    ) -> Dict[str, Any]:
        """
        Generate income statement via API.
        
        Args:
            start_date: Period start date (YYYY-MM-DD)
            end_date: Period end date (YYYY-MM-DD)
            format_type: Output format (json, pdf, excel, html)
            output_path: Custom output path
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            
        Returns:
            Income statement data or file response
            
        Raises:
            HTTPException: If generation fails
        """
        try:
            logger.info(
                f"API request for income statement: org={self.organization_id}, "
                f"period={start_date} to {end_date}, format={format_type}"
            )
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.INCOME_STATEMENT,
                format_type=OutputFormat(format_type.upper()),
                period_start=start_date,
                period_end=end_date,
                output_path=output_path,
                comparative_period=comparative_period,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            # Handle different response types based on format
            if format_type.lower() == "json":
                return self._transform_to_api_response(result, "income_statement")
            else:
                # For file formats, return file response
                return self._create_file_response(result)
            
        except Exception as e:
            logger.error(f"API adapter error generating income statement: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate income statement: {str(e)}"
            )

    async def get_balance_sheet(
        self,
        as_of_date: str,
        format_type: str = "json",
        output_path: Optional[str] = None,
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True
    ) -> Dict[str, Any]:
        """
        Generate balance sheet via API.
        
        Args:
            as_of_date: Balance sheet date (YYYY-MM-DD)
            format_type: Output format (json, pdf, excel, html)
            output_path: Custom output path
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            
        Returns:
            Balance sheet data or file response
            
        Raises:
            HTTPException: If generation fails
        """
        try:
            logger.info(
                f"API request for balance sheet: org={self.organization_id}, "
                f"as_of={as_of_date}, format={format_type}"
            )
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.BALANCE_SHEET,
                format_type=OutputFormat(format_type.upper()),
                as_of_date=as_of_date,
                output_path=output_path,
                comparative_period=comparative_period,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            # Handle different response types based on format
            if format_type.lower() == "json":
                return self._transform_to_api_response(result, "balance_sheet")
            else:
                # For file formats, return file response
                return self._create_file_response(result)
            
        except Exception as e:
            logger.error(f"API adapter error generating balance sheet: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate balance sheet: {str(e)}"
            )

    async def get_cash_flow_statement(
        self,
        start_date: str,
        end_date: str,
        format_type: str = "json",
        method: str = "indirect",
        output_path: Optional[str] = None,
        comparative_period: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True
    ) -> Dict[str, Any]:
        """
        Generate cash flow statement via API.
        
        Args:
            start_date: Period start date (YYYY-MM-DD)
            end_date: Period end date (YYYY-MM-DD)
            format_type: Output format (json, pdf, excel, html)
            method: Cash flow method (indirect or direct)
            output_path: Custom output path
            comparative_period: Include previous year comparison
            include_notes: Include explanatory notes
            include_ratios: Include cash flow ratios
            
        Returns:
            Cash flow statement data or file response
            
        Raises:
            HTTPException: If generation fails
        """
        try:
            logger.info(
                f"API request for cash flow: org={self.organization_id}, "
                f"period={start_date} to {end_date}, format={format_type}, method={method}"
            )
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.CASH_FLOW,
                format_type=OutputFormat(format_type.upper()),
                period_start=start_date,
                period_end=end_date,
                output_path=output_path,
                method=method,
                comparative_period=comparative_period,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            # Handle different response types based on format
            if format_type.lower() == "json":
                return self._transform_to_api_response(result, "cash_flow")
            else:
                # For file formats, return file response
                return self._create_file_response(result)
            
        except Exception as e:
            logger.error(f"API adapter error generating cash flow: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate cash flow statement: {str(e)}"
            )

    async def get_comprehensive_report(
        self,
        start_date: str,
        end_date: str,
        formats: Optional[list] = None,
        output_directory: Optional[str] = None,
        include_management_analysis: bool = True,
        include_notes: bool = True,
        include_ratios: bool = True
    ) -> Dict[str, Any]:
        """
        Generate comprehensive report package via API.
        
        Args:
            start_date: Period start date (YYYY-MM-DD)
            end_date: Period end date (YYYY-MM-DD)
            formats: List of output formats to generate
            output_directory: Custom output directory
            include_management_analysis: Include management discussion & analysis
            include_notes: Include explanatory notes
            include_ratios: Include financial ratios
            
        Returns:
            Comprehensive report data
            
        Raises:
            HTTPException: If generation fails
        """
        try:
            logger.info(
                f"API request for comprehensive report: org={self.organization_id}, "
                f"period={start_date} to {end_date}, formats={formats}"
            )
            
            # Generate using unified engine
            result = self.engine.generate_report(
                report_type=ReportType.COMPREHENSIVE,
                format_type=OutputFormat.JSON,  # Always start with JSON for comprehensive
                period_start=start_date,
                period_end=end_date,
                as_of_date=end_date,  # Use end date as balance sheet date
                output_path=output_directory,
                formats=formats or ["json"],
                include_management_analysis=include_management_analysis,
                include_notes=include_notes,
                include_ratios=include_ratios
            )
            
            return self._transform_to_api_response(result, "comprehensive")
            
        except Exception as e:
            logger.error(f"API adapter error generating comprehensive report: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate comprehensive report: {str(e)}"
            )

    async def get_report_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get status of async report generation.
        
        Args:
            task_id: Celery task ID
            
        Returns:
            Task status information
        """
        try:
            # Import Celery here to avoid circular imports
            from celery.result import AsyncResult
            
            result = AsyncResult(task_id)
            
            return {
                "task_id": task_id,
                "status": result.status,
                "result": result.result if result.successful() else None,
                "error": str(result.result) if result.failed() else None,
                "organization_id": self.organization_id
            }
            
        except Exception as e:
            logger.error(f"Error getting report status: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get report status: {str(e)}"
            )

    async def generate_report_async(
        self,
        report_type: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Start async report generation.
        
        Args:
            report_type: Type of report (income_statement, balance_sheet, cash_flow, comprehensive)
            **kwargs: Report-specific parameters
            
        Returns:
            Task information
            
        Raises:
            HTTPException: If task creation fails
        """
        try:
            # Import task functions
            if report_type == "income_statement":
from mcx3d_finance.tasks.report_tasks import generate_income_statement_async
                task = generate_income_statement_async.delay(
                    organization_id=self.organization_id,
                    period_start=kwargs.get("start_date", ""),
                    period_end=kwargs.get("end_date", ""),
                    output_format=kwargs.get("format_type", "pdf"),
                    output_path=kwargs.get("output_path"),
                    comparative_period=kwargs.get("comparative_period", True),
                    include_notes=kwargs.get("include_notes", True),
                    include_ratios=kwargs.get("include_ratios", True)
                )
            elif report_type == "balance_sheet":
from mcx3d_finance.tasks.report_tasks import generate_balance_sheet_async
                task = generate_balance_sheet_async.delay(
                    organization_id=self.organization_id,
                    as_of_date=kwargs.get("as_of_date"),
                    output_format=kwargs.get("format_type", "pdf"),
                    output_path=kwargs.get("output_path"),
                    comparative_period=kwargs.get("comparative_period", True),
                    include_notes=kwargs.get("include_notes", True),
                    include_ratios=kwargs.get("include_ratios", True)
                )
            elif report_type == "cash_flow":
from mcx3d_finance.tasks.report_tasks import generate_cash_flow_async
                task = generate_cash_flow_async.delay(
                    organization_id=self.organization_id,
                    period_start=kwargs.get("start_date", ""),
                    period_end=kwargs.get("end_date", ""),
                    output_format=kwargs.get("format_type", "pdf"),
                    method=kwargs.get("method", "indirect"),
                    output_path=kwargs.get("output_path"),
                    comparative_period=kwargs.get("comparative_period", True),
                    include_notes=kwargs.get("include_notes", True),
                    include_ratios=kwargs.get("include_ratios", True)
                )
            elif report_type == "comprehensive":
from mcx3d_finance.tasks.report_tasks import generate_comprehensive_report_async
                task = generate_comprehensive_report_async.delay(
                    organization_id=self.organization_id,
                    period_start=kwargs.get("start_date", ""),
                    period_end=kwargs.get("end_date", ""),
                    formats=kwargs.get("formats", ["json"]),
                    output_directory=kwargs.get("output_directory"),
                    include_management_analysis=kwargs.get("include_management_analysis", True),
                    include_notes=kwargs.get("include_notes", True),
                    include_ratios=kwargs.get("include_ratios", True)
                )
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unsupported report type: {report_type}"
                )
            
            return {
                "task_id": task.id,
                "status": "started",
                "report_type": report_type,
                "organization_id": self.organization_id,
                "message": f"Report generation started. Use task ID {task.id} to check status."
            }
            
        except Exception as e:
            logger.error(f"Error starting async report generation: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to start report generation: {str(e)}"
            )

    def _transform_to_api_response(
        self,
        engine_result: Dict[str, Any],
        report_name: str
    ) -> Dict[str, Any]:
        """
        Transform engine response to API-compatible format.
        
        Args:
            engine_result: Result from the report engine
            report_name: Name of the report type
            
        Returns:
            API-compatible response dictionary
        """
        # For JSON responses, return the full report data
        if engine_result.get("format", "").lower() == "json":
            return {
                "success": True,
                "organization_id": self.organization_id,
                "report_type": report_name,
                "data": engine_result.get("report_data", {}),
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "format": engine_result.get("format", ""),
                    "generation_time_seconds": engine_result.get("generation_time", 0),
                    "file_size_bytes": engine_result.get("file_size_bytes", 0)
                }
            }
        else:
            # For file formats, return metadata and download info
            return {
                "success": True,
                "organization_id": self.organization_id,
                "report_type": report_name,
                "file_info": {
                    "filename": engine_result.get("filename", ""),
                    "file_path": engine_result.get("output_path", ""),
                    "format": engine_result.get("format", ""),
                    "file_size_bytes": engine_result.get("file_size_bytes", 0)
                },
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "generation_time_seconds": engine_result.get("generation_time", 0)
                }
            }

    def _create_file_response(self, engine_result: Dict[str, Any]) -> FileResponse:
        """
        Create FastAPI FileResponse for file downloads.
        
        Args:
            engine_result: Result from the report engine containing file info
            
        Returns:
            FastAPI FileResponse for file download
        """
        file_path = engine_result.get("output_path")
        filename = engine_result.get("filename", "report")
        format_type = engine_result.get("format", "").lower()
        
        # Determine media type based on format
        media_type_map = {
            "pdf": "application/pdf",
            "excel": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "html": "text/html",
            "json": "application/json"
        }
        
        media_type = media_type_map.get(format_type, "application/octet-stream")
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "X-Organization-ID": str(self.organization_id),
                "X-Report-Type": engine_result.get("report_type", ""),
                "X-Generation-Time": str(engine_result.get("generation_time", 0))
            }
        )

    def _validate_date_format(self, date_string: str, field_name: str) -> None:
        """
        Validate date format for API inputs.
        
        Args:
            date_string: Date string to validate
            field_name: Name of the field for error messages
            
        Raises:
            HTTPException: If date format is invalid
        """
        try:
            datetime.strptime(date_string, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid {field_name} format. Expected YYYY-MM-DD, got: {date_string}"
            )

    def _validate_format_type(self, format_type: str) -> None:
        """
        Validate output format type.
        
        Args:
            format_type: Format type to validate
            
        Raises:
            HTTPException: If format is not supported
        """
        supported_formats = ["json", "pdf", "excel", "xlsx", "html"]
        if format_type.lower() not in supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported format: {format_type}. Supported formats: {supported_formats}"
            )

    def _validate_cash_flow_method(self, method: str) -> None:
        """
        Validate cash flow method.
        
        Args:
            method: Cash flow method to validate
            
        Raises:
            HTTPException: If method is not supported
        """
        supported_methods = ["direct", "indirect"]
        if method.lower() not in supported_methods:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported cash flow method: {method}. Supported methods: {supported_methods}"
            )

    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the report generation system.
        
        Returns:
            Health status information
        """
        try:
            # Test basic engine functionality
            engine_status = "healthy"
            try:
                # Simple validation that doesn't generate reports
                self.engine._validate_organization()
            except Exception as e:
                engine_status = f"error: {str(e)}"
            
            return {
                "status": "healthy" if engine_status == "healthy" else "degraded",
                "organization_id": self.organization_id,
                "engine_status": engine_status,
                "timestamp": datetime.now().isoformat(),
                "supported_reports": ["income_statement", "balance_sheet", "cash_flow", "comprehensive"],
                "supported_formats": ["json", "pdf", "excel", "html"]
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "organization_id": self.organization_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }