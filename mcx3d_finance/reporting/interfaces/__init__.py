"""
Interface adapters for the consolidated report generation system.

These adapters provide thin layers that connect existing CLI, API, and Task
interfaces to the new unified report engine.
"""

from mcx3d_finance.reporting.interfaces.cli_adapter import CLIReportAdapter
from mcx3d_finance.reporting.interfaces.api_adapter import APIReportAdapter
from mcx3d_finance.reporting.interfaces.task_adapter import TaskReportAdapter

__all__ = [
    'CLIReportAdapter',
    'APIReportAdapter',
    'TaskReportAdapter',
]