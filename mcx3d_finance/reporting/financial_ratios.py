"""
Calculates key financial ratios based on financial statements.
"""

from typing import Dict, Any

def calculate_financial_ratios(
    income_statement: Dict[str, Any],
    balance_sheet: Dict[str, Any],
    cash_flow_statement: Dict[str, Any],
    shares_outstanding: int = 1_000_000
) -> Dict[str, float]:
    """
    Calculate key financial ratios.

    Args:
        income_statement: Dictionary representing the income statement.
        balance_sheet: Dictionary representing the balance sheet.
        cash_flow_statement: Dictionary representing the cash flow statement.
        shares_outstanding: Number of shares outstanding for EPS calculation.

    Returns:
        A dictionary of calculated financial ratios.
    """
    ratios = {}

    # Profitability Ratios
    net_income = income_statement.get("profit_after_tax", 0)
    revenue = income_statement.get("turnover", 0)
    gross_profit = income_statement.get("gross_profit", 0)
    
    equity = balance_sheet.get("capital_and_reserves", {}).get("total", 0)
    
    ratios["net_profit_margin"] = (float(net_income) / float(revenue)) if revenue else 0
    ratios["gross_profit_margin"] = (float(gross_profit) / float(revenue)) if revenue else 0
    ratios["return_on_equity"] = (float(net_income) / float(equity)) if equity else 0

    # Liquidity Ratios
    current_assets = balance_sheet.get("current_assets", {}).get("total", 0)
    inventory = balance_sheet.get("current_assets", {}).get("stocks", 0)
    current_liabilities = balance_sheet.get("current_liabilities", {}).get("total", 0)

    ratios["current_ratio"] = (float(current_assets) / float(current_liabilities)) if current_liabilities else 0
    ratios["quick_ratio"] = ((float(current_assets) - float(inventory)) / float(current_liabilities)) if current_liabilities else 0

    # Leverage Ratios
    total_debt = balance_sheet.get("creditors_due_within_one_year", {}).get("total", 0) + \
                 balance_sheet.get("creditors_due_after_one_year", {}).get("total", 0)

    ratios["debt_to_equity_ratio"] = (float(total_debt) / float(equity)) if equity else 0

    # Market Value Ratios
    ratios["earnings_per_share"] = (float(net_income) / float(shares_outstanding)) if shares_outstanding else 0

    return ratios