"""
Input validation for report generation requests.

Consolidates validation logic scattered across CLI, API, and Task layers.
"""

import os
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Union, Optional, TYPE_CHECKING
from datetime import datetime

from mcx3d_finance.exceptions.custom import ReportValidationError

if TYPE_CHECKING:
from mcx3d_finance.reporting.engine.report_engine import ReportType, OutputFormat

logger = LoggerFactory.get_logger(__name__, domain='utils')


class ReportValidator:
    """
    Validates report generation requests.
    
    Consolidates validation logic previously scattered across:
    - CLI command validation
    - API parameter validation
    - Task input validation
    """

    def validate_request(
        self,
        report_type: "ReportType",
        format_type: "OutputFormat", 
        organization_id: int,
        period_start: Optional[Union[str, datetime]] = None,
        period_end: Optional[Union[str, datetime]] = None,
        as_of_date: Optional[Union[str, datetime]] = None
    ) -> None:
        """
        Validate a complete report generation request.
        
        Args:
            report_type: Type of report to generate
            format_type: Output format
            organization_id: Organization ID
            period_start: Period start date
            period_end: Period end date  
            as_of_date: As-of date for point-in-time reports
            
        Raises:
            ReportValidationError: If validation fails
        """
        self.validate_organization_id(organization_id)
        self.validate_dates(report_type, period_start, period_end, as_of_date)

    def validate_organization_id(self, organization_id: int) -> None:
        """
        Validate organization ID.
        
        Args:
            organization_id: Organization identifier
            
        Raises:
            ReportValidationError: If organization ID is invalid
        """
        if not isinstance(organization_id, int):
            raise ReportValidationError(
                f"Organization ID must be an integer, got {type(organization_id)}"
            )
        
        if organization_id <= 0:
            raise ReportValidationError(
                f"Organization ID must be positive, got {organization_id}"
            )

    def validate_dates(
        self,
        report_type: "ReportType",
        period_start: Optional[Union[str, datetime]] = None,
        period_end: Optional[Union[str, datetime]] = None,
        as_of_date: Optional[Union[str, datetime]] = None
    ) -> None:
        """
        Validate date parameters based on report type.
        
        Args:
            report_type: Type of report being generated
            period_start: Period start date
            period_end: Period end date
            as_of_date: As-of date for point-in-time reports
            
        Raises:
            ReportValidationError: If date validation fails
        """
from mcx3d_finance.reporting.engine.report_engine import ReportType
        
        if report_type == ReportType.BALANCE_SHEET:
            # Balance sheet needs as_of_date
            if as_of_date is None:
                logger.info("No as_of_date provided for balance sheet, using current date")
        else:
            # Period-based reports need start/end dates
            if period_start is None and period_end is None:
                logger.info("No period provided, will use current year")
                return
            
            if period_start and period_end:
                # Validate that start is before end
from mcx3d_finance.reporting.utils.period_parser import PeriodParser
                parser = PeriodParser()
                
                start_dt = parser.parse_date(period_start)
                end_dt = parser.parse_date(period_end)
                
                if start_dt > end_dt:
                    raise ReportValidationError(
                        f"Period start ({start_dt.date()}) must be before or equal to period end ({end_dt.date()})"
                    )

    def validate_period_string(self, period: str) -> None:
        """
        Validate period string format.
        
        Args:
            period: Period string to validate
            
        Raises:
            ReportValidationError: If period format is invalid
        """
        if not isinstance(period, str) or not period.strip():
            raise ReportValidationError("Period must be a non-empty string")
        
        # Use period parser to validate format
from mcx3d_finance.reporting.utils.period_parser import PeriodParser
        parser = PeriodParser()
        
        try:
            parser.parse_period(period.strip())
        except Exception as e:
            raise ReportValidationError(f"Invalid period format: {e}")

    def validate_output_path(self, output_path: str) -> None:
        """
        Validate output path.
        
        Args:
            output_path: Path to validate
            
        Raises:
            ReportValidationError: If path is invalid
        """
        if not isinstance(output_path, str) or not output_path.strip():
            raise ReportValidationError("Output path must be a non-empty string")
        
        from pathlib import Path
        
        try:
            path = Path(output_path)
            
            # Check if parent directory exists or can be created
            parent = path.parent
            if not parent.exists():
                try:
                    parent.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    raise ReportValidationError(f"Cannot create output directory: {e}")
            
            # Check write permissions
            if parent.exists() and not os.access(parent, os.W_OK):
                raise ReportValidationError(f"No write permission for directory: {parent}")
                
        except Exception as e:
            if not isinstance(e, ReportValidationError):
                raise ReportValidationError(f"Invalid output path: {e}")
            raise