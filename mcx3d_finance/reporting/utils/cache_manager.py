"""
Cache management for report generation.

Consolidates caching logic from API layer and provides unified cache interface.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Any, Dict, Optional
import hashlib
import json
from datetime import datetime, timedelta

logger = LoggerFactory.get_logger(__name__, domain='utils')


class CacheManager:
    """
    Manages report caching with configurable backends.
    
    Consolidates caching logic previously scattered in API layer.
    """

    def __init__(self, enabled: bool = True, ttl: int = 3600, backend: str = "redis") -> None:
        """
        Initialize cache manager.
        
        Args:
            enabled: Whether caching is enabled
            ttl: Default time-to-live in seconds
            backend: Cache backend ("redis" or "memory")
        """
        self.enabled = enabled
        self.ttl = ttl
        self.backend = backend
        self._cache = None
        self._stats = {"hits": 0, "misses": 0, "sets": 0}

        if self.enabled:
            self._initialize_backend()

    def _initialize_backend(self) -> None:
        """Initialize the cache backend."""
        if self.backend == "redis":
            try:
from mcx3d_finance.core.cache import get_cache
                self._cache = get_cache()
            except Exception as e:
                logger.warning(f"Failed to initialize Redis cache: {e}, falling back to memory")
                self._initialize_memory_cache()
        else:
            self._initialize_memory_cache()

    def _initialize_memory_cache(self) -> None:
        """Initialize in-memory cache as fallback."""
        self._cache = {}
        self.backend = "memory"

    def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        if not self.enabled or not self._cache:
            return None

        try:
            if self.backend == "redis":
                value = self._cache.get(key)
                if value:
                    self._stats["hits"] += 1
                    if isinstance(value, str):
                        return json.loads(value)
                    return value
                else:
                    self._stats["misses"] += 1
                    return None
            else:
                # Memory cache
                if key in self._cache:
                    entry = self._cache[key]
                    if entry["expires_at"] > datetime.now():
                        self._stats["hits"] += 1
                        return entry["value"]
                    else:
                        # Expired
                        del self._cache[key]
                
                self._stats["misses"] += 1
                return None
                
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            self._stats["misses"] += 1
            return None

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (optional)
            
        Returns:
            True if successfully cached
        """
        if not self.enabled or not self._cache:
            return False

        cache_ttl = ttl or self.ttl

        try:
            if self.backend == "redis":
                serialized_value = json.dumps(value, default=str)
                result = self._cache.set(key, serialized_value, ttl=cache_ttl)
                if result:
                    self._stats["sets"] += 1
                return result
            else:
                # Memory cache
                expires_at = datetime.now() + timedelta(seconds=cache_ttl)
                self._cache[key] = {
                    "value": value,
                    "expires_at": expires_at
                }
                self._stats["sets"] += 1
                return True
                
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False

    def delete(self, key: str) -> bool:
        """
        Delete value from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if key was deleted
        """
        if not self.enabled or not self._cache:
            return False

        try:
            if self.backend == "redis":
                return bool(self._cache.delete(key))
            else:
                # Memory cache
                if key in self._cache:
                    del self._cache[key]
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False

    def clear(self, pattern: Optional[str] = None) -> int:
        """
        Clear cache entries.
        
        Args:
            pattern: Optional pattern to match keys for deletion
            
        Returns:
            Number of keys deleted
        """
        if not self.enabled or not self._cache:
            return 0

        try:
            if self.backend == "redis":
                if pattern:
                    keys = self._cache.keys(pattern)
                    if keys:
                        return self._cache.delete(*keys)
                    return 0
                else:
                    return self._cache.flushdb()
            else:
                # Memory cache
                if pattern:
                    import fnmatch
                    keys_to_delete = [
                        key for key in self._cache.keys()
                        if fnmatch.fnmatch(key, pattern)
                    ]
                    for key in keys_to_delete:
                        del self._cache[key]
                    return len(keys_to_delete)
                else:
                    count = len(self._cache)
                    self._cache.clear()
                    return count
                    
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return 0

    def get_stats(self) -> Dict[str, Any]:
        """Get cache usage statistics."""
        total_requests = self._stats["hits"] + self._stats["misses"]
        hit_rate = (self._stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        stats = {
            "enabled": self.enabled,
            "backend": self.backend,
            "hits": self._stats["hits"],
            "misses": self._stats["misses"],
            "sets": self._stats["sets"],
            "hit_rate_percent": round(hit_rate, 2),
            "total_requests": total_requests
        }
        
        # Add backend-specific stats
        if self.backend == "memory" and self._cache:
            stats["memory_keys"] = len(self._cache)
        
        return stats

    def generate_key(self, *components) -> str:
        """
        Generate cache key from components.
        
        Args:
            *components: Key components to hash
            
        Returns:
            Generated cache key
        """
        key_string = "|".join(str(c) for c in components)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"report_cache:{key_hash}"