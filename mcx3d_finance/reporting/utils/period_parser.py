"""
Unified period and date parsing for all report generation.

Consolidates the scattered period parsing logic from CLI, API, and other layers.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Tuple, Dict, Union, Optional
from datetime import datetime, timedelta
import re

from mcx3d_finance.exceptions.custom import ReportValidationError

logger = LoggerFactory.get_logger(__name__, domain='utils')


class PeriodParser:
    """
    Unified parser for financial reporting periods and dates.
    
    Consolidates period parsing logic previously scattered across:
    - CLI reports.py parse_period()
    - API validation
    - Task parameter parsing
    """

    def parse_period(self, period: str) -> Tuple[datetime, datetime]:
        """
        Parse period string into start and end dates.
        
        Supports formats:
        - YYYY-QN: Quarter (e.g., "2023-Q4")
        - YYYY-MM: Month (e.g., "2023-12")
        - YYYY: Full year (e.g., "2023")
        
        Args:
            period: Period string to parse
            
        Returns:
            Tuple of (start_date, end_date)
            
        Raises:
            ReportValidationError: If period format is invalid
        """
        try:
            period = period.strip()
            
            if "Q" in period:
                return self._parse_quarter(period)
            elif "-" in period and len(period) == 7:  # YYYY-MM format
                return self._parse_month(period)
            elif len(period) == 4:  # YYYY format
                return self._parse_year(period)
            else:
                raise ValueError("Invalid period format. Use YYYY-QN, YYYY-MM, or YYYY")
                
        except Exception as e:
            raise ReportValidationError(f"Invalid period format: {e}", field_name="period")

    def _parse_quarter(self, period: str) -> Tuple[datetime, datetime]:
        """Parse quarter period (e.g., "2023-Q4")."""
        match = re.match(r'^(\d{4})-Q([1-4])$', period)
        if not match:
            raise ValueError(f"Invalid quarter format: {period}. Use YYYY-QN (e.g., 2023-Q4)")
        
        year = int(match.group(1))
        quarter = int(match.group(2))
        
        start_month = (quarter - 1) * 3 + 1
        end_month = start_month + 2
        
        start_date = datetime(year, start_month, 1)
        
        # Get last day of end month
        if end_month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, end_month + 1, 1) - timedelta(days=1)
        
        return start_date, end_date

    def _parse_month(self, period: str) -> Tuple[datetime, datetime]:
        """Parse month period (e.g., "2023-12")."""
        try:
            year_str, month_str = period.split("-")
            year = int(year_str)
            month = int(month_str)
            
            if not (1 <= month <= 12):
                raise ValueError(f"Invalid month: {month}. Must be 1-12")
            
            start_date = datetime(year, month, 1)
            
            # Get last day of month
            if month == 12:
                end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(year, month + 1, 1) - timedelta(days=1)
            
            return start_date, end_date
            
        except ValueError as e:
            raise ValueError(f"Invalid month format: {period}. {e}")

    def _parse_year(self, period: str) -> Tuple[datetime, datetime]:
        """Parse year period (e.g., "2023")."""
        try:
            year = int(period)
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            return start_date, end_date
        except ValueError:
            raise ValueError(f"Invalid year format: {period}")

    def parse_date(self, date_input: Union[str, datetime]) -> datetime:
        """
        Parse various date input formats into datetime object.
        
        Args:
            date_input: Date as string or datetime object
            
        Returns:
            Parsed datetime object
            
        Raises:
            ReportValidationError: If date format is invalid
        """
        if isinstance(date_input, datetime):
            return date_input
        
        if not isinstance(date_input, str):
            raise ReportValidationError(f"Invalid date type: {type(date_input)}")
        
        # Use the existing date parser utility
from mcx3d_finance.utils.date_parser import parse_date_safe
        
        parsed_date = parse_date_safe(date_input)
        if parsed_date is None:
            raise ReportValidationError(f"Could not parse date: {date_input}")
        
        return parsed_date

    def get_current_year_period(self) -> Dict[str, datetime]:
        """Get current year as period start/end."""
        current_year = datetime.now().year
        return {
            "period_start": datetime(current_year, 1, 1),
            "period_end": datetime(current_year, 12, 31)
        }

    def get_previous_year_period(self) -> Dict[str, datetime]:
        """Get previous year as period start/end."""
        previous_year = datetime.now().year - 1
        return {
            "period_start": datetime(previous_year, 1, 1),
            "period_end": datetime(previous_year, 12, 31)
        }

    def format_period_display(self, start_date: datetime, end_date: datetime) -> str:
        """Format period for display purposes."""
        if start_date.year == end_date.year:
            if start_date.month == 1 and end_date.month == 12:
                return str(start_date.year)
            elif start_date.day == 1 and end_date == datetime(end_date.year, end_date.month + 1, 1) - timedelta(days=1):
                return f"{start_date.year}-{start_date.month:02d}"
        
        return f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"