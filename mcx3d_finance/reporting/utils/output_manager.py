"""
Unified output file management for report generation.

Consolidates scattered file naming and path logic from CLI, API, and other layers.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional, TYPE_CHECKING
from datetime import datetime
from pathlib import Path
import os

if TYPE_CHECKING:
from mcx3d_finance.reporting.engine.report_engine import ReportType, OutputFormat

logger = LoggerFactory.get_logger(__name__, domain='utils')


class OutputManager:
    """
    Manages output file paths, naming conventions, and directory organization.
    
    Consolidates file management logic previously scattered across:
    - CLI output path generation
    - API temporary file handling
    - Task output management
    - Comprehensive report saving
    """

    def __init__(self, base_output_dir: Optional[str] = None) -> None:
        """
        Initialize output manager.
        
        Args:
            base_output_dir: Base directory for all report outputs
        """
        self.base_output_dir = Path(base_output_dir or "reports")
        self.timestamp_format = "%Y%m%d_%H%M%S"

    def generate_path(
        self,
        organization_id: int,
        report_type: "ReportType",
        format_type: "OutputFormat", 
        dates: Dict[str, datetime],
        custom_name: Optional[str] = None
    ) -> str:
        """
        Generate standardized output file path.
        
        Args:
            organization_id: Organization identifier
            report_type: Type of report
            format_type: Output format
            dates: Date information for the report
            custom_name: Custom filename (optional)
            
        Returns:
            Full path to output file
        """
        # Determine subdirectory based on report period/date
        if "as_of_date" in dates:
            # Point-in-time reports organized by year
            subdir = str(dates["as_of_date"].year)
        elif "period_end" in dates:
            # Period reports organized by end year
            subdir = str(dates["period_end"].year)
        else:
            # Fallback to current year
            subdir = str(datetime.now().year)

        # Create directory structure: reports/YYYY/
        output_dir = self.base_output_dir / subdir
        output_dir.mkdir(parents=True, exist_ok=True)

        # Generate filename
        if custom_name:
            filename = custom_name
        else:
            filename = self._generate_filename(
                organization_id=organization_id,
                report_type=report_type,
                format_type=format_type,
                dates=dates
            )

        full_path = output_dir / filename
        return str(full_path.resolve())

    def _generate_filename(
        self,
        organization_id: int,
        report_type: "ReportType",
        format_type: "OutputFormat",
        dates: Dict[str, datetime]
    ) -> str:
        """Generate standardized filename."""
        timestamp = datetime.now().strftime(self.timestamp_format)
        
        # Build filename components
        components = [
            report_type.value,
            f"org{organization_id}"
        ]
        
        # Add date component
        if "as_of_date" in dates:
            date_str = dates["as_of_date"].strftime("%Y%m%d")
            components.append(date_str)
        elif "period_start" in dates and "period_end" in dates:
            start_str = dates["period_start"].strftime("%Y%m%d")
            end_str = dates["period_end"].strftime("%Y%m%d")
            components.append(f"{start_str}_{end_str}")
        
        components.append(timestamp)
        
        # Join with underscores and add extension
        base_name = "_".join(components)
        return f"{base_name}.{format_type.value}"

    def ensure_directory_exists(self, file_path: str) -> str:
        """
        Ensure the directory for the given file path exists.
        
        Args:
            file_path: Full path to the file
            
        Returns:
            Validated file path
            
        Raises:
            PermissionError: If directory cannot be created or is not writable
        """
        file_path = Path(file_path)
        output_dir = file_path.parent
        
        # Create directory if it doesn't exist
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Test write permissions
        test_file = output_dir / ".test_write_permissions"
        try:
            test_file.touch()
            test_file.unlink()
        except PermissionError:
            raise PermissionError(f"No write permission for directory: {output_dir}")
            
        return str(file_path.resolve())

    def cleanup_old_files(self, max_age_days: int = 30) -> int:
        """
        Clean up old report files.
        
        Args:
            max_age_days: Maximum age of files to keep
            
        Returns:
            Number of files cleaned up
        """
        if not self.base_output_dir.exists():
            return 0
        
        cutoff_time = datetime.now().timestamp() - (max_age_days * 24 * 3600)
        cleanup_count = 0
        
        for file_path in self.base_output_dir.rglob("*"):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    cleanup_count += 1
                    logger.debug(f"Cleaned up old file: {file_path}")
                except Exception as e:
                    logger.warning(f"Could not clean up file {file_path}: {e}")
        
        logger.info(f"Cleaned up {cleanup_count} old report files")
        return cleanup_count

    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about a generated file."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {"exists": False}
        
        stat = file_path.stat()
        return {
            "exists": True,
            "size_bytes": stat.st_size,
            "size_mb": round(stat.st_size / (1024 * 1024), 2),
            "created_at": datetime.fromtimestamp(stat.st_ctime),
            "modified_at": datetime.fromtimestamp(stat.st_mtime),
            "extension": file_path.suffix.lower(),
            "directory": str(file_path.parent)
        }

    def organize_existing_files(self) -> Dict[str, int]:
        """
        Organize existing files into the new directory structure.
        
        Moves files from scattered locations into organized year-based directories.
        
        Returns:
            Dictionary with organization statistics
        """
        stats = {"moved": 0, "skipped": 0, "errors": 0}
        
        # Look for files in project root matching report patterns
        project_root = Path.cwd()
        
        # Patterns for existing report files
        patterns = [
            "income_statement_*.json",
            "balance_sheet_*.json", 
            "cash_flow_*.json",
            "comprehensive_*.json"
        ]
        
        for pattern in patterns:
            for file_path in project_root.glob(pattern):
                try:
                    # Extract year from filename if possible
                    year = self._extract_year_from_filename(file_path.name)
                    
                    # Create target directory
                    target_dir = self.base_output_dir / str(year)
                    target_dir.mkdir(parents=True, exist_ok=True)
                    
                    # Move file
                    target_path = target_dir / file_path.name
                    if not target_path.exists():
                        file_path.rename(target_path)
                        stats["moved"] += 1
                        logger.info(f"Moved {file_path.name} to {target_dir}")
                    else:
                        stats["skipped"] += 1
                        logger.debug(f"Skipped {file_path.name} (already exists)")
                        
                except Exception as e:
                    stats["errors"] += 1
                    logger.error(f"Error organizing file {file_path}: {e}")
        
        return stats

    def _extract_year_from_filename(self, filename: str) -> int:
        """Extract year from filename, defaulting to current year."""
        import re
        
        # Look for 4-digit year in filename
        year_match = re.search(r'(20\d{2})', filename)
        if year_match:
            return int(year_match.group(1))
        
        # Default to current year
        return datetime.now().year