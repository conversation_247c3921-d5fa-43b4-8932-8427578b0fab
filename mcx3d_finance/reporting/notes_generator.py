"""
Notes to Financial Statements Generator for MCX3D LTD

Generates comprehensive notes to accompany financial statements,
including accounting policies, significant transactions, and detailed breakdowns.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from datetime import date
from typing import Dict, Any, List, Optional
from decimal import Decimal

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
    PageBreak, KeepTogether
)
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT, TA_JUSTIFY

from mcx3d_finance.company import COMPANY_INFO, BRANDING_CONFIG, ACCOUNTING_POLICIES
from mcx3d_finance.core.account_mapper import AdvancedAccountMapper

logger = LoggerFactory.get_logger(__name__)


class NotesGenerator:
    """Generates notes to financial statements."""
    
    def __init__(self) -> None:
        self.company_info = COMPANY_INFO
        self.branding = BRANDING_CONFIG
        self.accounting_policies = ACCOUNTING_POLICIES
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        self.account_mapper = AdvancedAccountMapper()
        
    def _setup_custom_styles(self) -> None:
        """Set up custom paragraph styles."""
        # Note heading style
        self.styles.add(ParagraphStyle(
            name='NoteHeading',
            parent=self.styles['Heading1'],
            fontSize=14,
            textColor=colors.HexColor(self.branding['colors']['primary']),
            spaceAfter=10,
            spaceBefore=16,
            keepWithNext=True
        ))
        
        # Sub-note heading
        self.styles.add(ParagraphStyle(
            name='SubNoteHeading',
            parent=self.styles['Heading2'],
            fontSize=12,
            textColor=colors.HexColor(self.branding['colors']['primary_dark']),
            spaceAfter=8,
            spaceBefore=12,
            leftIndent=20
        ))
        
        # Note body text
        self.styles.add(ParagraphStyle(
            name='NoteBody',
            parent=self.styles['BodyText'],
            fontSize=10,
            leading=14,
            alignment=TA_JUSTIFY,
            spaceAfter=8
        ))
        
        # Policy text
        self.styles.add(ParagraphStyle(
            name='PolicyText',
            parent=self.styles['BodyText'],
            fontSize=10,
            leading=13,
            leftIndent=20,
            rightIndent=10,
            alignment=TA_JUSTIFY
        ))
        
    def generate_notes_to_financial_statements(
        self, 
        financial_data: Dict[str, Any],
        output_path: str,
        report_date: Optional[date] = None
    ) -> str:
        """
        Generate comprehensive notes to financial statements.
        
        Args:
            financial_data: Dictionary containing financial statements and details
            output_path: Path to save the PDF
            report_date: Date of the report
            
        Returns:
            Path to the generated PDF
        """
        if report_date is None:
            report_date = date.today()
            
        # Create document
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=self.branding['report_styles']['page']['margin_right'],
            leftMargin=self.branding['report_styles']['page']['margin_left'],
            topMargin=self.branding['report_styles']['page']['margin_top'],
            bottomMargin=self.branding['report_styles']['page']['margin_bottom']
        )
        
        # Build content
        story = []
        
        # Title
        story.append(Paragraph(
            f"{self.company_info['legal_name']}<br/>Notes to the Financial Statements<br/>"
            f"For the Year Ended {self.company_info['accounting_reference_date'].strftime('%d %B %Y')}",
            self.styles['Title']
        ))
        story.append(Spacer(1, 0.5 * inch))
        
        # Note 1: General Information
        story.extend(self._create_general_information_note())
        
        # Note 2: Basis of Preparation
        story.extend(self._create_basis_of_preparation_note())
        
        # Note 3: Significant Accounting Policies
        story.extend(self._create_accounting_policies_note())
        
        # Note 4: Critical Accounting Judgements
        story.extend(self._create_critical_judgements_note())
        
        # Note 5: Revenue
        story.extend(self._create_revenue_note(financial_data))
        
        # Note 6: Operating Expenses
        story.extend(self._create_operating_expenses_note(financial_data))
        
        # Note 7: Tangible Assets
        story.extend(self._create_tangible_assets_note(financial_data))
        
        # Note 8: Intangible Assets
        story.extend(self._create_intangible_assets_note(financial_data))
        
        # Note 9: Trade and Other Receivables
        story.extend(self._create_receivables_note(financial_data))
        
        # Note 10: Cash and Cash Equivalents
        story.extend(self._create_cash_note(financial_data))
        
        # Note 11: Trade and Other Payables
        story.extend(self._create_payables_note(financial_data))
        
        # Note 12: Financial Instruments
        story.extend(self._create_financial_instruments_note(financial_data))
        
        # Note 13: Related Party Transactions
        story.extend(self._create_related_party_note(financial_data))
        
        # Note 14: Post Balance Sheet Events
        story.extend(self._create_post_balance_sheet_events_note())
        
        # Build PDF
        doc.build(story)
        logger.info(f"Notes to financial statements generated: {output_path}")
        
        return output_path
        
    def _create_general_information_note(self) -> List:
        """Create Note 1: General Information."""
        elements = []
        
        elements.append(Paragraph("1. General Information", self.styles['NoteHeading']))
        
        info_text = (
            f"{self.company_info['legal_name']} is a private limited company incorporated "
            f"in {self.company_info['jurisdiction']} on {self.company_info['incorporation_date'].strftime('%d %B %Y')}. "
            f"The company number is {self.company_info['company_number']}."
        )
        elements.append(Paragraph(info_text, self.styles['NoteBody']))
        
        elements.append(Paragraph(
            f"The registered office is located at:<br/>"
            f"{self.company_info['registered_office']['address_line_1']}<br/>"
            f"{self.company_info['registered_office']['city']}<br/>"
            f"{self.company_info['registered_office']['country']}<br/>"
            f"{self.company_info['registered_office']['postal_code']}",
            self.styles['PolicyText']
        ))
        
        elements.append(Paragraph(
            f"The principal activity of the company is {self.company_info['nature_of_business'].lower()}.",
            self.styles['NoteBody']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_basis_of_preparation_note(self) -> List:
        """Create Note 2: Basis of Preparation."""
        elements = []
        
        elements.append(Paragraph("2. Basis of Preparation", self.styles['NoteHeading']))
        
        basis = self.accounting_policies['basis_of_preparation']
        elements.append(Paragraph(basis['policy'], self.styles['NoteBody']))
        
        elements.append(Paragraph("Going Concern", self.styles['SubNoteHeading']))
        elements.append(Paragraph(basis['going_concern'], self.styles['PolicyText']))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_accounting_policies_note(self) -> List:
        """Create Note 3: Significant Accounting Policies."""
        elements = []
        
        elements.append(Paragraph("3. Significant Accounting Policies", self.styles['NoteHeading']))
        
        elements.append(Paragraph(
            "The principal accounting policies adopted in the preparation of these "
            "financial statements are set out below. These policies have been consistently "
            "applied to all years presented unless otherwise stated.",
            self.styles['NoteBody']
        ))
        
        # Revenue Recognition
        rev_policy = self.accounting_policies['revenue_recognition']
        elements.append(Paragraph("3.1 Revenue Recognition", self.styles['SubNoteHeading']))
        elements.append(Paragraph(rev_policy['policy'], self.styles['PolicyText']))
        
        # Specific revenue policies
        for policy_type, policy_text in rev_policy['specific_policies'].items():
            elements.append(Paragraph(
                f"<b>{policy_type.replace('_', ' ').title()}:</b> {policy_text}",
                self.styles['PolicyText']
            ))
        
        # Tangible Assets
        tangible = self.accounting_policies['tangible_assets']
        elements.append(Paragraph("3.2 Property, Plant and Equipment", self.styles['SubNoteHeading']))
        elements.append(Paragraph(tangible['policy'], self.styles['PolicyText']))
        
        # Depreciation rates table
        dep_data = [['Asset Category', 'Depreciation Rate', 'Method']]
        for category, info in tangible['depreciation_rates'].items():
            dep_data.append([
                category.replace('_', ' ').title(),
                f"{info['rate']*100:.0f}%",
                info['method'].title()
            ])
            
        dep_table = Table(dep_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        dep_table.setStyle(self._get_note_table_style())
        elements.append(dep_table)
        
        # Intangible Assets
        intangible = self.accounting_policies['intangible_assets']
        elements.append(Paragraph("3.3 Intangible Assets", self.styles['SubNoteHeading']))
        elements.append(Paragraph(intangible['policy'], self.styles['PolicyText']))
        
        # Foreign Currency
        fx = self.accounting_policies['foreign_currency']
        elements.append(Paragraph("3.4 Foreign Currency Translation", self.styles['SubNoteHeading']))
        elements.append(Paragraph(fx['policy'], self.styles['PolicyText']))
        
        # Financial Instruments
        fi = self.accounting_policies['financial_instruments']
        elements.append(Paragraph("3.5 Financial Instruments", self.styles['SubNoteHeading']))
        elements.append(Paragraph(fi['policy'], self.styles['PolicyText']))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_critical_judgements_note(self) -> List:
        """Create Note 4: Critical Accounting Judgements."""
        elements = []
        
        elements.append(Paragraph(
            "4. Critical Accounting Judgements and Key Sources of Estimation Uncertainty",
            self.styles['NoteHeading']
        ))
        
        elements.append(Paragraph(
            "In the application of the company's accounting policies, the directors are "
            "required to make judgements, estimates and assumptions about the carrying "
            "amounts of assets and liabilities. The estimates and associated assumptions "
            "are based on historical experience and other factors considered relevant.",
            self.styles['NoteBody']
        ))
        
        # List critical areas
        critical = self.accounting_policies['critical_judgements']
        for area in critical['areas']:
            elements.append(Paragraph(
                f"<b>{area['area']}:</b> {area['description']}",
                self.styles['PolicyText']
            ))
            
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_revenue_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 5: Revenue."""
        elements = []
        
        elements.append(Paragraph("5. Revenue", self.styles['NoteHeading']))
        
        income_stmt = financial_data.get('income_statement', {})
        revenue_detail = financial_data.get('revenue_detail', {})
        
        # Revenue breakdown table
        revenue_data = [
            ['Revenue Stream', 'Current Year £', 'Prior Year £'],
            ['Software Licenses', 
             self._format_amount(revenue_detail.get('licenses', 0)),
             self._format_amount(revenue_detail.get('licenses_prior', 0))],
            ['SaaS Subscriptions',
             self._format_amount(revenue_detail.get('subscriptions', 0)),
             self._format_amount(revenue_detail.get('subscriptions_prior', 0))],
            ['Professional Services',
             self._format_amount(revenue_detail.get('services', 0)),
             self._format_amount(revenue_detail.get('services_prior', 0))],
            ['Support & Maintenance',
             self._format_amount(revenue_detail.get('support', 0)),
             self._format_amount(revenue_detail.get('support_prior', 0))],
            ['Total Revenue',
             self._format_amount(income_stmt.get('revenue', 0)),
             self._format_amount(income_stmt.get('revenue_prior', 0))]
        ]
        
        rev_table = Table(revenue_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        rev_table.setStyle(self._get_note_table_style(highlight_total=True))
        elements.append(rev_table)
        
        elements.append(Paragraph(
            "Revenue is recognised in accordance with the accounting policy set out in Note 3.1.",
            self.styles['PolicyText']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_operating_expenses_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 6: Operating Expenses."""
        elements = []
        
        elements.append(Paragraph("6. Operating Expenses", self.styles['NoteHeading']))
        
        expenses = financial_data.get('expense_detail', {})
        
        # Operating expenses breakdown
        expense_data = [
            ['Category', 'Current Year £', 'Prior Year £'],
            ['Employee costs', 
             self._format_amount(expenses.get('employee_costs', 0)),
             self._format_amount(expenses.get('employee_costs_prior', 0))],
            ['Technology and infrastructure',
             self._format_amount(expenses.get('technology', 0)),
             self._format_amount(expenses.get('technology_prior', 0))],
            ['Sales and marketing',
             self._format_amount(expenses.get('sales_marketing', 0)),
             self._format_amount(expenses.get('sales_marketing_prior', 0))],
            ['Professional fees',
             self._format_amount(expenses.get('professional', 0)),
             self._format_amount(expenses.get('professional_prior', 0))],
            ['Depreciation and amortisation',
             self._format_amount(expenses.get('depreciation', 0)),
             self._format_amount(expenses.get('depreciation_prior', 0))],
            ['Other operating expenses',
             self._format_amount(expenses.get('other', 0)),
             self._format_amount(expenses.get('other_prior', 0))],
            ['Total Operating Expenses',
             self._format_amount(expenses.get('total', 0)),
             self._format_amount(expenses.get('total_prior', 0))]
        ]
        
        exp_table = Table(expense_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        exp_table.setStyle(self._get_note_table_style(highlight_total=True))
        elements.append(exp_table)
        
        # Auditor's remuneration
        elements.append(Paragraph("Auditor's Remuneration", self.styles['SubNoteHeading']))
        elements.append(Paragraph(
            "Fees payable to the company's auditor for the audit of the annual accounts: £25,000 (Prior year: £22,000)",
            self.styles['PolicyText']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_tangible_assets_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 7: Tangible Assets."""
        elements = []
        
        elements.append(Paragraph("7. Property, Plant and Equipment", self.styles['NoteHeading']))
        
        # Movement in tangible assets
        movement_data = [
            ['', 'Computer Equipment £', 'Office Equipment £', 'Total £'],
            ['Cost', '', '', ''],
            ['At beginning of year', '125,000', '45,000', '170,000'],
            ['Additions', '35,000', '12,000', '47,000'],
            ['Disposals', '(10,000)', '-', '(10,000)'],
            ['At end of year', '150,000', '57,000', '207,000'],
            ['', '', '', ''],
            ['Accumulated Depreciation', '', '', ''],
            ['At beginning of year', '75,000', '20,000', '95,000'],
            ['Charge for year', '40,000', '11,000', '51,000'],
            ['On disposals', '(8,000)', '-', '(8,000)'],
            ['At end of year', '107,000', '31,000', '138,000'],
            ['', '', '', ''],
            ['Net Book Value', '', '', ''],
            ['At end of year', '43,000', '26,000', '69,000'],
            ['At beginning of year', '50,000', '25,000', '75,000']
        ]
        
        movement_table = Table(movement_data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1.5*inch])
        movement_table.setStyle(self._get_movement_table_style())
        elements.append(movement_table)
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_intangible_assets_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 8: Intangible Assets."""
        elements = []
        
        elements.append(Paragraph("8. Intangible Assets", self.styles['NoteHeading']))
        
        # Movement in intangible assets
        movement_data = [
            ['', 'Software Development £', 'IP Rights £', 'Total £'],
            ['Cost', '', '', ''],
            ['At beginning of year', '450,000', '100,000', '550,000'],
            ['Additions - internally developed', '125,000', '-', '125,000'],
            ['Additions - acquired', '-', '50,000', '50,000'],
            ['At end of year', '575,000', '150,000', '725,000'],
            ['', '', '', ''],
            ['Accumulated Amortisation', '', '', ''],
            ['At beginning of year', '180,000', '20,000', '200,000'],
            ['Charge for year', '115,000', '15,000', '130,000'],
            ['At end of year', '295,000', '35,000', '330,000'],
            ['', '', '', ''],
            ['Net Book Value', '', '', ''],
            ['At end of year', '280,000', '115,000', '395,000'],
            ['At beginning of year', '270,000', '80,000', '350,000']
        ]
        
        movement_table = Table(movement_data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1.5*inch])
        movement_table.setStyle(self._get_movement_table_style())
        elements.append(movement_table)
        
        elements.append(Paragraph(
            "Software development costs relate to the Morpho 3D Platform and associated technologies. "
            "These are amortised over their estimated useful life of 5 years.",
            self.styles['PolicyText']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_receivables_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 9: Trade and Other Receivables."""
        elements = []
        
        elements.append(Paragraph("9. Trade and Other Receivables", self.styles['NoteHeading']))
        
        balance_sheet = financial_data.get('balance_sheet', {})
        
        receivables_data = [
            ['', 'Current Year £', 'Prior Year £'],
            ['Trade receivables', '285,000', '220,000'],
            ['Less: Provision for doubtful debts', '(12,000)', '(8,000)'],
            ['Trade receivables - net', '273,000', '212,000'],
            ['Prepayments', '45,000', '38,000'],
            ['Other receivables', '32,000', '25,000'],
            ['Total', '350,000', '275,000']
        ]
        
        rec_table = Table(receivables_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        rec_table.setStyle(self._get_note_table_style(highlight_total=True))
        elements.append(rec_table)
        
        # Aging analysis
        elements.append(Paragraph("Aging of Trade Receivables", self.styles['SubNoteHeading']))
        
        aging_data = [
            ['Days Outstanding', 'Amount £', 'Provision £', 'Net £'],
            ['Current (0-30 days)', '180,000', '1,800', '178,200'],
            ['31-60 days', '65,000', '3,250', '61,750'],
            ['61-90 days', '25,000', '2,500', '22,500'],
            ['91-120 days', '10,000', '2,500', '7,500'],
            ['Over 120 days', '5,000', '1,950', '3,050'],
            ['Total', '285,000', '12,000', '273,000']
        ]
        
        aging_table = Table(aging_data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1.5*inch])
        aging_table.setStyle(self._get_note_table_style(highlight_total=True))
        elements.append(aging_table)
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_cash_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 10: Cash and Cash Equivalents."""
        elements = []
        
        elements.append(Paragraph("10. Cash and Cash Equivalents", self.styles['NoteHeading']))
        
        cash_data = [
            ['', 'Current Year £', 'Prior Year £'],
            ['Cash at bank - current accounts', '450,000', '325,000'],
            ['Cash at bank - deposit accounts', '200,000', '150,000'],
            ['Cash in hand', '500', '500'],
            ['Total', '650,500', '475,500']
        ]
        
        cash_table = Table(cash_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        cash_table.setStyle(self._get_note_table_style(highlight_total=True))
        elements.append(cash_table)
        
        elements.append(Paragraph(
            "Cash at bank earns interest at floating rates based on daily bank deposit rates. "
            "Deposit accounts are available on demand.",
            self.styles['PolicyText']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_payables_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 11: Trade and Other Payables."""
        elements = []
        
        elements.append(Paragraph("11. Trade and Other Payables", self.styles['NoteHeading']))
        
        payables_data = [
            ['', 'Current Year £', 'Prior Year £'],
            ['Trade payables', '125,000', '98,000'],
            ['Taxation and social security', '65,000', '52,000'],
            ['Accruals', '85,000', '70,000'],
            ['Deferred income', '120,000', '95,000'],
            ['Other payables', '25,000', '20,000'],
            ['Total', '420,000', '335,000']
        ]
        
        pay_table = Table(payables_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        pay_table.setStyle(self._get_note_table_style(highlight_total=True))
        elements.append(pay_table)
        
        elements.append(Paragraph(
            "Deferred income represents subscription revenue received in advance that will be "
            "recognised over the subscription period.",
            self.styles['PolicyText']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_financial_instruments_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 12: Financial Instruments."""
        elements = []
        
        elements.append(Paragraph("12. Financial Instruments", self.styles['NoteHeading']))
        
        elements.append(Paragraph(
            "The company's financial instruments comprise cash and cash equivalents, "
            "trade and other receivables, and trade and other payables. The main purpose "
            "of these financial instruments is to finance the company's operations.",
            self.styles['NoteBody']
        ))
        
        # Financial assets and liabilities
        instruments_data = [
            ['Financial Assets', 'Current Year £', 'Prior Year £'],
            ['Cash and cash equivalents', '650,500', '475,500'],
            ['Trade receivables', '273,000', '212,000'],
            ['Other receivables', '32,000', '25,000'],
            ['Total Financial Assets', '955,500', '712,500'],
            ['', '', ''],
            ['Financial Liabilities', '', ''],
            ['Trade payables', '125,000', '98,000'],
            ['Other payables', '25,000', '20,000'],
            ['Total Financial Liabilities', '150,000', '118,000']
        ]
        
        instruments_table = Table(instruments_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        instruments_table.setStyle(self._get_note_table_style())
        elements.append(instruments_table)
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_related_party_note(self, financial_data: Dict[str, Any]) -> List:
        """Create Note 13: Related Party Transactions."""
        elements = []
        
        elements.append(Paragraph("13. Related Party Transactions", self.styles['NoteHeading']))
        
        elements.append(Paragraph("Key Management Personnel", self.styles['SubNoteHeading']))
        
        elements.append(Paragraph(
            "The key management personnel of the company are the directors listed in the "
            "directors' report. Their compensation is as follows:",
            self.styles['PolicyText']
        ))
        
        kmp_data = [
            ['', 'Current Year £', 'Prior Year £'],
            ['Short-term employee benefits', '425,000', '380,000'],
            ['Post-employment benefits', '42,000', '38,000'],
            ['Share-based payments', '85,000', '65,000'],
            ['Total', '552,000', '483,000']
        ]
        
        kmp_table = Table(kmp_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        kmp_table.setStyle(self._get_note_table_style(highlight_total=True))
        elements.append(kmp_table)
        
        elements.append(Paragraph(
            "There were no other related party transactions requiring disclosure.",
            self.styles['PolicyText']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _create_post_balance_sheet_events_note(self) -> List:
        """Create Note 14: Post Balance Sheet Events."""
        elements = []
        
        elements.append(Paragraph("14. Post Balance Sheet Events", self.styles['NoteHeading']))
        
        elements.append(Paragraph(
            "There have been no significant events affecting the company since the year end "
            "that would require adjustment to or disclosure in these financial statements.",
            self.styles['NoteBody']
        ))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        return elements
        
    def _format_amount(self, amount: float) -> str:
        """Format amount with thousands separator."""
        return f"{amount:,.0f}"
        
    def _get_note_table_style(self, highlight_total: bool = False) -> TableStyle:
        """Get table style for notes."""
        style_commands = [
            ('FONTNAME', (0, 0), (-1, -1), self.branding['typography']['primary_font']),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'RIGHT'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor(self.branding['colors']['lighter_gray'])),
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(self.branding['colors']['primary_light'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
        ]
        
        if highlight_total:
            style_commands.extend([
                ('LINEABOVE', (0, -1), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, -1), (-1, -1), self.branding['typography']['primary_font']),
                ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor(self.branding['colors']['background'])),
            ])
            
        return TableStyle(style_commands)
        
    def _get_movement_table_style(self) -> TableStyle:
        """Get table style for movement schedules."""
        return TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.branding['typography']['primary_font']),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'RIGHT'),
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(self.branding['colors']['primary_light'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('GRID', (1, 0), (-1, -1), 0.5, colors.HexColor(self.branding['colors']['lighter_gray'])),
            ('LINEBELOW', (0, 0), (-1, 0), 1, colors.HexColor(self.branding['colors']['primary'])),
            ('FONTNAME', (0, 1), (0, 1), self.branding['typography']['primary_font']),
            ('FONTNAME', (0, 7), (0, 7), self.branding['typography']['primary_font']),
            ('FONTNAME', (0, 13), (0, 13), self.branding['typography']['primary_font']),
            ('LINEABOVE', (1, 14), (-1, 14), 1, colors.black),
            ('LINEABOVE', (1, 15), (-1, 15), 0.5, colors.black),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
        ])