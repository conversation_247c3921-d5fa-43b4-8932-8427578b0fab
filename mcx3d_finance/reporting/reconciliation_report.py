"""
Bank reconciliation report generator for PDF and Excel outputs.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

# PDF generation
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, landscape
from reportlab.platypus import (
    SimpleDocTemplate,
    Table,
    TableStyle,
    Paragraph,
    Spacer,
    PageBreak,
)
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT

# Excel generation
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from sqlalchemy.orm import Session

from mcx3d_finance.db.models import BankReconciliation, ReconciliationMatch, BankTransaction, Organization

logger = LoggerFactory.get_logger(__name__)


class ReconciliationReportGenerator:
    """Generate comprehensive reconciliation reports in PDF and Excel formats."""
    
    def __init__(self) -> None:
        """Initialize the report generator."""
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        
    def _setup_custom_styles(self) -> None:
        """Set up custom paragraph styles for the PDF."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            textColor=colors.HexColor('#1E3A8A'),
            spaceAfter=30,
        ))
        
        # Subtitle style
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            textColor=colors.HexColor('#3B82F6'),
            spaceAfter=20,
        ))
        
        # Normal text with better spacing
        self.styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=self.styles['Normal'],
            fontSize=10,
            leading=14,
            spaceAfter=12,
        ))
        
    def generate_pdf_report(
        self,
        reconciliation_id: int,
        db: Session,
        output_path: str
    ) -> None:
        """
        Generate a PDF reconciliation report.
        
        Args:
            reconciliation_id: ID of the reconciliation
            db: Database session
            output_path: Path to save the PDF
        """
        try:
            # Get reconciliation data
            reconciliation = db.query(BankReconciliation).get(reconciliation_id)
            if not reconciliation:
                raise ValueError(f"Reconciliation {reconciliation_id} not found")
            
            organization = db.query(Organization).get(reconciliation.organization_id)
            
            # Create PDF document
            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )
            
            # Build content
            story = []
            
            # Title
            story.append(Paragraph(
                f"Bank Reconciliation Report",
                self.styles['CustomTitle']
            ))
            
            # Organization and period info
            story.append(Paragraph(
                f"<b>Organization:</b> {organization.name}<br/>"
                f"<b>Bank Account:</b> {reconciliation.bank_account_id}<br/>"
                f"<b>Period:</b> {reconciliation.start_date.strftime('%Y-%m-%d')} to "
                f"{reconciliation.end_date.strftime('%Y-%m-%d')}<br/>"
                f"<b>Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                self.styles['CustomNormal']
            ))
            
            story.append(Spacer(1, 0.5*inch))
            
            # Summary statistics
            story.append(Paragraph("Summary", self.styles['CustomSubtitle']))
            
            match_rate = (
                reconciliation.matched_transactions / reconciliation.total_transactions * 100
                if reconciliation.total_transactions > 0 else 0
            )
            
            summary_data = [
                ['Metric', 'Value'],
                ['Total Bank Transactions', str(reconciliation.total_transactions)],
                ['Matched Transactions', str(reconciliation.matched_transactions)],
                ['Unmatched Transactions', str(reconciliation.unmatched_transactions)],
                ['Match Rate', f"{match_rate:.1f}%"],
                ['Total Amount', f"${reconciliation.total_amount:,.2f}"],
                ['Matched Amount', f"${reconciliation.matched_amount:,.2f}"],
                ['Unmatched Amount', f"${reconciliation.unmatched_amount:,.2f}"],
            ]
            
            summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            story.append(summary_table)
            story.append(Spacer(1, 0.5*inch))
            
            # Matched transactions
            if reconciliation.matched_transactions > 0:
                story.append(Paragraph("Matched Transactions", self.styles['CustomSubtitle']))
                
                matches = db.query(ReconciliationMatch).filter(
                    ReconciliationMatch.reconciliation_id == reconciliation_id
                ).order_by(ReconciliationMatch.statement_date).all()
                
                matched_data = [['Date', 'Bank Transaction', 'Statement Entry', 'Amount', 'Confidence']]
                
                for match in matches[:50]:  # Show first 50 matches
                    bank_tx = db.query(BankTransaction).get(match.bank_transaction_id)
                    
                    matched_data.append([
                        match.statement_date.strftime('%Y-%m-%d') if match.statement_date else '',
                        bank_tx.reference or 'N/A',
                        match.statement_description[:40] + '...' if len(match.statement_description) > 40 else match.statement_description,
                        f"${match.statement_amount:,.2f}",
                        f"{match.match_confidence:.0%}"
                    ])
                
                if len(matches) > 50:
                    matched_data.append(['...', f'and {len(matches) - 50} more matches', '', '', ''])
                
                matched_table = Table(matched_data, colWidths=[1.2*inch, 1.5*inch, 2.3*inch, 1*inch, 1*inch])
                matched_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('ALIGN', (3, 0), (4, -1), 'RIGHT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                ]))
                
                story.append(matched_table)
                story.append(Spacer(1, 0.5*inch))
            
            # Unmatched transactions
            if reconciliation.results and reconciliation.results.get('unmatched_bank'):
                story.append(PageBreak())
                story.append(Paragraph("Unmatched Bank Transactions", self.styles['CustomSubtitle']))
                
                unmatched_data = [['Date', 'Reference', 'Description', 'Amount', 'Type']]
                
                for tx in reconciliation.results['unmatched_bank'][:50]:
                    unmatched_data.append([
                        tx['date'][:10] if isinstance(tx['date'], str) else tx['date'].strftime('%Y-%m-%d'),
                        tx.get('reference', 'N/A'),
                        tx.get('description', 'N/A')[:40] + '...' if len(tx.get('description', '')) > 40 else tx.get('description', 'N/A'),
                        f"${tx['total']:,.2f}",
                        tx.get('type', 'N/A')
                    ])
                
                if len(reconciliation.results['unmatched_bank']) > 50:
                    unmatched_data.append(['...', f"and {len(reconciliation.results['unmatched_bank']) - 50} more unmatched transactions", '', '', ''])
                
                unmatched_table = Table(unmatched_data, colWidths=[1.2*inch, 1.5*inch, 2.3*inch, 1*inch, 1*inch])
                unmatched_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('ALIGN', (3, 0), (3, -1), 'RIGHT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                ]))
                
                story.append(unmatched_table)
            
            # Build PDF
            doc.build(story)
            logger.info(f"Generated PDF reconciliation report: {output_path}")
            
        except Exception as e:
            logger.error(f"Error generating PDF report: {e}")
            raise
    
    def generate_excel_report(
        self,
        reconciliation_id: int,
        db: Session,
        output_path: str
    ) -> None:
        """
        Generate an Excel reconciliation report with multiple sheets.
        
        Args:
            reconciliation_id: ID of the reconciliation
            db: Database session
            output_path: Path to save the Excel file
        """
        try:
            # Get reconciliation data
            reconciliation = db.query(BankReconciliation).get(reconciliation_id)
            if not reconciliation:
                raise ValueError(f"Reconciliation {reconciliation_id} not found")
            
            organization = db.query(Organization).get(reconciliation.organization_id)
            
            # Create workbook
            wb = Workbook()
            
            # Summary sheet
            ws_summary = wb.active
            ws_summary.title = "Summary"
            self._create_summary_sheet(ws_summary, reconciliation, organization)
            
            # Matched transactions sheet
            ws_matched = wb.create_sheet("Matched Transactions")
            self._create_matched_sheet(ws_matched, reconciliation_id, db)
            
            # Unmatched bank transactions sheet
            if reconciliation.results and reconciliation.results.get('unmatched_bank'):
                ws_unmatched_bank = wb.create_sheet("Unmatched Bank")
                self._create_unmatched_bank_sheet(ws_unmatched_bank, reconciliation)
            
            # Unmatched statement entries sheet
            if reconciliation.results and reconciliation.results.get('unmatched_statement'):
                ws_unmatched_stmt = wb.create_sheet("Unmatched Statement")
                self._create_unmatched_statement_sheet(ws_unmatched_stmt, reconciliation)
            
            # Save workbook
            wb.save(output_path)
            logger.info(f"Generated Excel reconciliation report: {output_path}")
            
        except Exception as e:
            logger.error(f"Error generating Excel report: {e}")
            raise
    
    def _create_summary_sheet(self, ws: Any, reconciliation: Any, organization: Any) -> None:
        """Create the summary worksheet."""
        # Title
        ws['A1'] = "Bank Reconciliation Report"
        ws['A1'].font = Font(size=20, bold=True, color="1E3A8A")
        ws.merge_cells('A1:E1')
        
        # Organization info
        ws['A3'] = "Organization:"
        ws['B3'] = organization.name
        ws['A4'] = "Bank Account:"
        ws['B4'] = reconciliation.bank_account_id
        ws['A5'] = "Period:"
        ws['B5'] = f"{reconciliation.start_date.strftime('%Y-%m-%d')} to {reconciliation.end_date.strftime('%Y-%m-%d')}"
        ws['A6'] = "Generated:"
        ws['B6'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Format info section
        for row in range(3, 7):
            ws[f'A{row}'].font = Font(bold=True)
        
        # Summary statistics
        ws['A8'] = "Summary Statistics"
        ws['A8'].font = Font(size=14, bold=True, color="3B82F6")
        ws.merge_cells('A8:B8')
        
        match_rate = (
            reconciliation.matched_transactions / reconciliation.total_transactions * 100
            if reconciliation.total_transactions > 0 else 0
        )
        
        summary_data = [
            ("Total Bank Transactions", reconciliation.total_transactions),
            ("Matched Transactions", reconciliation.matched_transactions),
            ("Unmatched Transactions", reconciliation.unmatched_transactions),
            ("Match Rate", f"{match_rate:.1f}%"),
            ("Total Amount", f"${reconciliation.total_amount:,.2f}"),
            ("Matched Amount", f"${reconciliation.matched_amount:,.2f}"),
            ("Unmatched Amount", f"${reconciliation.unmatched_amount:,.2f}"),
        ]
        
        row_num = 10
        for label, value in summary_data:
            ws[f'A{row_num}'] = label
            ws[f'B{row_num}'] = value
            ws[f'A{row_num}'].font = Font(bold=True)
            row_num += 1
        
        # Apply borders
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in range(10, row_num):
            ws[f'A{row}'].border = thin_border
            ws[f'B{row}'].border = thin_border
        
        # Column widths
        ws.column_dimensions['A'].width = 25
        ws.column_dimensions['B'].width = 20
    
    def _create_matched_sheet(self, ws: Any, reconciliation_id: int, db: Session) -> None:
        """Create the matched transactions worksheet."""
        # Headers
        headers = ['Date', 'Bank Reference', 'Statement Description', 'Amount', 'Confidence', 'Match Type']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # Get matched transactions
        matches = db.query(ReconciliationMatch).filter(
            ReconciliationMatch.reconciliation_id == reconciliation_id
        ).order_by(ReconciliationMatch.statement_date).all()
        
        # Add data
        row_num = 2
        for match in matches:
            bank_tx = db.query(BankTransaction).get(match.bank_transaction_id)
            
            ws.cell(row=row_num, column=1, value=match.statement_date.strftime('%Y-%m-%d') if match.statement_date else '')
            ws.cell(row=row_num, column=2, value=bank_tx.reference or 'N/A')
            ws.cell(row=row_num, column=3, value=match.statement_description)
            ws.cell(row=row_num, column=4, value=match.statement_amount)
            ws.cell(row=row_num, column=5, value=match.match_confidence)
            ws.cell(row=row_num, column=6, value=match.match_type)
            
            # Format cells
            ws.cell(row=row_num, column=4).number_format = '$#,##0.00'
            ws.cell(row=row_num, column=5).number_format = '0%'
            
            # Apply alternating row colors
            if row_num % 2 == 0:
                for col in range(1, 7):
                    ws.cell(row=row_num, column=col).fill = PatternFill(
                        start_color="F2F2F2", end_color="F2F2F2", fill_type="solid"
                    )
            
            row_num += 1
        
        # Auto-fit columns
        for col in range(1, 7):
            ws.column_dimensions[get_column_letter(col)].width = 20
    
    def _create_unmatched_bank_sheet(self, ws: Any, reconciliation: Any) -> None:
        """Create the unmatched bank transactions worksheet."""
        # Headers
        headers = ['Date', 'Reference', 'Description', 'Amount', 'Type']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="E74C3C", end_color="E74C3C", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # Add data
        row_num = 2
        for tx in reconciliation.results.get('unmatched_bank', []):
            ws.cell(row=row_num, column=1, value=tx['date'][:10] if isinstance(tx['date'], str) else tx['date'].strftime('%Y-%m-%d'))
            ws.cell(row=row_num, column=2, value=tx.get('reference', 'N/A'))
            ws.cell(row=row_num, column=3, value=tx.get('description', 'N/A'))
            ws.cell(row=row_num, column=4, value=tx['total'])
            ws.cell(row=row_num, column=5, value=tx.get('type', 'N/A'))
            
            # Format cells
            ws.cell(row=row_num, column=4).number_format = '$#,##0.00'
            
            # Apply alternating row colors
            if row_num % 2 == 0:
                for col in range(1, 6):
                    ws.cell(row=row_num, column=col).fill = PatternFill(
                        start_color="F2F2F2", end_color="F2F2F2", fill_type="solid"
                    )
            
            row_num += 1
        
        # Auto-fit columns
        for col in range(1, 6):
            ws.column_dimensions[get_column_letter(col)].width = 20
    
    def _create_unmatched_statement_sheet(self, ws: Any, reconciliation: Any) -> None:
        """Create the unmatched statement entries worksheet."""
        # Headers
        headers = ['Date', 'Reference', 'Description', 'Amount']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="F39C12", end_color="F39C12", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # Add data
        row_num = 2
        for entry in reconciliation.results.get('unmatched_statement', []):
            ws.cell(row=row_num, column=1, value=entry.get('date', ''))
            ws.cell(row=row_num, column=2, value=entry.get('reference', 'N/A'))
            ws.cell(row=row_num, column=3, value=entry.get('description', 'N/A'))
            ws.cell(row=row_num, column=4, value=entry.get('amount', 0))
            
            # Format cells
            ws.cell(row=row_num, column=4).number_format = '$#,##0.00'
            
            # Apply alternating row colors
            if row_num % 2 == 0:
                for col in range(1, 5):
                    ws.cell(row=row_num, column=col).fill = PatternFill(
                        start_color="F2F2F2", end_color="F2F2F2", fill_type="solid"
                    )
            
            row_num += 1
        
        # Auto-fit columns
        for col in range(1, 5):
            ws.column_dimensions[get_column_letter(col)].width = 20