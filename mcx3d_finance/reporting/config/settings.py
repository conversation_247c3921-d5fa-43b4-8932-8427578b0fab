"""
Report generation settings and configuration.
"""

import os
from typing import Dict, Any
from pathlib import Path


class ReportSettings:
    """
    Centralized settings for report generation.
    
    Consolidates configuration previously scattered across modules.
    """

    def __init__(self) -> None:
        # Base directories
        self.base_output_dir = os.getenv("REPORTS_OUTPUT_DIR", "reports")
        self.template_dir = Path(__file__).parent.parent / "templates"
        
        # Cache settings
        self.cache_enabled = os.getenv("REPORTS_CACHE_ENABLED", "true").lower() == "true"
        self.cache_ttl = int(os.getenv("REPORTS_CACHE_TTL", "3600"))  # 1 hour
        self.cache_backend = os.getenv("REPORTS_CACHE_BACKEND", "redis")
        
        # File settings
        self.max_file_age_days = int(os.getenv("REPORTS_MAX_FILE_AGE_DAYS", "30"))
        self.timestamp_format = "%Y%m%d_%H%M%S"
        
        # Performance settings
        self.enable_parallel_generation = os.getenv("REPORTS_PARALLEL_ENABLED", "true").lower() == "true"
        self.max_concurrent_reports = int(os.getenv("REPORTS_MAX_CONCURRENT", "3"))
        
        # Format-specific settings
        self.pdf_settings = {
            "page_size": "A4",
            "margin": "1in",
            "font_family": "Arial",
            "font_size": 10
        }
        
        self.excel_settings = {
            "auto_width": True,
            "freeze_panes": True,
            "include_formatting": True
        }
        
        # Company branding
        self.company_logo_path = os.getenv("COMPANY_LOGO_PATH")
        self.company_colors = {
            "primary": "#1f4e79",
            "secondary": "#70ad47", 
            "accent": "#ffc000"
        }

    def get_format_settings(self, format_type: str) -> Dict[str, Any]:
        """Get settings for specific output format."""
        format_settings: Dict[str, Dict[str, Any]] = {
            "pdf": self.pdf_settings,
            "excel": self.excel_settings,
            "json": {"indent": 2, "ensure_ascii": False},
            "html": {"include_css": True, "responsive": True}
        }
        
        return format_settings.get(format_type, {})

    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration."""
        return {
            "base_dir": self.base_output_dir,
            "timestamp_format": self.timestamp_format,
            "max_age_days": self.max_file_age_days,
            "organize_by_year": True
        }

    def get_cache_config(self) -> Dict[str, Any]:
        """Get cache configuration."""
        return {
            "enabled": self.cache_enabled,
            "ttl": self.cache_ttl,
            "backend": self.cache_backend
        }