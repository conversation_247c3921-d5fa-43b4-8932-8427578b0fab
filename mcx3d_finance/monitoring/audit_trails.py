"""
Audit Trails System for MCX3D Financial Platform

Provides comprehensive audit logging for financial transactions, user activities,
data modifications, and compliance tracking with immutable audit records.
"""

import json
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from mcx3d_finance.core.logging_factory import LoggerFactory

from mcx3d_finance.monitoring.structured_logger import main_logger, get_correlation_id

logger = LoggerFactory.get_logger(__name__, domain='monitoring')

class AuditEventType(Enum):
    """Types of auditable events."""
    FINANCIAL_TRANSACTION = "financial_transaction"
    DATA_MODIFICATION = "data_modification"
    USER_AUTHENTICATION = "user_authentication"
    REPORT_GENERATION = "report_generation"
    VALUATION_CALCULATION = "valuation_calculation"
    DATA_SYNC = "data_sync"
    SYSTEM_ACCESS = "system_access"
    CONFIGURATION_CHANGE = "configuration_change"
    SECURITY_EVENT = "security_event"
    COMPLIANCE_CHECK = "compliance_check"

class AuditSeverity(Enum):
    """Audit event severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class AuditRecord:
    """Immutable audit record structure."""
    event_id: str
    event_type: AuditEventType
    timestamp: datetime
    user_id: Optional[str]
    session_id: Optional[str]
    correlation_id: Optional[str]
    severity: AuditSeverity
    description: str
    entity_type: Optional[str]
    entity_id: Optional[str]
    old_values: Optional[Dict[str, Any]]
    new_values: Optional[Dict[str, Any]]
    metadata: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    checksum: str
    
    def __post_init__(self):
        """Calculate checksum after initialization."""
        if not self.checksum:
            self.checksum = self._calculate_checksum()
    
    def _calculate_checksum(self) -> str:
        """Calculate SHA-256 checksum for audit record integrity."""
        # Create a copy without checksum for hashing
        record_dict = asdict(self)
        record_dict.pop('checksum', None)
        
        # Convert datetime to ISO string for consistent hashing
        record_dict['timestamp'] = self.timestamp.isoformat()
        record_dict['event_type'] = self.event_type.value
        record_dict['severity'] = self.severity.value
        
        # Create deterministic JSON string
        json_str = json.dumps(record_dict, sort_keys=True, default=str)
        
        # Calculate SHA-256 hash
        return hashlib.sha256(json_str.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify audit record integrity using checksum."""
        expected_checksum = self._calculate_checksum()
        return self.checksum == expected_checksum

class AuditTrailManager:
    """
    Comprehensive audit trail management system.
    
    Features:
    - Immutable audit record creation
    - Financial transaction audit logging
    - User activity tracking
    - Data modification audit trails
    - Compliance reporting
    - Audit record integrity verification
    """
    
    def __init__(self, db_session=None):
        self.db = db_session
        self.audit_storage = []  # In-memory storage for demo; use database in production
    
    async def log_financial_transaction(self, transaction_data: Dict[str, Any], 
                                      user_id: Optional[str] = None,
                                      metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Log financial transaction for audit and compliance.
        
        Args:
            transaction_data: Financial transaction details
            user_id: User performing the transaction
            metadata: Additional audit metadata
            
        Returns:
            Audit record ID
        """
        audit_record = await self._create_audit_record(
            event_type=AuditEventType.FINANCIAL_TRANSACTION,
            description=f"Financial transaction: {transaction_data.get('type', 'unknown')}",
            user_id=user_id,
            severity=AuditSeverity.HIGH,
            entity_type="financial_transaction",
            entity_id=transaction_data.get('transaction_id'),
            new_values=transaction_data,
            metadata={
                'amount': transaction_data.get('amount'),
                'currency': transaction_data.get('currency', 'USD'),
                'transaction_type': transaction_data.get('type'),
                'reference': transaction_data.get('reference'),
                **(metadata or {})
            }
        )
        
        # Log to structured logging system
        main_logger.log_financial_transaction(
            transaction_data.get('type', 'unknown'),
            transaction_data.get('amount', 0),
            transaction_data.get('currency', 'USD'),
            audit_record_id=audit_record.event_id,
            **transaction_data
        )
        
        return audit_record.event_id
    
    async def log_data_modification(self, entity_type: str, entity_id: str,
                                  old_values: Dict[str, Any], new_values: Dict[str, Any],
                                  user_id: Optional[str] = None,
                                  metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Log data modification events for audit trail.
        
        Args:
            entity_type: Type of entity being modified
            entity_id: Unique identifier of the entity
            old_values: Previous values
            new_values: New values
            user_id: User making the modification
            metadata: Additional audit metadata
            
        Returns:
            Audit record ID
        """
        # Calculate changes
        changes = self._calculate_field_changes(old_values, new_values)
        
        audit_record = await self._create_audit_record(
            event_type=AuditEventType.DATA_MODIFICATION,
            description=f"Modified {entity_type} {entity_id}",
            user_id=user_id,
            severity=AuditSeverity.MEDIUM,
            entity_type=entity_type,
            entity_id=entity_id,
            old_values=old_values,
            new_values=new_values,
            metadata={
                'changes_count': len(changes),
                'changed_fields': list(changes.keys()),
                **(metadata or {})
            }
        )
        
        # Log to structured logging system
        main_logger.log_business_event(
            'data_modification',
            entity_type=entity_type,
            entity_id=entity_id,
            changes_count=len(changes),
            audit_record_id=audit_record.event_id
        )
        
        return audit_record.event_id
    
    async def log_user_authentication(self, user_id: str, authentication_method: str,
                                    success: bool, ip_address: Optional[str] = None,
                                    user_agent: Optional[str] = None,
                                    metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Log user authentication events for security audit.
        
        Args:
            user_id: User identifier
            authentication_method: Authentication method used
            success: Whether authentication was successful
            ip_address: Client IP address
            user_agent: Client user agent
            metadata: Additional audit metadata
            
        Returns:
            Audit record ID
        """
        severity = AuditSeverity.MEDIUM if success else AuditSeverity.HIGH
        
        audit_record = await self._create_audit_record(
            event_type=AuditEventType.USER_AUTHENTICATION,
            description=f"User authentication {'successful' if success else 'failed'}: {authentication_method}",
            user_id=user_id,
            severity=severity,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata={
                'authentication_method': authentication_method,
                'success': success,
                'authentication_timestamp': datetime.utcnow().isoformat(),
                **(metadata or {})
            }
        )
        
        # Log to structured logging system
        if success:
            main_logger.log_user_activity(
                'authentication_success',
                user_id,
                authentication_method,
                ip_address=ip_address,
                audit_record_id=audit_record.event_id
            )
        else:
            main_logger.log_security_event(
                'authentication_failure',
                severity='high',
                user_id=user_id,
                authentication_method=authentication_method,
                ip_address=ip_address,
                audit_record_id=audit_record.event_id
            )
        
        return audit_record.event_id
    
    async def log_report_generation(self, report_data: Dict[str, Any],
                                  user_id: Optional[str] = None,
                                  metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Log report generation events for business audit.
        
        Args:
            report_data: Report generation details
            user_id: User requesting the report
            metadata: Additional audit metadata
            
        Returns:
            Audit record ID
        """
        audit_record = await self._create_audit_record(
            event_type=AuditEventType.REPORT_GENERATION,
            description=f"Generated report: {report_data.get('type', 'unknown')}",
            user_id=user_id,
            severity=AuditSeverity.MEDIUM,
            entity_type="report",
            entity_id=report_data.get('report_id'),
            new_values=report_data,
            metadata={
                'report_type': report_data.get('type'),
                'output_format': report_data.get('format'),
                'organization_id': report_data.get('organization_id'),
                'generation_duration_ms': report_data.get('duration_ms'),
                'file_size_bytes': report_data.get('file_size_bytes'),
                **(metadata or {})
            }
        )
        
        # Log to structured logging system
        main_logger.log_business_event(
            'report_generation_audit',
            report_type=report_data.get('type'),
            output_format=report_data.get('format'),
            organization_id=report_data.get('organization_id'),
            audit_record_id=audit_record.event_id
        )
        
        return audit_record.event_id
    
    async def log_valuation_calculation(self, valuation_data: Dict[str, Any],
                                      user_id: Optional[str] = None,
                                      metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Log valuation calculations for financial audit.
        
        Args:
            valuation_data: Valuation calculation details
            user_id: User performing the valuation
            metadata: Additional audit metadata
            
        Returns:
            Audit record ID
        """
        audit_record = await self._create_audit_record(
            event_type=AuditEventType.VALUATION_CALCULATION,
            description=f"Valuation calculated: {valuation_data.get('model_type', 'unknown')}",
            user_id=user_id,
            severity=AuditSeverity.HIGH,
            entity_type="valuation",
            entity_id=valuation_data.get('valuation_id'),
            new_values=valuation_data,
            metadata={
                'model_type': valuation_data.get('model_type'),
                'valuation_amount': valuation_data.get('amount'),
                'currency': valuation_data.get('currency', 'USD'),
                'organization_id': valuation_data.get('organization_id'),
                'calculation_duration_ms': valuation_data.get('duration_ms'),
                'input_data_points': valuation_data.get('data_points_count'),
                **(metadata or {})
            }
        )
        
        # Log to structured logging system
        main_logger.log_business_event(
            'valuation_calculation_audit',
            model_type=valuation_data.get('model_type'),
            valuation_amount=valuation_data.get('amount'),
            organization_id=valuation_data.get('organization_id'),
            audit_record_id=audit_record.event_id
        )
        
        return audit_record.event_id
    
    async def log_security_event(self, event_description: str, severity: AuditSeverity,
                               user_id: Optional[str] = None,
                               ip_address: Optional[str] = None,
                               metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Log security-related events for compliance audit.
        
        Args:
            event_description: Description of the security event
            severity: Event severity level
            user_id: User associated with the event
            ip_address: Client IP address
            metadata: Additional security metadata
            
        Returns:
            Audit record ID
        """
        audit_record = await self._create_audit_record(
            event_type=AuditEventType.SECURITY_EVENT,
            description=event_description,
            user_id=user_id,
            severity=severity,
            ip_address=ip_address,
            metadata={
                'security_event_type': metadata.get('event_type', 'unknown'),
                'risk_level': severity.value,
                'detection_timestamp': datetime.utcnow().isoformat(),
                **(metadata or {})
            }
        )
        
        # Log to structured logging system
        main_logger.log_security_event(
            metadata.get('event_type', 'unknown'),
            severity=severity.value,
            user_id=user_id,
            ip_address=ip_address,
            audit_record_id=audit_record.event_id,
            description=event_description
        )
        
        return audit_record.event_id
    
    async def get_audit_trail(self, entity_type: Optional[str] = None,
                            entity_id: Optional[str] = None,
                            user_id: Optional[str] = None,
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None,
                            event_types: Optional[List[AuditEventType]] = None,
                            limit: int = 100) -> List[AuditRecord]:
        """
        Retrieve audit trail records with filtering options.
        
        Args:
            entity_type: Filter by entity type
            entity_id: Filter by entity ID
            user_id: Filter by user ID
            start_date: Filter by start date
            end_date: Filter by end date
            event_types: Filter by event types
            limit: Maximum number of records to return
            
        Returns:
            List of audit records
        """
        # In production, this would query the database
        # For now, filter in-memory storage
        
        filtered_records = []
        
        for record in self.audit_storage:
            # Apply filters
            if entity_type and record.entity_type != entity_type:
                continue
            if entity_id and record.entity_id != entity_id:
                continue
            if user_id and record.user_id != user_id:
                continue
            if start_date and record.timestamp < start_date:
                continue
            if end_date and record.timestamp > end_date:
                continue
            if event_types and record.event_type not in event_types:
                continue
            
            # Verify record integrity
            if not record.verify_integrity():
                logger.error(f"Audit record integrity check failed: {record.event_id}")
                continue
            
            filtered_records.append(record)
        
        # Sort by timestamp (most recent first) and apply limit
        filtered_records.sort(key=lambda r: r.timestamp, reverse=True)
        return filtered_records[:limit]
    
    async def generate_compliance_report(self, start_date: datetime, end_date: datetime,
                                       report_type: str = "full") -> Dict[str, Any]:
        """
        Generate compliance audit report for specified date range.
        
        Args:
            start_date: Report start date
            end_date: Report end date
            report_type: Type of compliance report
            
        Returns:
            Compliance report data
        """
        # Get all audit records in date range
        all_records = await self.get_audit_trail(
            start_date=start_date,
            end_date=end_date,
            limit=10000  # Large limit for comprehensive report
        )
        
        # Group records by event type
        records_by_type = {}
        for record in all_records:
            event_type = record.event_type.value
            if event_type not in records_by_type:
                records_by_type[event_type] = []
            records_by_type[event_type].append(record)
        
        # Generate statistics
        report = {
            'report_type': report_type,
            'date_range': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'summary': {
                'total_events': len(all_records),
                'events_by_type': {k: len(v) for k, v in records_by_type.items()},
                'unique_users': len(set(r.user_id for r in all_records if r.user_id)),
                'integrity_verified': all(r.verify_integrity() for r in all_records)
            },
            'high_risk_events': [
                {
                    'event_id': record.event_id,
                    'event_type': record.event_type.value,
                    'timestamp': record.timestamp.isoformat(),
                    'description': record.description,
                    'user_id': record.user_id
                }
                for record in all_records 
                if record.severity in [AuditSeverity.HIGH, AuditSeverity.CRITICAL]
            ],
            'compliance_checks': {
                'all_financial_transactions_audited': True,
                'user_authentication_logged': True,
                'data_modifications_tracked': True,
                'security_events_recorded': True
            },
            'generated_at': datetime.utcnow().isoformat()
        }
        
        return report
    
    # Helper methods
    
    async def _create_audit_record(self, event_type: AuditEventType, description: str,
                                 user_id: Optional[str] = None,
                                 severity: AuditSeverity = AuditSeverity.MEDIUM,
                                 entity_type: Optional[str] = None,
                                 entity_id: Optional[str] = None,
                                 old_values: Optional[Dict[str, Any]] = None,
                                 new_values: Optional[Dict[str, Any]] = None,
                                 ip_address: Optional[str] = None,
                                 user_agent: Optional[str] = None,
                                 metadata: Optional[Dict[str, Any]] = None) -> AuditRecord:
        """Create and store an audit record."""
        import uuid
        
        audit_record = AuditRecord(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            timestamp=datetime.utcnow(),
            user_id=user_id,
            session_id=None,  # Could be extracted from request context
            correlation_id=get_correlation_id(),
            severity=severity,
            description=description,
            entity_type=entity_type,
            entity_id=entity_id,
            old_values=old_values,
            new_values=new_values,
            metadata=metadata or {},
            ip_address=ip_address,
            user_agent=user_agent,
            checksum=""  # Will be calculated in __post_init__
        )
        
        # Store audit record (in production, save to database)
        self.audit_storage.append(audit_record)
        
        return audit_record
    
    def _calculate_field_changes(self, old_values: Dict[str, Any], 
                               new_values: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Calculate field-level changes between old and new values."""
        changes = {}
        
        # Check for modified fields
        for key in set(old_values.keys()) | set(new_values.keys()):
            old_val = old_values.get(key)
            new_val = new_values.get(key)
            
            if old_val != new_val:
                changes[key] = {
                    'old_value': old_val,
                    'new_value': new_val,
                    'change_type': self._determine_change_type(old_val, new_val)
                }
        
        return changes
    
    def _determine_change_type(self, old_value: Any, new_value: Any) -> str:
        """Determine the type of change between two values."""
        if old_value is None and new_value is not None:
            return 'created'
        elif old_value is not None and new_value is None:
            return 'deleted'
        else:
            return 'modified'


# Global audit manager instance
audit_manager = AuditTrailManager()

# Convenience functions for common audit operations
async def audit_financial_transaction(transaction_data: Dict[str, Any], 
                                    user_id: Optional[str] = None) -> str:
    """Convenience function to audit financial transactions."""
    return await audit_manager.log_financial_transaction(transaction_data, user_id)

async def audit_data_change(entity_type: str, entity_id: str, 
                          old_values: Dict[str, Any], new_values: Dict[str, Any],
                          user_id: Optional[str] = None) -> str:
    """Convenience function to audit data modifications."""
    return await audit_manager.log_data_modification(
        entity_type, entity_id, old_values, new_values, user_id
    )

async def audit_user_login(user_id: str, success: bool, 
                         authentication_method: str = "password",
                         ip_address: Optional[str] = None) -> str:
    """Convenience function to audit user authentication."""
    return await audit_manager.log_user_authentication(
        user_id, authentication_method, success, ip_address
    )

async def audit_report_creation(report_data: Dict[str, Any], 
                              user_id: Optional[str] = None) -> str:
    """Convenience function to audit report generation."""
    return await audit_manager.log_report_generation(report_data, user_id)

async def audit_valuation(valuation_data: Dict[str, Any], 
                        user_id: Optional[str] = None) -> str:
    """Convenience function to audit valuation calculations."""
    return await audit_manager.log_valuation_calculation(valuation_data, user_id)