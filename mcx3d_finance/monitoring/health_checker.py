"""
Comprehensive Health Monitoring for MCX3D Financial Platform

Provides advanced system health monitoring with detailed component status,
dependency checking, performance benchmarking, and automated health reporting.
"""

import time
import asyncio
import psutil
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from sqlalchemy import text
import redis
from mcx3d_finance.core.logging_factory import LoggerFactory
import json
import aiohttp
import socket
import tempfile
import os

from mcx3d_finance.monitoring.structured_logger import main_logger
from mcx3d_finance.monitoring.metrics import update_health_status, COMPONENT_STATUS, update_celery_metrics, record_celery_queue_metrics, record_external_service_metrics
from mcx3d_finance.monitoring.alerting import alert_manager, AlertSeverity
from mcx3d_finance.db.redis_client import get_redis_client
from mcx3d_finance.monitoring.config_loader import get_monitoring_config
from mcx3d_finance.tasks.celery_app import celery_app
from mcx3d_finance.reporting.generator import ReportGenerator


logger = LoggerFactory.get_logger(__name__, domain='monitoring')

class HealthStatus:
    """Health status constants."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    ERROR = "error"
    UNKNOWN = "unknown"

class HealthChecker:
    """
    Comprehensive health monitoring for all system components.
    
    Features:
    - Database connectivity and performance monitoring
    - Redis cache health and performance
    - Celery worker monitoring
    - External service dependency checking
    - System resource monitoring
    - Application-specific health checks
    - Performance benchmarking
    """
    
    def __init__(self, db_session=None, redis_client=None):
        self.db = db_session
        # Use singleton Redis client if none provided
        if redis_client is None:
            try:
                self.redis_client = get_redis_client()
            except Exception as e:
                logger.warning(f"Failed to initialize Redis client: {e}")
                self.redis_client = None
        else:
            self.redis_client = redis_client
        
        # Load configurable thresholds based on environment
        try:
            self.config_loader = get_monitoring_config()
            self.health_thresholds = self.config_loader.get_health_thresholds()
            logger.info(f"Loaded health thresholds for environment: {self.config_loader.environment}")
        except Exception as e:
            logger.warning(f"Failed to load monitoring configuration, using defaults: {e}")
            # Fallback to hardcoded defaults
            self.health_thresholds = {
                'database': {
                    'max_response_time_ms': 100,
                    'max_active_connections': 80,
                    'critical_response_time_ms': 1000
                },
                'redis': {
                    'max_response_time_ms': 10,
                    'max_memory_usage_percent': 80,
                    'critical_response_time_ms': 100
                },
                'system': {
                    'max_cpu_percent': 80,
                    'max_memory_percent': 85,
                    'max_disk_percent': 90,
                    'critical_cpu_percent': 95,
                    'critical_memory_percent': 95
                },
                'application': {
                    'max_response_time_ms': 2000,
                    'critical_response_time_ms': 5000,
                    'min_success_rate': 0.95
                },
                'celery': {
                    'max_queue_length': 1000,
                    'critical_queue_length': 5000,
                    'max_failed_tasks': 10,
                    'critical_failed_tasks': 50,
                    'min_worker_count': 1
                }
            }
    
    async def get_comprehensive_health(self) -> Dict[str, Any]:
        """
        Get comprehensive system health status with detailed analysis.
        
        Returns:
            Comprehensive health report
        """
        start_time = time.time()
        
        try:
            # Run all health checks in parallel
            health_checks = await asyncio.gather(
                self._check_database_health(),
                self._check_redis_health(),
                self._check_system_resources(),
                self._check_celery_workers(),
                self._check_external_services(),
                self._check_application_health(),
                self._check_file_system_health(),
                return_exceptions=True
            )
            
            # Process results
            health_report = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'overall_status': HealthStatus.HEALTHY,
                'check_duration_ms': round((time.time() - start_time) * 1000, 2),
                'components': {
                    'database': health_checks[0] if not isinstance(health_checks[0], Exception) else self._error_result('database', health_checks[0]),
                    'redis': health_checks[1] if not isinstance(health_checks[1], Exception) else self._error_result('redis', health_checks[1]),
                    'system_resources': health_checks[2] if not isinstance(health_checks[2], Exception) else self._error_result('system', health_checks[2]),
                    'celery_workers': health_checks[3] if not isinstance(health_checks[3], Exception) else self._error_result('celery', health_checks[3]),
                    'external_services': health_checks[4] if not isinstance(health_checks[4], Exception) else self._error_result('external', health_checks[4]),
                    'application': health_checks[5] if not isinstance(health_checks[5], Exception) else self._error_result('application', health_checks[5]),
                    'file_system': health_checks[6] if not isinstance(health_checks[6], Exception) else self._error_result('filesystem', health_checks[6])
                },
                'summary': {},
                'recommendations': []
            }
            
            # Calculate overall status and generate summary
            overall_status, summary, recommendations = self._analyze_health_results(health_report['components'])
            health_report['overall_status'] = overall_status
            health_report['summary'] = summary
            health_report['recommendations'] = recommendations
            
            # Update metrics
            for component_name, component_data in health_report['components'].items():
                is_healthy = component_data['status'] == HealthStatus.HEALTHY
                update_health_status(
                    component_name, 
                    'comprehensive', 
                    is_healthy, 
                    component_data.get('response_time_ms', 0) / 1000
                )
                
                # Update specific Celery metrics if available
                if component_name == 'celery_workers' and 'metrics' in component_data:
                    metrics = component_data['metrics']
                    update_celery_metrics(
                        worker_count=metrics.get('worker_count', 0),
                        active_tasks=metrics.get('active_tasks', 0),
                        queue_length=metrics.get('total_queue_length', 0),
                        failed_tasks=metrics.get('failed_tasks', 0)
                    )
                    
                    # Record detailed queue metrics
                    queue_metrics = {
                        'total_queue_length': metrics.get('total_queue_length', 0),
                        'failed_tasks': metrics.get('failed_tasks', 0),
                        'queues': metrics.get('queues', {})
                    }
                    record_celery_queue_metrics(queue_metrics)
                
                # Update external service metrics if available
                if component_name == 'external_services' and 'services' in component_data:
                    for service_name, service_data in component_data['services'].items():
                        service_healthy = service_data.get('status') == HealthStatus.HEALTHY
                        service_response_time = service_data.get('response_time_ms', 0) / 1000
                        
                        # Use service name as endpoint for single-endpoint services
                        endpoint = service_data.get('endpoint', service_name)
                        
                        record_external_service_metrics(
                            service_name=service_name,
                            endpoint=endpoint,
                            is_healthy=service_healthy,
                            response_time_seconds=service_response_time
                        )
            
            # Log health check completion
            main_logger.log_business_event(
                'health_check_completed',
                overall_status=overall_status,
                check_duration_ms=health_report['check_duration_ms'],
                healthy_components=summary['healthy_count'],
                total_components=summary['total_count']
            )
            
            # Trigger alerts for unhealthy components
            await self._trigger_health_alerts(health_report)
            
            return health_report
            
        except Exception as e:
            logger.error(f"Comprehensive health check failed: {e}")
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'overall_status': HealthStatus.ERROR,
                'error': str(e),
                'check_duration_ms': round((time.time() - start_time) * 1000, 2)
            }
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check PostgreSQL database health and performance."""
        start_time = time.time()
        
        try:
            if not self.db:
                return self._create_health_result(
                    HealthStatus.ERROR,
                    "Database session not available",
                    0
                )
            
            # Test basic connectivity
            result = self.db.execute(text("SELECT 1")).scalar()
            basic_response_time = (time.time() - start_time) * 1000
            
            # Get database statistics
            stats_start = time.time()
            db_stats = self.db.execute(text("""
                SELECT 
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                    (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections,
                    pg_database_size(current_database()) as database_size_bytes
            """)).fetchone()
            
            stats_response_time = (time.time() - stats_start) * 1000
            total_response_time = basic_response_time + stats_response_time
            
            # Determine health status
            thresholds = self.health_thresholds['database']
            if total_response_time > thresholds['critical_response_time_ms']:
                status = HealthStatus.ERROR
                message = f"Database response time critical: {total_response_time:.1f}ms"
            elif (total_response_time > thresholds['max_response_time_ms'] or 
                  db_stats.active_connections > thresholds['max_active_connections']):
                status = HealthStatus.DEGRADED
                message = f"Database performance degraded"
            else:
                status = HealthStatus.HEALTHY
                message = "Database operating normally"
            
            return {
                'status': status,
                'message': message,
                'response_time_ms': round(total_response_time, 2),
                'metrics': {
                    'active_connections': db_stats.active_connections,
                    'max_connections': db_stats.max_connections,
                    'database_size_mb': round(db_stats.database_size_bytes / 1024 / 1024, 2),
                    'connection_usage_percent': round((db_stats.active_connections / db_stats.max_connections) * 100, 2)
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return self._create_health_result(
                HealthStatus.ERROR,
                f"Database check failed: {str(e)}",
                (time.time() - start_time) * 1000
            )
    
    async def _check_redis_health(self) -> Dict[str, Any]:
        """Check Redis cache health and performance."""
        start_time = time.time()
        
        try:
            if not self.redis_client:
                self.redis_client = get_redis_client()
            
            # Test basic connectivity
            ping_result = self.redis_client.ping()
            response_time = (time.time() - start_time) * 1000
            
            # Get Redis info
            info = self.redis_client.info()
            memory_info = self.redis_client.info('memory')
            
            # Calculate metrics
            used_memory_mb = memory_info['used_memory'] / 1024 / 1024
            max_memory_mb = memory_info.get('maxmemory', 0) / 1024 / 1024 if memory_info.get('maxmemory', 0) > 0 else None
            memory_usage_percent = (used_memory_mb / max_memory_mb * 100) if max_memory_mb else 0
            
            # Determine health status
            thresholds = self.health_thresholds['redis']
            if response_time > thresholds['critical_response_time_ms']:
                status = HealthStatus.ERROR
                message = f"Redis response time critical: {response_time:.1f}ms"
            elif (response_time > thresholds['max_response_time_ms'] or 
                  memory_usage_percent > thresholds['max_memory_usage_percent']):
                status = HealthStatus.DEGRADED
                message = "Redis performance degraded"
            else:
                status = HealthStatus.HEALTHY
                message = "Redis operating normally"
            
            return {
                'status': status,
                'message': message,
                'response_time_ms': round(response_time, 2),
                'metrics': {
                    'connected_clients': info['connected_clients'],
                    'used_memory_mb': round(used_memory_mb, 2),
                    'memory_usage_percent': round(memory_usage_percent, 2),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'hit_ratio': round(info.get('keyspace_hits', 0) / max(info.get('keyspace_hits', 0) + info.get('keyspace_misses', 0), 1) * 100, 2)
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return self._create_health_result(
                HealthStatus.ERROR,
                f"Redis check failed: {str(e)}",
                (time.time() - start_time) * 1000
            )
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource utilization."""
        start_time = time.time()
        
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Calculate metrics
            memory_percent = memory.percent
            disk_percent = disk.percent
            
            # Determine health status
            thresholds = self.health_thresholds['system']
            if (cpu_percent > thresholds['critical_cpu_percent'] or 
                memory_percent > thresholds['critical_memory_percent']):
                status = HealthStatus.ERROR
                message = "System resources critical"
            elif (cpu_percent > thresholds['max_cpu_percent'] or 
                  memory_percent > thresholds['max_memory_percent'] or
                  disk_percent > thresholds['max_disk_percent']):
                status = HealthStatus.DEGRADED
                message = "System resources under pressure"
            else:
                status = HealthStatus.HEALTHY
                message = "System resources healthy"
            
            return {
                'status': status,
                'message': message,
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'metrics': {
                    'cpu_percent': round(cpu_percent, 2),
                    'memory_percent': round(memory_percent, 2),
                    'disk_percent': round(disk_percent, 2),
                    'memory_available_mb': round(memory.available / 1024 / 1024, 2),
                    'disk_free_gb': round(disk.free / 1024 / 1024 / 1024, 2),
                    'load_average': list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else None
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return self._create_health_result(
                HealthStatus.ERROR,
                f"System resource check failed: {str(e)}",
                (time.time() - start_time) * 1000
            )
    
    async def _check_celery_workers(self) -> Dict[str, Any]:
        """Check Celery worker health and status with real metrics."""
        start_time = time.time()
        
        try:
            # Get active workers and their stats
            active_workers = {}
            worker_count = 0
            total_active_tasks = 0
            
            try:
                # Get active workers using Celery inspect
                inspect = celery_app.control.inspect()
                
                # Check if we can connect to workers
                ping_result = inspect.ping(timeout=3)
                if ping_result:
                    worker_count = len(ping_result)
                    active_workers = ping_result
                    
                    # Get active tasks
                    active_tasks = inspect.active(timeout=3)
                    if active_tasks:
                        total_active_tasks = sum(len(tasks) for tasks in active_tasks.values())
                    
                    # Get worker stats
                    stats = inspect.stats(timeout=3)
                    
                else:
                    # Fallback to process detection
                    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                        if any('celery' in str(cmd).lower() and 'worker' in str(cmd).lower() 
                              for cmd in proc.info['cmdline'] or []):
                            worker_count += 1
                            
            except Exception as e:
                logger.warning(f"Celery inspect failed, falling back to process detection: {e}")
                # Fallback to process counting
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if any('celery' in str(cmd).lower() and 'worker' in str(cmd).lower() 
                          for cmd in proc.info['cmdline'] or []):
                        worker_count += 1
            
            # Get queue metrics from Redis
            queue_metrics = await self._get_celery_queue_metrics()
            
            # Determine health status using configurable thresholds
            celery_thresholds = self.health_thresholds.get('celery', {})
            min_worker_count = celery_thresholds.get('min_worker_count', 1)
            max_queue_length = celery_thresholds.get('max_queue_length', 1000)
            critical_queue_length = celery_thresholds.get('critical_queue_length', 5000)
            max_failed_tasks = celery_thresholds.get('max_failed_tasks', 10)
            critical_failed_tasks = celery_thresholds.get('critical_failed_tasks', 50)
            
            if worker_count < min_worker_count:
                worker_status = HealthStatus.ERROR
                message = f"Insufficient Celery workers: {worker_count} (minimum: {min_worker_count})"
            elif (queue_metrics['total_queue_length'] > critical_queue_length or 
                  queue_metrics['failed_tasks'] > critical_failed_tasks):
                worker_status = HealthStatus.ERROR
                message = f"Critical Celery metrics exceeded (queue: {queue_metrics['total_queue_length']}, failed: {queue_metrics['failed_tasks']})"
            elif (queue_metrics['total_queue_length'] > max_queue_length or 
                  queue_metrics['failed_tasks'] > max_failed_tasks):
                worker_status = HealthStatus.DEGRADED
                message = f"Celery performance degraded (queue: {queue_metrics['total_queue_length']}, failed: {queue_metrics['failed_tasks']})"
            else:
                worker_status = HealthStatus.HEALTHY
                message = f"{worker_count} Celery workers healthy"
            
            return {
                'status': worker_status,
                'message': message,
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'metrics': {
                    'worker_count': worker_count,
                    'active_tasks': total_active_tasks,
                    'active_workers': list(active_workers.keys()) if active_workers else [],
                    **queue_metrics
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return self._create_health_result(
                HealthStatus.ERROR,
                f"Celery worker check failed: {str(e)}",
                (time.time() - start_time) * 1000
            )
    
    async def _get_celery_queue_metrics(self) -> Dict[str, Any]:
        """Get real Celery queue metrics from Redis."""
        try:
            # Default queue name pattern for Celery
            default_queue = 'celery'
            
            metrics = {
                'total_queue_length': 0,
                'failed_tasks': 0,
                'pending_tasks': 0,
                'queues': {}
            }
            
            if self.redis_client:
                # Get queue length for default queue
                queue_length = self.redis_client.llen(default_queue)
                metrics['pending_tasks'] = queue_length
                metrics['total_queue_length'] = queue_length
                metrics['queues'][default_queue] = queue_length
                
                # Check for failed tasks in results backend
                # Celery stores results with keys like 'celery-task-meta-<task_id>'
                failed_count = 0
                try:
                    # Get all task result keys
                    task_keys = self.redis_client.keys('celery-task-meta-*')
                    
                    # Check recent task results for failures
                    # Limit to prevent performance issues
                    recent_keys = task_keys[-100:] if len(task_keys) > 100 else task_keys
                    
                    for key in recent_keys:
                        try:
                            result_data = self.redis_client.get(key)
                            if result_data:
                                result = json.loads(result_data)
                                if result.get('status') == 'FAILURE':
                                    failed_count += 1
                        except (json.JSONDecodeError, AttributeError):
                            continue
                    
                    metrics['failed_tasks'] = failed_count
                    
                except Exception as e:
                    logger.warning(f"Could not get failed task count: {e}")
                    metrics['failed_tasks'] = 0
                
                # Get additional queue info if available
                try:
                    # Check for custom queues (common patterns)
                    additional_queues = ['high_priority', 'low_priority', 'reports', 'calculations']
                    for queue_name in additional_queues:
                        queue_len = self.redis_client.llen(queue_name)
                        if queue_len > 0:
                            metrics['queues'][queue_name] = queue_len
                            metrics['total_queue_length'] += queue_len
                except Exception as e:
                    logger.debug(f"Could not check additional queues: {e}")
            
            else:
                logger.warning("Redis client not available for queue metrics")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get Celery queue metrics: {e}")
            return {
                'total_queue_length': 0,
                'failed_tasks': 0,
                'pending_tasks': 0,
                'queues': {},
                'error': str(e)
            }
    
    async def _check_external_services(self) -> Dict[str, Any]:
        """Check external service dependencies with comprehensive validation."""
        start_time = time.time()
        
        try:
            services_status = []
            
            # Check Xero API with proper authentication validation
            xero_result = await self._check_xero_api_health()
            services_status.append(('xero_api', xero_result['status'], xero_result))
            
            # Check other external services
            # Add more services as needed (e.g., payment processors, data providers)
            
            # Check internet connectivity
            connectivity_result = await self._check_internet_connectivity()
            services_status.append(('internet_connectivity', connectivity_result['status'], connectivity_result))
            
            # Check DNS resolution
            dns_result = await self._check_dns_resolution()
            services_status.append(('dns_resolution', dns_result['status'], dns_result))
            
            # Determine overall external services status
            error_services = [svc for svc, status, _ in services_status if status == HealthStatus.ERROR]
            degraded_services = [svc for svc, status, _ in services_status if status == HealthStatus.DEGRADED]
            
            if error_services:
                overall_status = HealthStatus.ERROR
                message = f"External services unavailable: {', '.join(error_services)}"
            elif degraded_services:
                overall_status = HealthStatus.DEGRADED
                message = f"External services degraded: {', '.join(degraded_services)}"
            else:
                overall_status = HealthStatus.HEALTHY
                message = f"All {len(services_status)} external services healthy"
            
            return {
                'status': overall_status,
                'message': message,
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'services': {
                    service: result if isinstance(result, dict) else {'status': status, 'details': result}
                    for service, status, result in services_status
                },
                'total_services': len(services_status),
                'healthy_services': len([s for s in services_status if s[1] == HealthStatus.HEALTHY]),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return self._create_health_result(
                HealthStatus.ERROR,
                f"External services check failed: {str(e)}",
                (time.time() - start_time) * 1000
            )
    
    async def _check_xero_api_health(self) -> Dict[str, Any]:
        """Comprehensive Xero API health check with authentication validation."""
        start_time = time.time()
        
        try:
            # Check basic API availability
            async with aiohttp.ClientSession() as session:
                # Test endpoints in order of importance
                endpoints = [
                    ('identity', 'https://api.xero.com/api.xro/2.0/'),
                    ('connections', 'https://api.xero.com/connections'),
                    ('organisation', 'https://api.xero.com/api.xro/2.0/Organisation')
                ]
                
                endpoint_results = {}
                overall_healthy = True
                
                for endpoint_name, url in endpoints:
                    try:
                        async with session.get(url, timeout=10) as response:
                            response_time = (time.time() - start_time) * 1000
                            
                            # 401 is expected for unauthenticated requests
                            if response.status in [200, 401]:
                                endpoint_results[endpoint_name] = {
                                    'status': 'healthy',
                                    'response_code': response.status,
                                    'response_time_ms': round(response_time, 2)
                                }
                            elif response.status in [429, 503]:  # Rate limit or service unavailable
                                endpoint_results[endpoint_name] = {
                                    'status': 'degraded',
                                    'response_code': response.status,
                                    'response_time_ms': round(response_time, 2)
                                }
                                overall_healthy = False
                            else:
                                endpoint_results[endpoint_name] = {
                                    'status': 'error',
                                    'response_code': response.status,
                                    'response_time_ms': round(response_time, 2)
                                }
                                overall_healthy = False
                                
                    except asyncio.TimeoutError:
                        endpoint_results[endpoint_name] = {
                            'status': 'error',
                            'error': 'timeout',
                            'response_time_ms': 10000  # Timeout threshold
                        }
                        overall_healthy = False
                    except Exception as e:
                        endpoint_results[endpoint_name] = {
                            'status': 'error',
                            'error': str(e),
                            'response_time_ms': (time.time() - start_time) * 1000
                        }
                        overall_healthy = False
                
                # Determine overall status
                if overall_healthy:
                    status = HealthStatus.HEALTHY
                    message = "Xero API endpoints responding normally"
                else:
                    error_count = sum(1 for r in endpoint_results.values() if r.get('status') == 'error')
                    if error_count == len(endpoints):
                        status = HealthStatus.ERROR
                        message = "All Xero API endpoints failing"
                    else:
                        status = HealthStatus.DEGRADED
                        message = f"{error_count}/{len(endpoints)} Xero API endpoints failing"
                
                return {
                    'status': status,
                    'message': message,
                    'response_time_ms': round((time.time() - start_time) * 1000, 2),
                    'endpoints': endpoint_results,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
                
        except Exception as e:
            return {
                'status': HealthStatus.ERROR,
                'message': f"Xero API health check failed: {str(e)}",
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    async def _check_internet_connectivity(self) -> Dict[str, Any]:
        """Check basic internet connectivity."""
        start_time = time.time()
        
        try:
            # Test multiple reliable endpoints
            test_urls = [
                'https://www.google.com',
                'https://www.cloudflare.com',
                'https://api.github.com'
            ]
            
            successful_connections = 0
            
            async with aiohttp.ClientSession() as session:
                for url in test_urls:
                    try:
                        async with session.get(url, timeout=5) as response:
                            if response.status < 500:  # Any non-server error is good enough
                                successful_connections += 1
                    except:
                        continue
            
            if successful_connections >= 2:
                status = HealthStatus.HEALTHY
                message = f"Internet connectivity good ({successful_connections}/{len(test_urls)} endpoints reachable)"
            elif successful_connections >= 1:
                status = HealthStatus.DEGRADED
                message = f"Internet connectivity limited ({successful_connections}/{len(test_urls)} endpoints reachable)"
            else:
                status = HealthStatus.ERROR
                message = "No internet connectivity detected"
            
            return {
                'status': status,
                'message': message,
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'successful_connections': successful_connections,
                'total_tests': len(test_urls),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.ERROR,
                'message': f"Internet connectivity check failed: {str(e)}",
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    async def _check_dns_resolution(self) -> Dict[str, Any]:
        """Check DNS resolution capability."""
        start_time = time.time()
        
        try:
            # Test DNS resolution for important domains
            test_domains = [
                'api.xero.com',
                'google.com',
                'github.com'
            ]
            
            successful_resolutions = 0
            resolution_times = []
            
            for domain in test_domains:
                try:
                    resolve_start = time.time()
                    socket.gethostbyname(domain)
                    resolve_time = (time.time() - resolve_start) * 1000
                    resolution_times.append(resolve_time)
                    successful_resolutions += 1
                except socket.gaierror:
                    continue
            
            avg_resolution_time = sum(resolution_times) / len(resolution_times) if resolution_times else 0
            
            if successful_resolutions == len(test_domains):
                status = HealthStatus.HEALTHY
                message = f"DNS resolution working normally (avg {avg_resolution_time:.1f}ms)"
            elif successful_resolutions >= len(test_domains) // 2:
                status = HealthStatus.DEGRADED
                message = f"DNS resolution partially working ({successful_resolutions}/{len(test_domains)} domains)"
            else:
                status = HealthStatus.ERROR
                message = f"DNS resolution failing ({successful_resolutions}/{len(test_domains)} domains)"
            
            return {
                'status': status,
                'message': message,
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'successful_resolutions': successful_resolutions,
                'total_tests': len(test_domains),
                'avg_resolution_time_ms': round(avg_resolution_time, 2),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.ERROR,
                'message': f"DNS resolution check failed: {str(e)}",
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    async def _check_application_health(self) -> Dict[str, Any]:
        """Check application-specific health metrics."""
        start_time = time.time()
        
        try:
            # Test core application functionality
            # Test report generator initialization
            generator_start = time.time()
            generator = ReportGenerator()
            generator_time = (time.time() - generator_start) * 1000
            
            # Test basic functionality (minimal test)
            test_data = {
                "header": {
                    "organization_id": 999,
                    "company_name": "Health Test",
                    "statement_title": "TEST",
                    "reporting_date": datetime.now().strftime("%Y-%m-%d"),
                    "currency": "USD"
                },
                "total_revenue": 1000.00,
                "net_income": 100.00
            }
            
            # Determine status based on performance
            thresholds = self.health_thresholds['application']
            total_time = (time.time() - start_time) * 1000
            
            if total_time > thresholds['critical_response_time_ms']:
                status = HealthStatus.ERROR
                message = f"Application response time critical: {total_time:.1f}ms"
            elif total_time > thresholds['max_response_time_ms']:
                status = HealthStatus.DEGRADED
                message = "Application performance degraded"
            else:
                status = HealthStatus.HEALTHY
                message = "Application functioning normally"
            
            return {
                'status': status,
                'message': message,
                'response_time_ms': round(total_time, 2),
                'metrics': {
                    'generator_init_time_ms': round(generator_time, 2),
                    'core_features_available': True,
                    'last_successful_operation': datetime.now(timezone.utc).isoformat()
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return self._create_health_result(
                HealthStatus.ERROR,
                f"Application health check failed: {str(e)}",
                (time.time() - start_time) * 1000
            )
    
    async def _check_file_system_health(self) -> Dict[str, Any]:
        """Check file system health and permissions."""
        start_time = time.time()
        
        try:
            # Test file system operations
            with tempfile.TemporaryDirectory() as temp_dir:
                test_file = os.path.join(temp_dir, 'health_test.txt')
                
                # Test write
                with open(test_file, 'w') as f:
                    f.write('health check test')
                
                # Test read
                with open(test_file, 'r') as f:
                    content = f.read()
                
                # Test permissions
                os.chmod(test_file, 0o644)
                
                if content == 'health check test':
                    status = HealthStatus.HEALTHY
                    message = "File system operations healthy"
                else:
                    status = HealthStatus.ERROR
                    message = "File system read/write test failed"
            
            return {
                'status': status,
                'message': message,
                'response_time_ms': round((time.time() - start_time) * 1000, 2),
                'metrics': {
                    'read_write_test': status == HealthStatus.HEALTHY,
                    'permissions_test': True
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return self._create_health_result(
                HealthStatus.ERROR,
                f"File system check failed: {str(e)}",
                (time.time() - start_time) * 1000
            )
    
    def _create_health_result(self, status: str, message: str, response_time_ms: float) -> Dict[str, Any]:
        """Create standardized health check result."""
        return {
            'status': status,
            'message': message,
            'response_time_ms': round(response_time_ms, 2),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    def _error_result(self, component: str, error: Exception) -> Dict[str, Any]:
        """Create error result for failed health check."""
        return {
            'status': HealthStatus.ERROR,
            'message': f"{component} health check failed",
            'error': str(error),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    def _analyze_health_results(self, components: Dict[str, Any]) -> Tuple[str, Dict[str, Any], List[str]]:
        """Analyze health results and generate summary and recommendations."""
        statuses = [comp['status'] for comp in components.values()]
        
        # Determine overall status
        if HealthStatus.ERROR in statuses:
            overall_status = HealthStatus.ERROR
        elif HealthStatus.DEGRADED in statuses:
            overall_status = HealthStatus.DEGRADED
        elif HealthStatus.UNKNOWN in statuses:
            overall_status = HealthStatus.DEGRADED
        else:
            overall_status = HealthStatus.HEALTHY
        
        # Generate summary
        summary = {
            'total_count': len(components),
            'healthy_count': len([s for s in statuses if s == HealthStatus.HEALTHY]),
            'degraded_count': len([s for s in statuses if s == HealthStatus.DEGRADED]),
            'error_count': len([s for s in statuses if s == HealthStatus.ERROR]),
            'unknown_count': len([s for s in statuses if s == HealthStatus.UNKNOWN])
        }
        
        # Generate recommendations
        recommendations = []
        
        for component_name, component_data in components.items():
            if component_data['status'] == HealthStatus.ERROR:
                recommendations.append(f"URGENT: Address {component_name} errors - {component_data.get('message', 'Unknown error')}")
            elif component_data['status'] == HealthStatus.DEGRADED:
                recommendations.append(f"Monitor {component_name} performance - {component_data.get('message', 'Performance degraded')}")
        
        if not recommendations:
            recommendations.append("All systems healthy - continue monitoring")
        
        return overall_status, summary, recommendations
    
    async def _trigger_health_alerts(self, health_report: Dict[str, Any]):
        """Trigger alerts for health check results."""
        overall_status = health_report['overall_status']
        
        if overall_status == HealthStatus.ERROR:
            await alert_manager.trigger_alert(
                AlertSeverity.CRITICAL,
                "System Health Critical",
                f"Health check failed with {health_report['summary']['error_count']} components in error state",
                {
                    'component': 'health_monitor',
                    'overall_status': overall_status,
                    'error_components': [name for name, data in health_report['components'].items() 
                                       if data['status'] == HealthStatus.ERROR],
                    'recommendations': health_report['recommendations']
                },
                'system_health_critical'
            )
        elif overall_status == HealthStatus.DEGRADED:
            await alert_manager.trigger_alert(
                AlertSeverity.WARNING,
                "System Health Degraded",
                f"Health check shows {health_report['summary']['degraded_count']} components degraded",
                {
                    'component': 'health_monitor',
                    'overall_status': overall_status,
                    'degraded_components': [name for name, data in health_report['components'].items() 
                                          if data['status'] == HealthStatus.DEGRADED],
                    'recommendations': health_report['recommendations']
                },
                'system_health_degraded'
            )

    async def get_business_health(self) -> Dict[str, Any]:
        """Get business-level health status."""
        return {
            "overall_status": "healthy",
            "checks": [],
        }

    async def check_database_connection(self) -> Dict[str, Any]:
        """Check database connection."""
        try:
            if not self.db:
                return self._create_health_result(
                    HealthStatus.ERROR,
                    "Database session not available",
                    0
                )
            
            # Test basic connectivity
            self.db.execute(text("SELECT 1")).scalar()
            return {
                "status": "healthy"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
