"""
Business Intelligence and KPI Monitoring for MCX3D Financial Platform

Provides comprehensive business metrics collection, anomaly detection, and financial
KPI monitoring with real-time dashboards and predictive analytics.
"""

import json
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import statistics
from mcx3d_finance.core.logging_factory import LoggerFactory

from mcx3d_finance.monitoring.structured_logger import main_logger
from mcx3d_finance.monitoring.alerting import alert_manager, AlertSeverity

logger = LoggerFactory.get_logger(__name__, domain='monitoring')

class TrendDirection(Enum):
    """Trend direction indicators."""
    UP = "up"
    DOWN = "down" 
    STABLE = "stable"
    VOLATILE = "volatile"

@dataclass
class FinancialKPI:
    """Financial Key Performance Indicator data structure."""
    name: str
    value: float
    target: float
    unit: str
    trend: TrendDirection
    timestamp: datetime
    previous_value: Optional[float] = None
    change_percent: Optional[float] = None
    is_healthy: bool = True
    context: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}
        
        # Calculate change percentage if previous value available
        if self.previous_value is not None:
            if self.previous_value != 0:
                self.change_percent = ((self.value - self.previous_value) / self.previous_value) * 100
            else:
                self.change_percent = 0.0
        
        # Determine health status
        self.is_healthy = self.value >= (self.target * 0.8)  # 80% of target considered healthy

@dataclass
class BusinessAnomalyAlert:
    """Business anomaly detection result."""
    anomaly_type: str
    severity: str
    kpi_name: str
    current_value: float
    expected_value: float
    deviation_percent: float
    confidence_score: float
    timestamp: datetime
    recommended_actions: List[str]

class BusinessIntelligenceCollector:
    """
    Comprehensive business intelligence and KPI monitoring system.
    
    Features:
    - Financial KPI collection and analysis
    - Real-time anomaly detection
    - Trend analysis and forecasting
    - Business health scoring
    - Automated alerting and recommendations
    """
    
    def __init__(self, db_session=None):
        self.db = db_session
        self.kpi_history = {}
        self.anomaly_thresholds = {
            'reports_generated_daily': {'min': 50, 'max': 1000, 'target': 200},
            'valuation_accuracy': {'min': 0.75, 'max': 1.0, 'target': 0.90},
            'average_response_time': {'min': 0.1, 'max': 5.0, 'target': 1.0},
            'user_engagement_score': {'min': 0.6, 'max': 1.0, 'target': 0.85},
            'system_availability': {'min': 0.995, 'max': 1.0, 'target': 0.999},
            'revenue_impact_score': {'min': 0.7, 'max': 1.0, 'target': 0.9}
        }
    
    async def collect_daily_kpis(self) -> Dict[str, FinancialKPI]:
        """
        Collect and analyze daily business KPIs.
        
        Returns:
            Dictionary of KPI name to FinancialKPI objects
        """
        today = datetime.now().date()
        kpis = {}
        
        try:
            # Report Generation KPIs
            reports_data = await self._get_report_generation_metrics(today)
            kpis['reports_generated'] = FinancialKPI(
                name='Daily Reports Generated',
                value=reports_data['count'],
                target=self.anomaly_thresholds['reports_generated_daily']['target'],
                unit='reports',
                trend=self._calculate_trend(reports_data['historical']),
                timestamp=datetime.now(),
                previous_value=reports_data.get('previous_day_count'),
                context={
                    'success_rate': reports_data['success_rate'],
                    'avg_generation_time': reports_data['avg_time'],
                    'most_popular_format': reports_data['popular_format']
                }
            )
            
            # Valuation Accuracy KPI
            valuation_data = await self._get_valuation_accuracy_metrics(today)
            kpis['valuation_accuracy'] = FinancialKPI(
                name='Valuation Model Accuracy',
                value=valuation_data['accuracy'],
                target=self.anomaly_thresholds['valuation_accuracy']['target'],
                unit='percentage',
                trend=self._calculate_trend(valuation_data['historical']),
                timestamp=datetime.now(),
                previous_value=valuation_data.get('previous_accuracy'),
                context={
                    'model_type_performance': valuation_data['model_breakdown'],
                    'total_valuations': valuation_data['total_count'],
                    'avg_valuation_amount': valuation_data['avg_amount']
                }
            )
            
            # System Performance KPI
            performance_data = await self._get_system_performance_metrics(today)
            kpis['system_performance'] = FinancialKPI(
                name='Average Response Time',
                value=performance_data['avg_response_time'],
                target=self.anomaly_thresholds['average_response_time']['target'],
                unit='seconds',
                trend=self._calculate_trend(performance_data['historical']),
                timestamp=datetime.now(),
                previous_value=performance_data.get('previous_avg'),
                context={
                    'p95_response_time': performance_data['p95'],
                    'error_rate': performance_data['error_rate'],
                    'availability': performance_data['availability']
                }
            )
            
            # User Engagement KPI
            engagement_data = await self._get_user_engagement_metrics(today)
            kpis['user_engagement'] = FinancialKPI(
                name='User Engagement Score',
                value=engagement_data['engagement_score'],
                target=self.anomaly_thresholds['user_engagement_score']['target'],
                unit='score',
                trend=self._calculate_trend(engagement_data['historical']),
                timestamp=datetime.now(),
                previous_value=engagement_data.get('previous_score'),
                context={
                    'active_users': engagement_data['active_users'],
                    'session_duration': engagement_data['avg_session_duration'],
                    'feature_adoption': engagement_data['feature_adoption']
                }
            )
            
            # Revenue Impact KPI
            revenue_data = await self._get_revenue_impact_metrics(today)
            kpis['revenue_impact'] = FinancialKPI(
                name='Revenue Impact Score',
                value=revenue_data['impact_score'],
                target=self.anomaly_thresholds['revenue_impact_score']['target'],
                unit='score',
                trend=self._calculate_trend(revenue_data['historical']),
                timestamp=datetime.now(),
                previous_value=revenue_data.get('previous_score'),
                context={
                    'total_valuations_value': revenue_data['total_value'],
                    'client_satisfaction': revenue_data['satisfaction_score'],
                    'repeat_usage_rate': revenue_data['repeat_rate']
                }
            )
            
            # Store KPI history for trend analysis
            self._store_kpi_history(kpis)
            
            # Log business event
            main_logger.log_business_event(
                'daily_kpis_collected',
                collection_date=today.isoformat(),
                kpi_count=len(kpis),
                healthy_kpis=len([kpi for kpi in kpis.values() if kpi.is_healthy])
            )
            
            return kpis
            
        except Exception as e:
            logger.error(f"Failed to collect daily KPIs: {e}")
            await alert_manager.trigger_alert(
                AlertSeverity.ERROR,
                "KPI Collection Failed",
                f"Failed to collect daily business KPIs: {str(e)}",
                {'component': 'business_intelligence', 'error': str(e)},
                'kpi_collection_failed'
            )
            return {}
    
    async def detect_anomalies(self, kpis: Dict[str, FinancialKPI]) -> List[BusinessAnomalyAlert]:
        """
        Detect business anomalies requiring attention.
        
        Args:
            kpis: Dictionary of current KPIs
            
        Returns:
            List of detected anomalies
        """
        anomalies = []
        
        for kpi_name, kpi in kpis.items():
            threshold_key = kpi_name.replace('_', '_').lower()
            if threshold_key in self.anomaly_thresholds:
                thresholds = self.anomaly_thresholds[threshold_key]
                
                # Check for value outside acceptable range
                if kpi.value < thresholds['min'] or kpi.value > thresholds['max']:
                    severity = self._calculate_anomaly_severity(kpi.value, thresholds)
                    deviation = self._calculate_deviation_percent(kpi.value, thresholds['target'])
                    
                    anomaly = BusinessAnomalyAlert(
                        anomaly_type='threshold_breach',
                        severity=severity,
                        kpi_name=kpi.name,
                        current_value=kpi.value,
                        expected_value=thresholds['target'],
                        deviation_percent=deviation,
                        confidence_score=0.9,
                        timestamp=datetime.now(),
                        recommended_actions=self._get_recommended_actions(kpi_name, kpi.value, thresholds)
                    )
                    anomalies.append(anomaly)
                
                # Check for significant trend changes
                if kpi.change_percent and abs(kpi.change_percent) > 25:  # 25% change threshold
                    trend_severity = "critical" if abs(kpi.change_percent) > 50 else "warning"
                    
                    anomaly = BusinessAnomalyAlert(
                        anomaly_type='trend_change',
                        severity=trend_severity,
                        kpi_name=kpi.name,
                        current_value=kpi.value,
                        expected_value=kpi.previous_value or kpi.target,
                        deviation_percent=kpi.change_percent,
                        confidence_score=0.8,
                        timestamp=datetime.now(),
                        recommended_actions=self._get_trend_actions(kpi_name, kpi.change_percent)
                    )
                    anomalies.append(anomaly)
        
        # Trigger alerts for detected anomalies
        for anomaly in anomalies:
            await self._trigger_anomaly_alert(anomaly)
        
        return anomalies
    
    async def generate_business_health_score(self, kpis: Dict[str, FinancialKPI]) -> Dict[str, Any]:
        """
        Generate overall business health score and insights.
        
        Args:
            kpis: Dictionary of current KPIs
            
        Returns:
            Business health analysis
        """
        if not kpis:
            return {'score': 0, 'status': 'unknown', 'insights': []}
        
        # Calculate weighted health score
        weights = {
            'reports_generated': 0.2,
            'valuation_accuracy': 0.3,
            'system_performance': 0.2,
            'user_engagement': 0.15,
            'revenue_impact': 0.15
        }
        
        total_score = 0
        total_weight = 0
        
        for kpi_name, kpi in kpis.items():
            weight = weights.get(kpi_name, 0.1)
            if kpi.target > 0:
                normalized_score = min(kpi.value / kpi.target, 1.0) * 100
                total_score += normalized_score * weight
                total_weight += weight
        
        health_score = total_score / total_weight if total_weight > 0 else 0
        
        # Determine health status
        if health_score >= 90:
            status = 'excellent'
        elif health_score >= 80:
            status = 'good'
        elif health_score >= 70:
            status = 'fair'
        elif health_score >= 60:
            status = 'poor'
        else:
            status = 'critical'
        
        # Generate insights
        insights = []
        
        # Identify top performers
        top_kpis = sorted(kpis.values(), key=lambda k: k.value / k.target if k.target > 0 else 0, reverse=True)
        if top_kpis:
            insights.append(f"Top performer: {top_kpis[0].name} at {(top_kpis[0].value / top_kpis[0].target * 100):.1f}% of target")
        
        # Identify areas needing attention
        bottom_kpis = [kpi for kpi in kpis.values() if not kpi.is_healthy]
        if bottom_kpis:
            insights.append(f"Needs attention: {len(bottom_kpis)} KPIs below health threshold")
        
        # Trend insights
        improving_kpis = [kpi for kpi in kpis.values() if kpi.change_percent and kpi.change_percent > 5]
        declining_kpis = [kpi for kpi in kpis.values() if kpi.change_percent and kpi.change_percent < -5]
        
        if improving_kpis:
            insights.append(f"{len(improving_kpis)} KPIs showing improvement")
        if declining_kpis:
            insights.append(f"{len(declining_kpis)} KPIs showing decline")
        
        health_data = {
            'score': round(health_score, 1),
            'status': status,
            'insights': insights,
            'kpi_summary': {
                'total': len(kpis),
                'healthy': len([kpi for kpi in kpis.values() if kpi.is_healthy]),
                'improving': len(improving_kpis),
                'declining': len(declining_kpis)
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # Log health score
        main_logger.log_business_event(
            'business_health_calculated',
            health_score=health_score,
            health_status=status,
            healthy_kpis=health_data['kpi_summary']['healthy'],
            total_kpis=len(kpis)
        )
        
        return health_data
    
    # Helper methods
    
    async def _get_report_generation_metrics(self, date) -> Dict[str, Any]:
        """Get report generation metrics for specified date."""
        # Mock data - in production, query actual database
        return {
            'count': 180,
            'success_rate': 0.95,
            'avg_time': 2.3,
            'popular_format': 'PDF',
            'previous_day_count': 165,
            'historical': [150, 160, 165, 170, 175, 180]
        }
    
    async def _get_valuation_accuracy_metrics(self, date) -> Dict[str, Any]:
        """Get valuation accuracy metrics for specified date."""
        return {
            'accuracy': 0.87,
            'total_count': 45,
            'avg_amount': 2500000,
            'previous_accuracy': 0.85,
            'model_breakdown': {'DCF': 0.89, 'Multiples': 0.85, 'SaaS': 0.88},
            'historical': [0.83, 0.84, 0.85, 0.86, 0.87, 0.87]
        }
    
    async def _get_system_performance_metrics(self, date) -> Dict[str, Any]:
        """Get system performance metrics for specified date."""
        return {
            'avg_response_time': 1.2,
            'p95': 2.1,
            'error_rate': 0.02,
            'availability': 0.998,
            'previous_avg': 1.1,
            'historical': [1.0, 1.1, 1.1, 1.0, 1.2, 1.2]
        }
    
    async def _get_user_engagement_metrics(self, date) -> Dict[str, Any]:
        """Get user engagement metrics for specified date."""
        return {
            'engagement_score': 0.82,
            'active_users': 120,
            'avg_session_duration': 1800,
            'feature_adoption': 0.75,
            'previous_score': 0.80,
            'historical': [0.78, 0.79, 0.80, 0.81, 0.82, 0.82]
        }
    
    async def _get_revenue_impact_metrics(self, date) -> Dict[str, Any]:
        """Get revenue impact metrics for specified date."""
        return {
            'impact_score': 0.88,
            'total_value': 125000000,
            'satisfaction_score': 0.91,
            'repeat_rate': 0.73,
            'previous_score': 0.86,
            'historical': [0.84, 0.85, 0.86, 0.87, 0.88, 0.88]
        }
    
    def _calculate_trend(self, historical_values: List[float]) -> TrendDirection:
        """Calculate trend direction from historical values."""
        if len(historical_values) < 2:
            return TrendDirection.STABLE
        
        recent_values = historical_values[-3:]  # Last 3 values
        
        if len(recent_values) >= 2:
            trend_slope = recent_values[-1] - recent_values[0]
            variation = statistics.stdev(recent_values) if len(recent_values) > 1 else 0
            
            if variation > (sum(recent_values) / len(recent_values)) * 0.1:  # 10% variation
                return TrendDirection.VOLATILE
            elif trend_slope > 0.05:
                return TrendDirection.UP
            elif trend_slope < -0.05:
                return TrendDirection.DOWN
            else:
                return TrendDirection.STABLE
        
        return TrendDirection.STABLE
    
    def _calculate_anomaly_severity(self, value: float, thresholds: Dict[str, float]) -> str:
        """Calculate anomaly severity based on deviation from thresholds."""
        target = thresholds['target']
        min_val = thresholds['min']
        max_val = thresholds['max']
        
        if value < min_val:
            deviation = (min_val - value) / target
        elif value > max_val:
            deviation = (value - max_val) / target
        else:
            return "info"
        
        if deviation > 0.5:
            return "critical"
        elif deviation > 0.25:
            return "error"
        elif deviation > 0.1:
            return "warning"
        else:
            return "info"
    
    def _calculate_deviation_percent(self, current: float, target: float) -> float:
        """Calculate percentage deviation from target."""
        if target == 0:
            return 0.0
        return ((current - target) / target) * 100
    
    def _get_recommended_actions(self, kpi_name: str, value: float, thresholds: Dict[str, float]) -> List[str]:
        """Get recommended actions for KPI threshold breaches."""
        actions = []
        
        if kpi_name == 'reports_generated':
            if value < thresholds['min']:
                actions.extend([
                    "Check report generation service health",
                    "Review user engagement and marketing campaigns",
                    "Investigate potential system bottlenecks"
                ])
        elif kpi_name == 'valuation_accuracy':
            if value < thresholds['min']:
                actions.extend([
                    "Review and retrain valuation models",
                    "Validate input data quality",
                    "Consider model parameter adjustments"
                ])
        elif kpi_name == 'system_performance':
            if value > thresholds['max']:
                actions.extend([
                    "Scale infrastructure resources",
                    "Optimize database queries",
                    "Review application performance bottlenecks"
                ])
        
        return actions or ["Monitor situation and investigate root cause"]
    
    def _get_trend_actions(self, kpi_name: str, change_percent: float) -> List[str]:
        """Get recommended actions for significant trend changes."""
        if change_percent > 0:
            return [f"Investigate factors driving {abs(change_percent):.1f}% improvement in {kpi_name}"]
        else:
            return [f"Investigate root cause of {abs(change_percent):.1f}% decline in {kpi_name}"]
    
    def _store_kpi_history(self, kpis: Dict[str, FinancialKPI]):
        """Store KPI history for trend analysis."""
        for kpi_name, kpi in kpis.items():
            if kpi_name not in self.kpi_history:
                self.kpi_history[kpi_name] = []
            
            self.kpi_history[kpi_name].append({
                'timestamp': kpi.timestamp.isoformat(),
                'value': kpi.value,
                'target': kpi.target
            })
            
            # Keep only last 30 entries
            self.kpi_history[kpi_name] = self.kpi_history[kpi_name][-30:]
    
    async def _trigger_anomaly_alert(self, anomaly: BusinessAnomalyAlert):
        """Trigger alert for detected business anomaly."""
        severity_map = {
            'info': AlertSeverity.INFO,
            'warning': AlertSeverity.WARNING,
            'error': AlertSeverity.ERROR,
            'critical': AlertSeverity.CRITICAL
        }
        
        await alert_manager.trigger_alert(
            severity_map.get(anomaly.severity, AlertSeverity.WARNING),
            f"Business Anomaly: {anomaly.kpi_name}",
            f"Detected {anomaly.anomaly_type} with {anomaly.deviation_percent:.1f}% deviation",
            {
                'component': 'business_intelligence',
                'kpi_name': anomaly.kpi_name,
                'current_value': anomaly.current_value,
                'expected_value': anomaly.expected_value,
                'deviation_percent': anomaly.deviation_percent,
                'confidence_score': anomaly.confidence_score,
                'recommended_actions': anomaly.recommended_actions
            },
            f'business_anomaly_{anomaly.kpi_name}_{anomaly.anomaly_type}'
        )