"""
Monitoring Configuration Loader for MCX3D Financial Platform

Provides environment-specific configuration loading for health check thresholds,
alerting settings, and monitoring intervals.
"""

import os
import yaml
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional
from pathlib import Path
from mcx3d_finance.core.config import detect_environment

logger = LoggerFactory.get_logger(__name__, domain='monitoring')

class MonitoringConfigLoader:
    """
    Load monitoring configuration based on environment.
    
    Supports configuration inheritance and environment-specific overrides.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._get_default_config_path()
        self.environment = detect_environment()
        self._config_cache = None
        self._load_timestamp = None
        
    def _get_default_config_path(self) -> str:
        """Get the default monitoring configuration file path."""
        current_dir = Path(__file__).parent.parent
        config_path = current_dir / 'config' / 'monitoring.yml'
        return str(config_path)
    
    def load_config(self, force_reload: bool = False) -> Dict[str, Any]:
        """
        Load monitoring configuration for current environment.
        
        Args:
            force_reload: Force reload from file even if cached
            
        Returns:
            Environment-specific monitoring configuration
        """
        # Check if we need to reload
        if not force_reload and self._config_cache is not None:
            try:
                current_mtime = os.path.getmtime(self.config_path)
                if current_mtime <= self._load_timestamp:
                    return self._config_cache
            except OSError:
                pass  # File might not exist, proceed with loading
        
        try:
            with open(self.config_path, 'r') as f:
                full_config = yaml.safe_load(f)
            
            # Get default configuration
            default_config = full_config.get('default', {})
            
            # Get environment-specific configuration
            env_config = full_config.get(self.environment, {})
            
            # Merge configurations (environment overrides default)
            merged_config = self._deep_merge(default_config, env_config)
            
            # Add metadata
            merged_config['_metadata'] = {
                'environment': self.environment,
                'config_path': self.config_path,
                'loaded_at': self._get_current_timestamp(),
                'available_environments': list(full_config.keys())
            }
            
            # Cache the configuration
            self._config_cache = merged_config
            self._load_timestamp = os.path.getmtime(self.config_path)
            
            logger.info(f"Loaded monitoring configuration for environment: {self.environment}")
            return merged_config
            
        except FileNotFoundError:
            logger.error(f"Monitoring configuration file not found: {self.config_path}")
            return self._get_fallback_config()
        except yaml.YAMLError as e:
            logger.error(f"Failed to parse monitoring configuration: {e}")
            return self._get_fallback_config()
        except Exception as e:
            logger.error(f"Unexpected error loading monitoring configuration: {e}")
            return self._get_fallback_config()
    
    def get_health_thresholds(self) -> Dict[str, Any]:
        """Get health check thresholds for current environment."""
        config = self.load_config()
        return config.get('health_thresholds', {})
    
    def get_alert_thresholds(self) -> Dict[str, Any]:
        """Get alert thresholds for current environment."""
        config = self.load_config()
        return config.get('alert_thresholds', {})
    
    def get_monitoring_intervals(self) -> Dict[str, int]:
        """Get monitoring intervals for current environment."""
        config = self.load_config()
        return config.get('monitoring_intervals', {})
    
    def get_threshold(self, component: str, metric: str, default: Any = None) -> Any:
        """
        Get specific threshold value.
        
        Args:
            component: Component name (e.g., 'database', 'redis', 'celery')
            metric: Metric name (e.g., 'max_response_time_ms')
            default: Default value if threshold not found
            
        Returns:
            Threshold value or default
        """
        thresholds = self.get_health_thresholds()
        component_thresholds = thresholds.get(component, {})
        return component_thresholds.get(metric, default)
    
    def should_send_alert(self, alert_type: str, severity: str) -> bool:
        """
        Check if alert should be sent based on configuration.
        
        Args:
            alert_type: Type of alert ('email_alerts', 'slack_alerts', 'pagerduty_alerts')
            severity: Alert severity ('INFO', 'WARNING', 'ERROR', 'CRITICAL')
            
        Returns:
            True if alert should be sent
        """
        alert_config = self.get_alert_thresholds()
        enabled_severities = alert_config.get(alert_type, [])
        return severity in enabled_severities
    
    def get_rate_limit_config(self) -> Dict[str, Any]:
        """Get rate limiting configuration for alerts."""
        alert_config = self.get_alert_thresholds()
        return alert_config.get('rate_limiting', {
            'max_alerts_per_window': 10,
            'rate_limit_window_minutes': 5,
            'deduplication_minutes': 1
        })
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep merge two dictionaries.
        
        Args:
            base: Base dictionary
            override: Override dictionary
            
        Returns:
            Merged dictionary
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _get_fallback_config(self) -> Dict[str, Any]:
        """Get fallback configuration when main config fails to load."""
        return {
            'health_thresholds': {
                'database': {
                    'max_response_time_ms': 100,
                    'max_active_connections': 80,
                    'critical_response_time_ms': 1000
                },
                'redis': {
                    'max_response_time_ms': 10,
                    'max_memory_usage_percent': 80,
                    'critical_response_time_ms': 100
                },
                'system': {
                    'max_cpu_percent': 80,
                    'max_memory_percent': 85,
                    'max_disk_percent': 90,
                    'critical_cpu_percent': 95,
                    'critical_memory_percent': 95
                },
                'application': {
                    'max_response_time_ms': 2000,
                    'critical_response_time_ms': 5000,
                    'min_success_rate': 0.95
                },
                'celery': {
                    'max_queue_length': 1000,
                    'critical_queue_length': 5000,
                    'max_failed_tasks': 10,
                    'critical_failed_tasks': 50,
                    'min_worker_count': 1
                },
                'external_services': {
                    'max_response_time_ms': 5000,
                    'critical_response_time_ms': 10000,
                    'min_success_rate': 0.9,
                    'timeout_seconds': 10
                }
            },
            'alert_thresholds': {
                'email_alerts': ['WARNING', 'ERROR', 'CRITICAL'],
                'slack_alerts': ['ERROR', 'CRITICAL'],
                'pagerduty_alerts': ['CRITICAL'],
                'rate_limiting': {
                    'max_alerts_per_window': 10,
                    'rate_limit_window_minutes': 5,
                    'deduplication_minutes': 1
                }
            },
            'monitoring_intervals': {
                'health_check_interval_seconds': 30,
                'metrics_collection_interval_seconds': 15,
                'external_service_check_interval_seconds': 60,
                'celery_metrics_interval_seconds': 20
            },
            '_metadata': {
                'environment': self.environment,
                'config_source': 'fallback',
                'loaded_at': self._get_current_timestamp()
            }
        }
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp as ISO string."""
        from datetime import datetime, timezone
        return datetime.now(timezone.utc).isoformat()
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Validate the loaded configuration.
        
        Returns:
            Validation results with any issues found
        """
        config = self.load_config()
        issues = []
        warnings = []
        
        # Validate required sections
        required_sections = ['health_thresholds', 'alert_thresholds', 'monitoring_intervals']
        for section in required_sections:
            if section not in config:
                issues.append(f"Missing required section: {section}")
        
        # Validate health thresholds
        health_thresholds = config.get('health_thresholds', {})
        required_components = ['database', 'redis', 'system', 'application', 'celery', 'external_services']
        
        for component in required_components:
            if component not in health_thresholds:
                warnings.append(f"Missing health thresholds for component: {component}")
            else:
                component_config = health_thresholds[component]
                # Validate that critical thresholds are higher than max thresholds
                for metric in component_config:
                    if metric.startswith('max_') and metric.endswith('_ms'):
                        critical_metric = metric.replace('max_', 'critical_')
                        if critical_metric in component_config:
                            if component_config[metric] >= component_config[critical_metric]:
                                issues.append(f"{component}.{metric} should be less than {critical_metric}")
        
        # Validate monitoring intervals
        intervals = config.get('monitoring_intervals', {})
        required_intervals = [
            'health_check_interval_seconds',
            'metrics_collection_interval_seconds',
            'external_service_check_interval_seconds',
            'celery_metrics_interval_seconds'
        ]
        
        for interval in required_intervals:
            if interval not in intervals:
                warnings.append(f"Missing monitoring interval: {interval}")
            elif not isinstance(intervals[interval], int) or intervals[interval] <= 0:
                issues.append(f"Invalid monitoring interval {interval}: must be positive integer")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'environment': self.environment,
            'config_path': self.config_path
        }


# Global configuration loader instance
_config_loader = None

def get_monitoring_config() -> MonitoringConfigLoader:
    """Get global monitoring configuration loader instance."""
    global _config_loader
    if _config_loader is None:
        _config_loader = MonitoringConfigLoader()
    return _config_loader

def reload_monitoring_config():
    """Force reload of monitoring configuration."""
    global _config_loader
    if _config_loader is not None:
        _config_loader.load_config(force_reload=True)