#!/usr/bin/env python3
"""
MCX3D Financials Data Ingestion Script

This script processes financial data from JSON files and generates comprehensive
financial reports using the MCX3D Financials application infrastructure.

Usage:
    python -m mcx3d_finance.cli.data_ingestion --file sample_data/company_data.json
    python -m mcx3d_finance.cli.data_ingestion --file sample_data/company_data.json --organization-id 2
    python -m mcx3d_finance.cli.data_ingestion --help
"""

import json
from mcx3d_finance.core.logging_factory import LoggerFactory
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional
import click
from decimal import Decimal

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from mcx3d_finance.core.data_processors import UKDataProcessor
from mcx3d_finance.core.data_validation import DataValidationEngine, ValidationSeverity
from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator
from mcx3d_finance.core.financials.balance_sheet import UKBalanceSheetGenerator
from mcx3d_finance.core.financials.cash_flow import UKCashFlowGenerator
from mcx3d_finance.core.account_mapper import IndustryType
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, Account, Contact, Invoice, BankTransaction

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = LoggerFactory.get_logger(__name__, domain='cli')


class DataIngestionProcessor:
    """Main class for processing financial data and generating reports."""
    
    def __init__(self, organization_id: int = 2, industry: IndustryType = IndustryType.TECHNOLOGY):
        self.organization_id = organization_id
        self.industry = industry
        self.db = SessionLocal()
        self.data_processor = UKDataProcessor(industry=industry)
        self.validation_engine = DataValidationEngine()
        
    def load_and_validate_data(self, file_path: str) -> Dict[str, Any]:
        """Load JSON data and perform initial validation."""
        logger.info(f"Loading data from {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Validate data structure
            required_keys = ['accounts', 'contacts', 'invoices', 'bank_transactions']
            missing_keys = [key for key in required_keys if key not in data]
            
            if missing_keys:
                raise ValueError(f"Missing required data sections: {missing_keys}")
            
            logger.info(f"Data loaded successfully:")
            logger.info(f"  - Accounts: {len(data.get('accounts', []))}")
            logger.info(f"  - Contacts: {len(data.get('contacts', []))}")
            logger.info(f"  - Invoices: {len(data.get('invoices', []))}")
            logger.info(f"  - Bank Transactions: {len(data.get('bank_transactions', []))}")
            logger.info(f"  - Transactions: {len(data.get('transactions', []))}")
            
            return data
            
        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def process_accounts(self, accounts_data: List[Dict[str, Any]]) -> int:
        """Process and store account data."""
        logger.info("Processing accounts data...")
        processed_count = 0
        
        for account_data in accounts_data:
            try:
                # Check if account already exists
                existing_account = self.db.query(Account).filter(
                    Account.xero_account_id == account_data.get('xero_account_id')
                ).first()
                
                if not existing_account:
                    account = Account(
                        xero_account_id=account_data.get('xero_account_id'),
                        code=account_data.get('code'),
                        name=account_data.get('name'),
                        type=account_data.get('type'),
                        tax_type=account_data.get('tax_type'),
                        description=account_data.get('description', ''),
                        class_type=account_data.get('class_type'),
                        status=account_data.get('status'),
                        currency_code=account_data.get('currency_code', 'GBP'),
                        reporting_code=account_data.get('reporting_code'),
                        reporting_code_name=account_data.get('reporting_code_name'),
                        organization_id=self.organization_id
                    )
                    self.db.add(account)
                    processed_count += 1
                    
            except Exception as e:
                logger.warning(f"Error processing account {account_data.get('name', 'Unknown')}: {e}")
                continue
        
        self.db.commit()
        logger.info(f"Processed {processed_count} accounts")
        return processed_count
    
    def process_contacts(self, contacts_data: List[Dict[str, Any]]) -> int:
        """Process and store contact data."""
        logger.info("Processing contacts data...")
        processed_count = 0
        
        for contact_data in contacts_data:
            try:
                # Check if contact already exists
                existing_contact = self.db.query(Contact).filter(
                    Contact.xero_contact_id == contact_data.get('xero_contact_id')
                ).first()
                
                if not existing_contact:
                    contact = Contact(
                        xero_contact_id=contact_data.get('xero_contact_id'),
                        contact_number=contact_data.get('contact_number'),
                        name=contact_data.get('name'),
                        first_name=contact_data.get('first_name'),
                        last_name=contact_data.get('last_name'),
                        email_address=contact_data.get('email_address'),
                        contact_status=contact_data.get('contact_status'),
                        organization_id=self.organization_id
                    )
                    self.db.add(contact)
                    processed_count += 1
                    
            except Exception as e:
                logger.warning(f"Error processing contact {contact_data.get('name', 'Unknown')}: {e}")
                continue
        
        self.db.commit()
        logger.info(f"Processed {processed_count} contacts")
        return processed_count
    
    def process_invoices(self, invoices_data: List[Dict[str, Any]]) -> int:
        """Process and store invoice data."""
        logger.info("Processing invoices data...")
        processed_count = 0

        for invoice_data in invoices_data:
            try:
                # Check if invoice already exists
                existing_invoice = self.db.query(Invoice).filter(
                    Invoice.xero_invoice_id == invoice_data.get('xero_invoice_id')
                ).first()

                if not existing_invoice:
                    # Parse date strings
                    date_str = invoice_data.get('date')
                    due_date_str = invoice_data.get('due_date')

                    date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00')) if date_str else None
                    due_date_obj = datetime.fromisoformat(due_date_str.replace('Z', '+00:00')) if due_date_str else None

                    # Handle contact_id - set to None if contact doesn't exist
                    contact_id = invoice_data.get('contact_id')
                    if contact_id:
                        existing_contact = self.db.query(Contact).filter(Contact.id == contact_id).first()
                        if not existing_contact:
                            logger.warning(f"Contact ID {contact_id} not found for invoice {invoice_data.get('invoice_number')}, setting to None")
                            contact_id = None

                    # Handle line_items - the data comes as JSON strings from the source
                    line_items_data = invoice_data.get('line_items', '[]')
                    # The data is already a JSON string, so we use it directly
                    line_items_json = line_items_data if isinstance(line_items_data, str) else json.dumps(line_items_data)

                    invoice = Invoice(
                        xero_invoice_id=invoice_data.get('xero_invoice_id'),
                        type=invoice_data.get('type'),
                        contact_id=contact_id,
                        date=date_obj,
                        due_date=due_date_obj,
                        invoice_number=invoice_data.get('invoice_number'),
                        reference=invoice_data.get('reference'),
                        sub_total=Decimal(str(invoice_data.get('sub_total', 0))),
                        total_tax=Decimal(str(invoice_data.get('total_tax', 0))),
                        total=Decimal(str(invoice_data.get('total', 0))),
                        amount_due=Decimal(str(invoice_data.get('amount_due', 0))),
                        amount_paid=Decimal(str(invoice_data.get('amount_paid', 0))),
                        currency_code=invoice_data.get('currency_code', 'GBP'),
                        line_items=line_items_json,
                        organization_id=self.organization_id
                    )
                    self.db.add(invoice)
                    processed_count += 1

            except Exception as e:
                logger.warning(f"Error processing invoice {invoice_data.get('invoice_number', 'Unknown')}: {e}")
                continue

        self.db.commit()
        logger.info(f"Processed {processed_count} invoices")
        return processed_count
    
    def process_bank_transactions(self, transactions_data: List[Dict[str, Any]]) -> int:
        """Process and store bank transaction data."""
        logger.info("Processing bank transactions data...")
        processed_count = 0

        for transaction_data in transactions_data:
            try:
                # Check if transaction already exists
                existing_transaction = self.db.query(BankTransaction).filter(
                    BankTransaction.xero_transaction_id == transaction_data.get('xero_transaction_id')
                ).first()

                if not existing_transaction:
                    # Parse date string
                    date_str = transaction_data.get('date')
                    date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00')) if date_str else None

                    # Handle contact_id - set to None if contact doesn't exist
                    contact_id = transaction_data.get('contact_id')
                    if contact_id:
                        existing_contact = self.db.query(Contact).filter(Contact.id == contact_id).first()
                        if not existing_contact:
                            logger.debug(f"Contact ID {contact_id} not found for transaction {transaction_data.get('reference')}, setting to None")
                            contact_id = None

                    # Handle line_items - the data comes as JSON strings from the source
                    line_items_data = transaction_data.get('line_items', '[]')
                    # The data is already a JSON string, so we use it directly
                    line_items_json = line_items_data if isinstance(line_items_data, str) else json.dumps(line_items_data)

                    transaction = BankTransaction(
                        xero_transaction_id=transaction_data.get('xero_transaction_id'),
                        type=transaction_data.get('type'),
                        contact_id=contact_id,
                        date=date_obj,
                        reference=transaction_data.get('reference'),
                        is_reconciled=transaction_data.get('is_reconciled', False),
                        sub_total=Decimal(str(transaction_data.get('sub_total', 0))),
                        total_tax=Decimal(str(transaction_data.get('total_tax', 0))),
                        total=Decimal(str(transaction_data.get('total', 0))),
                        currency_code=transaction_data.get('currency_code', 'GBP'),
                        line_items=line_items_json,
                        bank_account=json.dumps(transaction_data.get('bank_account', {})),
                        organization_id=self.organization_id
                    )
                    self.db.add(transaction)
                    processed_count += 1

            except Exception as e:
                logger.warning(f"Error processing transaction {transaction_data.get('reference', 'Unknown')}: {e}")
                continue

        self.db.commit()
        logger.info(f"Processed {processed_count} bank transactions")
        return processed_count

    def generate_financial_reports(self, output_dir: str = "reports") -> Dict[str, Any]:
        """Generate comprehensive financial reports."""
        logger.info("Generating financial reports...")

        # Create output directory
        Path(output_dir).mkdir(exist_ok=True)

        # Define reporting periods
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)  # Last 12 months

        reports = {}

        try:
            # Generate Profit & Loss Statement
            logger.info("Generating UK Profit & Loss Statement...")
            pl_generator = UKProfitAndLossGenerator(self.organization_id)
            pl_report = pl_generator.generate_profit_and_loss(start_date, end_date)

            # Save P&L report
            pl_file = Path(output_dir) / f"profit_and_loss_{end_date.strftime('%Y%m%d')}.json"
            with open(pl_file, 'w') as f:
                json.dump(pl_report, f, indent=2, default=str)

            reports['profit_and_loss'] = {
                'data': pl_report,
                'file': str(pl_file),
                'status': 'success'
            }
            logger.info(f"P&L report saved to {pl_file}")

        except Exception as e:
            logger.error(f"Error generating P&L report: {e}")
            reports['profit_and_loss'] = {'status': 'error', 'error': str(e)}

        try:
            # Generate Balance Sheet
            logger.info("Generating UK Balance Sheet...")
            bs_generator = UKBalanceSheetGenerator(self.organization_id)
            bs_report = bs_generator.generate_balance_sheet(end_date)

            # Save Balance Sheet report
            bs_file = Path(output_dir) / f"balance_sheet_{end_date.strftime('%Y%m%d')}.json"
            with open(bs_file, 'w') as f:
                json.dump(bs_report, f, indent=2, default=str)

            reports['balance_sheet'] = {
                'data': bs_report,
                'file': str(bs_file),
                'status': 'success'
            }
            logger.info(f"Balance Sheet report saved to {bs_file}")

        except Exception as e:
            logger.error(f"Error generating Balance Sheet report: {e}")
            reports['balance_sheet'] = {'status': 'error', 'error': str(e)}

        try:
            # Generate Cash Flow Statement
            logger.info("Generating UK Cash Flow Statement...")
            cf_generator = UKCashFlowGenerator(self.organization_id)
            cf_report = cf_generator.generate_cash_flow_statement(start_date, end_date)

            # Save Cash Flow report
            cf_file = Path(output_dir) / f"cash_flow_{end_date.strftime('%Y%m%d')}.json"
            with open(cf_file, 'w') as f:
                json.dump(cf_report, f, indent=2, default=str)

            reports['cash_flow'] = {
                'data': cf_report,
                'file': str(cf_file),
                'status': 'success'
            }
            logger.info(f"Cash Flow report saved to {cf_file}")

        except Exception as e:
            logger.error(f"Error generating Cash Flow report: {e}")
            reports['cash_flow'] = {'status': 'error', 'error': str(e)}

        return reports

    def validate_processed_data(self) -> Dict[str, Any]:
        """Validate the processed data using the validation engine."""
        logger.info("Validating processed data...")

        validation_results = {
            'accounts': [],
            'invoices': [],
            'bank_transactions': [],
            'summary': {}
        }

        try:
            # Get sample data for validation
            accounts = self.db.query(Account).filter(
                Account.organization_id == self.organization_id
            ).limit(10).all()

            invoices = self.db.query(Invoice).filter(
                Invoice.organization_id == self.organization_id
            ).limit(10).all()

            bank_transactions = self.db.query(BankTransaction).filter(
                BankTransaction.organization_id == self.organization_id
            ).limit(10).all()

            # Validate accounts
            for account in accounts:
                account_dict = {
                    'id': account.id,
                    'code': account.code,
                    'name': account.name,
                    'type': account.type,
                    'currency_code': account.currency_code
                }
                results = self.validation_engine.validate_account_data(account_dict)
                validation_results['accounts'].extend(results)

            # Count validation results by severity
            total_checks = len(validation_results['accounts'])
            errors = sum(1 for r in validation_results['accounts'] if r.severity == ValidationSeverity.ERROR)
            warnings = sum(1 for r in validation_results['accounts'] if r.severity == ValidationSeverity.WARNING)

            validation_results['summary'] = {
                'total_checks': total_checks,
                'errors': errors,
                'warnings': warnings,
                'passed': total_checks - errors - warnings
            }

            logger.info(f"Validation completed: {validation_results['summary']}")

        except Exception as e:
            logger.error(f"Error during validation: {e}")
            validation_results['error'] = str(e)

        return validation_results

    def close(self) -> None:
        """Clean up database connection."""
        if self.db:
            self.db.close()


@click.command()
@click.option('--file', '-f', required=True, help='Path to the JSON data file')
@click.option('--organization-id', '-o', default=2, help='Organization ID (default: 2)')
@click.option('--industry', '-i', default='TECHNOLOGY',
              type=click.Choice(['GENERAL', 'TECHNOLOGY', 'RETAIL', 'MANUFACTURING', 'SERVICES']),
              help='Industry type (default: TECHNOLOGY)')
@click.option('--output-dir', '-d', default='reports', help='Output directory for reports (default: reports)')
@click.option('--skip-validation', is_flag=True, help='Skip data validation step')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def main(file: str, organization_id: int, industry: str, output_dir: str, skip_validation: bool, verbose: bool) -> None:
    """
    MCX3D Financials Data Ingestion and Report Generation Tool

    This tool processes financial data from JSON files and generates comprehensive
    financial reports using the MCX3D Financials application infrastructure.

    Example usage:
        python -m mcx3d_finance.cli.data_ingestion -f sample_data/company_data.json
        python -m mcx3d_finance.cli.data_ingestion -f data.json -o 1 -i RETAIL -d my_reports
    """
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Convert industry string to enum
    industry_enum = getattr(IndustryType, industry)

    processor = None
    try:
        # Initialize processor
        logger.info("Initializing MCX3D Data Ingestion Processor...")
        processor = DataIngestionProcessor(
            organization_id=organization_id,
            industry=industry_enum
        )

        # Load and validate data
        logger.info("=" * 60)
        logger.info("STEP 1: Loading and validating data")
        logger.info("=" * 60)
        data = processor.load_and_validate_data(file)

        # Process data sections
        logger.info("=" * 60)
        logger.info("STEP 2: Processing data into database")
        logger.info("=" * 60)

        accounts_processed = processor.process_accounts(data.get('accounts', []))
        contacts_processed = processor.process_contacts(data.get('contacts', []))
        invoices_processed = processor.process_invoices(data.get('invoices', []))
        transactions_processed = processor.process_bank_transactions(data.get('bank_transactions', []))

        logger.info(f"Data processing completed:")
        logger.info(f"  - Accounts processed: {accounts_processed}")
        logger.info(f"  - Contacts processed: {contacts_processed}")
        logger.info(f"  - Invoices processed: {invoices_processed}")
        logger.info(f"  - Bank transactions processed: {transactions_processed}")

        # Validate processed data
        if not skip_validation:
            logger.info("=" * 60)
            logger.info("STEP 3: Validating processed data")
            logger.info("=" * 60)
            validation_results = processor.validate_processed_data()

            if 'error' not in validation_results:
                summary = validation_results['summary']
                logger.info(f"Validation summary:")
                logger.info(f"  - Total checks: {summary['total_checks']}")
                logger.info(f"  - Passed: {summary['passed']}")
                logger.info(f"  - Warnings: {summary['warnings']}")
                logger.info(f"  - Errors: {summary['errors']}")

                if summary['errors'] > 0:
                    logger.warning("Data validation found errors. Consider reviewing the data quality.")

        # Generate reports
        logger.info("=" * 60)
        logger.info("STEP 4: Generating financial reports")
        logger.info("=" * 60)
        reports = processor.generate_financial_reports(output_dir)

        # Summary
        logger.info("=" * 60)
        logger.info("PROCESSING COMPLETE")
        logger.info("=" * 60)

        successful_reports = [name for name, report in reports.items() if report['status'] == 'success']
        failed_reports = [name for name, report in reports.items() if report['status'] == 'error']

        logger.info(f"Successfully generated reports: {len(successful_reports)}")
        for report_name in successful_reports:
            logger.info(f"  - {report_name}: {reports[report_name]['file']}")

        if failed_reports:
            logger.warning(f"Failed to generate reports: {len(failed_reports)}")
            for report_name in failed_reports:
                logger.warning(f"  - {report_name}: {reports[report_name]['error']}")

        logger.info(f"Reports saved to directory: {output_dir}")
        logger.info("Data ingestion and report generation completed successfully!")

    except Exception as e:
        logger.error(f"Fatal error during processing: {e}")
        sys.exit(1)

    finally:
        if processor:
            processor.close()


if __name__ == '__main__':
    main()
