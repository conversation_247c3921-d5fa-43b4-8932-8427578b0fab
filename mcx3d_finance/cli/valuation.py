"""
Valuation CLI commands for DCF, multiples, and SaaS-specific valuations.
"""

import click
import json
from mcx3d_finance.core.logging_factory import LoggerFactory
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.multiples import MultiplesValuation
from mcx3d_finance.core.valuation.financial_projections import FinancialProjectionBuilder
from mcx3d_finance.core.valuation.saas_valuation import SaaSValuation
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator
from mcx3d_finance.core.financials.balance_sheet import UKBalanceSheetGenerator
from mcx3d_finance.reporting.generator import ReportGenerator

logger = LoggerFactory.get_logger(__name__, domain='cli')


def _create_output_structure(output_dir: str) -> str:
    """Create organized directory structure for reports."""
    timestamp = datetime.now().strftime("%Y%m%d")
    organized_dir = os.path.join(output_dir, "valuations", timestamp)
    os.makedirs(organized_dir, exist_ok=True)
    return organized_dir


@click.group()
def valuate() -> None:
    """Runs comprehensive valuation models with Xero data integration.
    
    Export Options:
    - Use --export pdf to generate professional PDF valuation reports
    - Use --export excel to create detailed Excel financial models
    - Combine with --output-dir to specify custom output location
    
    Examples:
    
    DCF Valuation with PDF export:
        python -m mcx3d_finance.cli.main valuate dcf -o 123 --export pdf
    
    SaaS Valuation with Excel export:
        python -m mcx3d_finance.cli.main valuate saas -o 123 --export excel --output-dir ./my-reports
    
    Comprehensive SaaS analysis with both console and PDF output:
        python -m mcx3d_finance.cli.main valuate saas-comprehensive -o 123 --output detailed --export pdf
    """
    pass


@valuate.command("dcf")
@click.option("--organization-id", "-o", required=True, type=int, help="Organization ID for Xero data")
@click.option("--config", type=click.Path(exists=True), help="Optional DCF configuration JSON file")
@click.option("--scenarios", multiple=True, default=["base", "upside", "downside"], 
              help="Valuation scenarios to run (default: base, upside, downside)")
@click.option("--discount-rate", type=float, default=0.12, help="Discount rate (default: 12%)")
@click.option("--terminal-growth", type=float, default=0.025, help="Terminal growth rate (default: 2.5%)")
@click.option("--projection-years", type=int, default=5, help="Number of projection years (default: 5)")
@click.option("--output", "-f", type=click.Choice(["json", "summary"]), default="summary", 
              help="Output format (default: summary)")
@click.option("--monte-carlo", is_flag=True, help="Include Monte Carlo simulation")
@click.option("--simulations", type=int, default=10000, help="Number of Monte Carlo simulations")
@click.option("--export", type=click.Choice(["pdf", "excel"]), help="Export professional valuation report")
@click.option("--output-dir", default="./reports", help="Output directory for exported reports (default: ./reports)")
@click.option("--async", "async_mode", is_flag=True, help="Run valuation asynchronously")
def dcf_command(organization_id: int, config: Optional[str], scenarios: List[str], 
                discount_rate: float, terminal_growth: float, projection_years: int,
                output: str, monte_carlo: bool, simulations: int, export: Optional[str], output_dir: str, async_mode: bool):
    """Run comprehensive DCF valuation using historical Xero data."""
    try:
        if async_mode:
            # Run asynchronously using Celery
            click.echo(f"🚀 Starting async DCF valuation for organization {organization_id}...")
            
            from mcx3d_finance.tasks.valuation_tasks import calculate_dcf_valuation_async
            
            # Dispatch async task
            task = calculate_dcf_valuation_async.delay(
                organization_id=organization_id,
                discount_rate=discount_rate,
                terminal_growth=terminal_growth,
                projection_years=projection_years,
                scenarios=list(scenarios),
                monte_carlo=monte_carlo,
                simulations=simulations
            )
            
            click.echo(f"\n✅ Valuation task queued with ID: {task.id}")
            click.echo(f"Use 'mcx3d-finance valuate status {task.id}' to check progress")
            click.echo("\nThe valuation will run in the background. You can continue using the CLI.")
            
            # If export was requested, save task info for later retrieval
            if export:
                task_info = {
                    "task_id": task.id,
                    "organization_id": organization_id,
                    "export_format": export,
                    "output_dir": output_dir,
                    "created_at": datetime.now().isoformat()
                }
                task_file = os.path.join(output_dir, f"dcf_task_{task.id}.json")
                os.makedirs(output_dir, exist_ok=True)
                with open(task_file, 'w') as f:
                    json.dump(task_info, f, indent=2)
                click.echo(f"Task info saved to: {task_file}")
            
            return
            
        # Run synchronously (existing code)
        click.echo(f"🔍 Running DCF valuation for organization {organization_id}...")
        
        # Initialize DCF valuation engine
        dcf_model = DCFValuation()
        
        # Build comprehensive financial projections from Xero data
        db = SessionLocal()
        projection_builder = FinancialProjectionBuilder(db)
        
        click.echo("📈 Analyzing historical performance and building projections...")
        comprehensive_projections = projection_builder.build_comprehensive_projections(
            organization_id, projection_years, scenarios=list(scenarios)
        )
        
        if not comprehensive_projections:
            click.echo("❌ Error: Unable to build financial projections from Xero data")
            db.close()
            return
        
        # Use base scenario projections for DCF
        projections = comprehensive_projections.get('base', [])
        
        # Load additional config if provided
        market_data = {}
        company_info = {"name": f"Organization {organization_id}"}
        
        if config:
            with open(config, 'r') as f:
                config_data = json.load(f)
                discount_rate = config_data.get("discount_rate", discount_rate)
                terminal_growth = config_data.get("terminal_growth_rate", terminal_growth)
                market_data = config_data.get("market_data", {})
                company_info = config_data.get("company_info", company_info)
        
        # Run comprehensive DCF analysis
        click.echo("📊 Calculating DCF valuation...")
        
        if monte_carlo:
            # Full comprehensive report with Monte Carlo
            dcf_result = dcf_model.generate_comprehensive_dcf_report(
                projections, discount_rate, terminal_growth, 
                company_info=company_info, market_data=market_data,
                include_monte_carlo=True, monte_carlo_simulations=simulations
            )
        else:
            # Multi-scenario DCF analysis using all projection scenarios
            dcf_results = {}
            for scenario, scenario_projections in comprehensive_projections.items():
                if scenario_projections:
                    dcf_results[scenario] = dcf_model.calculate_dcf_valuation(
                        scenario_projections, discount_rate, terminal_growth, scenarios=[scenario]
                    )
            
            dcf_result = {
                'methodology': 'Multi-Scenario DCF Analysis',
                'scenarios': dcf_results,
                'base_scenario': dcf_results.get('base', {}),
                'discount_rate': discount_rate,
                'terminal_growth_rate': terminal_growth,
                'projection_years': projection_years
            }
        
        db.close()
        
        # Export professional report if requested
        if export:
            report_generator = ReportGenerator()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create organized output structure
            organized_dir = _create_output_structure(output_dir)
            
            if export == "pdf":
                output_path = os.path.join(organized_dir, f"dcf_valuation_{organization_id}_{timestamp}.pdf")
            elif export == "excel":
                output_path = os.path.join(organized_dir, f"dcf_valuation_{organization_id}_{timestamp}.xlsx")
            
            # Use main generator with format-specific methods
            try:
                if export == "pdf":
                    report_generator.generate_dcf_valuation_pdf(dcf_result, output_path)
                elif export == "excel":
                    report_generator.generate_dcf_valuation_excel(dcf_result, output_path)
                click.echo(f"✅ {export.upper()} report generated: {output_path}")
            except Exception as e:
                click.echo(f"❌ Report generation failed: {str(e)}")
                return
        
        # Output results to console
        if output == "json":
            click.echo(json.dumps(dcf_result, indent=2, default=str))
        else:
            _display_dcf_summary(dcf_result, monte_carlo)
            
        click.echo("✅ DCF valuation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in DCF valuation: {e}")
        click.echo(f"❌ Error: {e}")


@valuate.command("multiples")
@click.option("--organization-id", "-o", required=True, type=int, help="Organization ID for Xero data")
@click.option("--config", type=click.Path(exists=True), help="Optional multiples configuration JSON file")
@click.option("--comparables", type=click.Path(exists=True), help="JSON file with comparable companies data")
@click.option("--multiples", multiple=True, default=["ev_revenue", "ev_ebitda", "pe_ratio"], 
              help="Valuation multiples to use")
@click.option("--output", "-f", type=click.Choice(["json", "summary"]), default="summary", 
              help="Output format (default: summary)")
@click.option("--export", type=click.Choice(["pdf", "excel"]), help="Export professional valuation report")
@click.option("--output-dir", default="./reports", help="Output directory for exported reports (default: ./reports)")
def multiples_command(organization_id: int, config: Optional[str], comparables: Optional[str],
                     multiples: List[str], output: str, export: Optional[str], output_dir: str):
    """Run comprehensive multiples-based valuation."""
    try:
        click.echo(f"🔍 Running multiples valuation for organization {organization_id}...")
        
        # Initialize multiples valuation engine
        multiples_model = MultiplesValuation()
        
        # Get target company metrics from Xero data
        target_metrics = _get_company_metrics(organization_id)
        
        if not target_metrics:
            click.echo("❌ Error: Unable to calculate company metrics from Xero data")
            return
        
        # Load comparable companies data
        comparable_companies = []
        if comparables:
            with open(comparables, 'r') as f:
                comparable_companies = json.load(f)
        else:
            # Use default industry comparables (would be loaded from database in production)
            comparable_companies = _get_default_comparables()
        
        # Load additional config if provided
        weights = None
        if config:
            with open(config, 'r') as f:
                config_data = json.load(f)
                multiples = config_data.get("multiples", list(multiples))
                weights = config_data.get("weights")
        
        # Run multiples analysis
        click.echo("📊 Calculating multiples-based valuation...")
        
        multiples_result = multiples_model.calculate_comprehensive_multiples_valuation(
            target_metrics, comparable_companies, list(multiples), weights
        )
        
        # Export professional report if requested
        if export:
            # Note: Multiples valuation templates are implemented and ready
            # Template methods need integration into main ReportGenerator class
            click.echo("📋 Note: Multiples report export templates are available.")
            click.echo("   To enable full export functionality, integrate multiples templates")
            click.echo("   into the ReportGenerator class (templates created separately).")
            click.echo("   For now, using JSON output for comprehensive multiples analysis.")
        
        # Output results to console
        if output == "json":
            click.echo(json.dumps(multiples_result, indent=2, default=str))
        else:
            _display_multiples_summary(multiples_result)
            
        click.echo("✅ Multiples valuation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in multiples valuation: {e}")
        click.echo(f"❌ Error: {e}")


@valuate.command("saas")
@click.option("--organization-id", "-o", required=True, type=int, help="Organization ID for Xero data")
@click.option("--period-months", type=int, default=12, help="Analysis period in months (default: 12)")
@click.option("--arr-multiple", type=float, default=8.0, help="ARR valuation multiple (default: 8x)")
@click.option("--output", "-f", type=click.Choice(["json", "summary"]), default="summary", 
              help="Output format (default: summary)")
@click.option("--export", type=click.Choice(["pdf", "excel"]), help="Export professional valuation report")
@click.option("--output-dir", default="./reports", help="Output directory for exported reports (default: ./reports)")
def saas_command(organization_id: int, period_months: int, arr_multiple: float, output: str, export: Optional[str], output_dir: str) -> None:
    """Run SaaS-specific valuation using ARR and unit economics."""
    try:
        click.echo(f"🔍 Running SaaS valuation for organization {organization_id}...")
        
        # Calculate SaaS KPIs first
        kpi_calculator = SaaSKPICalculator(SessionLocal())
        
        period_end = datetime.now()
        period_start = period_end - timedelta(days=period_months * 30)
        
        click.echo("📊 Calculating SaaS KPIs...")
        kpis = kpi_calculator.calculate_comprehensive_kpis(organization_id, period_start, period_end)
        
        # Extract key metrics for SaaS valuation
        revenue_metrics = kpis.get("kpis", {}).get("revenue_metrics", {})
        customer_metrics = kpis.get("kpis", {}).get("customer_metrics", {})
        
        arr = revenue_metrics.get("annual_recurring_revenue", 0)
        mrr = revenue_metrics.get("monthly_recurring_revenue", 0)
        ltv_cac_ratio = customer_metrics.get("ltv_cac_ratio", 0)
        churn_rate = customer_metrics.get("customer_churn_rate", 0)
        
        # Calculate SaaS-specific valuation
        click.echo("💰 Calculating SaaS valuation...")
        
        # Base ARR multiple valuation
        base_valuation = arr * arr_multiple
        
        # Apply quality adjustments based on SaaS metrics
        adjustments = {}
        
        # Growth adjustment
        growth_rate = revenue_metrics.get("revenue_growth_rate", {}).get("monthly", 0)
        if growth_rate > 15:
            adjustments["high_growth_premium"] = 0.25
        elif growth_rate < 5:
            adjustments["low_growth_discount"] = -0.15
        
        # Unit economics adjustment
        if ltv_cac_ratio > 5:
            adjustments["strong_unit_economics_premium"] = 0.15
        elif ltv_cac_ratio < 3:
            adjustments["weak_unit_economics_discount"] = -0.20
        
        # Churn adjustment
        if churn_rate < 3:
            adjustments["low_churn_premium"] = 0.10
        elif churn_rate > 7:
            adjustments["high_churn_discount"] = -0.20
        
        # Apply adjustments
        total_adjustment = sum(adjustments.values())
        adjusted_valuation = base_valuation * (1 + total_adjustment)
        
        saas_result = {
            "methodology": "SaaS ARR Multiple Valuation",
            "base_metrics": {
                "annual_recurring_revenue": arr,
                "monthly_recurring_revenue": mrr,
                "arr_multiple": arr_multiple,
                "ltv_cac_ratio": ltv_cac_ratio,
                "churn_rate": churn_rate,
                "growth_rate": growth_rate,
            },
            "valuation": {
                "base_valuation": base_valuation,
                "adjustments": adjustments,
                "total_adjustment": total_adjustment,
                "adjusted_valuation": adjusted_valuation,
            },
            "full_kpi_analysis": kpis,
        }
        
        # Export professional report if requested
        if export:
            report_generator = ReportGenerator()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create organized output structure
            organized_dir = _create_output_structure(output_dir)
            
            if export == "pdf":
                output_path = os.path.join(organized_dir, f"saas_valuation_{organization_id}_{timestamp}.pdf")
            elif export == "excel":
                output_path = os.path.join(organized_dir, f"saas_valuation_{organization_id}_{timestamp}.xlsx")
            
            # Use main generator with format-specific methods
            try:
                if export == "pdf":
                    report_generator.generate_saas_valuation_pdf(saas_result, output_path)
                elif export == "excel":
                    report_generator.generate_saas_valuation_excel(saas_result, output_path)
                click.echo(f"✅ {export.upper()} report generated: {output_path}")
            except Exception as e:
                click.echo(f"❌ Report generation failed: {str(e)}")
                return
        
        # Output results to console
        if output == "json":
            click.echo(json.dumps(saas_result, indent=2, default=str))
        else:
            _display_saas_summary(saas_result)
            
        click.echo("✅ SaaS valuation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in SaaS valuation: {e}")
        click.echo(f"❌ Error: {e}")


@valuate.command("saas-comprehensive")
@click.option("--organization-id", "-o", required=True, type=int, help="Organization ID for Xero data")
@click.option("--period-months", type=int, default=12, help="Analysis period in months (default: 12)")
@click.option("--arr-multiple", type=float, default=8.0, help="Base ARR valuation multiple (default: 8x)")
@click.option("--include-scenarios", is_flag=True, help="Include bull/bear scenario analysis")
@click.option("--output", "-f", type=click.Choice(["json", "summary", "detailed"]), default="summary", 
              help="Output format (default: summary)")
@click.option("--export", type=click.Choice(["pdf", "excel"]), help="Export professional valuation report")
@click.option("--output-dir", default="./reports", help="Output directory for exported reports (default: ./reports)")
def saas_comprehensive_command(organization_id: int, period_months: int, arr_multiple: float, 
                              include_scenarios: bool, output: str, export: Optional[str], output_dir: str):
    """Run comprehensive SaaS valuation using multiple methodologies."""
    try:
        click.echo(f"🔍 Running comprehensive SaaS valuation for organization {organization_id}...")
        
        # Initialize comprehensive SaaS valuation engine
        db = SessionLocal()
        saas_valuation = SaaSValuation(db)
        
        # Calculate analysis period
        period_end = datetime.now()
        period_start = period_end - timedelta(days=period_months * 30)
        
        click.echo("📊 Calculating comprehensive SaaS valuation using multiple methodologies...")
        click.echo(f"   • ARR Multiple Valuation (with quality adjustments)")
        click.echo(f"   • Revenue Multiple Valuation")
        click.echo(f"   • SaaS-optimized DCF Model")
        click.echo(f"   • Unit Economics Valuation")
        
        # Run comprehensive valuation
        comprehensive_result = saas_valuation.calculate_comprehensive_saas_valuation(
            organization_id, period_start, period_end, arr_multiple, include_scenarios
        )
        
        db.close()
        
        if 'error' in comprehensive_result:
            click.echo(f"❌ Error: {comprehensive_result['error']}")
            return
        
        # Export professional report if requested
        if export:
            report_generator = ReportGenerator()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create organized output structure
            organized_dir = _create_output_structure(output_dir)
            
            if export == "pdf":
                output_path = os.path.join(organized_dir, f"saas_comprehensive_{organization_id}_{timestamp}.pdf")
                report_generator.generate_saas_valuation_pdf(comprehensive_result, output_path)
                click.echo(f"📄 PDF report generated: {output_path}")
            elif export == "excel":
                output_path = os.path.join(organized_dir, f"saas_comprehensive_{organization_id}_{timestamp}.xlsx")
                report_generator.generate_saas_valuation_excel(comprehensive_result, output_path)
                click.echo(f"📊 Excel report generated: {output_path}")
        
        # Output results to console
        if output == "json":
            click.echo(json.dumps(comprehensive_result, indent=2, default=str))
        elif output == "detailed":
            _display_comprehensive_saas_summary(comprehensive_result, detailed=True)
        else:
            _display_comprehensive_saas_summary(comprehensive_result, detailed=False)
            
        click.echo("✅ Comprehensive SaaS valuation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in comprehensive SaaS valuation: {e}")
        click.echo(f"❌ Error: {e}")


# Removed simple projection builder - now using FinancialProjectionBuilder class


def _get_company_metrics(organization_id: int) -> Dict[str, float]:
    """Get company financial metrics from Xero data."""
    try:
        # Get current financial position
        db = SessionLocal()
        
        # Generate recent financial statements
        current_date = datetime.now()
        last_year = current_date - timedelta(days=365)
        
        income_gen = UKProfitAndLossGenerator(organization_id)
        balance_gen = UKBalanceSheetGenerator(organization_id)
        
        income_stmt = income_gen.generate_profit_and_loss(last_year, current_date)
        balance_sheet = balance_gen.generate_balance_sheet(current_date)
        
        # Extract metrics for multiples valuation
        income_totals = income_stmt.get("totals", {})
        
        metrics = {
            "revenue": income_totals.get("total_revenue", 0),
            "ebitda": income_totals.get("gross_profit", 0),  # Simplified
            "net_income": income_totals.get("net_income", 0),
            "sales": income_totals.get("total_revenue", 0),
            "free_cash_flow": income_totals.get("net_income", 0) * 0.8,  # Simplified
            "book_value": 1000000,  # Would calculate from balance sheet
            "revenue_growth_rate": 0.15,  # Would calculate from historical data
            "ebitda_margin": (income_totals.get("gross_profit", 0) / 
                             income_totals.get("total_revenue", 1)) if income_totals.get("total_revenue", 0) > 0 else 0,
            "debt_to_equity": 0.3,  # Would calculate from balance sheet
        }
        
        db.close()
        return metrics
        
    except Exception as e:
        logger.error(f"Error getting company metrics: {e}")
        return {}


def _get_default_comparables() -> List[Dict[str, Any]]:
    """Get default comparable companies data."""
    # In production, this would come from a database or external API
    return [
        {"company": "Comparable A", "ev_revenue": 8.5, "ev_ebitda": 25.0, "pe_ratio": 30.0},
        {"company": "Comparable B", "ev_revenue": 12.0, "ev_ebitda": 35.0, "pe_ratio": 45.0},
        {"company": "Comparable C", "ev_revenue": 6.0, "ev_ebitda": 20.0, "pe_ratio": 25.0},
        {"company": "Comparable D", "ev_revenue": 10.0, "ev_ebitda": 28.0, "pe_ratio": 40.0},
        {"company": "Comparable E", "ev_revenue": 9.0, "ev_ebitda": 22.0, "pe_ratio": 35.0},
    ]


def _display_dcf_summary(dcf_result: Dict[str, Any], include_monte_carlo: bool = False) -> None:
    """Display DCF valuation summary."""
    click.echo("\n" + "="*60)
    click.echo("📊 DCF VALUATION SUMMARY")
    click.echo("="*60)
    
    if "executive_summary" in dcf_result:
        # Comprehensive report format
        summary = dcf_result["executive_summary"]
        click.echo(f"Company: {summary.get('company_name', 'N/A')}")
        click.echo(f"Valuation Date: {summary.get('valuation_date', 'N/A')}")
        click.echo(f"Enterprise Value: ${summary.get('base_enterprise_value', 0):,.2f}")
        if summary.get('equity_value'):
            click.echo(f"Equity Value: ${summary.get('equity_value', 0):,.2f}")
        if summary.get('value_per_share'):
            click.echo(f"Value per Share: ${summary.get('value_per_share', 0):,.2f}")
    elif "scenarios" in dcf_result:
        # Multi-scenario format
        click.echo(f"Methodology: {dcf_result.get('methodology', 'DCF Analysis')}")
        click.echo(f"Discount Rate: {dcf_result.get('discount_rate', 0)*100:.1f}%")
        click.echo(f"Terminal Growth Rate: {dcf_result.get('terminal_growth_rate', 0)*100:.1f}%")
        click.echo(f"Projection Period: {dcf_result.get('projection_years', 5)} years")
        
        click.echo(f"\n📈 SCENARIO ANALYSIS:")
        scenarios = dcf_result.get("scenarios", {})
        
        for scenario_name, scenario_data in scenarios.items():
            valuation_results = scenario_data.get("valuation_results", {})
            if valuation_results:
                for result_name, result in valuation_results.items():
                    enterprise_value = result.get('enterprise_value', 0)
                    click.echo(f"  {scenario_name.upper()}: ${enterprise_value:,.2f}")
        
        # Show valuation range
        all_valuations = []
        for scenario_data in scenarios.values():
            valuation_results = scenario_data.get("valuation_results", {})
            for result in valuation_results.values():
                if result.get('enterprise_value'):
                    all_valuations.append(result['enterprise_value'])
        
        if all_valuations:
            min_val = min(all_valuations)
            max_val = max(all_valuations)
            avg_val = sum(all_valuations) / len(all_valuations)
            
            click.echo(f"\n💰 VALUATION RANGE:")
            click.echo(f"  Minimum: ${min_val:,.2f}")
            click.echo(f"  Average: ${avg_val:,.2f}")
            click.echo(f"  Maximum: ${max_val:,.2f}")
            click.echo(f"  Spread: {((max_val - min_val) / avg_val * 100):.1f}%")
    else:
        # Standard format
        valuation_results = dcf_result.get("valuation_results", {})
        for scenario, result in valuation_results.items():
            click.echo(f"\n{scenario.upper()} SCENARIO:")
            click.echo(f"  Enterprise Value: ${result.get('enterprise_value', 0):,.2f}")
    
    # Show sensitivity analysis
    sensitivity = dcf_result.get("sensitivity_analysis", {})
    if sensitivity:
        click.echo(f"\nSENSITIVITY ANALYSIS:")
        click.echo(f"  Valuation Range: ${sensitivity.get('min_valuation', 0):,.2f} - ${sensitivity.get('max_valuation', 0):,.2f}")
        click.echo(f"  Volatility: {sensitivity.get('volatility', 0)*100:.1f}%")
    
    # Show Monte Carlo results if available
    if include_monte_carlo and "monte_carlo_simulation" in dcf_result:
        monte_carlo = dcf_result["monte_carlo_simulation"]
        if "error" not in monte_carlo:
            click.echo(f"\nMONTE CARLO SIMULATION ({monte_carlo.get('num_simulations', 0):,} runs):")
            click.echo(f"  Mean Valuation: ${monte_carlo.get('mean_valuation', 0):,.2f}")
            click.echo(f"  90% Confidence Interval: ${monte_carlo.get('percentiles', {}).get('p5', 0):,.2f} - ${monte_carlo.get('percentiles', {}).get('p95', 0):,.2f}")
    
    click.echo("\n" + "="*60 + "\n")


def _display_multiples_summary(multiples_result: Dict[str, Any]) -> None:
    """Display multiples valuation summary."""
    click.echo("\n" + "="*60)
    click.echo("📊 MULTIPLES VALUATION SUMMARY")
    click.echo("="*60)
    
    summary = multiples_result.get("valuation_summary", {})
    click.echo(f"Valuation Date: {multiples_result.get('valuation_date', 'N/A')}")
    click.echo(f"Comparable Companies: {multiples_result.get('comparable_companies_count', 0)}")
    
    if "valuation_range" in summary:
        val_range = summary["valuation_range"]
        click.echo(f"\nVALUATION RANGE:")
        click.echo(f"  Minimum: ${val_range.get('minimum', 0):,.2f}")
        click.echo(f"  Maximum: ${val_range.get('maximum', 0):,.2f}")
        click.echo(f"  Average: ${val_range.get('average', 0):,.2f}")
    
    click.echo(f"\nRECOMMENDED VALUATION: ${summary.get('recommended_valuation', 0):,.2f}")
    click.echo(f"Confidence: {summary.get('valuation_confidence', 'N/A')}")
    
    # Show individual multiples
    individual_valuations = multiples_result.get("individual_valuations", {})
    if individual_valuations:
        click.echo(f"\nINDIVIDUAL MULTIPLES:")
        for multiple, data in individual_valuations.items():
            if "error" not in data:
                click.echo(f"  {multiple}: ${data.get('recommended_valuation', 0):,.2f} (confidence: {data.get('confidence_score', 0):.0%})")
    
    # Show key insights
    insights = summary.get("key_insights", [])
    if insights:
        click.echo(f"\nKEY INSIGHTS:")
        for insight in insights:
            click.echo(f"  • {insight}")
    
    click.echo("\n" + "="*60 + "\n")


def _display_saas_summary(saas_result: Dict[str, Any]) -> None:
    """Display SaaS valuation summary."""
    click.echo("\n" + "="*60)
    click.echo("📊 SAAS VALUATION SUMMARY")
    click.echo("="*60)
    
    base_metrics = saas_result.get("base_metrics", {})
    valuation = saas_result.get("valuation", {})
    
    click.echo(f"ARR: ${base_metrics.get('annual_recurring_revenue', 0):,.2f}")
    click.echo(f"MRR: ${base_metrics.get('monthly_recurring_revenue', 0):,.2f}")
    click.echo(f"Growth Rate: {base_metrics.get('growth_rate', 0):.1f}%")
    click.echo(f"LTV/CAC Ratio: {base_metrics.get('ltv_cac_ratio', 0):.1f}x")
    click.echo(f"Churn Rate: {base_metrics.get('churn_rate', 0):.1f}%")
    
    click.echo(f"\nVALUATION CALCULATION:")
    click.echo(f"  Base Valuation ({base_metrics.get('arr_multiple', 0)}x ARR): ${valuation.get('base_valuation', 0):,.2f}")
    
    adjustments = valuation.get("adjustments", {})
    if adjustments:
        click.echo(f"  Quality Adjustments:")
        for adj_name, adj_value in adjustments.items():
            sign = "+" if adj_value >= 0 else ""
            click.echo(f"    {adj_name.replace('_', ' ').title()}: {sign}{adj_value:.1%}")
    
    click.echo(f"\nFINAL VALUATION: ${valuation.get('adjusted_valuation', 0):,.2f}")
    
    # Show SaaS health summary if available
    full_kpis = saas_result.get("full_kpi_analysis", {})
    if full_kpis:
        health_score = full_kpis.get("kpis", {}).get("health_score", {})
        if health_score:
            click.echo(f"\nSaaS HEALTH SCORE: {health_score.get('overall_score', 0):.0f}/100 ({health_score.get('health_grade', 'N/A')})")
            click.echo(f"Status: {health_score.get('health_status', 'N/A')}")
    
    click.echo("\n" + "="*60 + "\n")


def _display_comprehensive_saas_summary(result: Dict[str, Any], detailed: bool = False) -> None:
    """Display comprehensive SaaS valuation summary."""
    click.echo("\n" + "="*70)
    click.echo("📊 COMPREHENSIVE SAAS VALUATION")
    click.echo("="*70)
    
    # Basic info
    click.echo(f"Organization ID: {result.get('organization_id', 'N/A')}")
    click.echo(f"Analysis Date: {result.get('valuation_date', 'N/A')}")
    click.echo(f"Methodology: {result.get('methodology', 'N/A')}")
    
    # Key metrics
    key_metrics = result.get('key_metrics', {})
    if key_metrics:
        click.echo(f"\n💰 KEY SAAS METRICS:")
        click.echo(f"  ARR: ${key_metrics.get('arr', 0):,.2f}")
        click.echo(f"  MRR: ${key_metrics.get('mrr', 0):,.2f}")
        click.echo(f"  Growth Rate: {key_metrics.get('growth_rate', 0):.1f}%")
        click.echo(f"  Churn Rate: {key_metrics.get('churn_rate', 0):.1f}%")
        click.echo(f"  LTV/CAC Ratio: {key_metrics.get('ltv_cac_ratio', 0):.1f}x")
        click.echo(f"  Net Revenue Retention: {key_metrics.get('nrr', 0):.1f}%")
        click.echo(f"  Active Customers: {key_metrics.get('active_customers', 0):,}")
    
    # Weighted valuation
    weighted_val = result.get('weighted_valuation', {})
    if weighted_val and 'error' not in weighted_val:
        click.echo(f"\n🎯 WEIGHTED AVERAGE VALUATION:")
        click.echo(f"  Primary Valuation: ${weighted_val.get('weighted_average_valuation', 0):,.2f}")
        
        val_range = weighted_val.get('valuation_range', {})
        if val_range:
            click.echo(f"  Valuation Range: ${val_range.get('minimum', 0):,.2f} - ${val_range.get('maximum', 0):,.2f}")
            click.echo(f"  Spread: {val_range.get('spread_percentage', 0):.1f}%")
        
        click.echo(f"  Methods Used: {weighted_val.get('methods_used', 0)}")
    
    # Individual valuation methods
    valuation_methods = result.get('valuation_methods', {})
    if valuation_methods and detailed:
        click.echo(f"\n📈 INDIVIDUAL VALUATION METHODS:")
        
        for method, data in valuation_methods.items():
            if 'error' not in data:
                method_name = method.replace('_', ' ').title()
                click.echo(f"\n  {method_name}:")
                
                if method == 'arr_multiple':
                    click.echo(f"    Base Valuation: ${data.get('base_valuation', 0):,.2f}")
                    click.echo(f"    Adjusted Valuation: ${data.get('adjusted_valuation', 0):,.2f}")
                    click.echo(f"    Multiple: {data.get('adjusted_multiple', 0):.1f}x")
                    click.echo(f"    Confidence: {data.get('confidence_score', 0):.0%}")
                    
                elif method == 'revenue_multiple':
                    click.echo(f"    Valuation: ${data.get('valuation', 0):,.2f}")
                    click.echo(f"    Multiple: {data.get('revenue_multiple', 0):.1f}x")
                    
                elif method == 'saas_dcf':
                    click.echo(f"    Enterprise Value: ${data.get('enterprise_value', 0):,.2f}")
                    click.echo(f"    Terminal Value: ${data.get('terminal_value', 0):,.2f}")
                    
                elif method == 'unit_economics':
                    click.echo(f"    Total Valuation: ${data.get('total_valuation', 0):,.2f}")
                    click.echo(f"    Customer Base Value: ${data.get('customer_base_value', 0):,.2f}")
    
    # Quality assessment
    quality_assessment = result.get('quality_assessment', {})
    if quality_assessment and 'error' not in quality_assessment:
        click.echo(f"\n🏥 BUSINESS QUALITY ASSESSMENT:")
        click.echo(f"  Overall Score: {quality_assessment.get('total_score', 0)}/100")
        click.echo(f"  Quality Grade: {quality_assessment.get('grade', 'N/A')}")
        click.echo(f"  Quality Tier: {quality_assessment.get('quality_tier', 'N/A')}")
        
        if detailed:
            component_scores = quality_assessment.get('component_scores', {})
            if component_scores:
                click.echo(f"  Component Scores:")
                for component, score in component_scores.items():
                    click.echo(f"    {component.replace('_', ' ').title()}: {score}/25")
    
    # Scenario analysis
    scenario_analysis = result.get('scenario_analysis', {})
    if scenario_analysis and detailed:
        click.echo(f"\n📊 SCENARIO ANALYSIS:")
        
        for scenario, data in scenario_analysis.items():
            if 'error' not in data:
                scenario_name = scenario.upper()
                adjusted_val = data.get('adjusted_valuation', 0)
                click.echo(f"  {scenario_name}: ${adjusted_val:,.2f}")
    
    # Industry benchmarks
    benchmarks = result.get('industry_benchmarks', {})
    if benchmarks and detailed:
        click.echo(f"\n📈 INDUSTRY BENCHMARKS:")
        key_metrics_bench = benchmarks.get('key_metrics', {})
        if key_metrics_bench:
            click.echo(f"  Growth Rate - Excellent: {key_metrics_bench.get('growth_rate', {}).get('excellent', 0)}%")
            click.echo(f"  Churn Rate - Excellent: {key_metrics_bench.get('churn_rate', {}).get('excellent', 0)}%")
            click.echo(f"  LTV/CAC - Excellent: {key_metrics_bench.get('ltv_cac_ratio', {}).get('excellent', 0)}x")
    
    click.echo("\n" + "="*70 + "\n")


@valuate.command("status")
@click.argument("task_id")
def check_valuation_status(task_id: str) -> None:
    """Check the status of an async valuation task."""
    try:
        from mcx3d_finance.tasks.celery_app import celery_app
        
        task = celery_app.AsyncResult(task_id)
        
        click.echo(f"Task ID: {task_id}")
        click.echo(f"Status: {task.status}")
        
        if task.status == "PENDING":
            click.echo("Task is waiting to be processed...")
        elif task.status == "PROGRESS":
            meta = task.info
            if meta:
                click.echo(f"Progress: {meta.get('progress', 0)}%")
                click.echo(f"Status: {meta.get('status', 'Processing...')}")
                if 'organization_id' in meta:
                    click.echo(f"Organization ID: {meta['organization_id']}")
        elif task.status == "SUCCESS":
            result = task.result
            if result:
                click.echo("\n✅ Valuation completed successfully!")
                
                # Display brief summary
                if 'scenarios' in result:
                    click.echo("\nValuation Summary:")
                    scenarios = result.get('scenarios', {})
                    for scenario, data in scenarios.items():
                        if isinstance(data, dict) and 'valuation_results' in data:
                            for val_name, val_data in data['valuation_results'].items():
                                if 'enterprise_value' in val_data:
                                    click.echo(f"  {scenario.upper()}: ${val_data['enterprise_value']:,.2f}")
                
                click.echo(f"\nFull results available. Use output format options to view detailed results.")
        elif task.status == "FAILURE":
            error_info = task.info
            click.echo(f"❌ Task failed with error: {error_info}")
        else:
            click.echo(f"Task status: {task.status}")
            if task.info:
                click.echo(f"Additional info: {task.info}")
    
    except Exception as e:
        logger.error(f"Error checking task status: {e}")
        click.echo(f"❌ Error: {e}")
