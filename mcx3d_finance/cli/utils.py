"""
CLI utility functions and decorators for standardized command handling.

Provides consistent error handling, output formatting, performance tracking,
and structured logging across all CLI commands.
"""

import click
import functools
import time
from mcx3d_finance.core.logging_factory import LoggerFactory
import json
import sys
from typing import Callable, Any, Dict, Optional
from datetime import datetime
import traceback

logger = LoggerFactory.get_logger(__name__, domain='cli')


class CLIFormatter:
    """Standardized output formatter for CLI commands."""
    
    @staticmethod
    def success(message: str, data: Optional[Dict[str, Any]] = None):
        """Format success messages."""
        click.echo(click.style(f"✅ {message}", fg="green"))
        if data:
            click.echo(json.dumps(data, indent=2))
    
    @staticmethod
    def error(message: str, details: Optional[str] = None):
        """Format error messages."""
        click.echo(click.style(f"❌ {message}", fg="red"), err=True)
        if details:
            click.echo(click.style(f"   Details: {details}", fg="red", dim=True), err=True)
    
    @staticmethod
    def warning(message: str):
        """Format warning messages."""
        click.echo(click.style(f"⚠️  {message}", fg="yellow"))
    
    @staticmethod
    def info(message: str):
        """Format info messages."""
        click.echo(click.style(f"ℹ️  {message}", fg="blue"))
    
    @staticmethod
    def progress(message: str):
        """Format progress messages."""
        click.echo(click.style(f"🔄 {message}", fg="cyan"))
    
    @staticmethod
    def table(headers: list, rows: list):
        """Format tabular data."""
        from tabulate import tabulate
        click.echo(tabulate(rows, headers=headers, tablefmt="grid"))


class PerformanceTracker:
    """Track and report command performance metrics."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.metrics = {}
    
    def start(self):
        """Start performance tracking."""
        self.start_time = time.time()
        self.metrics["start_timestamp"] = datetime.utcnow().isoformat()
    
    def end(self):
        """End performance tracking."""
        self.end_time = time.time()
        self.metrics["end_timestamp"] = datetime.utcnow().isoformat()
        self.metrics["duration_seconds"] = round(self.end_time - self.start_time, 2)
    
    def add_metric(self, key: str, value: Any):
        """Add a custom metric."""
        self.metrics[key] = value
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics."""
        return self.metrics


def handle_cli_errors(func: Callable) -> Callable:
    """
    Decorator to handle exceptions in CLI commands consistently.
    
    Provides user-friendly error messages and logs detailed errors.
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        formatter = CLIFormatter()
        try:
            return func(*args, **kwargs)
        except click.ClickException:
            # Let Click handle its own exceptions
            raise
        except ValueError as e:
            formatter.error(f"Invalid input: {str(e)}")
            logger.error(f"ValueError in {func.__name__}: {str(e)}")
            sys.exit(1)
        except FileNotFoundError as e:
            formatter.error(f"File not found: {str(e)}")
            logger.error(f"FileNotFoundError in {func.__name__}: {str(e)}")
            sys.exit(1)
        except PermissionError as e:
            formatter.error(f"Permission denied: {str(e)}")
            logger.error(f"PermissionError in {func.__name__}: {str(e)}")
            sys.exit(1)
        except ConnectionError as e:
            formatter.error(f"Connection error: {str(e)}")
            formatter.info("Please check your network connection and try again.")
            logger.error(f"ConnectionError in {func.__name__}: {str(e)}")
            sys.exit(1)
        except Exception as e:
            formatter.error(f"Unexpected error: {str(e)}")
            formatter.info("For more details, check the logs or run with --debug flag.")
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}\n{traceback.format_exc()}")
            sys.exit(1)
    
    return wrapper


def track_performance(func: Callable) -> Callable:
    """
    Decorator to track performance metrics for CLI commands.
    
    Logs execution time and provides performance data.
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        tracker = PerformanceTracker()
        tracker.start()
        
        # Inject tracker into kwargs for command use
        kwargs['_performance_tracker'] = tracker
        
        try:
            result = func(*args, **kwargs)
            tracker.end()
            
            # Log performance metrics
            metrics = tracker.get_metrics()
            logger.info(f"Command {func.__name__} completed in {metrics['duration_seconds']}s")
            
            # Show performance in debug mode
            ctx = click.get_current_context()
            if ctx.obj and ctx.obj.get('debug', False):
                click.echo(click.style(
                    f"\n⏱️  Performance: {metrics['duration_seconds']}s", 
                    fg="magenta", 
                    dim=True
                ))
            
            return result
        except Exception:
            tracker.end()
            tracker.add_metric("status", "failed")
            raise
    
    return wrapper


def standardize_output(func: Callable) -> Callable:
    """
    Decorator to standardize output formatting across commands.
    
    Provides consistent JSON/table output based on format flag.
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        # Get context for format option
        ctx = click.get_current_context()
        output_format = ctx.obj.get('format', 'human') if ctx.obj else 'human'
        
        # Execute the command
        result = func(*args, **kwargs)
        
        # Handle different output formats
        if result is not None:
            if output_format == 'json':
                click.echo(json.dumps(result, indent=2, default=str))
            elif output_format == 'table' and isinstance(result, dict) and 'table' in result:
                formatter = CLIFormatter()
                formatter.table(result['table']['headers'], result['table']['rows'])
            # For 'human' format, assume the command handles its own output
        
        return result
    
    return wrapper


def cli_command(name: str, help: str, **command_kwargs) -> Callable:
    """
    Standardized command decorator that combines all CLI utilities.
    
    Args:
        name: Command name
        help: Command help text
        **command_kwargs: Additional arguments to pass to click.command
    
    Returns:
        Decorated command function
    """
    def decorator(func: Callable) -> Callable:
        # Apply decorators in order
        func = standardize_output(func)
        func = track_performance(func)
        func = handle_cli_errors(func)
        
        # Apply click command decorator
        func = click.command(name=name, help=help, **command_kwargs)(func)
        
        return func
    
    return decorator


def setup_logging(debug: bool = False, log_file: Optional[str] = None):
    """
    Setup structured logging for CLI commands.
    
    Args:
        debug: Enable debug logging
        log_file: Optional log file path
    """
    log_level = logging.DEBUG if debug else logging.INFO
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logging.getLogger().addHandler(file_handler)
    
    # Reduce noise from third-party libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)


def validate_date(ctx: Any, param: Any, value: Optional[str]) -> Optional[str]:
    """Click callback to validate date format."""
    if value is None:
        return value
    
    try:
        datetime.strptime(value, '%Y-%m-%d')
        return value
    except ValueError:
        raise click.BadParameter('Date must be in YYYY-MM-DD format')


def validate_positive_float(ctx: Any, param: Any, value: Optional[float]) -> Optional[float]:
    """Click callback to validate positive float values."""
    if value is None:
        return value
    
    if value <= 0:
        raise click.BadParameter(f'{param.name} must be a positive number')
    
    return value


def validate_percentage(ctx: Any, param: Any, value: Optional[float]) -> Optional[float]:
    """Click callback to validate percentage values (0-100)."""
    if value is None:
        return value
    
    if not 0 <= value <= 100:
        raise click.BadParameter(f'{param.name} must be between 0 and 100')
    
    return value


def confirm_action(message: str, default: bool = False) -> bool:
    """
    Prompt user for confirmation with consistent formatting.
    
    Args:
        message: Confirmation message
        default: Default value if user just presses enter
    
    Returns:
        User's confirmation choice
    """
    formatter = CLIFormatter()
    formatter.warning(message)
    return click.confirm("Do you want to continue?", default=default)


def handle_async_task(task_id: str, check_interval: int = 2) -> Optional[Any]:
    """
    Handle async task monitoring with progress display.
    
    Args:
        task_id: Celery task ID
        check_interval: Seconds between status checks
    """
    formatter = CLIFormatter()
    
    formatter.progress(f"Task {task_id} is running...")
    formatter.info("You can check the status with: mcx3d analytics status")
    formatter.info(f"Or wait here for completion (checking every {check_interval}s)...")
    
    # Import here to avoid circular dependency
    from mcx3d_finance.tasks.celery_app import celery_app
    
    with click.progressbar(length=100, label='Processing') as bar:
        last_progress: int = 0
        while True:
            result = celery_app.AsyncResult(task_id)
            
            if result.ready():
                bar.update(100 - last_progress)
                if result.successful():
                    formatter.success("Task completed successfully!")
                    return result.result
                else:
                    formatter.error(f"Task failed: {result.info}")
                    return None
            
            # Update progress bar if we have progress info
            if result.state == 'PROGRESS':
                current = result.info.get('current', 0)
                total = result.info.get('total', 1)
                progress = int((current / total) * 100)
                bar.update(progress - last_progress)
                last_progress = progress
            
            time.sleep(check_interval)


# Command group decorators for consistent group styling
def cli_group(name: str, help: str, **group_kwargs) -> Callable:
    """
    Create a standardized command group.
    
    Args:
        name: Group name
        help: Group help text
        **group_kwargs: Additional arguments to pass to click.group
    
    Returns:
        Decorated group function
    """
    def decorator(func: Callable) -> Callable:
        # Apply click group decorator
        group = click.group(name=name, help=help, **group_kwargs)(func)
        
        # Add context defaults
        original_invoke = group.invoke
        
        def invoke(ctx: Any) -> Any:
            if ctx.obj is None:
                ctx.obj = {}
            
            # Add common context values
            ctx.obj['command_group'] = name
            ctx.obj['start_time'] = datetime.utcnow()
            
            return original_invoke(ctx)
        
        group.invoke = invoke
        return group
    
    return decorator