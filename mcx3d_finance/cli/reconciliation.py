"""
Bank reconciliation CLI commands.
"""

import click
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Op<PERSON>
from datetime import datetime, timedelta
from pathlib import Path

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, BankReconciliation
from mcx3d_finance.core.reconciliation import BankReconciliationService
from mcx3d_finance.reporting.reconciliation_report import ReconciliationReportGenerator
from mcx3d_finance.cli.error_handler import handle_cli_errors, display_success_message

logger = LoggerFactory.get_logger(__name__, domain='cli')


@click.group()
def reconcile() -> None:
    """Bank reconciliation commands for matching transactions."""
    pass


@reconcile.command()
@click.option('--org-id', required=True, type=int, help='Organization ID')
@click.option('--bank-account', required=True, help='Bank account identifier')
@click.option('--start-date', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', required=True, help='End date (YYYY-MM-DD)')
@click.option('--statement', required=True, type=click.Path(exists=True), help='Bank statement file (CSV or Excel)')
@click.option('--output-dir', default='./reconciliation_reports', help='Output directory for reports')
@handle_cli_errors()
def start(org_id: int, bank_account: str, start_date: str, end_date: str, 
          statement: str, output_dir: str):
    """
    Start a new bank reconciliation process.
    
    Example:
        mcx3d-finance reconcile start --org-id 123 --bank-account "HSBC-001" \\
            --start-date 2024-01-01 --end-date 2024-01-31 \\
            --statement bank_statement.csv
    """
    db = SessionLocal()
    try:
        # Validate organization
        organization = db.query(Organization).filter(Organization.id == org_id).first()
        if not organization:
            click.echo(click.style(f"❌ Organization with ID {org_id} not found", fg="red"))
            return
        
        # Parse dates
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        click.echo(click.style(f"\n🏦 Starting Bank Reconciliation", fg="blue", bold=True))
        click.echo(f"Organization: {organization.name}")
        click.echo(f"Bank Account: {bank_account}")
        click.echo(f"Period: {start_date} to {end_date}")
        click.echo(f"Statement File: {statement}")
        
        # Initialize service
        service = BankReconciliationService(db)
        
        # Start reconciliation
        click.echo("\n📊 Creating reconciliation record...")
        reconciliation = service.start_reconciliation(
            organization_id=org_id,
            bank_account_id=bank_account,
            start_date=start_dt,
            end_date=end_dt
        )
        
        click.echo(f"✅ Reconciliation ID: {reconciliation.id}")
        
        # Process bank statement
        click.echo("\n📄 Processing bank statement...")
        
        # Determine file format
        file_path = Path(statement)
        file_format = 'excel' if file_path.suffix.lower() in ['.xlsx', '.xls'] else 'csv'
        
        with open(statement, 'rb') as f:
            results = service.process_bank_statement(
                reconciliation_id=reconciliation.id,
                statement_file=f,
                file_format=file_format
            )
        
        # Display results
        summary = results['summary']
        click.echo("\n" + "="*60)
        click.echo(click.style("RECONCILIATION RESULTS", fg="green", bold=True))
        click.echo("="*60)
        
        click.echo(f"\n📈 Summary:")
        click.echo(f"  • Total Bank Transactions: {summary['total_bank_transactions']}")
        click.echo(f"  • Total Statement Entries: {summary['total_statement_entries']}")
        click.echo(f"  • Matched Transactions: {summary['matched_count']}")
        click.echo(f"  • Unmatched Bank Transactions: {summary['unmatched_bank_count']}")
        click.echo(f"  • Unmatched Statement Entries: {summary['unmatched_statement_count']}")
        
        match_rate = (
            summary['matched_count'] / summary['total_bank_transactions'] * 100
            if summary['total_bank_transactions'] > 0 else 0
        )
        click.echo(f"  • Match Rate: {match_rate:.1f}%")
        
        # Show match types
        if results['matches']:
            click.echo("\n📊 Match Confidence Distribution:")
            match_types = {}
            for match in results['matches']:
                match_type = match['match_type']
                match_types[match_type] = match_types.get(match_type, 0) + 1
            
            for match_type, count in sorted(match_types.items()):
                click.echo(f"  • {match_type}: {count}")
        
        # Generate report
        click.echo(f"\n📄 Generating reconciliation report...")
        report_gen = ReconciliationReportGenerator()
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate PDF report
        pdf_path = output_path / f"reconciliation_{reconciliation.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        report_gen.generate_pdf_report(
            reconciliation_id=reconciliation.id,
            db=db,
            output_path=str(pdf_path)
        )
        
        # Generate Excel report
        excel_path = output_path / f"reconciliation_{reconciliation.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        report_gen.generate_excel_report(
            reconciliation_id=reconciliation.id,
            db=db,
            output_path=str(excel_path)
        )
        
        click.echo(f"✅ PDF Report: {pdf_path}")
        click.echo(f"✅ Excel Report: {excel_path}")
        
        # Show unmatched transactions if any
        if results['unmatched_bank']:
            click.echo(f"\n⚠️  {len(results['unmatched_bank'])} unmatched bank transactions")
            click.echo("Run 'mcx3d-finance reconcile show-unmatched' to view details")
        
        display_success_message("Bank reconciliation completed successfully!")
        
    except Exception as e:
        logger.error(f"Reconciliation failed: {e}")
        click.echo(click.style(f"❌ Reconciliation failed: {e}", fg="red"))
    finally:
        db.close()


@reconcile.command()
@click.option('--org-id', required=True, type=int, help='Organization ID')
@click.option('--days', default=30, help='Number of days to look back')
@handle_cli_errors()
def list(org_id: int, days: int) -> None:
    """List recent reconciliations for an organization."""
    db = SessionLocal()
    try:
        # Get recent reconciliations
        since_date = datetime.now() - timedelta(days=days)
        
        reconciliations = db.query(BankReconciliation).filter(
            BankReconciliation.organization_id == org_id,
            BankReconciliation.created_at >= since_date
        ).order_by(BankReconciliation.created_at.desc()).all()
        
        if not reconciliations:
            click.echo(click.style(f"No reconciliations found for organization {org_id} in the last {days} days", fg="yellow"))
            return
        
        click.echo(click.style(f"\n🏦 Recent Reconciliations (Last {days} days)", fg="blue", bold=True))
        click.echo("="*80)
        
        for recon in reconciliations:
            status_color = {
                'completed': 'green',
                'in_progress': 'yellow',
                'failed': 'red'
            }.get(recon.status, 'white')
            
            click.echo(f"\n📋 Reconciliation ID: {recon.id}")
            click.echo(f"   Bank Account: {recon.bank_account_id}")
            click.echo(f"   Period: {recon.start_date.strftime('%Y-%m-%d')} to {recon.end_date.strftime('%Y-%m-%d')}")
            click.echo(f"   Status: {click.style(recon.status.upper(), fg=status_color)}")
            
            if recon.status == 'completed':
                match_rate = (
                    recon.matched_transactions / recon.total_transactions * 100
                    if recon.total_transactions > 0 else 0
                )
                click.echo(f"   Matched: {recon.matched_transactions}/{recon.total_transactions} ({match_rate:.1f}%)")
                click.echo(f"   Total Amount: ${recon.total_amount:,.2f}")
                click.echo(f"   Unmatched Amount: ${recon.unmatched_amount:,.2f}")
            
            click.echo(f"   Created: {recon.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if recon.completed_at:
                click.echo(f"   Completed: {recon.completed_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
    finally:
        db.close()


@reconcile.command()
@click.option('--reconciliation-id', required=True, type=int, help='Reconciliation ID')
@handle_cli_errors()
def show_unmatched(reconciliation_id: int) -> None:
    """Show unmatched transactions from a reconciliation."""
    db = SessionLocal()
    try:
        reconciliation = db.query(BankReconciliation).get(reconciliation_id)
        if not reconciliation:
            click.echo(click.style(f"❌ Reconciliation {reconciliation_id} not found", fg="red"))
            return
        
        if not reconciliation.results:
            click.echo(click.style("No reconciliation results available", fg="yellow"))
            return
        
        results = reconciliation.results
        unmatched_bank = results.get('unmatched_bank', [])
        unmatched_statement = results.get('unmatched_statement', [])
        
        if not unmatched_bank and not unmatched_statement:
            click.echo(click.style("✅ All transactions matched!", fg="green"))
            return
        
        # Show unmatched bank transactions
        if unmatched_bank:
            click.echo(click.style(f"\n💳 Unmatched Bank Transactions ({len(unmatched_bank)})", fg="yellow", bold=True))
            click.echo("="*80)
            
            for tx in unmatched_bank[:20]:  # Show first 20
                click.echo(f"\n  ID: {tx['id']}")
                click.echo(f"  Date: {tx['date']}")
                click.echo(f"  Amount: ${tx['total']:,.2f}")
                click.echo(f"  Type: {tx['type']}")
                if tx.get('reference'):
                    click.echo(f"  Reference: {tx['reference']}")
                if tx.get('description'):
                    click.echo(f"  Description: {tx['description']}")
            
            if len(unmatched_bank) > 20:
                click.echo(f"\n  ... and {len(unmatched_bank) - 20} more")
        
        # Show unmatched statement entries
        if unmatched_statement:
            click.echo(click.style(f"\n📄 Unmatched Statement Entries ({len(unmatched_statement)})", fg="yellow", bold=True))
            click.echo("="*80)
            
            for entry in unmatched_statement[:20]:  # Show first 20
                click.echo(f"\n  Date: {entry['date']}")
                click.echo(f"  Amount: ${entry['amount']:,.2f}")
                if entry.get('reference'):
                    click.echo(f"  Reference: {entry['reference']}")
                if entry.get('description'):
                    click.echo(f"  Description: {entry['description']}")
            
            if len(unmatched_statement) > 20:
                click.echo(f"\n  ... and {len(unmatched_statement) - 20} more")
        
        click.echo("\n💡 Tip: Review these transactions manually or adjust matching parameters")
        
    finally:
        db.close()


@reconcile.command()
@click.option('--reconciliation-id', required=True, type=int, help='Reconciliation ID')
@click.option('--output-dir', default='./reconciliation_reports', help='Output directory for report')
@click.option('--format', 'output_format', type=click.Choice(['pdf', 'excel', 'both']), default='both', help='Report format')
@handle_cli_errors()
def generate_report(reconciliation_id: int, output_dir: str, output_format: str) -> None:
    """Generate a reconciliation report."""
    db = SessionLocal()
    try:
        reconciliation = db.query(BankReconciliation).get(reconciliation_id)
        if not reconciliation:
            click.echo(click.style(f"❌ Reconciliation {reconciliation_id} not found", fg="red"))
            return
        
        if reconciliation.status != 'completed':
            click.echo(click.style(f"⚠️  Reconciliation is {reconciliation.status}, report may be incomplete", fg="yellow"))
        
        click.echo(f"📄 Generating reconciliation report...")
        
        report_gen = ReconciliationReportGenerator()
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if output_format in ['pdf', 'both']:
            pdf_path = output_path / f"reconciliation_{reconciliation_id}_{timestamp}.pdf"
            report_gen.generate_pdf_report(
                reconciliation_id=reconciliation_id,
                db=db,
                output_path=str(pdf_path)
            )
            click.echo(f"✅ PDF Report: {pdf_path}")
        
        if output_format in ['excel', 'both']:
            excel_path = output_path / f"reconciliation_{reconciliation_id}_{timestamp}.xlsx"
            report_gen.generate_excel_report(
                reconciliation_id=reconciliation_id,
                db=db,
                output_path=str(excel_path)
            )
            click.echo(f"✅ Excel Report: {excel_path}")
        
        display_success_message("Report generated successfully!")
        
    finally:
        db.close()