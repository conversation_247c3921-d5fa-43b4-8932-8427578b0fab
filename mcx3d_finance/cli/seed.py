"""
CLI commands for seeding and managing test data.
"""
import click
from mcx3d_finance.core.logging_factory import LoggerFactory
import json
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal
import random
from typing import List, Dict, Any

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import (
    Organization,
    Account,
    Contact,
    Transaction,
    Invoice,
    BankTransaction
)
from mcx3d_finance.cli.error_handler import handle_cli_errors, display_success_message

logger = LoggerFactory.get_logger(__name__, domain='cli')


@click.group()
def seed() -> None:
    """Manage test data and database seeding."""
    pass


@seed.command("sample-data")
@click.option("--org-id", required=True, type=int, help="Organization ID to seed data for")
@click.option("--months", default=72, help="Number of months (6 years) of historical data to generate")
@click.option("--clear", is_flag=True, help="Clear existing data before seeding")
@handle_cli_errors()
def seed_sample_data(org_id: int, months: int, clear: bool) -> None:
    """Seed sample financial data for testing and demonstration.
    
    This command generates realistic financial data including:
    - Chart of accounts
    - Contacts (customers and suppliers)
    - Transactions
    - Invoices
    - Bank transactions
    
    Examples:
        mcx3d seed sample-data --org-id 1
        mcx3d seed sample-data --org-id 1 --months 12 --clear
    """
    db = SessionLocal()
    
    try:
        # Verify organization exists
        org = db.query(Organization).filter(Organization.id == org_id).first()
        if not org:
            click.echo(click.style(f"❌ Organization with ID {org_id} not found", fg="red"))
            return
            
        click.echo(click.style(f"🌱 Seeding sample data for: {org.name}", fg="blue", bold=True))
        
        if clear:
            click.echo("🗑️  Clearing existing data...")
            # Clear existing data for this organization
            db.query(Transaction).filter(Transaction.organization_id == org_id).delete()
            db.query(Invoice).filter(Invoice.organization_id == org_id).delete()
            db.query(BankTransaction).filter(BankTransaction.organization_id == org_id).delete()
            db.query(Contact).filter(Contact.organization_id == org_id).delete()
            db.query(Account).filter(Account.organization_id == org_id).delete()
            db.commit()
            click.echo("✅ Existing data cleared")
        
        # Create accounts
        click.echo("\n📊 Creating chart of accounts...")
        accounts = create_sample_accounts(db, org_id)
        click.echo(f"✅ Created {len(accounts)} accounts")
        
        # Create contacts
        click.echo("\n👥 Creating contacts...")
        contacts = create_sample_contacts(db, org_id)
        click.echo(f"✅ Created {len(contacts)} contacts")
        
        # Create transactions
        click.echo("\n💰 Creating transactions...")
        transactions = create_sample_transactions(db, org_id, accounts, months)
        click.echo(f"✅ Created {len(transactions)} transactions")
        
        # Create invoices
        click.echo("\n📄 Creating invoices...")
        invoices = create_sample_invoices(db, org_id, contacts, accounts, months)
        click.echo(f"✅ Created {len(invoices)} invoices")
        
        # Create bank transactions
        click.echo("\n🏦 Creating bank transactions...")
        bank_transactions = create_sample_bank_transactions(db, org_id, accounts, months)
        click.echo(f"✅ Created {len(bank_transactions)} bank transactions")
        
        # Commit all changes
        db.commit()
        
        # Display summary
        success_msg = f"""
Sample data seeded successfully!

Organization: {org.name} (ID: {org_id})
Period: Last {months} months
Accounts: {len(accounts)}
Contacts: {len(contacts)}
Transactions: {len(transactions)}
Invoices: {len(invoices)}
Bank Transactions: {len(bank_transactions)}

You can now:
- Generate financial reports
- Test data synchronization
- Explore the application features
"""
        display_success_message(success_msg)
        
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to seed sample data: {e}")
        click.echo(click.style(f"❌ Failed to seed data: {e}", fg="red"))
    finally:
        db.close()


def create_sample_accounts(db, org_id: int) -> List[Account]:
    """Create a standard chart of accounts."""
    accounts = []
    
    account_templates = [
        # Assets
        {"code": "1000", "name": "Cash and Cash Equivalents", "type": "BANK", "class_type": "ASSET"},
        {"code": "1100", "name": "Accounts Receivable", "type": "CURRENT", "class_type": "ASSET"},
        {"code": "1200", "name": "Inventory", "type": "INVENTORY", "class_type": "ASSET"},
        {"code": "1500", "name": "Property and Equipment", "type": "FIXED", "class_type": "ASSET"},
        
        # Liabilities
        {"code": "2000", "name": "Accounts Payable", "type": "CURRLIAB", "class_type": "LIABILITY"},
        {"code": "2100", "name": "Credit Card", "type": "CREDITCARD", "class_type": "LIABILITY"},
        {"code": "2500", "name": "Long Term Debt", "type": "TERMLIAB", "class_type": "LIABILITY"},
        
        # Equity
        {"code": "3000", "name": "Common Stock", "type": "EQUITY", "class_type": "EQUITY"},
        {"code": "3100", "name": "Retained Earnings", "type": "EQUITY", "class_type": "EQUITY"},
        
        # Revenue
        {"code": "4000", "name": "Sales Revenue", "type": "REVENUE", "class_type": "REVENUE"},
        {"code": "4100", "name": "Service Revenue", "type": "REVENUE", "class_type": "REVENUE"},
        {"code": "4200", "name": "Interest Income", "type": "OTHERINCOME", "class_type": "REVENUE"},
        
        # Expenses
        {"code": "5000", "name": "Cost of Goods Sold", "type": "DIRECTCOSTS", "class_type": "EXPENSE"},
        {"code": "6000", "name": "Salaries and Wages", "type": "EXPENSE", "class_type": "EXPENSE"},
        {"code": "6100", "name": "Rent Expense", "type": "EXPENSE", "class_type": "EXPENSE"},
        {"code": "6200", "name": "Utilities", "type": "EXPENSE", "class_type": "EXPENSE"},
        {"code": "6300", "name": "Marketing", "type": "EXPENSE", "class_type": "EXPENSE"},
        {"code": "6400", "name": "Office Supplies", "type": "EXPENSE", "class_type": "EXPENSE"},
    ]
    
    for template in account_templates:
        account = Account(
            organization_id=org_id,
            xero_account_id=f"sample-{org_id}-{template['code']}",
            code=template['code'],
            name=template['name'],
            type=template['type'],
            class_type=template['class_type'],
            description=f"{template['name']} account",
            status="ACTIVE",
            bank_account_type="BANK" if template['type'] == 'BANK' else None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(account)
        accounts.append(account)
    
    db.flush()  # Get IDs without committing
    return accounts


def create_sample_contacts(db, org_id: int) -> List[Contact]:
    """Create sample contacts."""
    contacts = []
    
    contact_templates = [
        {"name": "Acme Corporation", "is_customer": True, "is_supplier": False},
        {"name": "TechStart Inc", "is_customer": True, "is_supplier": False},
        {"name": "Global Supplies Ltd", "is_customer": False, "is_supplier": True},
        {"name": "Office Direct", "is_customer": False, "is_supplier": True},
        {"name": "Consulting Partners", "is_customer": True, "is_supplier": True},
        {"name": "Digital Agency", "is_customer": True, "is_supplier": False},
        {"name": "Cloud Services Co", "is_customer": False, "is_supplier": True},
        {"name": "Marketing Solutions", "is_customer": False, "is_supplier": True},
    ]
    
    for template in contact_templates:
        contact = Contact(
            organization_id=org_id,
            xero_contact_id=f"sample-{org_id}-{template['name'].lower().replace(' ', '-')}",
            name=template['name'],
            email_address=f"contact@{template['name'].lower().replace(' ', '')}.com",
            is_supplier=template['is_supplier'],
            is_customer=template['is_customer'],
            contact_status="ACTIVE",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(contact)
        contacts.append(contact)
    
    db.flush()
    return contacts


def create_sample_transactions(db, org_id: int, accounts: List[Account], months: int) -> List[Transaction]:
    """Create sample transactions."""
    transactions = []
    
    # Get organization for currency
    org = db.query(Organization).filter(Organization.id == org_id).first()
    if not org:
        return transactions
    
    # Get account types
    bank_account = next((a for a in accounts if a.type == "BANK"), None)
    revenue_accounts = [a for a in accounts if a.class_type == "REVENUE"]
    expense_accounts = [a for a in accounts if a.class_type == "EXPENSE"]
    
    if not (bank_account and revenue_accounts and expense_accounts):
        return transactions
    
    # Generate transactions for each month
    for month in range(months):
        base_date = datetime.utcnow() - timedelta(days=30 * month)
        
        # Revenue transactions
        for _ in range(random.randint(10, 25)):
            amount = Decimal(random.uniform(1000, 25000)).quantize(Decimal('0.01'))
            transaction = Transaction(
                organization_id=org_id,
                xero_transaction_id=f"sample-trans-{org_id}-{len(transactions)}",
                type="RECEIVE",
                date=base_date - timedelta(days=random.randint(0, 29)),
                amount=float(amount),
                description=f"Sales revenue - {base_date.strftime('%B %Y')}",
                reference=f"INV-{random.randint(1000, 9999)}",
                is_reconciled=random.choice([True, False]),
                currency_code=org.base_currency,
                total=float(amount),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.add(transaction)
            transactions.append(transaction)
        
        # Expense transactions
        for _ in range(random.randint(15, 30)):
            amount = Decimal(random.uniform(100, 5000)).quantize(Decimal('0.01'))
            expense_account = random.choice(expense_accounts)
            transaction = Transaction(
                organization_id=org_id,
                xero_transaction_id=f"sample-trans-{org_id}-{len(transactions)}",
                type="SPEND",
                date=base_date - timedelta(days=random.randint(0, 29)),
                amount=float(amount),  # Store as positive
                description=f"{expense_account.name} - {base_date.strftime('%B %Y')}",
                reference=f"EXP-{random.randint(1000, 9999)}",
                is_reconciled=random.choice([True, False]),
                currency_code=org.base_currency,
                total=float(amount),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.add(transaction)
            transactions.append(transaction)
    
    db.flush()
    return transactions


def create_sample_invoices(db, org_id: int, contacts: List[Contact], accounts: List[Account], months: int) -> List[Invoice]:
    """Create sample invoices."""
    invoices = []
    
    customers = [c for c in contacts if c.is_customer]
    revenue_accounts = [a for a in accounts if a.class_type == "REVENUE"]
    
    if not (customers and revenue_accounts):
        return invoices
    
    # Get organization for currency
    org = db.query(Organization).filter(Organization.id == org_id).first()
    currency = org.base_currency if org else "USD"
    
    # Generate invoices for each month
    for month in range(months):
        base_date = datetime.utcnow() - timedelta(days=30 * month)
        
        for _ in range(random.randint(5, 15)):
            invoice_number = f"INV-{random.randint(10000, 99999)}"
            due_date = base_date + timedelta(days=30)
            
            # Calculate amounts
            subtotal = Decimal(random.uniform(1000, 25000)).quantize(Decimal('0.01'))
            tax_rate = Decimal('0.10')  # 10% tax
            tax_amount = (subtotal * tax_rate).quantize(Decimal('0.01'))
            total = subtotal + tax_amount
            
            invoice = Invoice(
                organization_id=org_id,
                xero_invoice_id=f"sample-inv-{org_id}-{invoice_number}",
                invoice_number=invoice_number,
                contact_id=random.choice(customers).id,
                date=base_date,
                due_date=due_date,
                status=random.choice(["DRAFT", "AUTHORISED", "PAID"]),
                line_amount_types="Exclusive",
                sub_total=float(subtotal),
                total=float(total),
                total_tax=float(tax_amount),
                currency_code=currency,
                currency_rate=1.0,
                type="ACCREC",  # Accounts Receivable
                reference=f"Project-{random.randint(100, 999)}",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.add(invoice)
            invoices.append(invoice)
    
    db.flush()
    return invoices


def create_sample_bank_transactions(db, org_id: int, accounts: List[Account], months: int) -> List[BankTransaction]:
    """Create sample bank transactions."""
    bank_transactions = []
    
    bank_accounts = [a for a in accounts if a.type == "BANK"]
    if not bank_accounts:
        return bank_transactions
    
    bank_account = bank_accounts[0]
    
    # Get organization for currency
    org = db.query(Organization).filter(Organization.id == org_id).first()
    currency = org.base_currency if org else "USD"
    
    # Generate bank transactions for each month
    for month in range(months):
        base_date = datetime.utcnow() - timedelta(days=30 * month)
        
        for _ in range(random.randint(20, 50)):
            amount = Decimal(random.uniform(-5000, 10000)).quantize(Decimal('0.01'))
            
            bank_transaction = BankTransaction(
                organization_id=org_id,
                xero_transaction_id=f"sample-bank-{org_id}-{len(bank_transactions)}",
                type="SPEND" if amount < 0 else "RECEIVE",
                reference=f"REF-{random.randint(10000, 99999)}",
                total=float(abs(amount)),
                currency_code=currency,
                date=base_date - timedelta(days=random.randint(0, 29)),
                status="AUTHORISED",
                is_reconciled=random.choice([True, False]),
                bank_account={"account_id": bank_account.id, "code": bank_account.code, "name": bank_account.name},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.add(bank_transaction)
            bank_transactions.append(bank_transaction)
    
    db.flush()
    return bank_transactions


@seed.command("from-json")
@click.option("--org-id", required=True, type=int, help="Organization ID to import data for")
@click.option("--file", required=True, type=click.Path(exists=True), help="JSON file to import")
@click.option("--data-type", required=True, type=click.Choice(['accounts', 'contacts', 'transactions', 'invoices']), 
              help="Type of data to import")
@handle_cli_errors()
def import_from_json(org_id: int, file: str, data_type: str) -> None:
    """Import financial data from JSON file.
    
    Examples:
        mcx3d seed from-json --org-id 1 --file accounts.json --data-type accounts
        mcx3d seed from-json --org-id 1 --file contacts.json --data-type contacts
    """
    db = SessionLocal()
    
    try:
        # Verify organization exists
        org = db.query(Organization).filter(Organization.id == org_id).first()
        if not org:
            click.echo(click.style(f"❌ Organization with ID {org_id} not found", fg="red"))
            return
            
        # Load JSON data
        with open(file, 'r') as f:
            data = json.load(f)
            
        click.echo(click.style(f"📥 Importing {data_type} from {file}", fg="blue"))
        
        if data_type == 'accounts':
            count = import_accounts(db, org_id, data)
        elif data_type == 'contacts':
            count = import_contacts(db, org_id, data)
        elif data_type == 'transactions':
            count = import_transactions(db, org_id, data)
        elif data_type == 'invoices':
            count = import_invoices(db, org_id, data)
            
        db.commit()
        
        success_msg = f"Successfully imported {count} {data_type} for {org.name}"
        display_success_message(success_msg)
        
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to import data: {e}")
        click.echo(click.style(f"❌ Failed to import: {e}", fg="red"))
    finally:
        db.close()


def import_accounts(db, org_id: int, data: List[Dict[str, Any]]) -> int:
    """Import accounts from JSON data."""
    count = 0
    for item in data:
        account = Account(
            organization_id=org_id,
            xero_account_id=item.get('account_id', f"imported-{count}"),
            code=item.get('code', str(1000 + count)),
            name=item['account_name'],
            type=item.get('account_type', 'BANK'),
            description=item.get('description', ''),
            status="ACTIVE",
            bank_account_type="BANK" if item.get('account_type') == 'Bank' else None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(account)
        count += 1
    return count


def import_contacts(db, org_id: int, data: List[Dict[str, Any]]) -> int:
    """Import contacts from JSON data."""
    count = 0
    for item in data:
        contact = Contact(
            organization_id=org_id,
            xero_contact_id=item.get('contact_id', f"imported-{count}"),
            name=item['name'],
            email_address=item.get('email', ''),
            is_supplier=item.get('is_supplier', False),
            is_customer=item.get('is_customer', True),
            contact_status="ACTIVE",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(contact)
        count += 1
    return count


def import_transactions(db, org_id: int, data: List[Dict[str, Any]]) -> int:
    """Import transactions from JSON data."""
    count = 0
    for item in data:
        transaction = Transaction(
            organization_id=org_id,
            xero_transaction_id=item.get('transaction_id', f"imported-{count}"),
            type=item.get('type', 'SPEND' if float(item['amount']) < 0 else 'RECEIVE'),
            date=datetime.fromisoformat(item['date'].replace('Z', '+00:00')) if isinstance(item['date'], str) else item['date'],
            amount=abs(float(item['amount'])),
            description=item.get('description', ''),
            reference=item.get('reference', ''),
            is_reconciled=item.get('is_reconciled', False),
            currency_code=item.get('currency', 'USD'),
            total=abs(float(item['amount'])),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(transaction)
        count += 1
    return count


def import_invoices(db, org_id: int, data: List[Dict[str, Any]]) -> int:
    """Import invoices from JSON data."""
    count = 0
    
    # Get organization for currency
    org = db.query(Organization).filter(Organization.id == org_id).first()
    currency = org.base_currency if org else "USD"
    
    for item in data:
        invoice = Invoice(
            organization_id=org_id,
            xero_invoice_id=item.get('invoice_id', f"imported-{count}"),
            invoice_number=item.get('invoice_number', f"IMP-{count}"),
            date=datetime.fromisoformat(item['date'].replace('Z', '+00:00')) if isinstance(item['date'], str) else item['date'],
            due_date=datetime.fromisoformat(item['due_date'].replace('Z', '+00:00')) if 'due_date' in item and isinstance(item['due_date'], str) else None,
            status=item.get('status', 'DRAFT'),
            sub_total=float(item.get('sub_total', 0)),
            total_tax=float(item.get('tax_amount', 0)),
            total=float(item.get('total', 0)),
            currency_code=currency,
            currency_rate=1.0,
            type=item.get('type', 'ACCREC'),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(invoice)
        count += 1
    return count