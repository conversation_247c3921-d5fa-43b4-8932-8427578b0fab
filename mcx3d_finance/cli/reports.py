"""
Reports CLI module with standardized command structure.

Refactored version using the new CLI utilities for consistent error handling,
performance tracking, and output formatting.
"""

import click
from mcx3d_finance.core.logging_factory import LoggerFactory
import time
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
from datetime import datetime, timedelta

from mcx3d_finance.cli.utils import (
    cli_command, cli_group, CLIFormatter, validate_date,
    validate_positive_float, confirm_action, handle_async_task,
    setup_logging
)
from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.reporting.comprehensive_report import ComprehensiveReportGenerator
from mcx3d_finance.core.financials import (
    income_statement as income_statement_calc,
    balance_sheet as balance_sheet_calc,
    cash_flow as cash_flow_calc,
)
from mcx3d_finance.exceptions import ValidationError
from mcx3d_finance.db.session import get_db, SessionLocal
from mcx3d_finance.db.models import Transaction, Account, Invoice, BankTransaction
from sqlalchemy.orm import Session

logger = LoggerFactory.get_logger(__name__, domain='cli')
formatter = CLIFormatter()


def parse_period(period: str) -> Tuple[Optional[str], Optional[str]]:
    """Parse period string into start and end dates."""
    try:
        if "Q" in period:
            year, quarter_str = period.split("-Q")
            quarter = int(quarter_str)
            if quarter not in [1, 2, 3, 4]:
                raise ValueError(f"Invalid quarter: {quarter}. Must be 1-4.")
                
            start_month = (quarter - 1) * 3 + 1
            end_month = start_month + 2
            start_date = datetime(int(year), start_month, 1)
            
            # Get last day of end month
            if end_month == 12:
                end_date = datetime(int(year) + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(int(year), end_month + 1, 1) - timedelta(days=1)
            
            return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
            
        elif "-" in period and len(period) == 7:  # YYYY-MM format
            year, month = period.split("-")
            start_date = datetime(int(year), int(month), 1)
            
            # Get last day of month
            if int(month) == 12:
                end_date = datetime(int(year) + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(int(year), int(month) + 1, 1) - timedelta(days=1)
            
            return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
            
        elif len(period) == 4:  # YYYY format
            year = int(period)
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            
            return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
            
        else:
            raise ValueError("Invalid period format. Use YYYY-QN, YYYY-MM, or YYYY")
            
    except Exception as e:
        raise ValidationError(f"Invalid period format: {e}", field_name="period")


def validate_organization_id(ctx, param, value):
    """Click callback to validate organization ID."""
    if value is None:
        return value
    
    try:
        org_id = int(value)
        if org_id <= 0:
            raise click.BadParameter('Organization ID must be positive')
        return org_id
    except ValueError:
        raise click.BadParameter('Organization ID must be a valid integer')


def ensure_output_directory(output_path: str) -> str:
    """Ensure output directory exists and is writable."""
    try:
        output_file = Path(output_path)
        output_dir = output_file.parent
        
        # Create directory if it doesn't exist
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Test write permissions
        test_file = output_dir / ".test_write_permissions"
        try:
            test_file.touch()
            test_file.unlink()
        except PermissionError:
            raise PermissionError(f"No write permission for directory: {output_dir}")
            
        return str(output_file.resolve())
        
    except Exception as e:
        raise ValidationError(
            f"Output path validation failed: {e}",
            field_name="output_path"
        )


@cli_group(name="generate", help="Generate financial reports with enhanced error handling")
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.option('--format', 
              type=click.Choice(['human', 'json', 'table']), 
              default='human',
              help='Output format for results')
@click.option('--log-file', help='Log output to file')
@click.pass_context
def generate(ctx, debug, format, log_file):
    """Generate financial reports with enhanced error handling and fallback options."""
    # Setup context
    ctx.ensure_object(dict)
    ctx.obj['debug'] = debug
    ctx.obj['format'] = format
    
    # Setup logging
    setup_logging(debug=debug, log_file=log_file)


@generate.command("income-statement")
@click.option("--organization-id", 
              required=True, 
              callback=validate_organization_id,
              help="Organization ID (positive integer)")
@click.option("--period", 
              required=True, 
              help='Financial period: YYYY-QN (e.g., "2023-Q4"), YYYY-MM, or YYYY')
@click.option("--format",
              type=click.Choice(["pdf", "excel", "html", "csv", "json"]),
              default="pdf",
              help="Output format (PDF recommended)")
@click.option("--output", 
              help="Output file path (auto-generated if not provided)")
@click.option("--complexity", 
              type=click.Choice(["full", "standard", "minimal", "essential"]),
              help="Report complexity level (auto-detected if not specified)")
@click.option("--async", 
              "async_mode", 
              is_flag=True, 
              help="Generate report asynchronously")
@click.pass_context
def income_statement_command(ctx, organization_id, period, format, output, complexity, async_mode):
    """
    Generate income statement with enhanced error handling.
    
    Examples:
        mcx3d generate income-statement --organization-id 123 --period 2023-Q4
        mcx3d generate income-statement --organization-id 123 --period 2023-12 --format excel
        mcx3d generate income-statement --organization-id 123 --period 2023 --output /path/to/report.pdf
    """
    # Get performance tracker from context
    tracker = ctx.obj.get('_performance_tracker')
    
    formatter.info(f"Generating income statement for organization {organization_id}")
    
    try:
        # Parse period
        start_date, end_date = parse_period(period)
        
        # Generate output path if not provided
        if not output:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output = f"income_statement_org{organization_id}_{period}_{timestamp}.{format}"
        
        # Ensure output directory exists
        output = ensure_output_directory(output)
        
        if async_mode:
            formatter.progress("Starting async income statement generation...")
            
            from mcx3d_finance.tasks.report_tasks import generate_income_statement_async
            
            task = generate_income_statement_async.delay(
                organization_id=organization_id,
                period_start=start_date,
                period_end=end_date,
                output_format=format,
                output_path=output,
                complexity_level=complexity
            )
            
            # Handle async task
            result = handle_async_task(task.id)
            
            if result:
                formatter.success(f"Income statement generated successfully: {output}")
                return {"status": "success", "output": output, "task_id": task.id}
            else:
                raise Exception("Async task failed")
        
        else:
            # Synchronous generation
            formatter.progress("Generating income statement...")
            
            # Create generator
            generator = ReportGenerator()
            
            # Check if data exists
            db = SessionLocal()
            try:
                transactions = check_data_availability(
                    db, organization_id, start_date, end_date
                )
                
                if not transactions:
                    formatter.warning("No financial data found for the specified period")
                    if not confirm_action("Generate empty report template?"):
                        return {"status": "cancelled"}
            finally:
                db.close()
            
            # Generate report based on format
            formatter.progress(f"Creating {format.upper()} report...")
            
            if format == "pdf":
                result = generate_income_statement_pdf(
                    generator, organization_id, start_date, end_date, output
                )
            elif format == "excel":
                result = generate_income_statement_excel(
                    generator, organization_id, start_date, end_date, output
                )
            elif format == "json":
                result = generate_income_statement_json(
                    generator, organization_id, start_date, end_date, output
                )
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            formatter.success(f"Income statement generated successfully: {output}")
            
            # Add metrics
            if tracker:
                tracker.add_metric("transactions_processed", result.get("transaction_count", 0))
                tracker.add_metric("output_size", Path(output).stat().st_size)
            
            return {
                "status": "success",
                "output": output,
                "format": format,
                "period": {"start": start_date, "end": end_date},
                "organization_id": organization_id
            }
            
    except Exception as e:
        logger.error(f"Failed to generate income statement: {e}")
        raise


@generate.command("balance-sheet")
@click.option("--organization-id", 
              required=True,
              callback=validate_organization_id,
              help="Organization ID")
@click.option("--date", 
              required=True,
              callback=validate_date,
              help="Balance sheet date (YYYY-MM-DD)")
@click.option("--format",
              type=click.Choice(["pdf", "excel", "html", "csv", "json"]),
              default="pdf",
              help="Output format")
@click.option("--output", 
              help="Output file path")
@click.option("--async", 
              "async_mode", 
              is_flag=True, 
              help="Generate report asynchronously")
@click.pass_context
def balance_sheet_command(ctx, organization_id, date, format, output, async_mode):
    """
    Generate balance sheet as of specified date.
    
    Examples:
        mcx3d generate balance-sheet --organization-id 123 --date 2023-12-31
        mcx3d generate balance-sheet --organization-id 123 --date 2023-12-31 --format excel
    """
    formatter.info(f"Generating balance sheet for organization {organization_id}")
    
    # Generate output path if not provided
    if not output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output = f"balance_sheet_org{organization_id}_{date}_{timestamp}.{format}"
    
    # Ensure output directory exists
    output = ensure_output_directory(output)
    
    if async_mode:
        formatter.progress("Starting async balance sheet generation...")
        
        from mcx3d_finance.tasks.report_tasks import generate_balance_sheet_async
        
        task = generate_balance_sheet_async.delay(
            organization_id=organization_id,
            as_of_date=date,
            output_format=format,
            output_path=output
        )
        
        result = handle_async_task(task.id)
        
        if result:
            formatter.success(f"Balance sheet generated successfully: {output}")
            return {"status": "success", "output": output, "task_id": task.id}
    else:
        # Synchronous generation
        formatter.progress("Generating balance sheet...")
        
        generator = ReportGenerator()
        
        # Generate balance sheet data using optimized generator
        from mcx3d_finance.core.financials.balance_sheet import UKBalanceSheetGenerator

        balance_gen = UKBalanceSheetGenerator(organization_id)
        balance_data = balance_gen.generate_balance_sheet(
            datetime.strptime(date, "%Y-%m-%d")
        )
        
        # Generate based on format
        if format == "pdf":
            generator.generate_balance_sheet_pdf(balance_data, output)
        elif format == "excel":
            generator.generate_balance_sheet_excel(balance_data, output)
        else:
            raise ValueError(f"Format {format} not yet implemented")
        
        formatter.success(f"Balance sheet generated successfully: {output}")
        
        return {
            "status": "success",
            "output": output,
            "format": format,
            "as_of_date": date,
            "organization_id": organization_id
        }


@generate.command("cash-flow")
@click.option("--organization-id", 
              required=True,
              callback=validate_organization_id,
              help="Organization ID")
@click.option("--period", 
              required=True, 
              help='Financial period')
@click.option("--format",
              type=click.Choice(["pdf", "excel", "html", "csv", "json"]),
              default="pdf",
              help="Output format")
@click.option("--output", 
              help="Output file path")
@click.option("--async", 
              "async_mode", 
              is_flag=True, 
              help="Generate report asynchronously")
@click.pass_context
def cash_flow_command(ctx, organization_id, period, format, output, async_mode):
    """
    Generate cash flow statement for specified period.
    
    Examples:
        mcx3d generate cash-flow --organization-id 123 --period 2023-Q4
        mcx3d generate cash-flow --organization-id 123 --period 2023
    """
    formatter.info(f"Generating cash flow statement for organization {organization_id}")
    
    # Parse period
    start_date, end_date = parse_period(period)
    
    # Generate output path if not provided
    if not output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output = f"cash_flow_org{organization_id}_{period}_{timestamp}.{format}"
    
    # Ensure output directory exists
    output = ensure_output_directory(output)
    
    if async_mode:
        formatter.progress("Starting async cash flow generation...")
        
        from mcx3d_finance.tasks.report_tasks import generate_cash_flow_async
        
        task = generate_cash_flow_async.delay(
            organization_id=organization_id,
            period_start=start_date,
            period_end=end_date,
            output_format=format,
            output_path=output
        )
        
        result = handle_async_task(task.id)
        
        if result:
            formatter.success(f"Cash flow statement generated successfully: {output}")
            return {"status": "success", "output": output, "task_id": task.id}
    else:
        # Synchronous generation
        formatter.progress("Generating cash flow statement...")
        
        generator = ReportGenerator()
        
        # Generate cash flow data
        db = SessionLocal()
        try:
            cash_flow_gen = cash_flow_calc.CashFlowGenerator(db)
            cash_flow_data = cash_flow_gen.generate_cash_flow_statement(
                organization_id, 
                datetime.strptime(start_date, "%Y-%m-%d"),
                datetime.strptime(end_date, "%Y-%m-%d")
            )
        finally:
            db.close()
        
        # Generate report based on format
        if format == "pdf":
            generator.generate_cash_flow_pdf(cash_flow_data, output)
        elif format == "excel":
            generator.generate_cash_flow_excel(cash_flow_data, output)
        else:
            raise ValueError(f"Format {format} not yet implemented")
        
        formatter.success(f"Cash flow statement generated successfully: {output}")
        
        return {
            "status": "success",
            "output": output,
            "format": format,
            "period": {"start": start_date, "end": end_date},
            "organization_id": organization_id
        }


# Helper functions
def check_data_availability(db: Session, organization_id: int, 
                          start_date: str, end_date: str) -> bool:
    """Check if transaction data exists for the period."""
    # Check invoices
    invoice_count = db.query(Invoice).filter(
        Invoice.organization_id == organization_id,
        Invoice.date >= start_date,
        Invoice.date <= end_date
    ).count()
    
    # Check bank transactions
    bank_count = db.query(BankTransaction).filter(
        BankTransaction.organization_id == organization_id,
        BankTransaction.date >= start_date,
        BankTransaction.date <= end_date
    ).count()
    
    count = invoice_count + bank_count
    
    return count > 0


def generate_income_statement_pdf(generator, org_id, start_date, end_date, output):
    """Generate income statement in PDF format."""
    # Get income statement data using optimized generator
    from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator

    income_gen = UKProfitAndLossGenerator(org_id)
    income_data = income_gen.generate_profit_and_loss(
        datetime.strptime(start_date, "%Y-%m-%d"),
        datetime.strptime(end_date, "%Y-%m-%d")
    )
    
    # Generate PDF
    generator.generate_income_statement_pdf(income_data, output)
    
    return {"transaction_count": len(income_data.get("revenues", {}))}  # Changed from transactions


def generate_income_statement_excel(generator, org_id, start_date, end_date, output):
    """Generate income statement in Excel format."""
    # Get income statement data using optimized generator
    from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator

    income_gen = UKProfitAndLossGenerator(org_id)
    income_data = income_gen.generate_profit_and_loss(
        datetime.strptime(start_date, "%Y-%m-%d"),
        datetime.strptime(end_date, "%Y-%m-%d")
    )
    
    # Generate Excel
    generator.generate_income_statement_excel(income_data, output)
    
    return {"transaction_count": len(income_data.get("revenues", {}))}  # Changed from transactions


def generate_income_statement_json(generator, org_id, start_date, end_date, output):
    """Generate income statement in JSON format."""
    import json
    
    # Get income statement data using optimized generator
    from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator

    income_gen = UKProfitAndLossGenerator(org_id)
    income_data = income_gen.generate_profit_and_loss(
        datetime.strptime(start_date, "%Y-%m-%d"),
        datetime.strptime(end_date, "%Y-%m-%d")
    )
    
    # Write JSON
    with open(output, 'w') as f:
        json.dump(income_data, f, indent=2, default=str)
    
    return {"transaction_count": len(income_data.get("revenues", {}))}  # Changed from transactions


# Apply decorators to commands
income_statement_command = cli_command(
    name="income-statement",
    help="Generate income statement with enhanced error handling"
)(income_statement_command)

balance_sheet_command = cli_command(
    name="balance-sheet", 
    help="Generate balance sheet as of specified date"
)(balance_sheet_command)

cash_flow_command = cli_command(
    name="cash-flow",
    help="Generate cash flow statement for specified period"
)(cash_flow_command)


@generate.command("generate_comprehensive_report")
@click.option("--organization-id",
              required=True,
              callback=validate_organization_id,
              help="Organization ID")
@click.option("--year",
              required=True,
              type=int,
              help="The year for the report (e.g., 2024)")
def generate_comprehensive_report(organization_id, year):
    """
    Generate a comprehensive financial report that meets stock market standards.
    """
    formatter.info(f"Generating comprehensive report for organization {organization_id} for the year {year}")
    try:
        generator = ComprehensiveReportGenerator(organization_id, year)
        report = generator.generate()
        generator.save_reports(report)
        formatter.success("Comprehensive report generated successfully.")
        return {"status": "success", "output_dir": f"reports/{year}"}
    except Exception as e:
        logger.error(f"Failed to generate comprehensive report: {e}", exc_info=True)
        formatter.error(f"An error occurred: {e}")
        raise


if __name__ == "__main__":
    generate()