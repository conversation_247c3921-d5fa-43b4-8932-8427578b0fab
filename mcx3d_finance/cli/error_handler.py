"""
Enhanced CLI error handling with user-friendly messages and recovery suggestions.

Provides comprehensive error handling for the MCX3D CLI with contextual help,
actionable recovery suggestions, and graceful error reporting.
"""

import click
from mcx3d_finance.core.logging_factory import LoggerFactory
import traceback
import sys
from typing import Dict, Any, Optional, List, Callable
from functools import wraps
from pathlib import Path
import psutil

from mcx3d_finance.exceptions import (
    MCX3DException,
    ReportGenerationError,
    ReportOutputError,
    ReportMemoryError,
    ValidationError,
    BusinessRuleValidationError,
    SchemaValidationError,
    FinancialDataError,
    MCX3DResourceError,
    MCX3DTimeoutError,
    XeroIntegrationError,
    ChartGenerationError
)

logger = LoggerFactory.get_logger(__name__, domain='cli')


class CLIErrorHandler:
    """
    Enhanced CLI error handler with user-friendly messaging and recovery suggestions.
    
    Provides contextual error messages, actionable suggestions, and graceful
    degradation for CLI operations.
    """
    
    def __init__(self, debug_mode: bool = False):
        """
        Initialize the CLI error handler.
        
        Args:
            debug_mode: Whether to show detailed technical information
        """
        self.debug_mode = debug_mode
        self.error_suggestions = self._build_error_suggestions()
    
    def handle_exception(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        command_name: Optional[str] = None
    ) -> int:
        """
        Handle an exception with user-friendly messaging.
        
        Args:
            error: Exception to handle
            context: Additional context information
            command_name: Name of the command that failed
            
        Returns:
            Exit code (0 for success, non-zero for error)
        """
        try:
            # Get error details
            error_info = self._analyze_error(error, context, command_name)
            
            # Display user-friendly error message
            self._display_error_message(error_info)
            
            # Show recovery suggestions
            self._display_recovery_suggestions(error_info)
            
            # Show additional help if needed
            if error_info.get('show_help'):
                self._display_contextual_help(error_info)
            
            # Log technical details for debugging
            self._log_technical_details(error, error_info, context)
            
            # Return appropriate exit code
            return error_info.get('exit_code', 1)
            
        except Exception as handler_error:
            # Fallback error handling if our handler fails
            click.echo(f"❌ Critical error in error handler: {handler_error}", err=True)
            click.echo(f"❌ Original error: {error}", err=True)
            if self.debug_mode:
                traceback.print_exc()
            return 2
    
    def _analyze_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]],
        command_name: Optional[str]
    ) -> Dict[str, Any]:
        """Analyze error and prepare user-friendly information."""
        error_info = {
            'error_type': type(error).__name__,
            'command_name': command_name or 'unknown',
            'context': context or {},
            'exit_code': 1,
            'show_help': False,
            'category': 'general',
            'severity': 'error',
            'user_message': str(error),
            'technical_message': str(error),
            'suggestions': [],
            'next_steps': [],
            'resources_needed': [],
            'common_causes': []
        }
        
        # Analyze specific error types
        if isinstance(error, MCX3DException):
            self._analyze_mcx3d_exception(error, error_info)
        elif isinstance(error, click.ClickException):
            self._analyze_click_exception(error, error_info)
        elif isinstance(error, FileNotFoundError):
            self._analyze_file_error(error, error_info)
        elif isinstance(error, PermissionError):
            self._analyze_permission_error(error, error_info)
        elif isinstance(error, MemoryError):
            self._analyze_memory_error(error, error_info)
        elif isinstance(error, ConnectionError):
            self._analyze_connection_error(error, error_info)
        elif isinstance(error, KeyboardInterrupt):
            self._analyze_keyboard_interrupt(error, error_info)
        else:
            self._analyze_generic_error(error, error_info)
        
        return error_info
    
    def _analyze_mcx3d_exception(self, error: MCX3DException, error_info: Dict[str, Any]):
        """Analyze MCX3D-specific exceptions."""
        error_info.update({
            'user_message': error.user_message or error.message,
            'technical_message': error.message,
            'severity': error.severity,
            'error_code': getattr(error, 'error_code', None),
            'context': {**error_info['context'], **error.context}
        })
        
        if isinstance(error, ReportGenerationError):
            self._analyze_report_generation_error(error, error_info)
        elif isinstance(error, ReportOutputError):
            self._analyze_report_output_error(error, error_info)
        elif isinstance(error, ValidationError):
            self._analyze_validation_error(error, error_info)
        elif isinstance(error, MCX3DResourceError):
            self._analyze_resource_error(error, error_info)
        elif isinstance(error, XeroIntegrationError):
            self._analyze_xero_error(error, error_info)
    
    def _analyze_report_generation_error(self, error: ReportGenerationError, error_info: Dict[str, Any]):
        """Analyze report generation errors."""
        error_info.update({
            'category': 'report_generation',
            'suggestions': [
                "Check if all required data fields are present",
                "Verify data format matches expected schema",
                "Try generating with a simpler format (CSV/JSON)",
                "Check system resources (memory, disk space)"
            ],
            'next_steps': [
                "Review input data for completeness",
                "Try with --debug flag for detailed error information",
                "Consider reducing data complexity or size"
            ],
            'common_causes': [
                "Missing required data fields",
                "Invalid data formats",
                "Insufficient system resources",
                "Corrupted input data"
            ]
        })
        
        report_type = getattr(error, 'report_type', None)
        output_format = getattr(error, 'output_format', None)
        
        if report_type and output_format:
            error_info['user_message'] = f"Failed to generate {report_type} report in {output_format} format"
    
    def _analyze_report_output_error(self, error: ReportOutputError, error_info: Dict[str, Any]):
        """Analyze report output errors."""
        error_info.update({
            'category': 'file_output',
            'suggestions': [
                "Check if output directory exists and is writable",
                "Verify sufficient disk space is available",
                "Try writing to a different location",
                "Check file permissions"
            ],
            'next_steps': [
                "Create output directory if it doesn't exist",
                "Free up disk space",
                "Change output path to writable location"
            ],
            'resources_needed': ['disk_space', 'write_permissions']
        })
        
        output_path = getattr(error, 'output_path', None)
        if output_path:
            error_info['context']['output_path'] = output_path
    
    def _analyze_validation_error(self, error: ValidationError, error_info: Dict[str, Any]):
        """Analyze validation errors."""
        error_info.update({
            'category': 'data_validation',
            'suggestions': [
                "Review input data for required fields",
                "Check data types and formats",
                "Validate numerical ranges and constraints",
                "Ensure date formats are correct"
            ],
            'next_steps': [
                "Fix data quality issues",
                "Use validation tools to check data",
                "Review data preparation process"
            ],
            'show_help': True
        })
        
        if isinstance(error, SchemaValidationError):
            error_info['suggestions'].extend([
                "Check JSON schema compliance",
                "Verify all required fields are present"
            ])
        elif isinstance(error, BusinessRuleValidationError):
            error_info['suggestions'].extend([
                "Review business rules and constraints",
                "Check for data consistency issues"
            ])
    
    def _analyze_resource_error(self, error: MCX3DResourceError, error_info: Dict[str, Any]):
        """Analyze resource constraint errors."""
        error_info.update({
            'category': 'resource_constraints',
            'severity': 'warning',
            'suggestions': [
                "Close unnecessary applications to free memory",
                "Clean up temporary files to free disk space",
                "Try processing smaller data sets",
                "Use simplified report formats"
            ],
            'next_steps': [
                "Monitor system resources",
                "Consider upgrading hardware",
                "Implement data sampling for large datasets"
            ],
            'resources_needed': ['memory', 'disk_space', 'cpu']
        })
        
        resource_type = getattr(error, 'resource_type', None)
        if resource_type:
            error_info['context']['resource_type'] = resource_type
    
    def _analyze_xero_error(self, error: XeroIntegrationError, error_info: Dict[str, Any]):
        """Analyze Xero integration errors."""
        error_info.update({
            'category': 'external_integration',
            'suggestions': [
                "Check internet connection",
                "Verify Xero API credentials",
                "Check API rate limits",
                "Try again after a few minutes"
            ],
            'next_steps': [
                "Test Xero connection",
                "Review API configuration",
                "Check Xero service status"
            ]
        })
    
    def _analyze_click_exception(self, error: click.ClickException, error_info: Dict[str, Any]):
        """Analyze Click CLI framework exceptions."""
        error_info.update({
            'category': 'cli_usage',
            'show_help': True,
            'suggestions': [
                "Check command syntax and parameters",
                "Use --help flag for usage information",
                "Verify required parameters are provided"
            ],
            'next_steps': [
                f"Run '{error_info['command_name']} --help' for usage info",
                "Check parameter values and types"
            ]
        })
        
        if "Missing option" in str(error):
            error_info['suggestions'].insert(0, "Provide all required command options")
        elif "Invalid value" in str(error):
            error_info['suggestions'].insert(0, "Check parameter values are in correct format")
    
    def _analyze_file_error(self, error: FileNotFoundError, error_info: Dict[str, Any]):
        """Analyze file not found errors."""
        error_info.update({
            'category': 'file_access',
            'user_message': f"File not found: {error.filename if hasattr(error, 'filename') else 'Unknown file'}",
            'suggestions': [
                "Check if file path is correct",
                "Verify file exists in specified location",
                "Check file permissions",
                "Use absolute path instead of relative path"
            ],
            'next_steps': [
                "Verify file location",
                "Check spelling of file path",
                "Ensure file hasn't been moved or deleted"
            ]
        })
    
    def _analyze_permission_error(self, error: PermissionError, error_info: Dict[str, Any]):
        """Analyze permission errors."""
        error_info.update({
            'category': 'file_permissions',
            'user_message': "Permission denied - insufficient access rights",
            'suggestions': [
                "Run with administrator/sudo privileges",
                "Check file and directory permissions",
                "Change output location to writable directory",
                "Ensure you own the target files/directories"
            ],
            'next_steps': [
                "Use 'sudo' on Linux/Mac or run as Administrator on Windows",
                "Change file permissions using chmod/chown",
                "Select a different output location"
            ],
            'resources_needed': ['file_permissions']
        })
    
    def _analyze_memory_error(self, error: MemoryError, error_info: Dict[str, Any]):
        """Analyze memory errors."""
        error_info.update({
            'category': 'system_resources',
            'user_message': "Out of memory - system resources exhausted",
            'suggestions': [
                "Close other applications to free memory",
                "Process smaller data sets",
                "Use data sampling or filtering",
                "Restart the application"
            ],
            'next_steps': [
                "Reduce dataset size",
                "Implement data pagination",
                "Consider adding more RAM"
            ],
            'resources_needed': ['memory']
        })
    
    def _analyze_connection_error(self, error: ConnectionError, error_info: Dict[str, Any]):
        """Analyze connection errors."""
        error_info.update({
            'category': 'network_connectivity',
            'user_message': "Network connection failed",
            'suggestions': [
                "Check internet connection",
                "Verify firewall settings",
                "Try again after a few minutes",
                "Check if service is available"
            ],
            'next_steps': [
                "Test network connectivity",
                "Contact network administrator",
                "Check service status page"
            ]
        })
    
    def _analyze_keyboard_interrupt(self, error: KeyboardInterrupt, error_info: Dict[str, Any]):
        """Analyze keyboard interrupt (Ctrl+C)."""
        error_info.update({
            'category': 'user_interruption',
            'severity': 'info',
            'exit_code': 130,  # Standard exit code for SIGINT
            'user_message': "Operation cancelled by user",
            'suggestions': [
                "Operation was cancelled by user (Ctrl+C)",
                "Some files may be partially created",
                "Clean up temporary files if needed"
            ],
            'next_steps': [
                "Check for temporary files to clean up",
                "Re-run command to complete operation"
            ]
        })
    
    def _analyze_generic_error(self, error: Exception, error_info: Dict[str, Any]):
        """Analyze generic errors."""
        error_info.update({
            'category': 'unexpected_error',
            'user_message': f"Unexpected error: {type(error).__name__}",
            'suggestions': [
                "Try running with --debug flag for more information",
                "Check system resources and requirements",
                "Report this issue if problem persists"
            ],
            'next_steps': [
                "Gather debug information",
                "Check system logs",
                "Consider reporting the bug"
            ]
        })
    
    def _display_error_message(self, error_info: Dict[str, Any]):
        """Display user-friendly error message."""
        severity_icons = {
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        
        icon = severity_icons.get(error_info['severity'], '❌')
        command = error_info['command_name']
        message = error_info['user_message']
        
        click.echo(f"\n{icon} Error in '{command}' command:", err=True)
        click.echo(f"   {message}", err=True)
        
        # Show error code if available
        if error_info.get('error_code'):
            click.echo(f"   Error code: {error_info['error_code']}", err=True)
    
    def _display_recovery_suggestions(self, error_info: Dict[str, Any]):
        """Display actionable recovery suggestions."""
        suggestions = error_info.get('suggestions', [])
        next_steps = error_info.get('next_steps', [])
        
        if suggestions:
            click.echo("\n💡 Possible solutions:", err=True)
            for i, suggestion in enumerate(suggestions, 1):
                click.echo(f"   {i}. {suggestion}", err=True)
        
        if next_steps:
            click.echo("\n🔧 Next steps:", err=True)
            for i, step in enumerate(next_steps, 1):
                click.echo(f"   {i}. {step}", err=True)
        
        # Show resource requirements if any
        resources_needed = error_info.get('resources_needed', [])
        if resources_needed:
            self._display_resource_info(resources_needed)
    
    def _display_resource_info(self, resources_needed: List[str]):
        """Display current resource information."""
        click.echo("\n📊 System resources:", err=True)
        
        try:
            # Memory info
            if 'memory' in resources_needed:
                memory = psutil.virtual_memory()
                memory_pct = memory.percent
                memory_available = memory.available / (1024**3)  # GB
                
                status = "❌" if memory_pct > 90 else "⚠️" if memory_pct > 80 else "✅"
                click.echo(f"   {status} Memory: {memory_pct:.1f}% used, {memory_available:.1f}GB available", err=True)
            
            # Disk info
            if 'disk_space' in resources_needed:
                disk = psutil.disk_usage('/')
                disk_pct = (disk.used / disk.total) * 100
                disk_free = disk.free / (1024**3)  # GB
                
                status = "❌" if disk_pct > 95 else "⚠️" if disk_pct > 85 else "✅"
                click.echo(f"   {status} Disk: {disk_pct:.1f}% used, {disk_free:.1f}GB free", err=True)
            
            # CPU info (if relevant)
            if 'cpu' in resources_needed:
                cpu_pct = psutil.cpu_percent(interval=1)
                status = "❌" if cpu_pct > 90 else "⚠️" if cpu_pct > 80 else "✅"
                click.echo(f"   {status} CPU: {cpu_pct:.1f}% usage", err=True)
                
        except Exception as e:
            click.echo(f"   ⚠️ Could not retrieve resource info: {e}", err=True)
    
    def _display_contextual_help(self, error_info: Dict[str, Any]):
        """Display contextual help information."""
        category = error_info.get('category', 'general')
        command_name = error_info['command_name']
        
        click.echo("\n📖 Additional help:", err=True)
        
        help_messages = {
            'cli_usage': f"   • Use '{command_name} --help' for command usage",
            'data_validation': "   • Check data format requirements in documentation",
            'file_access': "   • Verify file paths and permissions",
            'report_generation': "   • Try simpler formats like CSV or JSON first",
            'external_integration': "   • Check service status and network connectivity",
            'resource_constraints': "   • Monitor system resources during operation"
        }
        
        help_msg = help_messages.get(category, f"• Use '{command_name} --help' for more information")
        click.echo(help_msg, err=True)
        
        # General help
        click.echo("   • Use --debug flag for detailed technical information", err=True)
        click.echo("   • Check logs for additional details", err=True)
    
    def _log_technical_details(
        self,
        error: Exception,
        error_info: Dict[str, Any],
        context: Optional[Dict[str, Any]]
    ):
        """Log technical details for debugging."""
        logger.error(f"CLI Error in {error_info['command_name']}: {error_info['technical_message']}")
        
        if context:
            logger.error(f"Context: {context}")
        
        if self.debug_mode:
            logger.error(f"Stack trace:", exc_info=True)
    
    def _build_error_suggestions(self) -> Dict[str, List[str]]:
        """Build common error suggestions database."""
        return {
            'file_not_found': [
                "Check file path spelling",
                "Use absolute paths instead of relative",
                "Verify file hasn't been moved or deleted"
            ],
            'permission_denied': [
                "Run with administrator privileges",
                "Check file permissions",
                "Change to writable output location"
            ],
            'network_error': [
                "Check internet connection",
                "Verify firewall settings",
                "Try again after a few minutes"
            ],
            'memory_error': [
                "Close other applications",
                "Process smaller data sets",
                "Restart the application"
            ]
        }


def handle_cli_errors(debug_mode: bool = False):
    """
    Decorator for handling CLI errors with enhanced messaging.
    
    Args:
        debug_mode: Whether to show detailed technical information
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            handler = CLIErrorHandler(debug_mode=debug_mode)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Extract command name from click context if available
                command_name = None
                if hasattr(click, 'get_current_context'):
                    try:
                        ctx = click.get_current_context(silent=True)
                        if ctx:
                            command_name = ctx.info_name
                    except:
                        pass
                
                # Handle the error
                exit_code = handler.handle_exception(
                    error=e,
                    context={'args': args, 'kwargs': kwargs},
                    command_name=command_name
                )
                
                sys.exit(exit_code)
        
        return wrapper
    return decorator


def display_success_message(message: str, details: Optional[Dict[str, Any]] = None):
    """
    Display a success message with optional details.
    
    Args:
        message: Success message to display
        details: Optional details to include
    """
    click.echo(f"\n✅ {message}")
    
    if details:
        for key, value in details.items():
            if isinstance(value, (int, float)):
                if key.endswith('_mb'):
                    click.echo(f"   📊 {key.replace('_', ' ').title()}: {value:.2f} MB")
                elif key.endswith('_seconds'):
                    click.echo(f"   ⏱️ {key.replace('_', ' ').title()}: {value:.2f}s")
                else:
                    click.echo(f"   📈 {key.replace('_', ' ').title()}: {value}")
            else:
                click.echo(f"   ℹ️ {key.replace('_', ' ').title()}: {value}")


def display_progress_info(message: str, step: Optional[int] = None, total: Optional[int] = None):
    """
    Display progress information.
    
    Args:
        message: Progress message
        step: Current step number
        total: Total number of steps
    """
    if step and total:
        click.echo(f"🔄 [{step}/{total}] {message}")
    else:
        click.echo(f"🔄 {message}")


def display_warning(message: str, suggestion: Optional[str] = None):
    """
    Display a warning message with optional suggestion.
    
    Args:
        message: Warning message
        suggestion: Optional suggestion
    """
    click.echo(f"⚠️ Warning: {message}", err=True)
    if suggestion:
        click.echo(f"   💡 Suggestion: {suggestion}", err=True)