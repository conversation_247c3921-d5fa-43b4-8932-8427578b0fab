"""
Analytics CLI commands for SaaS KPIs and business metrics.
"""

import click
import json
from mcx3d_finance.core.logging_factory import LoggerFactory
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator
from mcx3d_finance.db.session import SessionLocal

logger = LoggerFactory.get_logger(__name__, domain='cli')


@click.group()
def analytics() -> None:
    """Run comprehensive SaaS and business analytics."""
    pass


@analytics.command("saas-metrics")
@click.option("--organization-id", "-o", required=True, type=int, help="Organization ID for Xero data")
@click.option("--period-months", type=int, default=12, help="Analysis period in months (default: 12)")
@click.option("--output", "-f", type=click.Choice(["json", "summary", "detailed"]), default="summary", 
              help="Output format (default: summary)")
@click.option("--include-cohorts", is_flag=True, help="Include detailed cohort analysis")
@click.option("--include-benchmarks", is_flag=True, help="Include industry benchmark comparisons")
@click.option("--async", "async_mode", is_flag=True, help="Run analysis asynchronously")
def saas_metrics_command(organization_id: int, period_months: int, output: str, 
                        include_cohorts: bool, include_benchmarks: bool, async_mode: bool):
    """Calculate comprehensive SaaS KPIs and metrics."""
    try:
        if async_mode:
            # Run asynchronously using Celery
            click.echo(f"🚀 Starting async SaaS metrics analysis for organization {organization_id}...")
            
            from mcx3d_finance.tasks.valuation_tasks import calculate_financial_analytics_async
            
            # Calculate period
            period_end = datetime.now()
            period_start = period_end - timedelta(days=period_months * 30)
            
            # Determine analysis types based on options
            analysis_types = ["profitability", "liquidity", "efficiency", "leverage", "trends", "cashflow"]
            
            # Dispatch async task
            task = calculate_financial_analytics_async.delay(
                organization_id=organization_id,
                analysis_types=analysis_types,
                start_date=period_start.isoformat(),
                end_date=period_end.isoformat(),
                peer_comparison=include_benchmarks
            )
            
            click.echo(f"\n✅ Analytics task queued with ID: {task.id}")
            click.echo(f"Use 'mcx3d-finance analytics status {task.id}' to check progress")
            click.echo("\nThe analysis will run in the background. You can continue using the CLI.")
            
            return
            
        # Run synchronously (existing code)
        click.echo(f"🔍 Analyzing SaaS metrics for organization {organization_id}...")
        
        # Initialize SaaS KPI calculator
        kpi_calculator = SaaSKPICalculator(SessionLocal())
        
        # Calculate period
        period_end = datetime.now()
        period_start = period_end - timedelta(days=period_months * 30)
        
        click.echo(f"📊 Calculating KPIs for period {period_start.strftime('%Y-%m-%d')} to {period_end.strftime('%Y-%m-%d')}...")
        
        # Calculate comprehensive KPIs
        kpis = kpi_calculator.calculate_comprehensive_kpis(organization_id, period_start, period_end)
        
        # Output results
        if output == "json":
            click.echo(json.dumps(kpis, indent=2, default=str))
        elif output == "detailed":
            _display_detailed_saas_metrics(kpis, include_cohorts, include_benchmarks)
        else:
            _display_saas_metrics_summary(kpis, include_benchmarks)
            
        click.echo("✅ SaaS metrics analysis completed successfully!")
        
    except Exception as e:
        logger.error(f"Error calculating SaaS metrics: {e}")
        click.echo(f"❌ Error: {e}")


@analytics.command("growth-analysis")
@click.option("--organization-id", "-o", required=True, type=int, help="Organization ID for Xero data")
@click.option("--period-months", type=int, default=24, help="Analysis period in months (default: 24)")
@click.option("--output", "-f", type=click.Choice(["json", "summary"]), default="summary", 
              help="Output format (default: summary)")
def growth_analysis_command(organization_id: int, period_months: int, output: str) -> None:
    """Analyze growth trends and patterns."""
    try:
        click.echo(f"🚀 Running growth analysis for organization {organization_id}...")
        
        # Initialize KPI calculator
        kpi_calculator = SaaSKPICalculator(SessionLocal())
        
        # Calculate multiple periods for trend analysis
        current_date = datetime.now()
        periods = []
        
        # Get quarterly data for trend analysis
        for quarter in range(8):  # Last 2 years in quarters
            period_end = current_date - timedelta(days=quarter * 90)
            period_start = period_end - timedelta(days=90)
            
            quarterly_kpis = kpi_calculator.calculate_comprehensive_kpis(
                organization_id, period_start, period_end
            )
            
            periods.append({
                "period": f"Q{4 - (quarter % 4)} {period_end.year}",
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "kpis": quarterly_kpis.get("kpis", {}),
            })
        
        # Calculate growth trends
        growth_analysis = _calculate_growth_trends(periods)
        
        result = {
            "organization_id": organization_id,
            "analysis_type": "Growth Trend Analysis",
            "analysis_date": current_date.isoformat(),
            "periods": periods,
            "growth_trends": growth_analysis,
            "recommendations": _generate_growth_recommendations(growth_analysis),
        }
        
        # Output results
        if output == "json":
            click.echo(json.dumps(result, indent=2, default=str))
        else:
            _display_growth_analysis_summary(result)
            
        click.echo("✅ Growth analysis completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in growth analysis: {e}")
        click.echo(f"❌ Error: {e}")


@analytics.command("health-score")
@click.option("--organization-id", "-o", required=True, type=int, help="Organization ID for Xero data")
@click.option("--period-months", type=int, default=12, help="Analysis period in months (default: 12)")
@click.option("--output", "-f", type=click.Choice(["json", "summary"]), default="summary", 
              help="Output format (default: summary)")
def health_score_command(organization_id: int, period_months: int, output: str) -> None:
    """Calculate comprehensive business health score."""
    try:
        click.echo(f"🏥 Calculating business health score for organization {organization_id}...")
        
        # Initialize KPI calculator
        kpi_calculator = SaaSKPICalculator(SessionLocal())
        
        period_end = datetime.now()
        period_start = period_end - timedelta(days=period_months * 30)
        
        # Calculate KPIs
        kpis = kpi_calculator.calculate_comprehensive_kpis(organization_id, period_start, period_end)
        
        # Extract health score
        health_score = kpis.get("kpis", {}).get("health_score", {})
        
        if not health_score:
            click.echo("❌ Error: Unable to calculate health score")
            return
        
        result = {
            "organization_id": organization_id,
            "calculation_date": datetime.now().isoformat(),
            "period": {
                "start": period_start.isoformat(),
                "end": period_end.isoformat(),
            },
            "health_score": health_score,
            "key_metrics": kpis.get("summary", {}),
        }
        
        # Output results
        if output == "json":
            click.echo(json.dumps(result, indent=2, default=str))
        else:
            _display_health_score_summary(result)
            
        click.echo("✅ Health score calculation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error calculating health score: {e}")
        click.echo(f"❌ Error: {e}")


def _display_saas_metrics_summary(kpis: Dict[str, Any], include_benchmarks: bool = False) -> None:
    """Display SaaS metrics summary."""
    click.echo("\n" + "="*60)
    click.echo("📊 SAAS METRICS SUMMARY")
    click.echo("="*60)
    
    # Get key metrics
    revenue_metrics = kpis.get("kpis", {}).get("revenue_metrics", {})
    customer_metrics = kpis.get("kpis", {}).get("customer_metrics", {})
    efficiency_metrics = kpis.get("kpis", {}).get("efficiency_metrics", {})
    health_score = kpis.get("kpis", {}).get("health_score", {})
    
    # Revenue section
    click.echo("💰 REVENUE METRICS:")
    click.echo(f"  Monthly Recurring Revenue (MRR): ${revenue_metrics.get('monthly_recurring_revenue', 0):,.2f}")
    click.echo(f"  Annual Recurring Revenue (ARR): ${revenue_metrics.get('annual_recurring_revenue', 0):,.2f}")
    
    growth_rates = revenue_metrics.get("revenue_growth_rate", {})
    click.echo(f"  Monthly Growth Rate: {growth_rates.get('monthly', 0):.1f}%")
    click.echo(f"  Annual Growth Rate: {growth_rates.get('annual', 0):.1f}%")
    
    # Customer section  
    click.echo("\n👥 CUSTOMER METRICS:")
    click.echo(f"  Total Active Customers: {customer_metrics.get('active_customers', 0):,}")
    click.echo(f"  Customer Churn Rate: {customer_metrics.get('customer_churn_rate', 0):.1f}%")
    click.echo(f"  Average Revenue Per User (ARPU): ${revenue_metrics.get('average_revenue_per_user', 0):,.2f}")
    
    # Unit economics
    click.echo("\n💼 UNIT ECONOMICS:")
    click.echo(f"  Customer Acquisition Cost (CAC): ${customer_metrics.get('customer_acquisition_cost', 0):,.2f}")
    click.echo(f"  Customer Lifetime Value (LTV): ${customer_metrics.get('customer_lifetime_value', 0):,.2f}")
    click.echo(f"  LTV/CAC Ratio: {customer_metrics.get('ltv_cac_ratio', 0):.1f}x")
    click.echo(f"  Payback Period: {customer_metrics.get('payback_period_months', 0):.1f} months")
    
    # Retention
    click.echo("\n🔄 RETENTION METRICS:")
    click.echo(f"  Net Revenue Retention: {revenue_metrics.get('net_revenue_retention', 0):.1f}%")
    click.echo(f"  Gross Revenue Retention: {revenue_metrics.get('gross_revenue_retention', 0):.1f}%")
    click.echo(f"  Revenue Churn Rate: {revenue_metrics.get('revenue_churn_rate', 0):.1f}%")
    
    # Efficiency
    click.echo("\n⚡ EFFICIENCY METRICS:")
    click.echo(f"  Rule of 40: {efficiency_metrics.get('rule_of_40', 0):.1f}")
    click.echo(f"  Magic Number: {efficiency_metrics.get('magic_number', 0):.2f}")
    click.echo(f"  Revenue per Employee: ${revenue_metrics.get('revenue_per_employee', 0):,.0f}")
    
    # Health score
    if health_score:
        click.echo("\n🏥 BUSINESS HEALTH:")
        click.echo(f"  Overall Score: {health_score.get('overall_score', 0):.0f}/100 ({health_score.get('health_grade', 'N/A')})")
        click.echo(f"  Status: {health_score.get('health_status', 'N/A')}")
        
        # Show recommendations
        recommendations = health_score.get("recommendations", [])
        if recommendations:
            click.echo(f"  Key Recommendations:")
            for rec in recommendations[:3]:  # Show top 3
                click.echo(f"    • {rec}")
    
    # Show benchmarks if requested
    if include_benchmarks:
        benchmarks = kpis.get("kpis", {}).get("benchmarks", {})
        if benchmarks:
            click.echo("\n📈 INDUSTRY BENCHMARKS:")
            for metric, benchmark in benchmarks.items():
                if isinstance(benchmark, dict):
                    click.echo(f"  {metric.replace('_', ' ').title()}:")
                    for level, value in benchmark.items():
                        click.echo(f"    {level.title()}: {value}")
    
    click.echo("\n" + "="*60 + "\n")


def _display_detailed_saas_metrics(kpis: Dict[str, Any], include_cohorts: bool, include_benchmarks: bool) -> None:
    """Display detailed SaaS metrics."""
    # First show the summary
    _display_saas_metrics_summary(kpis, include_benchmarks)
    
    # Then add detailed sections
    if include_cohorts:
        cohort_analysis = kpis.get("kpis", {}).get("cohort_analysis", {})
        if cohort_analysis:
            click.echo("📊 COHORT ANALYSIS:")
            click.echo("-" * 30)
            
            avg_retention = cohort_analysis.get("average_retention", {})
            for period, rate in avg_retention.items():
                click.echo(f"  {period.replace('_', ' ').title()}: {rate:.1f}%")
            
            click.echo()
    
    # Show churn analysis
    churn_analysis = kpis.get("kpis", {}).get("churn_analysis", {})
    if churn_analysis:
        click.echo("🔄 CHURN ANALYSIS:")
        click.echo("-" * 20)
        
        churn_by_plan = churn_analysis.get("churn_by_plan", {})
        if churn_by_plan:
            click.echo("  Churn by Plan Type:")
            for plan, rate in churn_by_plan.items():
                click.echo(f"    {plan.title()}: {rate:.1f}%")
        
        click.echo(f"  Average Time to Churn: {churn_analysis.get('average_time_to_churn', 0):.1f} months")
        click.echo()
    
    # Show growth metrics
    growth_metrics = kpis.get("kpis", {}).get("growth_metrics", {})
    if growth_metrics:
        click.echo("🚀 GROWTH METRICS:")
        click.echo("-" * 20)
        
        click.echo(f"  Customer Growth Rate: {growth_metrics.get('customer_growth_rate', 0):.1f}%")
        click.echo(f"  Net New MRR: ${growth_metrics.get('net_new_mrr', 0):,.2f}")
        click.echo(f"  Growth Efficiency: {growth_metrics.get('growth_efficiency', 0):.2f}")
        click.echo()


def _display_growth_analysis_summary(result: Dict[str, Any]) -> None:
    """Display growth analysis summary."""
    click.echo("\n" + "="*60)
    click.echo("🚀 GROWTH TREND ANALYSIS")
    click.echo("="*60)
    
    growth_trends = result.get("growth_trends", {})
    
    # Show key trends
    click.echo("📈 KEY TRENDS:")
    mrr_trend = growth_trends.get("mrr_trend", {})
    if mrr_trend:
        direction = "↗️" if mrr_trend.get("direction", "") == "increasing" else "↘️" if mrr_trend.get("direction", "") == "decreasing" else "➡️"
        click.echo(f"  MRR Trend: {direction} {mrr_trend.get('direction', 'stable').title()}")
        click.echo(f"  Average Growth Rate: {mrr_trend.get('average_rate', 0):.1f}%")
    
    customer_trend = growth_trends.get("customer_trend", {})
    if customer_trend:
        direction = "↗️" if customer_trend.get("direction", "") == "increasing" else "↘️" if customer_trend.get("direction", "") == "decreasing" else "➡️"
        click.echo(f"  Customer Trend: {direction} {customer_trend.get('direction', 'stable').title()}")
        click.echo(f"  Average Growth Rate: {customer_trend.get('average_rate', 0):.1f}%")
    
    # Show recent periods
    periods = result.get("periods", [])
    if periods:
        click.echo(f"\n📊 RECENT PERFORMANCE (Last 4 Quarters):")
        for period in periods[:4]:
            period_name = period.get("period", "Unknown")
            kpis = period.get("kpis", {})
            revenue_metrics = kpis.get("revenue_metrics", {})
            customer_metrics = kpis.get("customer_metrics", {})
            
            mrr = revenue_metrics.get("monthly_recurring_revenue", 0)
            customers = customer_metrics.get("active_customers", 0)
            
            click.echo(f"  {period_name}: MRR ${mrr:,.0f}, Customers {customers:,}")
    
    # Show recommendations
    recommendations = result.get("recommendations", [])
    if recommendations:
        click.echo(f"\n💡 RECOMMENDATIONS:")
        for rec in recommendations:
            click.echo(f"  • {rec}")
    
    click.echo("\n" + "="*60 + "\n")


def _display_health_score_summary(result: Dict[str, Any]) -> None:
    """Display health score summary."""
    click.echo("\n" + "="*60)
    click.echo("🏥 BUSINESS HEALTH SCORE")
    click.echo("="*60)
    
    health_score = result.get("health_score", {})
    
    # Overall score
    click.echo(f"Overall Score: {health_score.get('overall_score', 0):.0f}/100")
    click.echo(f"Grade: {health_score.get('health_grade', 'N/A')}")
    click.echo(f"Status: {health_score.get('health_status', 'N/A')}")
    
    # Component scores
    component_scores = health_score.get("component_scores", {})
    if component_scores:
        click.echo(f"\n📊 COMPONENT SCORES:")
        for component, score in component_scores.items():
            percentage = score / 25 * 100 if score <= 25 else 100
            component_name = component.replace("_", " ").title()
            click.echo(f"  {component_name}: {score:.0f}/25 ({percentage:.0f}%)")
    
    # Recommendations
    recommendations = health_score.get("recommendations", [])
    if recommendations:
        click.echo(f"\n💡 IMPROVEMENT RECOMMENDATIONS:")
        for rec in recommendations:
            click.echo(f"  • {rec}")
    
    # Key metrics summary
    key_metrics = result.get("key_metrics", {})
    top_metrics = key_metrics.get("top_metrics", {})
    if top_metrics:
        click.echo(f"\n📈 KEY METRICS:")
        click.echo(f"  MRR: ${top_metrics.get('mrr', 0):,.2f}")
        click.echo(f"  ARR: ${top_metrics.get('arr', 0):,.2f}")
        click.echo(f"  Growth Rate: {top_metrics.get('growth_rate', 0):.1f}%")
        click.echo(f"  Churn Rate: {top_metrics.get('churn_rate', 0):.1f}%")
        click.echo(f"  LTV/CAC Ratio: {top_metrics.get('ltv_cac_ratio', 0):.1f}x")
    
    click.echo("\n" + "="*60 + "\n")


def _calculate_growth_trends(periods: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate growth trends from period data."""
    try:
        if len(periods) < 2:
            return {}
        
        # Extract MRR data
        mrr_values = []
        customer_values = []
        
        for period in reversed(periods):  # Reverse to get chronological order
            revenue_metrics = period.get("kpis", {}).get("revenue_metrics", {})
            customer_metrics = period.get("kpis", {}).get("customer_metrics", {})
            
            mrr_values.append(revenue_metrics.get("monthly_recurring_revenue", 0))
            customer_values.append(customer_metrics.get("active_customers", 0))
        
        # Calculate MRR trend
        mrr_trend = _calculate_trend(mrr_values)
        customer_trend = _calculate_trend(customer_values)
        
        return {
            "mrr_trend": mrr_trend,
            "customer_trend": customer_trend,
            "analysis_periods": len(periods),
        }
        
    except Exception as e:
        logger.error(f"Error calculating growth trends: {e}")
        return {}


def _calculate_trend(values: List[float]) -> Dict[str, Any]:
    """Calculate trend from a list of values."""
    try:
        if len(values) < 2:
            return {"direction": "unknown", "average_rate": 0}
        
        # Calculate period-over-period growth rates
        growth_rates = []
        for i in range(1, len(values)):
            if values[i-1] > 0:
                growth_rate = ((values[i] - values[i-1]) / values[i-1]) * 100
                growth_rates.append(growth_rate)
        
        if not growth_rates:
            return {"direction": "unknown", "average_rate": 0}
        
        # Calculate average growth rate
        avg_rate = sum(growth_rates) / len(growth_rates)
        
        # Determine direction
        if avg_rate > 1:
            direction = "increasing"
        elif avg_rate < -1:
            direction = "decreasing"
        else:
            direction = "stable"
        
        return {
            "direction": direction,
            "average_rate": avg_rate,
            "periods_analyzed": len(growth_rates),
            "latest_value": values[-1] if values else 0,
            "earliest_value": values[0] if values else 0,
        }
        
    except Exception as e:
        logger.error(f"Error calculating trend: {e}")
        return {"direction": "unknown", "average_rate": 0}


def _generate_growth_recommendations(growth_trends: Dict[str, Any]) -> List[str]:
    """Generate growth recommendations based on trends."""
    recommendations = []
    
    mrr_trend = growth_trends.get("mrr_trend", {})
    customer_trend = growth_trends.get("customer_trend", {})
    
    # MRR recommendations
    mrr_direction = mrr_trend.get("direction", "")
    mrr_rate = mrr_trend.get("average_rate", 0)
    
    if mrr_direction == "decreasing":
        recommendations.append("Address declining MRR through customer retention and expansion initiatives")
    elif mrr_direction == "stable" and mrr_rate < 10:
        recommendations.append("Accelerate MRR growth through improved sales and marketing efforts")
    elif mrr_direction == "increasing" and mrr_rate > 20:
        recommendations.append("Maintain strong MRR growth momentum and prepare for scaling challenges")
    
    # Customer recommendations
    customer_direction = customer_trend.get("direction", "")
    customer_rate = customer_trend.get("average_rate", 0)
    
    if customer_direction == "decreasing":
        recommendations.append("Focus on customer acquisition and retention to reverse customer count decline")
    elif customer_direction == "stable":
        recommendations.append("Implement customer acquisition strategies to drive growth")
    
    # Combined analysis
    if mrr_direction == "increasing" and customer_direction == "decreasing":
        recommendations.append("Strong revenue per customer growth - consider expanding to new market segments")
    elif mrr_direction == "stable" and customer_direction == "increasing":
        recommendations.append("Growing customer base with flat revenue suggests pricing optimization opportunity")
    
    return recommendations


@analytics.command("status")
@click.argument("task_id")
def check_analytics_status(task_id: str) -> None:
    """Check the status of an async analytics task."""
    try:
        from mcx3d_finance.tasks.celery_app import celery_app
        
        task = celery_app.AsyncResult(task_id)
        
        click.echo(f"Task ID: {task_id}")
        click.echo(f"Status: {task.status}")
        
        if task.status == "PENDING":
            click.echo("Task is waiting to be processed...")
        elif task.status == "PROGRESS":
            meta = task.info
            if meta:
                click.echo(f"Progress: {meta.get('progress', 0)}%")
                click.echo(f"Status: {meta.get('status', 'Processing...')}")
                if 'organization_id' in meta:
                    click.echo(f"Organization ID: {meta['organization_id']}")
        elif task.status == "SUCCESS":
            result = task.result
            if result:
                click.echo("\n✅ Analytics completed successfully!")
                
                # Display brief summary
                if 'analyses' in result:
                    click.echo("\nAnalysis Summary:")
                    analyses = result.get('analyses', {})
                    
                    if 'profitability' in analyses:
                        prof = analyses['profitability']
                        if isinstance(prof, dict) and 'gross_margin' in prof:
                            click.echo(f"  Gross Margin: {prof.get('gross_margin', 0):.1%}")
                    
                    if 'liquidity' in analyses:
                        liq = analyses['liquidity']
                        if isinstance(liq, dict) and 'current_ratio' in liq:
                            click.echo(f"  Current Ratio: {liq.get('current_ratio', 0):.2f}")
                    
                    if 'efficiency' in analyses:
                        eff = analyses['efficiency']
                        if isinstance(eff, dict) and 'asset_turnover' in eff:
                            click.echo(f"  Asset Turnover: {eff.get('asset_turnover', 0):.2f}")
                
                click.echo(f"\nFull results available. Use output format options to view detailed results.")
        elif task.status == "FAILURE":
            error_info = task.info
            click.echo(f"❌ Task failed with error: {error_info}")
        else:
            click.echo(f"Task status: {task.status}")
            if task.info:
                click.echo(f"Additional info: {task.info}")
    
    except Exception as e:
        logger.error(f"Error checking task status: {e}")
        click.echo(f"❌ Error: {e}")