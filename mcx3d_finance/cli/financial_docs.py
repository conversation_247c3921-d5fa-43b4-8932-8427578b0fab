"""
CLI commands for generating comprehensive financial documentation.

This module provides commands to generate financial statements, executive summaries,
and complete documentation packages for MCX3D LTD using real Xero data.
"""

import asyncio
import os
import sys
from datetime import date, datetime
from pathlib import Path
from typing import Dict, Any, Tuple
import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table
from rich.panel import Panel

from mcx3d_finance.integrations.xero_client import XeroClient
from mcx3d_finance.reporting.documentation_builder import FinancialDocumentationBuilder
from mcx3d_finance.core.config import settings
from mcx3d_finance.cli.utils import handle_cli_errors

console = Console()


@click.group(name="financial-docs")
def financial_docs() -> None:
    """Generate comprehensive financial documentation."""
    pass


@financial_docs.command(name="generate")
@click.option(
    "--output-dir",
    "-o",
    default="./reports",
    help="Output directory for generated documents",
    type=click.Path()
)
@click.option(
    "--formats",
    "-f",
    multiple=True,
    type=click.Choice(["pdf", "excel", "json"]),
    default=["pdf", "excel"],
    help="Output formats for documents"
)
@click.option(
    "--include-projections",
    "-p",
    is_flag=True,
    default=True,
    help="Include valuation and projection reports"
)
@click.option(
    "--report-date",
    "-d",
    type=click.DateTime(formats=["%Y-%m-%d"]),
    default=None,
    help="Report date (defaults to today)"
)
@click.option(
    "--organization-id",
    "-org",
    default=None,
    help="Xero organization ID (defaults to environment variable)"
)
@click.option(
    "--use-sample-data",
    is_flag=True,
    help="Use sample database data instead of Xero API (requires sample data to be imported first)"
)
@handle_cli_errors
def generate_command(
    output_dir: str, 
    formats: Tuple[str, ...], 
    include_projections: bool, 
    report_date: str, 
    organization_id: int, 
    use_sample_data: bool
) -> None:
    """
    Generate complete financial documentation package for MCX3D LTD.
    
    This command will:
    - Connect to Xero API OR use sample database data (with --use-sample-data flag)
    - Generate all financial statements (Balance Sheet, Income Statement, Cash Flow)
    - Create executive summary and management discussion
    - Generate notes to financial statements
    - Create KPI dashboards and analysis
    - Package everything into professional documents
    
    Examples:
        # Generate using Xero API (requires authentication)
        mcx3d financial-docs generate
        
        # Generate using sample data (requires data to be imported first)
        mcx3d financial-docs generate --use-sample-data
    """
    
    data_source = "sample database data" if use_sample_data else "real Xero data"
    console.print(Panel.fit(
        f"[bold blue]MCX3D LTD Financial Documentation Generator[/bold blue]\n"
        f"Generating comprehensive financial reports using {data_source}",
        border_style="blue"
    ))
    
    # Ensure output directory exists
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Set report date
    if report_date:
        report_date_obj = report_date.date()
    else:
        report_date_obj = date.today()
    
    console.print(f"\n[cyan]Report Date:[/cyan] {report_date_obj.strftime('%d %B %Y')}")
    console.print(f"[cyan]Output Directory:[/cyan] {output_path.absolute()}")
    console.print(f"[cyan]Formats:[/cyan] {', '.join(formats).upper()}")
    console.print(f"[cyan]Include Projections:[/cyan] {'Yes' if include_projections else 'No'}\n")
    
    # Initialize Xero client
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        console=console
    ) as progress:
        
        # Main task
        main_task = progress.add_task("Generating financial documentation...", total=100)
        
        try:
            if use_sample_data:
                # Step 1: Use sample data mode
                progress.update(main_task, advance=10, description="Connecting to sample database...")
                
                # For sample data, we need to find the MCX3D organization ID
                from mcx3d_finance.db.session import SessionLocal
                from mcx3d_finance.db.models import Organization
                
                db = SessionLocal()
                try:
                    mcx3d_org = db.query(Organization).filter(
                        Organization.name == "MCX3D LTD"
                    ).first()
                    
                    if not mcx3d_org:
                        console.print("[red]Error:[/red] MCX3D LTD organization not found in database.")
                        console.print("Please run: python scripts/seed_database.py --company-data --reset")
                        sys.exit(1)
                    
                    org_id = mcx3d_org.id
                    console.print(f"[green]✓[/green] Found MCX3D LTD organization (ID: {org_id})")
                    
                finally:
                    db.close()
                
                # Step 2: Initialize documentation builder for sample data
                progress.update(main_task, advance=10, description="Initializing document builder...")
                doc_builder = FinancialDocumentationBuilder.for_sample_data(org_id)
                
                # Step 3: Generate documentation
                progress.update(main_task, advance=10, description="Processing sample data...")
                
            else:
                # Step 1: Initialize Xero connection
                progress.update(main_task, advance=10, description="Connecting to Xero...")
                
                # Get organization ID from environment or parameter
                org_id = organization_id or os.getenv("XERO_TENANT_ID")
                if not org_id:
                    console.print("[red]Error:[/red] Xero organization ID not provided. "
                                "Set XERO_TENANT_ID environment variable or use --organization-id")
                    sys.exit(1)
                
                # Check for Xero credentials
                required_vars = ["XERO_CLIENT_ID", "XERO_CLIENT_SECRET", "XERO_ACCESS_TOKEN"]
                missing_vars = [var for var in required_vars if not os.getenv(var)]
                
                if missing_vars:
                    console.print(f"[red]Error:[/red] Missing required Xero credentials: {', '.join(missing_vars)}")
                    console.print("\nPlease set the following environment variables:")
                    for var in missing_vars:
                        console.print(f"  - {var}")
                    sys.exit(1)
                
                xero_client = XeroClient(organization_id=org_id)
                
                # Step 2: Initialize documentation builder
                progress.update(main_task, advance=10, description="Initializing document builder...")
                doc_builder = FinancialDocumentationBuilder(xero_client)
                
                # Step 3: Generate documentation
                progress.update(main_task, advance=10, description="Fetching data from Xero...")
            
            # Run async generation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    generate_documentation_async(
                        doc_builder, 
                        output_path, 
                        report_date_obj,
                        include_projections,
                        list(formats),
                        progress,
                        main_task
                    )
                )
            finally:
                loop.close()
            
            # Step 4: Display results
            progress.update(main_task, advance=10, description="Complete!")
            
            # Show summary table
            display_generation_summary(result, output_path)
            
        except Exception as e:
            console.print(f"\n[red]Error generating documentation:[/red] {str(e)}")
            raise


async def generate_documentation_async(doc_builder, output_path, report_date, 
                                     include_projections, formats, progress, task_id):
    """Async function to generate documentation with progress updates."""
    
    # Update progress callback
    def update_progress(message: str, percentage: int) -> None:
        progress.update(task_id, completed=percentage, description=message)
    
    # Generate package
    update_progress("Fetching financial data from Xero...", 20)
    
    result = await doc_builder.generate_complete_financial_package(
        output_directory=str(output_path),
        report_date=report_date,
        include_projections=include_projections,
        formats=formats
    )
    
    update_progress("Finalizing documentation package...", 90)
    
    return result


def display_generation_summary(result: Dict[str, Any], output_path: Path) -> None:
    """Display a summary of generated documents."""
    
    console.print("\n[bold green]✓ Financial documentation generated successfully![/bold green]\n")
    
    # Create summary table
    table = Table(title="Generated Documents", show_header=True, header_style="bold cyan")
    table.add_column("Document Type", style="cyan", width=30)
    table.add_column("Files Generated", style="green")
    
    # Add document categories
    for category, files in result['documents'].items():
        if files:
            category_name = category.replace('_', ' ').title()
            file_count = len(files)
            table.add_row(category_name, f"{file_count} file(s)")
    
    console.print(table)
    
    # Show package location
    if 'package_path' in result:
        console.print(f"\n[bold cyan]📦 Complete Package:[/bold cyan]")
        console.print(f"   {result['package_path']}")
    
    # Show summary metrics
    if 'summary' in result:
        summary = result['summary']
        console.print(f"\n[bold cyan]📊 Financial Highlights:[/bold cyan]")
        console.print(f"   Revenue: £{summary.get('revenue', 0):,.0f}")
        console.print(f"   Net Income: £{summary.get('net_income', 0):,.0f}")
        console.print(f"   Total Assets: £{summary.get('total_assets', 0):,.0f}")
        
        if 'key_metrics' in summary:
            metrics = summary['key_metrics']
            if 'arr' in metrics and metrics['arr'] > 0:
                console.print(f"   Annual Recurring Revenue: £{metrics['arr']:,.0f}")
            if 'gross_margin' in metrics:
                console.print(f"   Gross Margin: {metrics['gross_margin']:.1f}%")
    
    console.print(f"\n[dim]All documents saved to: {output_path.absolute()}[/dim]")


@financial_docs.command(name="list")
@click.option(
    "--reports-dir",
    "-d",
    default="./reports",
    help="Directory containing generated reports",
    type=click.Path(exists=True)
)
def list_documents(reports_dir: str) -> None:
    """List all generated financial documents."""
    
    reports_path = Path(reports_dir)
    
    console.print(Panel.fit(
        "[bold blue]Available Financial Documents[/bold blue]",
        border_style="blue"
    ))
    
    # Find all generated packages
    packages = list(reports_path.glob("MCX3D_Financial_Package_*.zip"))
    
    if not packages:
        console.print("\n[yellow]No financial packages found in the reports directory.[/yellow]")
        return
    
    # Create table
    table = Table(show_header=True, header_style="bold cyan")
    table.add_column("Generated Date", style="cyan", width=20)
    table.add_column("Package Name", style="green")
    table.add_column("Size", style="yellow", width=10)
    
    for package in sorted(packages, reverse=True):
        # Extract date from filename
        date_str = package.stem.split('_')[-1]
        try:
            gen_date = datetime.strptime(date_str, "%Y%m%d").strftime("%d %B %Y")
        except:
            gen_date = "Unknown"
        
        # Get file size
        size_mb = package.stat().st_size / (1024 * 1024)
        
        table.add_row(
            gen_date,
            package.name,
            f"{size_mb:.1f} MB"
        )
    
    console.print("\n")
    console.print(table)
    console.print(f"\n[dim]Total packages: {len(packages)}[/dim]")


@financial_docs.command(name="clean")
@click.option(
    "--reports-dir",
    "-d",
    default="./reports",
    help="Directory containing generated reports",
    type=click.Path(exists=True)
)
@click.option(
    "--keep-latest",
    "-k",
    default=5,
    help="Number of latest reports to keep",
    type=int
)
@click.confirmation_option(prompt="Are you sure you want to clean old reports?")
def clean_old_reports(reports_dir: str, keep_latest: int) -> None:
    """Clean up old financial report packages."""
    
    reports_path = Path(reports_dir)
    packages = sorted(reports_path.glob("MCX3D_Financial_Package_*.zip"), reverse=True)
    
    if len(packages) <= keep_latest:
        console.print(f"[green]No cleanup needed. Found {len(packages)} packages, keeping {keep_latest}.[/green]")
        return
    
    # Remove old packages
    to_remove = packages[keep_latest:]
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        task = progress.add_task(f"Removing {len(to_remove)} old packages...", total=len(to_remove))
        
        for package in to_remove:
            package.unlink()
            progress.update(task, advance=1)
    
    console.print(f"\n[green]✓ Removed {len(to_remove)} old packages.[/green]")
    console.print(f"[dim]Kept the latest {keep_latest} packages.[/dim]")


