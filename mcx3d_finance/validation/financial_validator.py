"""
Financial data validation for the MCX3D financial system.

Provides validation for financial calculations, projections, and data integrity.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional, Union
from decimal import Decimal, InvalidOperation

from mcx3d_finance.exceptions import (
    FinancialDataError,
    FinancialCalculationError,
    NumericalStabilityError,
    FinancialRangeError,
    ValidationError
)

logger = LoggerFactory.get_logger(__name__)


class FinancialDataValidator:
    """
    Validator for financial calculations and data integrity.
    
    Ensures financial data meets business rules and mathematical constraints.
    """
    
    # Industry-standard ranges for financial ratios and metrics
    FINANCIAL_RANGES = {
        'discount_rate': {'min': 0.01, 'max': 0.50, 'typical_min': 0.05, 'typical_max': 0.25},
        'growth_rate': {'min': -0.50, 'max': 5.00, 'typical_min': -0.20, 'typical_max': 1.00},
        'terminal_growth_rate': {'min': 0.00, 'max': 0.08, 'typical_min': 0.01, 'typical_max': 0.04},
        'tax_rate': {'min': 0.00, 'max': 0.70, 'typical_min': 0.15, 'typical_max': 0.35},
        'pe_ratio': {'min': 1.0, 'max': 100.0, 'typical_min': 5.0, 'typical_max': 40.0},
        'ev_revenue_multiple': {'min': 0.1, 'max': 50.0, 'typical_min': 1.0, 'typical_max': 15.0},
        'debt_to_equity': {'min': 0.0, 'max': 10.0, 'typical_min': 0.0, 'typical_max': 3.0},
        'current_ratio': {'min': 0.1, 'max': 20.0, 'typical_min': 1.0, 'typical_max': 3.0},
        'profit_margin': {'min': -1.0, 'max': 1.0, 'typical_min': 0.0, 'typical_max': 0.30},
        'roe': {'min': -1.0, 'max': 5.0, 'typical_min': 0.05, 'typical_max': 0.25},
        'churn_rate': {'min': 0.0, 'max': 1.0, 'typical_min': 0.01, 'typical_max': 0.10},
        'ltv_cac_ratio': {'min': 0.5, 'max': 50.0, 'typical_min': 3.0, 'typical_max': 8.0},
        'arr_multiple': {'min': 0.5, 'max': 100.0, 'typical_min': 2.0, 'typical_max': 20.0}
    }
    
    def __init__(self):
        """Initialize the financial data validator."""
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_financial_projections(
        self,
        projections: List[Dict[str, Any]],
        projection_type: str = "general"
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate financial projections for reasonableness and consistency.
        
        Args:
            projections: List of financial projections by year
            projection_type: Type of projections (revenue, cash_flow, etc.)
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            if not projections or not isinstance(projections, list):
                raise FinancialDataError(
                    "Projections must be a non-empty list",
                    data_type="projections"
                )
            
            # Validate individual projection periods
            for i, projection in enumerate(projections):
                year = i + 1
                self._validate_single_projection(projection, year, projection_type)
            
            # Validate projection consistency across years
            self._validate_projection_consistency(projections, projection_type)
            
            # Validate growth rates
            self._validate_projection_growth_rates(projections)
            
            is_valid = len(self.validation_errors) == 0
            
            if self.validation_errors:
                logger.warning(f"Financial projection validation failed with {len(self.validation_errors)} errors")
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            if isinstance(e, FinancialDataError):
                raise
            raise FinancialCalculationError(
                f"Projection validation failed: {e}",
                calculation_type="projection_validation"
            )
    
    def validate_valuation_inputs(
        self,
        valuation_data: Dict[str, Any],
        valuation_method: str
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate inputs for valuation calculations.
        
        Args:
            valuation_data: Dictionary containing valuation inputs
            valuation_method: Valuation method (DCF, multiples, etc.)
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            if valuation_method.upper() == 'DCF':
                self._validate_dcf_inputs(valuation_data)
            elif valuation_method.upper() == 'MULTIPLES':
                self._validate_multiples_inputs(valuation_data)
            elif valuation_method.upper() == 'SAAS':
                self._validate_saas_valuation_inputs(valuation_data)
            else:
                self.validation_errors.append(f"Unsupported valuation method: {valuation_method}")
            
            is_valid = len(self.validation_errors) == 0
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            if isinstance(e, (FinancialDataError, FinancialRangeError)):
                raise
            raise FinancialCalculationError(
                f"Valuation input validation failed: {e}",
                calculation_type=f"{valuation_method}_validation"
            )
    
    def validate_financial_ratios(
        self,
        ratios: Dict[str, Union[int, float]],
        company_context: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate financial ratios for reasonableness.
        
        Args:
            ratios: Dictionary of financial ratios
            company_context: Additional company context for validation
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            for ratio_name, value in ratios.items():
                self._validate_financial_ratio(ratio_name, value, company_context)
            
            # Cross-ratio consistency checks
            self._validate_ratio_consistency(ratios)
            
            is_valid = len(self.validation_errors) == 0
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            raise FinancialDataError(
                f"Financial ratio validation failed: {e}",
                data_type="financial_ratios"
            )
    
    def validate_numerical_stability(
        self,
        operation: str,
        operands: Dict[str, Union[int, float, Decimal]],
        result: Optional[Union[int, float, Decimal]] = None
    ) -> bool:
        """
        Validate numerical calculations for stability and overflow issues.
        
        Args:
            operation: Name of the mathematical operation
            operands: Input values for the operation
            result: Result of the operation (optional)
            
        Returns:
            True if calculation is numerically stable
            
        Raises:
            NumericalStabilityError: If numerical issues are detected
        """
        try:
            # Check for division by zero
            if operation.lower() in ['divide', 'division', 'ratio']:
                denominator = operands.get('denominator') or operands.get('divisor')
                if denominator is not None and abs(float(denominator)) < 1e-10:
                    raise NumericalStabilityError(
                        f"Division by zero or near-zero value: {denominator}",
                        operation=operation,
                        operands=operands
                    )
            
            # Check for overflow/underflow
            for name, value in operands.items():
                if value is not None:
                    abs_value = abs(float(value))
                    if abs_value > 1e308:  # Close to float64 max
                        raise NumericalStabilityError(
                            f"Potential overflow in {name}: {value}",
                            operation=operation,
                            operands=operands
                        )
                    elif 0 < abs_value < 1e-308:  # Close to float64 min
                        raise NumericalStabilityError(
                            f"Potential underflow in {name}: {value}",
                            operation=operation,
                            operands=operands
                        )
            
            # Check result if provided
            if result is not None:
                abs_result = abs(float(result))
                if abs_result > 1e308:
                    raise NumericalStabilityError(
                        f"Result overflow in {operation}: {result}",
                        operation=operation,
                        operands=operands
                    )
                elif not (result == 0 or abs_result >= 1e-308):
                    raise NumericalStabilityError(
                        f"Result underflow in {operation}: {result}",
                        operation=operation,
                        operands=operands
                    )
            
            return True
            
        except Exception as e:
            if isinstance(e, NumericalStabilityError):
                raise
            raise NumericalStabilityError(
                f"Numerical stability check failed: {e}",
                operation=operation,
                operands=operands
            )
    
    def _validate_single_projection(
        self,
        projection: Dict[str, Any],
        year: int,
        projection_type: str
    ):
        """Validate a single year's financial projection."""
        context = f"Year {year} {projection_type}"
        
        # Check required fields based on projection type
        required_fields = self._get_required_projection_fields(projection_type)
        
        for field in required_fields:
            if field not in projection:
                self.validation_errors.append(f"Missing {field} in {context}")
                continue
            
            value = projection[field]
            
            # Type validation
            if not isinstance(value, (int, float, Decimal)):
                self.validation_errors.append(f"{field} in {context} must be a number, got {type(value)}")
                continue
            
            # Numerical stability check
            try:
                self.validate_numerical_stability(f"projection_{field}", {field: value})
            except NumericalStabilityError as e:
                self.validation_errors.append(f"Numerical issue in {context} {field}: {e.message}")
            
            # Range validation for specific fields
            self._validate_projection_field_range(field, value, context)
    
    def _validate_projection_consistency(self, projections: List[Dict[str, Any]], projection_type: str):
        """Validate consistency across projection years."""
        if len(projections) < 2:
            return
        
        # Check for impossible changes between years
        for i in range(1, len(projections)):
            prev = projections[i-1]
            curr = projections[i]
            
            # Revenue shouldn't drop more than 50% in one year unless explained
            if 'revenue' in prev and 'revenue' in curr:
                if prev['revenue'] > 0:
                    change = (curr['revenue'] - prev['revenue']) / prev['revenue']
                    if change < -0.50:
                        self.validation_warnings.append(
                            f"Large revenue decline from Year {i} to {i+1}: {change:.1%}. "
                            "Please verify this is intentional."
                        )
    
    def _validate_projection_growth_rates(self, projections: List[Dict[str, Any]]):
        """Validate growth rates in projections."""
        for i in range(1, len(projections)):
            prev = projections[i-1]
            curr = projections[i]
            
            for field in ['revenue', 'free_cash_flow']:
                if field in prev and field in curr and prev[field] > 0:
                    growth_rate = (curr[field] - prev[field]) / prev[field]
                    
                    # Validate growth rate against reasonable ranges
                    field_range = self.FINANCIAL_RANGES.get('growth_rate', {})
                    if growth_rate < field_range.get('min', -1):
                        self.validation_errors.append(
                            f"Extreme negative growth in {field} (Year {i} to {i+1}): {growth_rate:.1%}"
                        )
                    elif growth_rate > field_range.get('max', 2):
                        self.validation_errors.append(
                            f"Extreme positive growth in {field} (Year {i} to {i+1}): {growth_rate:.1%}"
                        )
                    elif (growth_rate < field_range.get('typical_min', -0.2) or 
                          growth_rate > field_range.get('typical_max', 1.0)):
                        self.validation_warnings.append(
                            f"Unusual growth rate in {field} (Year {i} to {i+1}): {growth_rate:.1%}"
                        )
    
    def _validate_dcf_inputs(self, dcf_data: Dict[str, Any]):
        """Validate DCF-specific inputs."""
        # Discount rate validation
        if 'discount_rate' in dcf_data:
            self._validate_financial_ratio('discount_rate', dcf_data['discount_rate'])
        
        # Terminal growth rate validation
        if 'terminal_growth_rate' in dcf_data:
            self._validate_financial_ratio('terminal_growth_rate', dcf_data['terminal_growth_rate'])
        
        # Consistency check: terminal growth < discount rate
        discount_rate = dcf_data.get('discount_rate', 0)
        terminal_rate = dcf_data.get('terminal_growth_rate', 0)
        
        if discount_rate > 0 and terminal_rate >= discount_rate:
            self.validation_errors.append(
                f"Terminal growth rate ({terminal_rate:.1%}) must be less than discount rate ({discount_rate:.1%})"
            )
    
    def _validate_multiples_inputs(self, multiples_data: Dict[str, Any]):
        """Validate multiples valuation inputs."""
        # Validate individual multiples
        for multiple_name, value in multiples_data.items():
            if multiple_name.endswith('_multiple') or multiple_name in ['pe_ratio', 'pb_ratio']:
                self._validate_financial_ratio(multiple_name, value)
    
    def _validate_saas_valuation_inputs(self, saas_data: Dict[str, Any]):
        """Validate SaaS-specific valuation inputs."""
        # ARR multiple validation
        if 'arr_multiple' in saas_data:
            self._validate_financial_ratio('arr_multiple', saas_data['arr_multiple'])
        
        # SaaS metrics validation
        if 'churn_rate' in saas_data:
            self._validate_financial_ratio('churn_rate', saas_data['churn_rate'])
        
        if 'ltv_cac_ratio' in saas_data:
            self._validate_financial_ratio('ltv_cac_ratio', saas_data['ltv_cac_ratio'])
    
    def _validate_financial_ratio(
        self,
        ratio_name: str,
        value: Union[int, float],
        context: Optional[Dict[str, Any]] = None
    ):
        """Validate a single financial ratio."""
        if not isinstance(value, (int, float)):
            self.validation_errors.append(f"{ratio_name} must be a number, got {type(value)}")
            return
        
        # Get ranges for this ratio
        ranges = self.FINANCIAL_RANGES.get(ratio_name, {})
        
        if not ranges:
            # Generic validation for unknown ratios
            if abs(value) > 1e6:
                self.validation_warnings.append(f"Extreme value for {ratio_name}: {value}")
            return
        
        # Hard range validation
        min_val = ranges.get('min')
        max_val = ranges.get('max')
        
        if min_val is not None and value < min_val:
            raise FinancialRangeError(
                f"{ratio_name} below acceptable range",
                field_name=ratio_name,
                value=value,
                min_value=min_val
            )
        
        if max_val is not None and value > max_val:
            raise FinancialRangeError(
                f"{ratio_name} above acceptable range",
                field_name=ratio_name,
                value=value,
                max_value=max_val
            )
        
        # Typical range warnings
        typical_min = ranges.get('typical_min')
        typical_max = ranges.get('typical_max')
        
        if typical_min is not None and value < typical_min:
            self.validation_warnings.append(
                f"{ratio_name} below typical range: {value} (typical minimum: {typical_min})"
            )
        
        if typical_max is not None and value > typical_max:
            self.validation_warnings.append(
                f"{ratio_name} above typical range: {value} (typical maximum: {typical_max})"
            )
    
    def _validate_ratio_consistency(self, ratios: Dict[str, Union[int, float]]):
        """Validate consistency between different financial ratios."""
        # Example: High P/E ratio with low ROE might be inconsistent
        pe_ratio = ratios.get('pe_ratio')
        roe = ratios.get('roe')
        
        if pe_ratio and roe and pe_ratio > 25 and roe < 0.10:
            self.validation_warnings.append(
                f"High P/E ratio ({pe_ratio:.1f}) with low ROE ({roe:.1%}) may be inconsistent"
            )
        
        # Example: High debt-to-equity with low current ratio
        debt_equity = ratios.get('debt_to_equity')
        current_ratio = ratios.get('current_ratio')
        
        if debt_equity and current_ratio and debt_equity > 2.0 and current_ratio < 1.0:
            self.validation_warnings.append(
                f"High debt-to-equity ({debt_equity:.1f}) with low current ratio ({current_ratio:.1f}) indicates liquidity risk"
            )
    
    def _get_required_projection_fields(self, projection_type: str) -> List[str]:
        """Get required fields for different projection types."""
        base_fields = ['revenue']
        
        if projection_type.lower() == 'dcf':
            return base_fields + ['free_cash_flow']
        elif projection_type.lower() == 'income':
            return base_fields + ['expenses', 'net_income']
        elif projection_type.lower() == 'cash_flow':
            return ['operating_cash_flow', 'investing_cash_flow', 'financing_cash_flow']
        else:
            return base_fields
    
    def _validate_projection_field_range(self, field: str, value: Union[int, float], context: str):
        """Validate ranges for specific projection fields."""
        # Revenue should generally be positive
        if field == 'revenue' and value < 0:
            self.validation_errors.append(f"Negative revenue in {context}: {value}")
        
        # Free cash flow can be negative but extreme values should be flagged
        elif field == 'free_cash_flow' and abs(value) > 1e12:
            self.validation_warnings.append(f"Extreme free cash flow in {context}: {value}")
        
        # Expenses should generally be positive (representing costs)
        elif field == 'expenses' and value < 0:
            self.validation_warnings.append(f"Negative expenses in {context}: {value} (unusual but possible)")
        
        # Generic extreme value check
        if abs(value) > 1e15:  # $1 quadrillion
            self.validation_errors.append(f"Extreme value for {field} in {context}: {value}")