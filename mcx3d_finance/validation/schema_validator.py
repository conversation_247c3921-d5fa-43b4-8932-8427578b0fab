"""
Schema validation for the MCX3D financial system.

Provides JSON schema validation and data structure validation for API inputs and configuration.
"""

import json
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, List, Tuple, Optional, Union
from datetime import datetime

from mcx3d_finance.exceptions import (
    SchemaValidationError,
    ValidationError,
    DataTypeValidationError
)

logger = LoggerFactory.get_logger(__name__)


class SchemaValidator:
    """
    Validator for JSON schemas and data structure validation.
    
    Provides schema-based validation for API inputs, configuration files,
    and structured data throughout the MCX3D system.
    """
    
    # Schema definitions for different data types
    SCHEMAS = {
        'dcf_input': {
            'type': 'object',
            'required': ['company_name', 'projections', 'assumptions'],
            'properties': {
                'company_name': {'type': 'string', 'minLength': 1},
                'projections': {
                    'type': 'array',
                    'minItems': 1,
                    'items': {
                        'type': 'object',
                        'required': ['revenue', 'free_cash_flow'],
                        'properties': {
                            'revenue': {'type': 'number', 'minimum': 0},
                            'free_cash_flow': {'type': 'number'},
                            'year': {'type': 'integer', 'minimum': 1}
                        }
                    }
                },
                'assumptions': {
                    'type': 'object',
                    'required': ['discount_rate'],
                    'properties': {
                        'discount_rate': {'type': 'number', 'minimum': 0, 'maximum': 1},
                        'terminal_growth_rate': {'type': 'number', 'minimum': 0, 'maximum': 0.1},
                        'tax_rate': {'type': 'number', 'minimum': 0, 'maximum': 1}
                    }
                }
            }
        },
        
        'saas_metrics': {
            'type': 'object',
            'required': ['arr', 'mrr'],
            'properties': {
                'arr': {'type': 'number', 'minimum': 0},
                'mrr': {'type': 'number', 'minimum': 0},
                'customers': {'type': 'integer', 'minimum': 0},
                'churn_rate': {'type': 'number', 'minimum': 0, 'maximum': 1},
                'ltv_cac_ratio': {'type': 'number', 'minimum': 0},
                'nrr': {'type': 'number', 'minimum': 0}
            }
        },
        
        'financial_statement': {
            'type': 'object',
            'required': ['period_start', 'period_end', 'currency'],
            'properties': {
                'period_start': {'type': 'string', 'format': 'date-time'},
                'period_end': {'type': 'string', 'format': 'date-time'},
                'currency': {'type': 'string', 'pattern': '^[A-Z]{3}$'},
                'total_revenue': {'type': 'number'},
                'total_assets': {'type': 'number', 'minimum': 0},
                'total_liabilities': {'type': 'number', 'minimum': 0},
                'total_equity': {'type': 'number'}
            }
        },
        
        'multiples_input': {
            'type': 'object',
            'required': ['target_metrics', 'comparable_companies'],
            'properties': {
                'target_metrics': {
                    'type': 'object',
                    'required': ['revenue'],
                    'properties': {
                        'revenue': {'type': 'number', 'minimum': 0},
                        'ebitda': {'type': 'number'},
                        'net_income': {'type': 'number'}
                    }
                },
                'comparable_companies': {
                    'type': 'array',
                    'minItems': 1,
                    'items': {
                        'type': 'object',
                        'required': ['name', 'metrics'],
                        'properties': {
                            'name': {'type': 'string', 'minLength': 1},
                            'metrics': {
                                'type': 'object',
                                'properties': {
                                    'pe_ratio': {'type': 'number', 'minimum': 0},
                                    'ev_revenue': {'type': 'number', 'minimum': 0},
                                    'ev_ebitda': {'type': 'number', 'minimum': 0}
                                }
                            }
                        }
                    }
                }
            }
        },
        
        'report_config': {
            'type': 'object',
            'required': ['output_format'],
            'properties': {
                'output_format': {'type': 'string', 'enum': ['pdf', 'excel', 'html']},
                'include_charts': {'type': 'boolean'},
                'chart_style': {'type': 'string', 'enum': ['default', 'minimal', 'detailed']},
                'page_orientation': {'type': 'string', 'enum': ['portrait', 'landscape']},
                'custom_branding': {
                    'type': 'object',
                    'properties': {
                        'logo_path': {'type': 'string'},
                        'company_name': {'type': 'string'},
                        'colors': {
                            'type': 'object',
                            'properties': {
                                'primary': {'type': 'string', 'pattern': '^#[0-9A-Fa-f]{6}$'},
                                'secondary': {'type': 'string', 'pattern': '^#[0-9A-Fa-f]{6}$'}
                            }
                        }
                    }
                }
            }
        }
    }
    
    def __init__(self):
        """Initialize the schema validator."""
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_data_against_schema(
        self,
        data: Dict[str, Any],
        schema_name: str,
        custom_schema: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate data against a predefined or custom schema.
        
        Args:
            data: Data to validate
            schema_name: Name of predefined schema or 'custom'
            custom_schema: Custom schema definition (if schema_name is 'custom')
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            # Get schema
            if schema_name == 'custom':
                if not custom_schema:
                    raise ValidationError(
                        "Custom schema must be provided when schema_name is 'custom'",
                        field_name="schema_name"
                    )
                schema = custom_schema
            else:
                schema = self.SCHEMAS.get(schema_name)
                if not schema:
                    raise ValidationError(
                        f"Unknown schema name: {schema_name}",
                        field_name="schema_name"
                    )
            
            # Validate data structure
            self._validate_object(data, schema, "root")
            
            is_valid = len(self.validation_errors) == 0
            
            if self.validation_errors:
                logger.warning(f"Schema validation failed for {schema_name} with {len(self.validation_errors)} errors")
                
                detailed_errors = []
                for error in self.validation_errors:
                    detailed_errors.append({
                        'field': error.get('field', 'unknown'),
                        'type': error.get('type', 'validation'),
                        'message': error.get('message', 'Validation failed')
                    })
                
                raise SchemaValidationError(
                    f"Schema validation failed for {schema_name}",
                    schema_name=schema_name,
                    validation_errors=detailed_errors
                )
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            if isinstance(e, (SchemaValidationError, ValidationError)):
                raise
            raise ValidationError(
                f"Schema validation process failed: {e}",
                field_name="schema_validation"
            )
    
    def validate_json_string(
        self,
        json_string: str,
        schema_name: str
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate a JSON string against a schema.
        
        Args:
            json_string: JSON string to validate
            schema_name: Name of schema to validate against
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            # Parse JSON
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError as e:
                raise ValidationError(
                    f"Invalid JSON format: {e}",
                    field_name="json_format"
                )
            
            return self.validate_data_against_schema(data, schema_name)
            
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            raise ValidationError(
                f"JSON validation failed: {e}",
                field_name="json_validation"
            )
    
    def _validate_object(self, data: Any, schema: Dict[str, Any], path: str):
        """Validate an object against a schema."""
        schema_type = schema.get('type')
        
        if schema_type == 'object':
            self._validate_object_type(data, schema, path)
        elif schema_type == 'array':
            self._validate_array_type(data, schema, path)
        elif schema_type in ['string', 'number', 'integer', 'boolean']:
            self._validate_primitive_type(data, schema, path)
        else:
            self.validation_errors.append({
                'field': path,
                'type': 'schema_error',
                'message': f"Unknown schema type: {schema_type}"
            })
    
    def _validate_object_type(self, data: Any, schema: Dict[str, Any], path: str):
        """Validate object type and its properties."""
        if not isinstance(data, dict):
            self.validation_errors.append({
                'field': path,
                'type': 'type_error',
                'message': f"Expected object, got {type(data).__name__}"
            })
            return
        
        # Check required properties
        required = schema.get('required', [])
        for req_prop in required:
            if req_prop not in data or data[req_prop] is None:
                self.validation_errors.append({
                    'field': f"{path}.{req_prop}",
                    'type': 'required',
                    'message': f"Required property '{req_prop}' is missing"
                })
        
        # Validate properties
        properties = schema.get('properties', {})
        for prop_name, value in data.items():
            prop_schema = properties.get(prop_name)
            if prop_schema:
                self._validate_object(value, prop_schema, f"{path}.{prop_name}")
            else:
                self.validation_warnings.append(
                    f"Unknown property '{prop_name}' at {path}"
                )
        
        # Check additional properties
        if schema.get('additionalProperties') is False:
            extra_props = set(data.keys()) - set(properties.keys())
            if extra_props:
                self.validation_errors.append({
                    'field': path,
                    'type': 'additional_properties',
                    'message': f"Additional properties not allowed: {list(extra_props)}"
                })
    
    def _validate_array_type(self, data: Any, schema: Dict[str, Any], path: str):
        """Validate array type and its items."""
        if not isinstance(data, list):
            self.validation_errors.append({
                'field': path,
                'type': 'type_error',
                'message': f"Expected array, got {type(data).__name__}"
            })
            return
        
        # Check length constraints
        min_items = schema.get('minItems')
        max_items = schema.get('maxItems')
        
        if min_items is not None and len(data) < min_items:
            self.validation_errors.append({
                'field': path,
                'type': 'min_items',
                'message': f"Array must have at least {min_items} items, got {len(data)}"
            })
        
        if max_items is not None and len(data) > max_items:
            self.validation_errors.append({
                'field': path,
                'type': 'max_items',
                'message': f"Array must have at most {max_items} items, got {len(data)}"
            })
        
        # Validate items
        items_schema = schema.get('items')
        if items_schema:
            for i, item in enumerate(data):
                self._validate_object(item, items_schema, f"{path}[{i}]")
    
    def _validate_primitive_type(self, data: Any, schema: Dict[str, Any], path: str):
        """Validate primitive types (string, number, integer, boolean)."""
        schema_type = schema.get('type')
        
        # Type validation
        if schema_type == 'string':
            if not isinstance(data, str):
                self.validation_errors.append({
                    'field': path,
                    'type': 'type_error',
                    'message': f"Expected string, got {type(data).__name__}"
                })
                return
            self._validate_string_constraints(data, schema, path)
            
        elif schema_type == 'number':
            if not isinstance(data, (int, float)):
                self.validation_errors.append({
                    'field': path,
                    'type': 'type_error',
                    'message': f"Expected number, got {type(data).__name__}"
                })
                return
            self._validate_number_constraints(data, schema, path)
            
        elif schema_type == 'integer':
            if not isinstance(data, int) or isinstance(data, bool):
                self.validation_errors.append({
                    'field': path,
                    'type': 'type_error',
                    'message': f"Expected integer, got {type(data).__name__}"
                })
                return
            self._validate_number_constraints(data, schema, path)
            
        elif schema_type == 'boolean':
            if not isinstance(data, bool):
                self.validation_errors.append({
                    'field': path,
                    'type': 'type_error',
                    'message': f"Expected boolean, got {type(data).__name__}"
                })
                return
    
    def _validate_string_constraints(self, data: str, schema: Dict[str, Any], path: str):
        """Validate string-specific constraints."""
        # Length constraints
        min_length = schema.get('minLength')
        max_length = schema.get('maxLength')
        
        if min_length is not None and len(data) < min_length:
            self.validation_errors.append({
                'field': path,
                'type': 'min_length',
                'message': f"String must be at least {min_length} characters, got {len(data)}"
            })
        
        if max_length is not None and len(data) > max_length:
            self.validation_errors.append({
                'field': path,
                'type': 'max_length',
                'message': f"String must be at most {max_length} characters, got {len(data)}"
            })
        
        # Pattern validation
        pattern = schema.get('pattern')
        if pattern:
            import re
            try:
                if not re.match(pattern, data):
                    self.validation_errors.append({
                        'field': path,
                        'type': 'pattern',
                        'message': f"String does not match required pattern: {pattern}"
                    })
            except re.error as e:
                self.validation_errors.append({
                    'field': path,
                    'type': 'pattern_error',
                    'message': f"Invalid regex pattern: {e}"
                })
        
        # Enum validation
        enum = schema.get('enum')
        if enum and data not in enum:
            self.validation_errors.append({
                'field': path,
                'type': 'enum',
                'message': f"Value must be one of {enum}, got '{data}'"
            })
        
        # Format validation
        format_type = schema.get('format')
        if format_type:
            self._validate_string_format(data, format_type, path)
    
    def _validate_number_constraints(self, data: Union[int, float], schema: Dict[str, Any], path: str):
        """Validate number-specific constraints."""
        # Range constraints
        minimum = schema.get('minimum')
        maximum = schema.get('maximum')
        exclusive_minimum = schema.get('exclusiveMinimum')
        exclusive_maximum = schema.get('exclusiveMaximum')
        
        if minimum is not None and data < minimum:
            self.validation_errors.append({
                'field': path,
                'type': 'minimum',
                'message': f"Value must be >= {minimum}, got {data}"
            })
        
        if maximum is not None and data > maximum:
            self.validation_errors.append({
                'field': path,
                'type': 'maximum',
                'message': f"Value must be <= {maximum}, got {data}"
            })
        
        if exclusive_minimum is not None and data <= exclusive_minimum:
            self.validation_errors.append({
                'field': path,
                'type': 'exclusive_minimum',
                'message': f"Value must be > {exclusive_minimum}, got {data}"
            })
        
        if exclusive_maximum is not None and data >= exclusive_maximum:
            self.validation_errors.append({
                'field': path,
                'type': 'exclusive_maximum',
                'message': f"Value must be < {exclusive_maximum}, got {data}"
            })
        
        # Multiple of
        multiple_of = schema.get('multipleOf')
        if multiple_of is not None and data % multiple_of != 0:
            self.validation_errors.append({
                'field': path,
                'type': 'multiple_of',
                'message': f"Value must be a multiple of {multiple_of}, got {data}"
            })
    
    def _validate_string_format(self, data: str, format_type: str, path: str):
        """Validate string format constraints."""
        if format_type == 'date-time':
            try:
                # Try to parse as ISO format
                datetime.fromisoformat(data.replace('Z', '+00:00'))
            except ValueError:
                self.validation_errors.append({
                    'field': path,
                    'type': 'format',
                    'message': f"Invalid date-time format: {data}"
                })
        
        elif format_type == 'email':
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, data):
                self.validation_errors.append({
                    'field': path,
                    'type': 'format',
                    'message': f"Invalid email format: {data}"
                })
        
        elif format_type == 'uri':
            try:
                from urllib.parse import urlparse
                result = urlparse(data)
                if not all([result.scheme, result.netloc]):
                    raise ValueError("Invalid URI format")
            except Exception:
                self.validation_errors.append({
                    'field': path,
                    'type': 'format',
                    'message': f"Invalid URI format: {data}"
                })
        
        else:
            self.validation_warnings.append(
                f"Unknown string format type '{format_type}' at {path}"
            )