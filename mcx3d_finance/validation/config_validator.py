"""
Configuration validation for the MCX3D financial system.

Provides validation for system configuration, user settings, and application parameters.
"""

import json
from mcx3d_finance.core.logging_factory import LoggerFactory
import os
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional, Union
from urllib.parse import urlparse

from mcx3d_finance.exceptions import (
    ConfigurationValidationError,
    ValidationError,
    MCX3DConfigurationError
)

logger = LoggerFactory.get_logger(__name__)


class ConfigurationValidator:
    """
    Validator for system configuration and user settings.
    
    Ensures configuration values are valid, secure, and consistent.
    """
    
    # Required configuration sections and their required fields
    REQUIRED_SECTIONS = {
        'database': ['url'],
        'reporting': ['output_dir', 'default_format'],
        'xero': ['client_id', 'client_secret'],
    }
    
    # Configuration field types and validation rules
    FIELD_VALIDATIONS = {
        'database': {
            'url': {'type': str, 'required': True, 'validation': 'database_url'},
            'pool_size': {'type': int, 'required': False, 'min': 1, 'max': 100},
            'timeout': {'type': int, 'required': False, 'min': 1, 'max': 300},
        },
        'redis': {
            'url': {'type': str, 'required': False, 'validation': 'redis_url'},
            'password': {'type': str, 'required': False, 'sensitive': True},
            'db': {'type': int, 'required': False, 'min': 0, 'max': 15},
        },
        'xero': {
            'client_id': {'type': str, 'required': True, 'min_length': 10},
            'client_secret': {'type': str, 'required': True, 'sensitive': True, 'min_length': 20},
            'redirect_uri': {'type': str, 'required': True, 'validation': 'url'},
            'webhook_key': {'type': str, 'required': False, 'sensitive': True},
        },
        'reporting': {
            'output_dir': {'type': str, 'required': True, 'validation': 'directory_path'},
            'default_format': {'type': str, 'required': True, 'choices': ['pdf', 'excel', 'html']},
            'max_file_size_mb': {'type': int, 'required': False, 'min': 1, 'max': 1000},
            'enable_charts': {'type': bool, 'required': False},
            'chart_dpi': {'type': int, 'required': False, 'min': 72, 'max': 600},
        },
        'security': {
            'secret_key': {'type': str, 'required': True, 'sensitive': True, 'min_length': 32},
            'token_expiry_minutes': {'type': int, 'required': False, 'min': 15, 'max': 1440},
            'max_login_attempts': {'type': int, 'required': False, 'min': 3, 'max': 10},
        },
        'logging': {
            'level': {'type': str, 'required': False, 'choices': ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']},
            'format': {'type': str, 'required': False},
            'log_file': {'type': str, 'required': False, 'validation': 'file_path'},
            'max_file_size_mb': {'type': int, 'required': False, 'min': 1, 'max': 100},
        },
        'performance': {
            'memory_limit_mb': {'type': int, 'required': False, 'min': 128, 'max': 8192},
            'timeout_seconds': {'type': int, 'required': False, 'min': 30, 'max': 300},
            'parallel_workers': {'type': int, 'required': False, 'min': 1, 'max': 16},
        }
    }
    
    def __init__(self):
        """Initialize the configuration validator."""
        self.validation_errors = []
        self.validation_warnings = []
        self.sensitive_fields = set()
        
        # Collect sensitive field names
        for section, fields in self.FIELD_VALIDATIONS.items():
            for field, rules in fields.items():
                if rules.get('sensitive', False):
                    self.sensitive_fields.add(f"{section}.{field}")
    
    def validate_configuration(
        self,
        config: Dict[str, Any],
        config_source: str = "application"
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate complete application configuration.
        
        Args:
            config: Configuration dictionary
            config_source: Source of configuration (file, env, etc.)
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            # Validate required sections
            self._validate_required_sections(config)
            
            # Validate each section
            for section_name, section_config in config.items():
                if section_name in self.FIELD_VALIDATIONS:
                    self._validate_section(section_name, section_config)
                else:
                    self.validation_warnings.append(f"Unknown configuration section: {section_name}")
            
            # Cross-section consistency checks
            self._validate_configuration_consistency(config)
            
            # Security validations
            self._validate_security_configuration(config)
            
            # Environment-specific validations
            self._validate_environment_specific_config(config)
            
            is_valid = len(self.validation_errors) == 0
            
            if self.validation_errors:
                logger.error(f"Configuration validation failed with {len(self.validation_errors)} errors")
                # Don't log sensitive configuration data
                sanitized_errors = self._sanitize_error_messages(self.validation_errors)
                logger.error(f"Configuration errors: {sanitized_errors}")
            
            if self.validation_warnings:
                logger.warning(f"Configuration validation completed with {len(self.validation_warnings)} warnings")
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            raise ConfigurationValidationError(
                f"Configuration validation process failed: {e}",
                config_section=config_source
            )
    
    def validate_file_configuration(self, config_file_path: str) -> Tuple[bool, List[str], List[str]]:
        """
        Validate configuration from a file.
        
        Args:
            config_file_path: Path to configuration file
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            if not os.path.exists(config_file_path):
                raise ConfigurationValidationError(
                    f"Configuration file not found: {config_file_path}",
                    config_section="file_system",
                    config_parameter="config_file_path"
                )
            
            # Validate file permissions
            if not os.access(config_file_path, os.R_OK):
                raise ConfigurationValidationError(
                    f"Configuration file is not readable: {config_file_path}",
                    config_section="file_system",
                    config_parameter="file_permissions"
                )
            
            # Load and parse configuration file
            try:
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    if config_file_path.lower().endswith('.json'):
                        config = json.load(f)
                    elif config_file_path.lower().endswith(('.yml', '.yaml')):
                        import yaml
                        config = yaml.safe_load(f)
                    else:
                        raise ConfigurationValidationError(
                            f"Unsupported configuration file format: {config_file_path}",
                            config_section="file_format",
                            expected_values=['.json', '.yml', '.yaml']
                        )
            except (json.JSONDecodeError, yaml.YAMLError) as e:
                raise ConfigurationValidationError(
                    f"Invalid configuration file format: {e}",
                    config_section="file_format",
                    config_parameter="syntax"
                )
            
            return self.validate_configuration(config, f"file:{config_file_path}")
            
        except Exception as e:
            if isinstance(e, ConfigurationValidationError):
                raise
            raise ConfigurationValidationError(
                f"Failed to validate configuration file: {e}",
                config_section="file_validation"
            )
    
    def validate_environment_configuration(self, env_prefix: str = "MCX3D_") -> Tuple[bool, List[str], List[str]]:
        """
        Validate configuration from environment variables.
        
        Args:
            env_prefix: Prefix for environment variables
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            config = {}
            
            # Extract configuration from environment variables
            for key, value in os.environ.items():
                if key.startswith(env_prefix):
                    # Convert ENV_VAR_NAME to nested config structure
                    config_key = key[len(env_prefix):].lower()
                    if '_' in config_key:
                        section, field = config_key.split('_', 1)
                        if section not in config:
                            config[section] = {}
                        config[section][field] = value
                    else:
                        config[config_key] = value
            
            return self.validate_configuration(config, "environment")
            
        except Exception as e:
            raise ConfigurationValidationError(
                f"Failed to validate environment configuration: {e}",
                config_section="environment"
            )
    
    def _validate_required_sections(self, config: Dict[str, Any]):
        """Validate that required configuration sections are present."""
        for section_name, required_fields in self.REQUIRED_SECTIONS.items():
            if section_name not in config:
                self.validation_errors.append(f"Missing required configuration section: {section_name}")
                continue
            
            section_config = config[section_name]
            if not isinstance(section_config, dict):
                self.validation_errors.append(f"Configuration section '{section_name}' must be an object/dictionary")
                continue
            
            # Check required fields in section
            for field_name in required_fields:
                if field_name not in section_config or section_config[field_name] is None:
                    self.validation_errors.append(f"Missing required field: {section_name}.{field_name}")
    
    def _validate_section(self, section_name: str, section_config: Dict[str, Any]):
        """Validate a configuration section."""
        section_validations = self.FIELD_VALIDATIONS.get(section_name, {})
        
        for field_name, field_value in section_config.items():
            field_validation = section_validations.get(field_name)
            
            if not field_validation:
                self.validation_warnings.append(f"Unknown configuration field: {section_name}.{field_name}")
                continue
            
            self._validate_field(section_name, field_name, field_value, field_validation)
    
    def _validate_field(
        self,
        section_name: str,
        field_name: str,
        field_value: Any,
        field_validation: Dict[str, Any]
    ):
        """Validate a single configuration field."""
        field_path = f"{section_name}.{field_name}"
        
        # Required field check
        if field_validation.get('required', False) and field_value is None:
            self.validation_errors.append(f"Required field is missing: {field_path}")
            return
        
        if field_value is None:
            return  # Optional field not provided
        
        # Type validation
        expected_type = field_validation.get('type')
        if expected_type and not isinstance(field_value, expected_type):
            self.validation_errors.append(
                f"Invalid type for {field_path}: expected {expected_type.__name__}, got {type(field_value).__name__}"
            )
            return
        
        # Choice validation
        choices = field_validation.get('choices')
        if choices and field_value not in choices:
            self.validation_errors.append(
                f"Invalid value for {field_path}: {field_value}. Must be one of: {choices}"
            )
            return
        
        # Range validation for numeric fields
        if isinstance(field_value, (int, float)):
            min_val = field_validation.get('min')
            max_val = field_validation.get('max')
            
            if min_val is not None and field_value < min_val:
                self.validation_errors.append(f"{field_path} must be at least {min_val}, got {field_value}")
            
            if max_val is not None and field_value > max_val:
                self.validation_errors.append(f"{field_path} must be at most {max_val}, got {field_value}")
        
        # String length validation
        if isinstance(field_value, str):
            min_length = field_validation.get('min_length')
            max_length = field_validation.get('max_length')
            
            if min_length is not None and len(field_value) < min_length:
                self.validation_errors.append(f"{field_path} must be at least {min_length} characters long")
            
            if max_length is not None and len(field_value) > max_length:
                self.validation_errors.append(f"{field_path} must be at most {max_length} characters long")
        
        # Custom validation
        validation_type = field_validation.get('validation')
        if validation_type:
            self._apply_custom_validation(field_path, field_value, validation_type)
    
    def _apply_custom_validation(self, field_path: str, field_value: str, validation_type: str):
        """Apply custom validation rules."""
        try:
            if validation_type == 'database_url':
                self._validate_database_url(field_path, field_value)
            elif validation_type == 'redis_url':
                self._validate_redis_url(field_path, field_value)
            elif validation_type == 'url':
                self._validate_url(field_path, field_value)
            elif validation_type == 'directory_path':
                self._validate_directory_path(field_path, field_value)
            elif validation_type == 'file_path':
                self._validate_file_path(field_path, field_value)
        except Exception as e:
            self.validation_errors.append(f"Validation error for {field_path}: {e}")
    
    def _validate_database_url(self, field_path: str, url: str):
        """Validate database URL format."""
        try:
            parsed = urlparse(url)
            if not parsed.scheme:
                raise ValueError("Missing database scheme")
            if parsed.scheme not in ['postgresql', 'postgres', 'mysql', 'sqlite']:
                self.validation_warnings.append(f"Unusual database scheme in {field_path}: {parsed.scheme}")
            if not parsed.netloc and parsed.scheme != 'sqlite':
                raise ValueError("Missing database host")
        except Exception as e:
            self.validation_errors.append(f"Invalid database URL format in {field_path}: {e}")
    
    def _validate_redis_url(self, field_path: str, url: str):
        """Validate Redis URL format."""
        try:
            parsed = urlparse(url)
            if parsed.scheme not in ['redis', 'rediss']:
                raise ValueError(f"Invalid Redis scheme: {parsed.scheme}")
        except Exception as e:
            self.validation_errors.append(f"Invalid Redis URL format in {field_path}: {e}")
    
    def _validate_url(self, field_path: str, url: str):
        """Validate general URL format."""
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid URL format")
            if parsed.scheme not in ['http', 'https']:
                self.validation_warnings.append(f"Unusual URL scheme in {field_path}: {parsed.scheme}")
        except Exception as e:
            self.validation_errors.append(f"Invalid URL format in {field_path}: {e}")
    
    def _validate_directory_path(self, field_path: str, path: str):
        """Validate directory path."""
        try:
            path_obj = Path(path)
            if not path_obj.is_absolute():
                self.validation_warnings.append(f"Relative path in {field_path}: {path}")
            
            # Check if parent directory exists and is writable
            parent = path_obj.parent
            if parent.exists():
                if not os.access(str(parent), os.W_OK):
                    self.validation_errors.append(f"Parent directory not writable for {field_path}: {parent}")
            else:
                self.validation_warnings.append(f"Parent directory does not exist for {field_path}: {parent}")
        except Exception as e:
            self.validation_errors.append(f"Invalid directory path in {field_path}: {e}")
    
    def _validate_file_path(self, field_path: str, path: str):
        """Validate file path."""
        try:
            path_obj = Path(path)
            if path_obj.exists():
                if not os.access(str(path_obj), os.R_OK):
                    self.validation_errors.append(f"File not readable at {field_path}: {path}")
            else:
                # Check if parent directory exists
                parent = path_obj.parent
                if not parent.exists():
                    self.validation_errors.append(f"Parent directory does not exist for {field_path}: {parent}")
                elif not os.access(str(parent), os.W_OK):
                    self.validation_errors.append(f"Cannot create file at {field_path}: parent directory not writable")
        except Exception as e:
            self.validation_errors.append(f"Invalid file path in {field_path}: {e}")
    
    def _validate_configuration_consistency(self, config: Dict[str, Any]):
        """Validate consistency between configuration sections."""
        # Example: If Redis is configured, check it's used in session configuration
        redis_config = config.get('redis')
        session_config = config.get('session', {})
        
        if redis_config and session_config.get('storage') == 'memory':
            self.validation_warnings.append(
                "Redis is configured but session storage is set to memory. "
                "Consider using Redis for session storage in production."
            )
        
        # Example: Performance settings consistency
        performance = config.get('performance', {})
        memory_limit = performance.get('memory_limit_mb', 0)
        parallel_workers = performance.get('parallel_workers', 1)
        
        if memory_limit > 0 and parallel_workers > 1:
            memory_per_worker = memory_limit / parallel_workers
            if memory_per_worker < 128:  # Less than 128MB per worker
                self.validation_warnings.append(
                    f"Low memory per worker ({memory_per_worker:.0f}MB). "
                    "Consider reducing parallel workers or increasing memory limit."
                )
    
    def _validate_security_configuration(self, config: Dict[str, Any]):
        """Validate security-related configuration."""
        security = config.get('security', {})
        
        # Secret key validation
        secret_key = security.get('secret_key', '')
        if secret_key:
            if len(secret_key) < 32:
                self.validation_errors.append("Security secret_key must be at least 32 characters long")
            elif secret_key in ['changeme', 'secret', 'password', '12345']:
                self.validation_errors.append("Security secret_key appears to be a default/weak value")
        
        # Check for sensitive data in logs
        logging_config = config.get('logging', {})
        log_level = logging_config.get('level', 'INFO')
        
        if log_level == 'DEBUG':
            self.validation_warnings.append(
                "Debug logging is enabled. Ensure this is disabled in production to prevent sensitive data leakage."
            )
    
    def _sanitize_error_messages(self, error_messages: List[str]) -> List[str]:
        """Remove sensitive information from error messages."""
        sanitized = []
        
        for message in error_messages:
            # Remove values for sensitive fields
            sanitized_message = message
            for sensitive_field in self.sensitive_fields:
                if sensitive_field in message:
                    # Replace the value part with [REDACTED]
                    sanitized_message = message.split(':')[0] + ': [REDACTED]'
                    break
            sanitized.append(sanitized_message)
        
        return sanitized
    
    def _validate_environment_specific_config(self, config: Dict[str, Any]):
        """Validate environment-specific configuration requirements."""
        environment = config.get('environment', 'production')
        
        # Production-specific validations
        if environment == 'production':
            security_config = config.get('security', {})
            
            # Debug mode must be disabled in production
            if security_config.get('debug', False):
                self.validation_errors.append("Debug mode must be disabled in production")
            
            # HTTPS enforcement warnings
            ssl_config = config.get('ssl', {})
            if not ssl_config.get('enforce_https', True):
                self.validation_warnings.append("HTTPS enforcement should be enabled in production")
            
            # Monitoring should be enabled
            monitoring_config = config.get('monitoring', {})
            if not monitoring_config.get('enable_alerting', True):
                self.validation_warnings.append("Alerting should be enabled in production")
        
        # Development-specific warnings
        elif environment == 'development':
            # Warn about insecure settings in development
            security_config = config.get('security', {})
            if security_config.get('access_token_expire_minutes', 15) > 60:
                self.validation_warnings.append("Long token expiration times detected in development")
        
        # Staging should mirror production security
        elif environment == 'staging':
            security_config = config.get('security', {})
            if security_config.get('debug', False):
                self.validation_warnings.append("Debug mode should be disabled in staging to mirror production")
    
    def _validate_secret_key(self, field_path: str, secret_key: str):
        """Validate secret key security requirements."""
        if len(secret_key) < 32:
            raise ValueError(f"Secret key at {field_path} must be at least 32 characters long")
        
        # Check for common insecure patterns
        insecure_patterns = ['changeme', 'secret', 'password', '12345', 'test', 'default']
        if any(pattern in secret_key.lower() for pattern in insecure_patterns):
            raise ValueError(f"Insecure secret key detected at {field_path}")
    
    def _validate_encryption_key(self, field_path: str, encryption_key: str):
        """Validate Fernet encryption key format."""
        try:
            from cryptography.fernet import Fernet
            # This will raise an exception if the key is invalid
            Fernet(encryption_key.encode())
        except Exception:
            raise ValueError(f"Invalid Fernet encryption key at {field_path}")
    
    def _validate_xero_client_id(self, field_path: str, client_id: str):
        """Validate Xero client ID."""
        placeholder_patterns = ['YOUR_CLIENT_ID', 'CHANGE_ME', 'PLACEHOLDER']
        if any(pattern in client_id.upper() for pattern in placeholder_patterns):
            raise ValueError(f"Placeholder value detected for Xero client ID at {field_path}")
    
    def _validate_xero_client_secret(self, field_path: str, client_secret: str):
        """Validate Xero client secret."""
        placeholder_patterns = ['YOUR_CLIENT_SECRET', 'CHANGE_ME', 'PLACEHOLDER']
        if any(pattern in client_secret.upper() for pattern in placeholder_patterns):
            raise ValueError(f"Placeholder value detected for Xero client secret at {field_path}")
    
    def _validate_cors_origins(self, field_path: str, origins: list):
        """Validate CORS origins configuration."""
        for origin in origins:
            if origin == '*':
                self.validation_warnings.append(f"Wildcard CORS origin detected at {field_path}. This should be restricted in production.")
            elif not origin.startswith(('http://', 'https://')):
                self.validation_errors.append(f"Invalid CORS origin format at {field_path}: {origin}")