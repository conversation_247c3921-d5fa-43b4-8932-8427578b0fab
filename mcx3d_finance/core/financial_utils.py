"""
Centralized financial utility functions for the MCX3D Financial Platform.

This module provides common financial calculations and utilities that are
used across multiple modules, eliminating code duplication.
"""

from decimal import Decimal, ROUND_HALF_UP
from typing import Union, Optional, Dict, Any
from datetime import datetime, timedelta
import numpy as np

from mcx3d_finance.core.logging_factory import LoggerFactory

logger = LoggerFactory.get_logger(__name__, domain='core')


class FinancialUtils:
    """Utility class for common financial calculations and operations."""
    
    # Standard precision for currency calculations
    CURRENCY_PRECISION = Decimal('0.01')
    PERCENTAGE_PRECISION = Decimal('0.0001')  # 0.01% precision
    
    @classmethod
    def round_currency(cls, value: Union[float, Decimal, int]) -> Decimal:
        """
        Round currency values to 2 decimal places using banker's rounding.
        
        Args:
            value: The value to round
            
        Returns:
            Decimal rounded to 2 decimal places
        """
        try:
            # Convert to Decimal if not already
            if isinstance(value, (int, float)):
                decimal_value = Decimal(str(value))
            else:
                decimal_value = value
                
            return decimal_value.quantize(cls.CURRENCY_PRECISION, rounding=ROUND_HALF_UP)
        except Exception as e:
            logger.error(f"Error rounding currency value {value}: {e}")
            raise ValueError(f"Invalid currency value: {value}")
    
    @classmethod
    def round_percentage(cls, value: Union[float, Decimal]) -> Decimal:
        """
        Round percentage values to 4 decimal places (0.01% precision).
        
        Args:
            value: The percentage value to round
            
        Returns:
            Decimal rounded to 4 decimal places
        """
        try:
            if isinstance(value, float):
                decimal_value = Decimal(str(value))
            else:
                decimal_value = value
                
            return decimal_value.quantize(cls.PERCENTAGE_PRECISION, rounding=ROUND_HALF_UP)
        except Exception as e:
            logger.error(f"Error rounding percentage value {value}: {e}")
            raise ValueError(f"Invalid percentage value: {value}")
    
    @staticmethod
    def calculate_percentage_change(
        current: Union[float, Decimal], 
        previous: Union[float, Decimal]
    ) -> Optional[float]:
        """
        Calculate percentage change between two values.
        
        Args:
            current: Current value
            previous: Previous value
            
        Returns:
            Percentage change as a float, or None if previous is 0
        """
        try:
            current_decimal = Decimal(str(current))
            previous_decimal = Decimal(str(previous))
            
            if previous_decimal == 0:
                return None
                
            change = ((current_decimal - previous_decimal) / previous_decimal) * 100
            return float(change)
        except Exception as e:
            logger.error(f"Error calculating percentage change: {e}")
            return None
    
    @staticmethod
    def calculate_growth_rate(
        start_value: Union[float, Decimal],
        end_value: Union[float, Decimal],
        periods: int
    ) -> Optional[float]:
        """
        Calculate compound annual growth rate (CAGR).
        
        Args:
            start_value: Starting value
            end_value: Ending value
            periods: Number of periods (typically years)
            
        Returns:
            Growth rate as a percentage (float)
        """
        try:
            if start_value <= 0 or end_value <= 0 or periods <= 0:
                return None
                
            # CAGR = (End Value / Start Value)^(1/periods) - 1
            growth_rate = ((float(end_value) / float(start_value)) ** (1 / periods) - 1) * 100
            return growth_rate
        except Exception as e:
            logger.error(f"Error calculating growth rate: {e}")
            return None
    
    @staticmethod
    def calculate_margin(
        revenue: Union[float, Decimal],
        cost: Union[float, Decimal]
    ) -> Optional[float]:
        """
        Calculate margin percentage.
        
        Args:
            revenue: Revenue amount
            cost: Cost amount
            
        Returns:
            Margin percentage as a float
        """
        try:
            revenue_decimal = Decimal(str(revenue))
            cost_decimal = Decimal(str(cost))
            
            if revenue_decimal == 0:
                return None
                
            margin = ((revenue_decimal - cost_decimal) / revenue_decimal) * 100
            return float(margin)
        except Exception as e:
            logger.error(f"Error calculating margin: {e}")
            return None
    
    @staticmethod
    def calculate_days_between(
        start_date: datetime,
        end_date: datetime,
        business_days_only: bool = False
    ) -> int:
        """
        Calculate the number of days between two dates.
        
        Args:
            start_date: Start date
            end_date: End date
            business_days_only: If True, only count business days (Mon-Fri)
            
        Returns:
            Number of days between dates
        """
        if business_days_only:
            # Calculate business days
            days = 0
            current = start_date
            while current <= end_date:
                if current.weekday() < 5:  # Monday = 0, Friday = 4
                    days += 1
                current += timedelta(days=1)
            return days
        else:
            return (end_date - start_date).days
    
    @staticmethod
    def format_currency(
        amount: Union[float, Decimal],
        currency: str = "GBP",
        include_symbol: bool = True
    ) -> str:
        """
        Format currency amount for display.
        
        Args:
            amount: Amount to format
            currency: Currency code (default: GBP)
            include_symbol: Whether to include currency symbol
            
        Returns:
            Formatted currency string
        """
        currency_symbols = {
            "GBP": "£",
            "USD": "$",
            "EUR": "€",
            "JPY": "¥",
            "AUD": "A$",
            "CAD": "C$",
            "CHF": "Fr.",
            "CNY": "¥",
            "SEK": "kr",
            "NZD": "NZ$"
        }
        
        # Round to currency precision
        rounded_amount = FinancialUtils.round_currency(amount)
        
        # Format with thousands separator
        formatted = f"{rounded_amount:,.2f}"
        
        if include_symbol and currency in currency_symbols:
            return f"{currency_symbols[currency]}{formatted}"
        else:
            return f"{formatted} {currency}"
    
    @staticmethod
    def calculate_npv(
        cash_flows: list,
        discount_rate: float,
        initial_investment: Optional[float] = None
    ) -> float:
        """
        Calculate Net Present Value (NPV) of cash flows.
        
        Args:
            cash_flows: List of cash flows by period
            discount_rate: Discount rate (as decimal, e.g., 0.1 for 10%)
            initial_investment: Initial investment (if not included in cash_flows)
            
        Returns:
            NPV as a float
        """
        try:
            if initial_investment is not None:
                cash_flows = [-initial_investment] + cash_flows
                
            return float(np.npv(discount_rate, cash_flows))
        except Exception as e:
            logger.error(f"Error calculating NPV: {e}")
            raise ValueError(f"Error calculating NPV: {e}")
    
    @staticmethod
    def calculate_irr(cash_flows: list) -> Optional[float]:
        """
        Calculate Internal Rate of Return (IRR) of cash flows.
        
        Args:
            cash_flows: List of cash flows (first should be negative for investment)
            
        Returns:
            IRR as a percentage, or None if cannot be calculated
        """
        try:
            # Ensure we have at least one positive and one negative cash flow
            has_positive = any(cf > 0 for cf in cash_flows)
            has_negative = any(cf < 0 for cf in cash_flows)
            
            if not (has_positive and has_negative):
                return None
                
            irr = np.irr(cash_flows)
            return float(irr * 100)  # Convert to percentage
        except Exception as e:
            logger.warning(f"Could not calculate IRR: {e}")
            return None
    
    @staticmethod
    def calculate_payback_period(
        cash_flows: list,
        initial_investment: float
    ) -> Optional[float]:
        """
        Calculate payback period for an investment.
        
        Args:
            cash_flows: List of positive cash flows by period
            initial_investment: Initial investment amount (positive value)
            
        Returns:
            Payback period in periods, or None if never pays back
        """
        try:
            cumulative_cash_flow = -abs(initial_investment)
            
            for period, cash_flow in enumerate(cash_flows):
                cumulative_cash_flow += cash_flow
                
                if cumulative_cash_flow >= 0:
                    # Calculate fractional period
                    if period == 0:
                        return period
                    else:
                        # Interpolate the exact payback period
                        previous_cumulative = cumulative_cash_flow - cash_flow
                        fraction = -previous_cumulative / cash_flow
                        return period + fraction
            
            return None  # Never pays back
        except Exception as e:
            logger.error(f"Error calculating payback period: {e}")
            return None
    
    @staticmethod
    def validate_financial_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean financial data.
        
        Args:
            data: Dictionary of financial data
            
        Returns:
            Validated and cleaned data
            
        Raises:
            ValueError: If data validation fails
        """
        validated = {}
        
        for key, value in data.items():
            if value is None:
                validated[key] = None
            elif isinstance(value, (int, float, Decimal)):
                # Ensure numeric values are valid
                if np.isnan(float(value)) or np.isinf(float(value)):
                    raise ValueError(f"Invalid numeric value for {key}: {value}")
                validated[key] = value
            else:
                validated[key] = value
                
        return validated


# Convenience functions for backward compatibility
def round_currency(value: Union[float, Decimal, int]) -> Decimal:
    """Convenience function for rounding currency."""
    return FinancialUtils.round_currency(value)


def calculate_percentage_change(
    current: Union[float, Decimal], 
    previous: Union[float, Decimal]
) -> Optional[float]:
    """Convenience function for calculating percentage change."""
    return FinancialUtils.calculate_percentage_change(current, previous)


def format_currency(
    amount: Union[float, Decimal],
    currency: str = "GBP",
    include_symbol: bool = True
) -> str:
    """Convenience function for formatting currency."""
    return FinancialUtils.format_currency(amount, currency, include_symbol)