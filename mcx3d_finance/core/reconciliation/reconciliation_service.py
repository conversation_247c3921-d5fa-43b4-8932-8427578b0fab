"""
Bank reconciliation service for orchestrating the reconciliation process.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
import csv
# import json
from typing import List, Dict, Any, Optional, BinaryIO
from datetime import datetime, timezone
# from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_
import pandas as pd

from mcx3d_finance.db.models import (
    BankTransaction, BankReconciliation, ReconciliationMatch
)
from mcx3d_finance.core.reconciliation.matching_engine import TransactionMatcher

logger = LoggerFactory.get_logger(__name__, domain="core")


class BankReconciliationService:
    """Service for performing bank reconciliations."""

    def __init__(self, db: Session):
        """Initialize the reconciliation service."""
        self.db = db
        self.matcher = TransactionMatcher()

    def start_reconciliation(
        self,
        organization_id: int,
        bank_account_id: str,
        start_date: datetime,
        end_date: datetime,
        user_id: Optional[int] = None
    ) -> BankReconciliation:
        """
        Start a new bank reconciliation process.

        Args:
            organization_id: Organization ID
            bank_account_id: Bank account identifier
            start_date: Start date for reconciliation
            end_date: End date for reconciliation
            user_id: User performing the reconciliation

        Returns:
            New BankReconciliation instance
        """
        reconciliation = BankReconciliation(
            organization_id=organization_id,
            bank_account_id=bank_account_id,
            start_date=start_date,
            end_date=end_date,
            status='in_progress',
            user_id=user_id
        )

        self.db.add(reconciliation)
        self.db.commit()

        logger.info(
            f"Started reconciliation {reconciliation.id} for organization {organization_id} "
            f"from {start_date} to {end_date}"
        )

        return reconciliation

    def process_bank_statement(
        self,
        reconciliation_id: int,
        statement_file: BinaryIO,
        file_format: str = 'csv'
    ) -> Dict[str, Any]:
        """
        Process a bank statement file and perform reconciliation.

        Args:
            reconciliation_id: Reconciliation ID
            statement_file: File-like object containing bank statement
            file_format: Format of the file ('csv' or 'excel')

        Returns:
            Reconciliation results
        """
        try:
            # Get reconciliation
            reconciliation = self.db.query(BankReconciliation).get(reconciliation_id)
            if not reconciliation:
                raise ValueError(f"Reconciliation {reconciliation_id} not found")

            # Parse bank statement
            statement_entries = self._parse_bank_statement(statement_file, file_format)

            # Get bank transactions for the period
            bank_transactions = self._get_bank_transactions(
                reconciliation.organization_id,
                reconciliation.start_date,
                reconciliation.end_date
            )

            # Perform matching
            matching_results = self.matcher.find_matches(
                bank_transactions,
                statement_entries
            )

            # Save matches to database
            self._save_matches(reconciliation, matching_results['matches'])

            # Update reconciliation statistics
            self._update_reconciliation_stats(reconciliation, matching_results)

            # Mark reconciliation as completed
            reconciliation.status = 'completed'
            reconciliation.completed_at = datetime.now(timezone.utc)
            reconciliation.results = matching_results
            self.db.commit()

            logger.info(
                f"Completed reconciliation {reconciliation_id} with "
                f"{len(matching_results['matches'])} matches"
            )

            return matching_results

        except Exception as e:
            logger.error(f"Error processing reconciliation {reconciliation_id}: {e}")

            # Update reconciliation status
            reconciliation = self.db.query(BankReconciliation).get(reconciliation_id)
            if reconciliation:
                reconciliation.status = 'failed'
                reconciliation.errors = {'error': str(e)}
                self.db.commit()

            raise

    def _parse_bank_statement(
        self,
        statement_file: BinaryIO,
        file_format: str
    ) -> List[Dict[str, Any]]:
        """Parse bank statement file."""
        if file_format == 'csv':
            return self._parse_csv_statement(statement_file)
        elif file_format == 'excel':
            return self._parse_excel_statement(statement_file)
        else:
            raise ValueError(f"Unsupported file format: {file_format}")

    def _parse_csv_statement(self, csv_file: BinaryIO) -> List[Dict[str, Any]]:
        """Parse CSV bank statement."""
        entries = []

        # Decode bytes to string if needed
        if hasattr(csv_file, 'read'):
            content = csv_file.read()
            if isinstance(content, bytes):
                content = content.decode('utf-8')
            lines = content.splitlines()
        else:
            lines = csv_file

        reader = csv.DictReader(lines)

        for row in reader:
            # Map common column names
            entry = {
                'date': self._parse_date(
                    row.get('Date') or row.get('Transaction Date') or row.get('date')
                ),
                'description': (
                    row.get('Description') or row.get('description') or
                    row.get('Narrative') or ''
                ),
                'reference': (
                    row.get('Reference') or row.get('reference') or
                    row.get('Transaction ID') or ''
                ),
                'amount': self._parse_amount(
                    row.get('Amount') or row.get('amount') or
                    row.get('Debit') or row.get('Credit') or '0'
                )
            }

            # Handle separate debit/credit columns
            if 'Debit' in row and 'Credit' in row:
                debit = self._parse_amount(row.get('Debit', '0'))
                credit = self._parse_amount(row.get('Credit', '0'))
                entry['amount'] = credit - debit

            entries.append(entry)

        return entries

    def _parse_excel_statement(self, excel_file: BinaryIO) -> List[Dict[str, Any]]:
        """Parse Excel bank statement."""
        df = pd.read_excel(excel_file)
        entries = []

        # Map common column names
        column_mapping = {
            'Date': 'date',
            'Transaction Date': 'date',
            'Description': 'description',
            'Narrative': 'description',
            'Reference': 'reference',
            'Transaction ID': 'reference',
            'Amount': 'amount',
            'Debit': 'debit',
            'Credit': 'credit'
        }

        # Rename columns
        df.rename(columns=column_mapping, inplace=True)

        for _, row in df.iterrows():
            entry = {
                'date': self._parse_date(row.get('date')),
                'description': str(row.get('description', '')),
                'reference': str(row.get('reference', '')),
                'amount': 0.0
            }

            # Handle amount
            if 'amount' in row:
                entry['amount'] = self._parse_amount(row['amount'])
            elif 'debit' in row and 'credit' in row:
                debit = self._parse_amount(row.get('debit', 0))
                credit = self._parse_amount(row.get('credit', 0))
                entry['amount'] = credit - debit

            entries.append(entry)

        return entries

    def _parse_date(self, date_value: Any) -> Optional[datetime]:
        """Parse date from various formats."""
        if not date_value:
            return None

        if isinstance(date_value, datetime):
            return date_value

        if isinstance(date_value, str):
            # Try common date formats
            formats = [
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%Y/%m/%d',
                '%d-%m-%Y',
                '%m-%d-%Y'
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_value, fmt)
                except ValueError:
                    continue

        return None

    def _parse_amount(self, amount_value: Any) -> float:
        """Parse amount from various formats."""
        if isinstance(amount_value, (int, float)):
            return float(amount_value)

        if isinstance(amount_value, str):
            # Remove currency symbols and spaces
            cleaned = amount_value.replace('$', '').replace('£', '').replace(',', '').strip()

            # Handle parentheses for negative amounts
            if cleaned.startswith('(') and cleaned.endswith(')'):
                cleaned = '-' + cleaned[1:-1]

            try:
                return float(cleaned)
            except ValueError:
                return 0.0

        return 0.0

    def _get_bank_transactions(
        self,
        organization_id: int,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get bank transactions for the reconciliation period."""
        transactions = self.db.query(BankTransaction).filter(
            and_(
                BankTransaction.organization_id == organization_id,
                BankTransaction.date >= start_date,
                BankTransaction.date <= end_date,
                BankTransaction.is_reconciled == False
            )
        ).all()

        # Convert to dictionaries
        return [
            {
                'id': tx.id,
                'date': tx.date,
                'total': float(tx.total or 0),
                'reference': tx.reference or '',
                'description': self._get_transaction_description(tx),
                'type': tx.type
            }
            for tx in transactions
        ]

    def _get_transaction_description(self, transaction: BankTransaction) -> str:
        """Get description for a bank transaction."""
        parts = []

        if transaction.contact:
            parts.append(transaction.contact.name)

        if transaction.line_items:
            # Extract descriptions from line items
            for item in transaction.line_items:
                if isinstance(item, dict) and 'description' in item:
                    parts.append(item['description'])

        return ' - '.join(parts) if parts else ''

    def _save_matches(
        self,
        reconciliation: BankReconciliation,
        matches: List[Dict[str, Any]]
    ):
        """Save reconciliation matches to database."""
        for match in matches:
            match_record = ReconciliationMatch(
                reconciliation_id=reconciliation.id,
                bank_transaction_id=match['bank_transaction_id'],
                statement_date=match['statement_entry']['date'],
                statement_description=match['statement_entry']['description'],
                statement_amount=match['statement_entry']['amount'],
                statement_reference=match['statement_entry']['reference'],
                match_type=match['match_type'],
                match_confidence=match['match_confidence'],
                match_criteria=match['match_criteria']
            )
            self.db.add(match_record)

            # Mark bank transaction as reconciled
            bank_tx = self.db.query(BankTransaction).get(match['bank_transaction_id'])
            if bank_tx:
                bank_tx.is_reconciled = True

        self.db.commit()

    def _update_reconciliation_stats(
        self,
        reconciliation: BankReconciliation,
        matching_results: Dict[str, Any]
    ):
        """Update reconciliation statistics."""
        summary = matching_results['summary']

        reconciliation.total_transactions = summary['total_bank_transactions']
        reconciliation.matched_transactions = summary['matched_count']
        reconciliation.unmatched_transactions = summary['unmatched_bank_count']

        # Calculate amounts
        total_amount = sum(
            tx['total'] for tx in matching_results['unmatched_bank']
        ) + sum(
            match['statement_entry']['amount']
            for match in matching_results['matches']
        )

        matched_amount = sum(
            match['statement_entry']['amount']
            for match in matching_results['matches']
        )

        unmatched_amount = sum(
            tx['total'] for tx in matching_results['unmatched_bank']
        )

        reconciliation.total_amount = total_amount
        reconciliation.matched_amount = matched_amount
        reconciliation.unmatched_amount = unmatched_amount

    def get_reconciliation_summary(
        self,
        reconciliation_id: int
    ) -> Dict[str, Any]:
        """Get summary of a reconciliation."""
        reconciliation = self.db.query(BankReconciliation).get(reconciliation_id)
        if not reconciliation:
            raise ValueError(f"Reconciliation {reconciliation_id} not found")

        # Get matched transactions
        matches = self.db.query(ReconciliationMatch).filter(
            ReconciliationMatch.reconciliation_id == reconciliation_id
        ).all()

        return {
            'reconciliation_id': reconciliation.id,
            'organization_id': reconciliation.organization_id,
            'bank_account_id': reconciliation.bank_account_id,
            'period': {
                'start': reconciliation.start_date.isoformat(),
                'end': reconciliation.end_date.isoformat()
            },
            'status': reconciliation.status,
            'statistics': {
                'total_transactions': reconciliation.total_transactions,
                'matched_transactions': reconciliation.matched_transactions,
                'unmatched_transactions': reconciliation.unmatched_transactions,
                'match_rate': (
                    reconciliation.matched_transactions / reconciliation.total_transactions * 100
                    if reconciliation.total_transactions > 0 else 0
                ),
                'total_amount': reconciliation.total_amount,
                'matched_amount': reconciliation.matched_amount,
                'unmatched_amount': reconciliation.unmatched_amount
            },
            'matches': [
                {
                    'bank_transaction_id': match.bank_transaction_id,
                    'statement_date': match.statement_date.isoformat() if match.statement_date else None,
                    'statement_amount': match.statement_amount,
                    'match_confidence': match.match_confidence,
                    'match_type': match.match_type
                }
                for match in matches
            ],
            'created_at': reconciliation.created_at.isoformat(),
            'completed_at': reconciliation.completed_at.isoformat() if reconciliation.completed_at else None
        }
