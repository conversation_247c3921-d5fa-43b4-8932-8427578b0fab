"""
Transaction matching engine for bank reconciliation.

Implements various matching strategies including exact matches,
fuzzy matching, and date/amount tolerance matching.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import List, Dict, Any, Tuple, Optional, Set
from datetime import datetime
# from decimal import Decimal
import re
from difflib import SequenceMatcher

logger = LoggerFactory.get_logger(__name__, domain="core")


class TransactionMatcher:
    """Intelligent transaction matching engine."""

    def __init__(
        self,
        date_tolerance_days: int = 3,
        amount_tolerance_percent: float = 0.01,
        min_description_similarity: float = 0.7
    ):
        """
        Initialize the matching engine.

        Args:
            date_tolerance_days: Maximum days difference for date matching
            amount_tolerance_percent: Maximum percentage difference for amount matching (0.01 = 1%)
            min_description_similarity: Minimum similarity score for description matching (0-1)
        """
        self.date_tolerance_days = date_tolerance_days
        self.amount_tolerance_percent = amount_tolerance_percent
        self.min_description_similarity = min_description_similarity

    def find_matches(
        self,
        bank_transactions: List[Dict[str, Any]],
        statement_entries: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Find matches between bank transactions and statement entries.

        Args:
            bank_transactions: List of transactions from the system
            statement_entries: List of entries from bank statement

        Returns:
            List of matches with confidence scores
        """
        matches = []
        matched_bank_ids: Set[str] = set()
        matched_statement_indices: Set[int] = set()

        # Sort by amount for more efficient matching
        bank_transactions_sorted = sorted(
            bank_transactions,
            key=lambda x: abs(x.get('total', 0)),
            reverse=True
        )

        for bank_tx in bank_transactions_sorted:
            if bank_tx['id'] in matched_bank_ids:
                continue

            best_match = self._find_best_match(
                bank_tx,
                statement_entries,
                matched_statement_indices
            )

            if best_match:
                match_data, statement_idx = best_match
                matches.append(match_data)
                matched_bank_ids.add(bank_tx['id'])
                matched_statement_indices.add(statement_idx)

        # Identify unmatched transactions
        unmatched_bank = [
            tx for tx in bank_transactions
            if tx['id'] not in matched_bank_ids
        ]

        unmatched_statement = [
            entry for idx, entry in enumerate(statement_entries)
            if idx not in matched_statement_indices
        ]

        return {
            'matches': matches,
            'unmatched_bank': unmatched_bank,
            'unmatched_statement': unmatched_statement,
            'summary': {
                'total_bank_transactions': len(bank_transactions),
                'total_statement_entries': len(statement_entries),
                'matched_count': len(matches),
                'unmatched_bank_count': len(unmatched_bank),
                'unmatched_statement_count': len(unmatched_statement)
            }
        }

    def _find_best_match(
        self,
        bank_tx: Dict[str, Any],
        statement_entries: List[Dict[str, Any]],
        matched_indices: set
    ) -> Optional[Tuple[Dict[str, Any], int]]:
        """Find the best matching statement entry for a bank transaction."""
        potential_matches = []

        for idx, statement in enumerate(statement_entries):
            if idx in matched_indices:
                continue

            match_score = self._calculate_match_score(bank_tx, statement)

            if match_score > 0:
                potential_matches.append((match_score, idx, statement))

        if not potential_matches:
            return None

        # Sort by match score and get the best match
        potential_matches.sort(key=lambda x: x[0], reverse=True)
        best_score, best_idx, best_statement = potential_matches[0]

        # Only accept matches above a threshold
        if best_score < 0.5:
            return None

        match_data = {
            'bank_transaction_id': bank_tx['id'],
            'statement_entry': best_statement,
            'match_confidence': best_score,
            'match_type': self._determine_match_type(best_score),
            'match_criteria': self._get_match_criteria(bank_tx, best_statement)
        }

        return match_data, best_idx

    def _calculate_match_score(
        self,
        bank_tx: Dict[str, Any],
        statement: Dict[str, Any]
    ) -> float:
        """Calculate match score between bank transaction and statement entry."""
        score = 0.0
        weights = {
            'amount': 0.4,
            'date': 0.3,
            'reference': 0.2,
            'description': 0.1
        }

        # Amount matching
        amount_score = self._match_amount(
            bank_tx.get('total', 0),
            statement.get('amount', 0)
        )
        score += amount_score * weights['amount']

        # Date matching
        date_score = self._match_date(
            bank_tx.get('date'),
            statement.get('date')
        )
        score += date_score * weights['date']

        # Reference matching
        reference_score = self._match_reference(
            bank_tx.get('reference', ''),
            statement.get('reference', '')
        )
        score += reference_score * weights['reference']

        # Description matching
        desc_score = self._match_description(
            bank_tx.get('description', ''),
            statement.get('description', '')
        )
        score += desc_score * weights['description']

        return score

    def _match_amount(self, amount1: float, amount2: float) -> float:
        """Match amounts with tolerance."""
        if amount1 == 0 or amount2 == 0:
            return 0.0

        # Check if amounts have opposite signs (one is debit, other is credit)
        if (amount1 > 0) != (amount2 > 0):
            return 0.0

        # Calculate percentage difference
        diff_percent = abs(amount1 - amount2) / abs(amount1)

        if diff_percent == 0:
            return 1.0
        elif diff_percent <= self.amount_tolerance_percent:
            # Linear score decrease based on difference
            return 1.0 - (diff_percent / self.amount_tolerance_percent)
        else:
            return 0.0

    def _match_date(self, date1: Any, date2: Any) -> float:
        """Match dates with tolerance."""
        if not date1 or not date2:
            return 0.0

        # Convert to datetime if needed
        if isinstance(date1, str):
            date1 = datetime.fromisoformat(date1.replace('Z', '+00:00'))
        if isinstance(date2, str):
            date2 = datetime.fromisoformat(date2.replace('Z', '+00:00'))

        # Calculate day difference
        diff_days = abs((date1 - date2).days)

        if diff_days == 0:
            return 1.0
        elif diff_days <= self.date_tolerance_days:
            # Linear score decrease based on difference
            return float(1.0 - (diff_days / self.date_tolerance_days))
        else:
            return 0.0

    def _match_reference(self, ref1: str, ref2: str) -> float:
        """Match reference numbers."""
        if not ref1 or not ref2:
            return 0.0

        ref1 = self._normalize_reference(ref1)
        ref2 = self._normalize_reference(ref2)

        if ref1 == ref2:
            return 1.0
        elif ref1 in ref2 or ref2 in ref1:
            return 0.8
        else:
            return 0.0

    def _match_description(self, desc1: str, desc2: str) -> float:
        """Match descriptions using fuzzy matching."""
        if not desc1 or not desc2:
            return 0.0

        desc1 = self._normalize_description(desc1)
        desc2 = self._normalize_description(desc2)

        # Use sequence matcher for fuzzy matching
        similarity = SequenceMatcher(None, desc1, desc2).ratio()

        if similarity >= self.min_description_similarity:
            return similarity
        else:
            return 0.0

    def _normalize_reference(self, reference: str) -> str:
        """Normalize reference for matching."""
        # Remove spaces and convert to uppercase
        return re.sub(r'\s+', '', reference.upper())

    def _normalize_description(self, description: str) -> str:
        """Normalize description for matching."""
        # Convert to uppercase and remove extra spaces
        normalized = re.sub(r'\s+', ' ', description.upper().strip())

        # Remove common words that don't add value
        stop_words = ['THE', 'AND', 'OR', 'FOR', 'TO', 'FROM', 'OF', 'IN', 'ON']
        words = normalized.split()
        filtered_words = [w for w in words if w not in stop_words]

        return ' '.join(filtered_words)

    def _determine_match_type(self, score: float) -> str:
        """Determine match type based on score."""
        if score >= 0.95:
            return 'exact'
        elif score >= 0.8:
            return 'high_confidence'
        elif score >= 0.6:
            return 'medium_confidence'
        else:
            return 'low_confidence'

    def _get_match_criteria(
        self,
        bank_tx: Dict[str, Any],
        statement: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get detailed match criteria for audit trail."""
        return {
            'amount_match': self._match_amount(
                bank_tx.get('total', 0),
                statement.get('amount', 0)
            ),
            'date_match': self._match_date(
                bank_tx.get('date'),
                statement.get('date')
            ),
            'reference_match': self._match_reference(
                bank_tx.get('reference', ''),
                statement.get('reference', '')
            ),
            'description_match': self._match_description(
                bank_tx.get('description', ''),
                statement.get('description', '')
            )
        }
