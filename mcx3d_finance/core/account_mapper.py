"""
Advanced account mapping module with industry-specific chart of accounts for UK FRS 102.
Provides flexible mapping rules and intelligent account classification, with integrated
structured logging for better observability.
"""

from dataclasses import dataclass
from enum import Enum
from functools import lru_cache
import re
from typing import List

from mcx3d_finance.core.account_classifications import FRS102AccountClassification
from mcx3d_finance.utils.logging_config import get_core_logger

logger = get_core_logger("account_mapper")


class IndustryType(Enum):
    """Industry classifications for specialized account mapping."""
    TECHNOLOGY = "technology"
    MANUFACTURING = "manufacturing"
    RETAIL = "retail"
    HEALTHCARE = "healthcare"
    FINANCIAL_SERVICES = "financial_services"
    REAL_ESTATE = "real_estate"
    CONSTRUCTION = "construction"
    PROFESSIONAL_SERVICES = "professional_services"
    HOSPITALITY = "hospitality"
    EDUCATION = "education"
    NON_PROFIT = "non_profit"
    GENERAL = "general"


class AccountType(Enum):
    """Extended account types for comprehensive classification."""
    ASSET = "asset"
    LIABILITY = "liability"
    EQUITY = "equity"
    REVENUE = "revenue"
    EXPENSE = "expense"
    CONTRA_ASSET = "contra_asset"
    CONTRA_LIABILITY = "contra_liability"
    CONTRA_EQUITY = "contra_equity"
    CONTRA_REVENUE = "contra_revenue"


@dataclass
class AccountMappingRule:
    """Account mapping rule definition."""
    pattern: str  # Regex pattern or exact match
    frs102_classification: FRS102AccountClassification
    account_type: AccountType
    industry_specific: bool = False
    confidence_score: float = 1.0
    description: str = ""

    def matches(self, account_code: str, account_name: str) -> bool:
        """Check if this rule matches the given account."""
        try:
            if self.pattern == account_code:
                return True
            if re.match(self.pattern, account_code, re.IGNORECASE):
                return True
            if re.search(self.pattern, account_name, re.IGNORECASE):
                return True
            return False
        except re.error as e:
            logger.error(
                "Invalid regex pattern in account mapping rule.",
                pattern=self.pattern,
                error=str(e),
                exc_info=True,
            )
            return (self.pattern.lower() in account_code.lower() or
                    self.pattern.lower() in account_name.lower())


@dataclass
class MappingResult:
    """Result of account mapping operation."""
    original_code: str
    original_name: str
    frs102_classification: FRS102AccountClassification
    account_type: AccountType
    confidence_score: float
    rule_description: str
    industry_specific: bool = False


class AdvancedAccountMapper:
    """Advanced account mapper with industry-specific rules for FRS 102."""

    def __init__(self, industry: IndustryType = IndustryType.GENERAL):
        self.industry = industry
        self.mapping_rules: List[AccountMappingRule] = self._load_mapping_rules()
        self.custom_rules: List[AccountMappingRule] = []

    def _load_mapping_rules(self) -> List[AccountMappingRule]:
        """Load comprehensive mapping rules for FRS 102."""
        rules = []
        rules.extend(self._get_standard_asset_rules())
        rules.extend(self._get_standard_liability_rules())
        rules.extend(self._get_standard_equity_rules())
        rules.extend(self._get_standard_revenue_rules())
        rules.extend(self._get_standard_expense_rules())
        logger.info(f"Loaded {len(rules)} standard mapping rules.")
        return rules

    def _get_standard_asset_rules(self) -> List[AccountMappingRule]:
        """Get standard asset account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"cash|checking|savings",
                frs102_classification=FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND,
                account_type=AccountType.ASSET,
                description="Cash and bank accounts"
            ),
            AccountMappingRule(
                pattern=r"debtors|receivable|a/r",
                frs102_classification=FRS102AccountClassification.DEBTORS,
                account_type=AccountType.ASSET,
                description="Debtors (Accounts Receivable)"
            ),
            AccountMappingRule(
                pattern=r"stock|inventory",
                frs102_classification=FRS102AccountClassification.STOCKS,
                account_type=AccountType.ASSET,
                description="Stocks (Inventory)"
            ),
            AccountMappingRule(
                pattern=r"equipment|machinery|building|land|vehicle",
                frs102_classification=FRS102AccountClassification.TANGIBLE_ASSETS,
                account_type=AccountType.ASSET,
                description="Tangible Fixed Assets"
            ),
            AccountMappingRule(
                pattern=r"patent|trademark|goodwill",
                frs102_classification=FRS102AccountClassification.INTANGIBLE_ASSETS,
                account_type=AccountType.ASSET,
                description="Intangible Fixed Assets"
            ),
        ]

    def _get_standard_liability_rules(self) -> List[AccountMappingRule]:
        """Get standard liability account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"creditors|payable|a/p",
                frs102_classification=FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR,
                account_type=AccountType.LIABILITY,
                description="Creditors due within one year"
            ),
            AccountMappingRule(
                pattern=r"loan|mortgage|bond",
                frs102_classification=FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,
                account_type=AccountType.LIABILITY,
                description="Creditors due after one year"
            ),
        ]

    def _get_standard_equity_rules(self) -> List[AccountMappingRule]:
        """Get standard equity account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"share capital|common stock",
                frs102_classification=FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL,
                account_type=AccountType.EQUITY,
                description="Called up share capital"
            ),
            AccountMappingRule(
                pattern=r"retained earnings|profit and loss",
                frs102_classification=FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT,
                account_type=AccountType.EQUITY,
                description="Profit and loss account"
            ),
        ]

    def _get_standard_revenue_rules(self) -> List[AccountMappingRule]:
        """Get standard revenue account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"turnover|revenue|sales",
                frs102_classification=FRS102AccountClassification.TURNOVER,
                account_type=AccountType.REVENUE,
                description="Turnover"
            ),
        ]

    def _get_standard_expense_rules(self) -> List[AccountMappingRule]:
        """Get standard expense account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"cost of sales|cogs",
                frs102_classification=FRS102AccountClassification.COST_OF_SALES,
                account_type=AccountType.EXPENSE,
                description="Cost of sales"
            ),
            AccountMappingRule(
                pattern=r"distribution|delivery|freight",
                frs102_classification=FRS102AccountClassification.DISTRIBUTION_COSTS,
                account_type=AccountType.EXPENSE,
                description="Distribution costs"
            ),
            AccountMappingRule(
                pattern=r"admin|administrative|office|salaries",
                frs102_classification=FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
                account_type=AccountType.EXPENSE,
                description="Administrative expenses"
            ),
            AccountMappingRule(
                pattern=r"interest payable",
                frs102_classification=FRS102AccountClassification.INTEREST_PAYABLE,
                account_type=AccountType.EXPENSE,
                description="Interest payable"
            ),
            AccountMappingRule(
                pattern=r"tax|corporation tax",
                frs102_classification=FRS102AccountClassification.TAXATION,
                account_type=AccountType.EXPENSE,
                description="Taxation"
            ),
        ]

    @lru_cache(maxsize=2048)
    def map_account(self, account_code: str, account_name: str = "") -> MappingResult:
        """Map an account to FRS 102 classification using a rule-based system."""
        for rule in self.custom_rules:
            if rule.matches(account_code, account_name):
                logger.info(
                    "Account matched using custom rule.",
                    account_code=account_code,
                    account_name=account_name,
                    rule_description=rule.description,
                )
                return MappingResult(
                    original_code=account_code,
                    original_name=account_name,
                    frs102_classification=rule.frs102_classification,
                    account_type=rule.account_type,
                    confidence_score=rule.confidence_score,
                    rule_description=f"Custom: {rule.description}",
                    industry_specific=rule.industry_specific,
                )

        best_match = None
        for rule in self.mapping_rules:
            if rule.matches(account_code, account_name):
                best_match = rule
                break

        if best_match:
            logger.debug(
                "Account matched using standard rule.",
                account_code=account_code,
                account_name=account_name,
                rule_description=best_match.description,
            )
            return MappingResult(
                original_code=account_code,
                original_name=account_name,
                frs102_classification=best_match.frs102_classification,
                account_type=best_match.account_type,
                confidence_score=best_match.confidence_score,
                rule_description=best_match.description,
                industry_specific=best_match.industry_specific,
            )

        logger.warning(
            "No specific mapping rule found for account. Applying fallback.",
            account_code=account_code,
            account_name=account_name,
        )
        return MappingResult(
            original_code=account_code,
            original_name=account_name,
            frs102_classification=FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
            account_type=AccountType.EXPENSE,
            confidence_score=0.1,
            rule_description="Fallback mapping",
            industry_specific=False,
        )

    def add_custom_rule(self, rule: AccountMappingRule):
        """Add a custom mapping rule to the beginning of the rule list."""
        self.custom_rules.insert(0, rule)
        self.map_account.cache_clear()
        logger.info(
            "Custom mapping rule added and cache cleared.",
            rule_description=rule.description,
            pattern=rule.pattern,
        )
