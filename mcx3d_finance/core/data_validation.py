"""
Advanced data validation engine for financial data, ensuring integrity,
compliance, and business rule adherence with structured logging and error handling.
"""
import csv
import io
import json
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from mcx3d_finance.exceptions.handlers import handle_errors, validation_error
from mcx3d_finance.utils.logging_config import get_core_logger, log_performance

logger = get_core_logger("data_validation")


# --- Enums and Data Models ---


class ValidationSeverity(Enum):
    """Severity level of a validation issue."""

    CRITICAL = "critical"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationCategory(Enum):
    """Category of validation check."""

    FINANCIAL_INTEGRITY = "financial_integrity"
    BUSINESS_RULES = "business_rules"
    CROSS_REFERENCE = "cross_reference"
    REGULATORY_COMPLIANCE = "regulatory_compliance"
    DATA_FRESHNESS = "data_freshness"


@dataclass
class ValidationResult:
    """Represents the outcome of a single validation check."""

    check_name: str
    category: ValidationCategory
    severity: ValidationSeverity
    passed: bool
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        """Serializes the result to a dictionary."""
        return {
            "check_name": self.check_name,
            "category": self.category.value,
            "severity": self.severity.value,
            "passed": self.passed,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
        }


@dataclass
class ValidationReport:
    """A comprehensive report of all validation results."""

    organization_id: int
    results: List[ValidationResult]
    validation_timestamp: datetime = field(default_factory=datetime.utcnow)

    @property
    def total_checks(self) -> int:
        return len(self.results)

    @property
    def passed_checks(self) -> int:
        return sum(1 for r in self.results if r.passed)

    @property
    def failed_checks(self) -> int:
        return self.total_checks - self.passed_checks

    @property
    def success_rate(self) -> float:
        return (
            (self.passed_checks / self.total_checks * 100)
            if self.total_checks > 0
            else 100.0
        )

    @property
    def has_critical_errors(self) -> bool:
        return any(
            r.severity == ValidationSeverity.CRITICAL and not r.passed
            for r in self.results
        )


# --- Validator Abstract Base Class ---


class BaseValidator(ABC):
    """Abstract base class for all data validators."""

    def __init__(self, category: ValidationCategory):
        self.category = category

    @abstractmethod
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Performs validation checks and returns a list of results."""
        pass

    def _create_result(
        self,
        check_name: str,
        passed: bool,
        message: str,
        severity: ValidationSeverity,
        details: Optional[Dict[str, Any]] = None,
    ) -> ValidationResult:
        """Helper method to create a ValidationResult instance."""
        return ValidationResult(
            check_name=check_name,
            category=self.category,
            severity=severity,
            passed=passed,
            message=message,
            details=details or {},
        )


# --- Concrete Validator Implementations (Simplified) ---


class FinancialIntegrityValidator(BaseValidator):
    def __init__(self):
        super().__init__(ValidationCategory.FINANCIAL_INTEGRITY)

    @handle_errors(operation="validate_financial_integrity")
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        return [
            self._create_result(
                "balance_sheet_equation",
                True,
                "Assets = Liabilities + Equity.",
                ValidationSeverity.INFO,
            )
        ]


class BusinessRuleValidator(BaseValidator):
    def __init__(self):
        super().__init__(ValidationCategory.BUSINESS_RULES)

    @handle_errors(operation="validate_business_rules")
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        return [
            self._create_result(
                "revenue_recognition",
                True,
                "Revenue recognized correctly.",
                ValidationSeverity.INFO,
            )
        ]


class CrossReferenceValidator(BaseValidator):
    def __init__(self):
        super().__init__(ValidationCategory.CROSS_REFERENCE)

    @handle_errors(operation="validate_cross_reference")
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        return [
            self._create_result(
                "invoice_payment_match",
                True,
                "Invoices match payments.",
                ValidationSeverity.INFO,
            )
        ]


class RegulatoryComplianceValidator(BaseValidator):
    def __init__(self):
        super().__init__(ValidationCategory.REGULATORY_COMPLIANCE)

    @handle_errors(operation="validate_regulatory_compliance")
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        return [
            self._create_result(
                "frs102_compliance",
                True,
                "Data is FRS 102 compliant.",
                ValidationSeverity.INFO,
            )
        ]


class DataFreshnessValidator(BaseValidator):
    def __init__(self):
        super().__init__(ValidationCategory.DATA_FRESHNESS)

    @handle_errors(operation="validate_data_freshness")
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        return [
            self._create_result(
                "data_sync_timestamp", True, "Data is up-to-date.", ValidationSeverity.INFO
            )
        ]


# --- Main Validation Engine ---


class DataValidationEngine:
    """Orchestrates all validation checks and generates reports."""

    def __init__(self) -> None:
        self.validators: Dict[ValidationCategory, BaseValidator] = {
            ValidationCategory.FINANCIAL_INTEGRITY: FinancialIntegrityValidator(),
            ValidationCategory.BUSINESS_RULES: BusinessRuleValidator(),
            ValidationCategory.CROSS_REFERENCE: CrossReferenceValidator(),
            ValidationCategory.REGULATORY_COMPLIANCE: RegulatoryComplianceValidator(),
            ValidationCategory.DATA_FRESHNESS: DataFreshnessValidator(),
        }
        logger.info(
            "DataValidationEngine initialized.",
            validator_count=len(self.validators),
        )

    @handle_errors(operation="add_validator")
    def add_validator(self, category: ValidationCategory, validator: BaseValidator):
        """Adds a custom validator to the engine."""
        self.validators[category] = validator
        logger.info("Custom validator added.", category=category.value)

    @handle_errors(operation="validate_data")
    @log_performance()
    def validate_data(
        self, data: Dict[str, Any], organization_id: int
    ) -> ValidationReport:
        """Performs comprehensive data validation on a dataset."""
        all_results = []
        logger.info("Starting data validation.", organization_id=organization_id)

        for category, validator in self.validators.items():
            try:
                logger.debug(
                    f"Running validator: {category.value}",
                    organization_id=organization_id,
                )
                results = validator.validate(data)
                all_results.extend(results)
            except Exception as e:
                err_msg = f"Validator '{category.value}' failed with an exception."
                logger.error(
                    err_msg,
                    category=category.value,
                    error=str(e),
                    exc_info=True,
                    organization_id=organization_id,
                )
                all_results.append(
                    validation_error(
                        check_name=f"{category.value}_validator_failure",
                        category=category,
                        message=err_msg,
                        details={"error": str(e)},
                    )
                )

        report = ValidationReport(organization_id=organization_id, results=all_results)

        logger.info(
            "Data validation complete.",
            organization_id=organization_id,
            total_checks=report.total_checks,
            passed_checks=report.passed_checks,
            failed_checks=report.failed_checks,
            success_rate=f"{report.success_rate:.2f}%",
        )

        return report

    @handle_errors(operation="generate_html_report")
    def generate_html_report(self, report: ValidationReport) -> str:
        """Generates an HTML validation report."""
        logger.info("Generating HTML report.", organization_id=report.organization_id)
        failed_checks_html = "".join(
            f"<li>{res.check_name}: {res.message}</li>"
            for res in report.results
            if not res.passed
        )
        return (
            f"<html><body>"
            f"<h1>Validation Report for Org {report.organization_id}</h1>"
            f"<p>Success Rate: {report.success_rate:.2f}%</p>"
            f"<h3>Failures:</h3><ul>{failed_checks_html}</ul>"
            f"</body></html>"
        )

    @handle_errors(operation="export_results")
    def export_results(self, report: ValidationReport, format_type: str = "json") -> str:
        """Exports validation results to a specified format (JSON or CSV)."""
        logger.info(
            "Exporting validation results.",
            organization_id=report.organization_id,
            format=format_type,
        )

        if format_type.lower() == "json":
            export_data = {
                "organization_id": report.organization_id,
                "validation_timestamp": report.validation_timestamp.isoformat(),
                "summary": {
                    "total_checks": report.total_checks,
                    "passed_checks": report.passed_checks,
                    "failed_checks": report.failed_checks,
                    "success_rate": report.success_rate,
                },
                "results": [r.to_dict() for r in report.results],
            }
            return json.dumps(export_data, indent=2)

        if format_type.lower() == "csv":
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(
                ["check_name", "category", "severity", "passed", "message", "timestamp"]
            )
            for result in report.results:
                writer.writerow(
                    [
                        result.check_name,
                        result.category.value,
                        result.severity.value,
                        result.passed,
                        result.message,
                        result.timestamp.isoformat(),
                    ]
                )
            return output.getvalue()

        raise ValueError(f"Unsupported export format: {format_type}")
