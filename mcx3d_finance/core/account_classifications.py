"""
UK GAAP/FRS 102 account classifications and financial statement categorization.
"""

from enum import Enum
from typing import Optional

from mcx3d_finance.core.logging_factory import LoggerFactory

logger = LoggerFactory.get_logger(__name__, domain='core')


class FRS102AccountClassification(Enum):
    """FRS 102-compliant account classifications for UK financial reporting."""

    # Balance Sheet
    FIXED_ASSETS = "fixed_assets"
    TANGIBLE_ASSETS = "tangible_assets"
    INTANGIBLE_ASSETS = "intangible_assets"
    INVESTMENTS = "investments"

    CURRENT_ASSETS = "current_assets"
    STOCKS = "stocks"
    DEBTORS = "debtors"
    CASH_AT_BANK_AND_IN_HAND = "cash_at_bank_and_in_hand"

    CREDITORS_DUE_WITHIN_ONE_YEAR = "creditors_due_within_one_year"
    CREDITORS_DUE_AFTER_ONE_YEAR = "creditors_due_after_one_year"
    PROVISIONS_FOR_LIABILITIES = "provisions_for_liabilities"

    CAPITAL_AND_RESERVES = "capital_and_reserves"
    CALLED_UP_SHARE_CAPITAL = "called_up_share_capital"
    PROFIT_AND_LOSS_ACCOUNT = "profit_and_loss_account"

    # Profit & Loss Account
    TURNOVER = "turnover"
    COST_OF_SALES = "cost_of_sales"
    GROSS_PROFIT = "gross_profit"

    DISTRIBUTION_COSTS = "distribution_costs"
    ADMINISTRATIVE_EXPENSES = "administrative_expenses"
    OPERATING_PROFIT = "operating_profit"

    INTEREST_RECEIVABLE = "interest_receivable"
    INTEREST_PAYABLE = "interest_payable"
    PROFIT_BEFORE_TAX = "profit_before_tax"

    TAXATION = "taxation"
    PROFIT_AFTER_TAX = "profit_after_tax"


class FinancialStatementCategory(Enum):
    """Financial statement categories for reporting."""

    BALANCE_SHEET = "balance_sheet"
    PROFIT_AND_LOSS_ACCOUNT = "profit_and_loss_account"
    CASH_FLOW_STATEMENT = "cash_flow_statement"


class AccountTypeMapping:
    """Maps Xero account types to FRS 102 classifications."""

    XERO_TO_FRS102_MAPPING = {
        # Assets
        "BANK": FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND,
        "CURRENT": FRS102AccountClassification.DEBTORS,
        "FIXED": FRS102AccountClassification.TANGIBLE_ASSETS,
        "INVENTORY": FRS102AccountClassification.STOCKS,
        "NONCURRENT": FRS102AccountClassification.TANGIBLE_ASSETS,
        "PREPAYMENT": FRS102AccountClassification.DEBTORS,
        # Liabilities
        "CURRLIAB": FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR,
        "LIABILITY": FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,
        "TERMLIAB": FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,
        # Equity
        "EQUITY": FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT,
        # Revenue
        "REVENUE": FRS102AccountClassification.TURNOVER,
        "SALES": FRS102AccountClassification.TURNOVER,
        "OTHERINCOME": FRS102AccountClassification.TURNOVER,
        # Expenses
        "DIRECTCOSTS": FRS102AccountClassification.COST_OF_SALES,
        "EXPENSE": FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
        "OVERHEADS": FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
        "DEPRECIATN": FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
    }

    @classmethod
    def get_frs102_classification(
        cls, xero_account_type: str
    ) -> Optional[FRS102AccountClassification]:
        """Get FRS 102 classification for a Xero account type."""
        classification = cls.XERO_TO_FRS102_MAPPING.get(xero_account_type.upper())
        if not classification:
            logger.warning(
                "No FRS 102 classification found for Xero account type.",
                xero_account_type=xero_account_type,
            )
        return classification

    @classmethod
    def get_financial_statement_category(
        cls, frs102_classification: FRS102AccountClassification
    ) -> FinancialStatementCategory:
        """Get financial statement category for an FRS 102 classification."""
        balance_sheet_classifications = {
            FRS102AccountClassification.FIXED_ASSETS,
            FRS102AccountClassification.TANGIBLE_ASSETS,
            FRS102AccountClassification.INTANGIBLE_ASSETS,
            FRS102AccountClassification.INVESTMENTS,
            FRS102AccountClassification.CURRENT_ASSETS,
            FRS102AccountClassification.STOCKS,
            FRS102AccountClassification.DEBTORS,
            FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND,
            FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR,
            FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,
            FRS102AccountClassification.PROVISIONS_FOR_LIABILITIES,
            FRS102AccountClassification.CAPITAL_AND_RESERVES,
            FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL,
            FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT,
        }

        profit_and_loss_classifications = {
            FRS102AccountClassification.TURNOVER,
            FRS102AccountClassification.COST_OF_SALES,
            FRS102AccountClassification.GROSS_PROFIT,
            FRS102AccountClassification.DISTRIBUTION_COSTS,
            FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
            FRS102AccountClassification.OPERATING_PROFIT,
            FRS102AccountClassification.INTEREST_RECEIVABLE,
            FRS102AccountClassification.INTEREST_PAYABLE,
            FRS102AccountClassification.PROFIT_BEFORE_TAX,
            FRS102AccountClassification.TAXATION,
            FRS102AccountClassification.PROFIT_AFTER_TAX,
        }

        if frs102_classification in balance_sheet_classifications:
            return FinancialStatementCategory.BALANCE_SHEET
        elif frs102_classification in profit_and_loss_classifications:
            return FinancialStatementCategory.PROFIT_AND_LOSS_ACCOUNT
        else:
            # This path indicates a potential gap in the logic, as cash flow items
            # are not explicitly categorized here.
            logger.warning(
                "Could not determine financial statement category for classification, defaulting to Balance Sheet.",
                frs102_classification=frs102_classification.value,
            )
            return FinancialStatementCategory.BALANCE_SHEET

    @classmethod
    def frs102_to_gaap_classification(
        cls, frs102_classification: FRS102AccountClassification
    ) -> str:
        """Convert FRS 102 classification to a standard GAAP-style string."""
        frs102_to_gaap_mapping = {
            # Assets
            FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND: "cash_and_equivalents",
            FRS102AccountClassification.DEBTORS: "accounts_receivable",
            FRS102AccountClassification.STOCKS: "inventory",
            FRS102AccountClassification.TANGIBLE_ASSETS: "property_plant_equipment",
            FRS102AccountClassification.INTANGIBLE_ASSETS: "intangible_assets",
            FRS102AccountClassification.INVESTMENTS: "other_current_assets",
            FRS102AccountClassification.CURRENT_ASSETS: "other_current_assets",
            FRS102AccountClassification.FIXED_ASSETS: "property_plant_equipment",
            # Liabilities
            FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR: "accounts_payable",
            FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR: "long_term_debt",
            FRS102AccountClassification.PROVISIONS_FOR_LIABILITIES: "accrued_liabilities",
            # Equity
            FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL: "common_stock",
            FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT: "retained_earnings",
            FRS102AccountClassification.CAPITAL_AND_RESERVES: "retained_earnings",
            # Revenue
            FRS102AccountClassification.TURNOVER: "service_revenue",
            # Expenses
            FRS102AccountClassification.COST_OF_SALES: "cost_of_goods_sold",
            FRS102AccountClassification.DISTRIBUTION_COSTS: "sales_and_marketing",
            FRS102AccountClassification.ADMINISTRATIVE_EXPENSES: "general_and_administrative",
            FRS102AccountClassification.INTEREST_RECEIVABLE: "interest_income",
            FRS102AccountClassification.INTEREST_PAYABLE: "interest_expense",
            FRS102AccountClassification.TAXATION: "income_tax_expense",
            # Calculated fields
            FRS102AccountClassification.GROSS_PROFIT: "gross_profit",
            FRS102AccountClassification.OPERATING_PROFIT: "operating_income",
            FRS102AccountClassification.PROFIT_BEFORE_TAX: "income_before_taxes",
            FRS102AccountClassification.PROFIT_AFTER_TAX: "net_income",
        }

        default_classification = "general_and_administrative"
        classification = frs102_to_gaap_mapping.get(
            frs102_classification, default_classification
        )

        if classification == default_classification and frs102_classification not in [
            FRS102AccountClassification.ADMINISTRATIVE_EXPENSES
        ]:
            logger.warning(
                "FRS 102 classification has no direct GAAP mapping, using default.",
                frs102_classification=frs102_classification.value,
                default_gaap_classification=default_classification,
            )

        return classification
