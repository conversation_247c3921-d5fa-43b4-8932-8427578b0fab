"""
Intelligent transaction categorization using rules-based classification for UK FRS 102,
refactored with structured logging and error handling.
"""

import re
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Tuple

from mcx3d_finance.exceptions.handlers import error_boundary, handle_errors
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.utils.logging_config import get_core_logger, log_performance

logger = get_core_logger("transaction_classifier")


class UKTransactionCategory(Enum):
    """UK-compliant transaction categories for classification."""
    # Revenue Categories
    TURNOVER = "turnover"
    INTEREST_RECEIVABLE = "interest_receivable"
    OTHER_INCOME = "other_income"

    # Expense Categories
    COST_OF_SALES = "cost_of_sales"
    DISTRIBUTION_COSTS = "distribution_costs"
    ADMINISTRATIVE_EXPENSES = "administrative_expenses"
    INTEREST_PAYABLE = "interest_payable"
    TAXATION = "taxation"

    # Asset/Liability Categories
    DEBTORS = "debtors"
    CREDITORS = "creditors"
    STOCKS = "stocks"
    TANGIBLE_ASSETS_PURCHASE = "tangible_assets_purchase"
    LOAN_PAYMENT = "loan_payment"

    # Cash Flow Categories
    CUSTOMER_PAYMENT = "customer_payment"
    SUPPLIER_PAYMENT = "supplier_payment"
    BANK_TRANSFER = "bank_transfer"

    UNCATEGORIZED = "uncategorized"


class ConfidenceLevel(Enum):
    """Confidence levels for classification."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class ClassificationRule:
    """Transaction classification rule."""
    category: UKTransactionCategory
    patterns: List[str]
    confidence_score: float = 1.0
    description: str = ""

    def matches(self, transaction: Dict[str, Any]) -> Tuple[bool, float]:
        """Check if this rule matches the transaction."""
        description = transaction.get("description", "").lower()
        if any(re.search(pattern, description, re.IGNORECASE) for pattern in self.patterns):
            return True, self.confidence_score
        return False, 0.0


@dataclass
class ClassificationResult:
    """Result of transaction classification."""
    transaction_id: str
    category: UKTransactionCategory
    confidence_score: float
    confidence_level: ConfidenceLevel
    rule_description: str


class TransactionClassifier:
    """Intelligent transaction classifier for UK financial reporting."""

    def __init__(self):
        self.classification_rules = self._load_classification_rules()
        self.custom_rules: List[ClassificationRule] = []
        logger.info(
            "TransactionClassifier initialized",
            rule_count=len(self.classification_rules),
        )

    def _load_classification_rules(self) -> List[ClassificationRule]:
        """Load comprehensive classification rules for UK standards."""
        rules = []
        rules.extend(self._get_revenue_rules())
        rules.extend(self._get_expense_rules())
        rules.extend(self._get_asset_liability_rules())
        return rules

    def _get_revenue_rules(self) -> List[ClassificationRule]:
        """Get revenue classification rules."""
        return [
            ClassificationRule(
                category=UKTransactionCategory.TURNOVER,
                patterns=[r"sale|customer payment|invoice"],
                description="Turnover from sales"
            ),
            ClassificationRule(
                category=UKTransactionCategory.INTEREST_RECEIVABLE,
                patterns=[r"interest received|bank interest"],
                description="Interest receivable"
            ),
        ]

    def _get_expense_rules(self) -> List[ClassificationRule]:
        """Get expense classification rules."""
        return [
            ClassificationRule(
                category=UKTransactionCategory.COST_OF_SALES,
                patterns=[r"cost of goods|direct cost"],
                description="Cost of sales"
            ),
            ClassificationRule(
                category=UKTransactionCategory.DISTRIBUTION_COSTS,
                patterns=[r"delivery|freight|postage"],
                description="Distribution costs"
            ),
            ClassificationRule(
                category=UKTransactionCategory.ADMINISTRATIVE_EXPENSES,
                patterns=[r"salary|rent|utilities|office supplies"],
                description="Administrative expenses"
            ),
            ClassificationRule(
                category=UKTransactionCategory.INTEREST_PAYABLE,
                patterns=[r"interest paid|loan interest"],
                description="Interest payable"
            ),
            ClassificationRule(
                category=UKTransactionCategory.TAXATION,
                patterns=[r"hmrc|vat|corporation tax"],
                description="Taxation"
            ),
        ]

    def _get_asset_liability_rules(self) -> List[ClassificationRule]:
        """Get asset/liability classification rules."""
        return [
            ClassificationRule(
                category=UKTransactionCategory.TANGIBLE_ASSETS_PURCHASE,
                patterns=[r"equipment|machinery|vehicle"],
                description="Purchase of tangible assets"
            ),
            ClassificationRule(
                category=UKTransactionCategory.LOAN_PAYMENT,
                patterns=[r"loan payment|mortgage payment"],
                description="Loan payments"
            ),
        ]

    @handle_errors(operation="classify_transaction")
    @log_performance()
    def classify(self, transaction: Dict[str, Any]) -> Dict[str, Any]:
        """Classifies a single transaction and enriches it with the result."""
        correlation_id = get_correlation_id()
        transaction_id = transaction.get("id", "unknown")

        with error_boundary(
            "classify_transaction",
            correlation_id=correlation_id,
            transaction_id=transaction_id,
        ):
            best_match = None
            best_score = 0.0

            for rule in self.custom_rules + self.classification_rules:
                matches, score = rule.matches(transaction)
                if matches and score > best_score:
                    best_match = rule
                    best_score = score

            if best_match:
                result = ClassificationResult(
                    transaction_id=transaction_id,
                    category=best_match.category,
                    confidence_score=best_score,
                    confidence_level=self._get_confidence_level(best_score),
                    rule_description=best_match.description,
                )
                logger.debug(
                    "Transaction classified.",
                    transaction_id=transaction_id,
                    category=result.category.value,
                    confidence=result.confidence_level.value,
                )
            else:
                result = ClassificationResult(
                    transaction_id=transaction_id,
                    category=UKTransactionCategory.UNCATEGORIZED,
                    confidence_score=0.1,
                    confidence_level=ConfidenceLevel.LOW,
                    rule_description="No matching rule found",
                )
                logger.debug(
                    "Transaction uncategorized.", transaction_id=transaction_id
                )

            transaction["classification"] = result.category.value
            transaction["classification_confidence"] = result.confidence_level.value
            return transaction

    def _get_confidence_level(self, score: float) -> ConfidenceLevel:
        """Convert numeric score to confidence level."""
        if score >= 0.8:
            return ConfidenceLevel.HIGH
        elif score >= 0.5:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW

    @handle_errors(operation="add_custom_rule")
    def add_custom_rule(self, rule: ClassificationRule):
        """Add a custom classification rule."""
        self.custom_rules.insert(0, rule)  # Custom rules take precedence
        logger.info(
            "Custom classification rule added.", description=rule.description
        )
