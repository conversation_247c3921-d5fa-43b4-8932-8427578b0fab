"""
This module provides a centralized access point to the financial calculation
and valuation classes within the core application. It imports and exposes
the primary calculator classes for use by other parts of the system,
such as the API and CLI layers.

By convention, this module acts as a facade, simplifying the import paths
for consumers of the core financial logic.
"""

# Import the refactored calculator classes from their new locations.
from .financials.profit_and_loss_calculator import ProfitAndLossCalculator
from .financials.saas_kpi_calculator import SaasKpiCalculator
from .valuation.dcf import DCFValuation

# The classes `UKFinancialCalculator` and `DCFCalculator` previously defined in this
# file have been deprecated and removed. Their logic is now encapsulated in the
# imported classes:
#
# - `UKFinancialCalculator` has been split into:
#   - `ProfitAndLossCalculator` for P&L-related calculations.
#   - `SaasKpiCalculator` for SaaS metric calculations.
#
# - `DCFCalculator` has been replaced by the more comprehensive `DCFValuation`
#   class from the `core.valuation` module.

__all__ = [
    "ProfitAndLossCalculator",
    "SaasKpiCalculator",
    "DCFValuation",
]
