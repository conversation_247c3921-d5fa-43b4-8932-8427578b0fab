"""
Enhanced data processing module with FRS 102 compliance, structured logging,
and robust error handling.
"""

from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

from mcx3d_finance.core.account_classifications import FRS102AccountClassification
from mcx3d_finance.core.account_mapper import Advanced<PERSON>ccountMapper, IndustryType
from mcx3d_finance.core.config import get_xero_config
from mcx3d_finance.core.currency_converter import CurrencyConverter
from mcx3d_finance.core.data_validation import DataValidationEngine
from mcx3d_finance.core.duplicate_detector import DuplicateDetector, MergeEngine
from mcx3d_finance.core.transaction_classifier import TransactionClassifier
from mcx3d_finance.core.transformation_engine import (
    BatchTransformationEngine,
    DataQualityScorer,
    TransformationRule,
    TransformationRuleType,
)
from mcx3d_finance.core.validation_integration import IntegratedValidationEngine
from mcx3d_finance.exceptions.handlers import (
    error_boundary,
    handle_errors,
    processing_error,
)
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.utils.logging_config import get_core_logger, log_performance

logger = get_core_logger("data_processor")


class UKDataProcessor:
    """
    Enhanced UK data processor with FRS 102 compliance, advanced validation,
    and structured logging.
    """

    def __init__(
        self, base_currency: str = "GBP", industry: IndustryType = IndustryType.GENERAL
    ):
        self.xero_config = get_xero_config()
        self.frs102_mappings = self._load_frs102_account_mappings()
        self.precision = Decimal("0.01")
        self.base_currency = base_currency
        self.industry = industry
        self.currency_converter = CurrencyConverter(base_currency=base_currency)
        self.account_mapper = AdvancedAccountMapper(industry=industry)
        self.transaction_classifier = TransactionClassifier()
        self.duplicate_detector = DuplicateDetector()
        self.merge_engine = MergeEngine()
        self.transformation_engine = BatchTransformationEngine()
        self.quality_scorer = DataQualityScorer()
        self.validation_engine = DataValidationEngine()
        self.integrated_validation = IntegratedValidationEngine(self.validation_engine)

        self._setup_default_transformation_rules()
        logger.info(
            "UKDataProcessor initialized.",
            base_currency=base_currency,
            industry=industry.value,
        )

    def _load_frs102_account_mappings(self) -> Dict[str, FRS102AccountClassification]:
        """Load FRS 102-compliant account mappings for UK reporting."""
        # This mapping would typically be more extensive and loaded from a
        # configuration file or database.
        return {
            "1200": FRS102AccountClassification.DEBTORS,
            "3100": FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT,
            "4000": FRS102AccountClassification.TURNOVER,
            "5000": FRS102AccountClassification.COST_OF_SALES,
            "6100": FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
        }

    def _setup_default_transformation_rules(self):
        """Sets up default data transformation rules in the engine."""
        rules = [
            TransformationRule(
                name="normalize_text_fields",
                rule_type=TransformationRuleType.NORMALIZE,
                params={"fields": ["description", "reference"], "method": "lowercase"},
            ),
            TransformationRule(
                name="round_monetary_values",
                rule_type=TransformationRuleType.ROUND,
                params={"fields": ["amount", "unit_price"], "precision": 2},
            ),
        ]
        self.transformation_engine.load_rules(rules)
        logger.info(f"Loaded {len(rules)} default transformation rules.")

    @handle_errors(operation="process_financial_data")
    @log_performance()
    def process_financial_data(
        self, raw_data: List[Dict[str, Any]], organization_id: int
    ) -> List[Dict[str, Any]]:
        """
        A comprehensive pipeline to process raw financial data.
        """
        correlation_id = get_correlation_id()
        with error_boundary(
            "process_financial_data",
            correlation_id=correlation_id,
            organization_id=organization_id,
            record_count=len(raw_data),
        ):
            if not raw_data:
                logger.warning(
                    "process_financial_data called with empty dataset.",
                    organization_id=organization_id,
                )
                return []

            logger.info(
                "Starting financial data processing pipeline.",
                record_count=len(raw_data),
            )

            # 1. Validation
            validation_results = self.validation_engine.validate_batch(raw_data)
            if not validation_results["overall_status"] == "success":
                logger.error(
                    "Initial data validation failed.",
                    errors=validation_results["errors"],
                )
                raise processing_error(
                    "Initial data validation failed.",
                    validation_errors=validation_results["errors"],
                )
            logger.info("Initial data validation passed.")

            # 2. Transformation
            transformed_data = self.transformation_engine.apply_rules(raw_data)
            logger.info(
                "Data transformation complete.",
                rules_applied=len(self.transformation_engine.rules),
            )

            # 3. Classification
            classified_data = [
                self.transaction_classifier.classify(tx) for tx in transformed_data
            ]
            logger.info("Transaction classification complete.")

            # 4. FRS 102 Mapping
            final_data = [self._classify_for_frs102(tx) for tx in classified_data]
            logger.info("FRS 102 classification complete.")

            quality_score = self.quality_scorer.score_batch(final_data)
            logger.info(
                "Data processing pipeline finished.",
                final_record_count=len(final_data),
                data_quality_score=quality_score,
            )

            return final_data

    @handle_errors(operation="detect_and_merge_duplicates")
    @log_performance()
    def detect_and_merge_duplicates(
        self, transactions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Detects and merges duplicate transactions."""
        correlation_id = get_correlation_id()
        with error_boundary(
            "detect_and_merge_duplicates", correlation_id=correlation_id
        ):
            logger.info(
                "Starting duplicate detection.", record_count=len(transactions)
            )
            potential_duplicates = self.duplicate_detector.find_duplicates(
                transactions
            )
            if not potential_duplicates:
                logger.info("No potential duplicates found.")
                return transactions

            logger.info(
                "Found potential duplicate groups.",
                group_count=len(potential_duplicates),
            )
            merged_data = self.merge_engine.merge_duplicates(
                transactions, potential_duplicates
            )
            logger.info(
                "Duplicate merging process completed.",
                initial_count=len(transactions),
                final_count=len(merged_data),
            )
            return merged_data

    def _classify_for_frs102(
        self, transaction: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Helper to classify a single transaction for FRS 102 reporting."""
        account_code = transaction.get("account_code")
        if account_code in self.frs102_mappings:
            transaction["frs102_classification"] = self.frs102_mappings[
                account_code
            ].value
        else:
            # Use the advanced mapper as a fallback
            mapping_result = self.account_mapper.map_account(
                account_code, transaction.get("account_name", "")
            )
            transaction["frs102_classification"] = (
                mapping_result.frs102_classification.value
            )
            if mapping_result.confidence_score < 0.5:
                logger.debug(
                    "Low confidence FRS 102 mapping applied.",
                    account_code=account_code,
                    classification=mapping_result.frs102_classification.value,
                    confidence=mapping_result.confidence_score,
                )
        return transaction
