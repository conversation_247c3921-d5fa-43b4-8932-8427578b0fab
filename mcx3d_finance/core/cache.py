"""
Redis caching configuration and utilities for MCX3D Financials, refactored with
structured logging and centralized error handling.
"""
import hashlib
import json
import os
import pickle
from functools import wraps
from typing import Any, Callable, Optional, List, Dict

import redis
from redis import ConnectionPool, Redis

from mcx3d_finance.exceptions.handlers import handle_errors
from mcx3d_finance.utils.logging_config import get_core_logger

logger = get_core_logger("cache")


class CacheConfig:
    """Redis cache configuration."""

    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD")
    REDIS_MAX_CONNECTIONS: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "50"))
    REDIS_SOCKET_TIMEOUT: int = int(os.getenv("REDIS_SOCKET_TIMEOUT", "5"))
    REDIS_HEALTH_CHECK_INTERVAL: int = int(
        os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30")
    )

    CACHE_TTL_DEFAULT: int = int(os.getenv("CACHE_TTL_DEFAULT", "3600"))  # 1 hour
    CACHE_TTL_SHORT: int = int(os.getenv("CACHE_TTL_SHORT", "300"))  # 5 minutes
    CACHE_TTL_MEDIUM: int = int(os.getenv("CACHE_TTL_MEDIUM", "1800"))  # 30 minutes
    CACHE_TTL_LONG: int = int(os.getenv("CACHE_TTL_LONG", "86400"))  # 24 hours

    KEY_PREFIX: str = "mcx3d"
    KEY_SEPARATOR: str = ":"
    USE_PICKLE: bool = os.getenv("CACHE_USE_PICKLE", "true").lower() == "true"


class RedisCache:
    """Enhanced Redis cache client with connection pooling, batch operations, and comprehensive statistics."""

    def __init__(self):
        self._pool: Optional[ConnectionPool] = None
        self._client: Optional[Redis] = None
        self._connected: bool = False
        
        # Statistics tracking (similar to SharedCacheManager)
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0,
            'batch_operations': 0
        }
        
        # Cache type prefixes for organization
        self.cache_types = {
            'validation_results': 'val',
            'quality_scores': 'qual',
            'transformation_results': 'trans',
            'organization_lookups': 'org',
            'general': 'gen'
        }
        
        self._initialize_connection()

    def _initialize_connection(self) -> None:
        """Initialize Redis connection pool."""
        try:
            pool_kwargs = {
                "max_connections": CacheConfig.REDIS_MAX_CONNECTIONS,
                "socket_timeout": CacheConfig.REDIS_SOCKET_TIMEOUT,
                "health_check_interval": CacheConfig.REDIS_HEALTH_CHECK_INTERVAL,
                "password": CacheConfig.REDIS_PASSWORD,
            }
            self._pool = redis.ConnectionPool.from_url(
                CacheConfig.REDIS_URL, **pool_kwargs
            )
            self._client = redis.Redis(
                connection_pool=self._pool, decode_responses=False
            )
            self._client.ping()
            self._connected = True
            logger.info("Enhanced Redis cache connected successfully.", redis_url=CacheConfig.REDIS_URL)
        except redis.exceptions.ConnectionError as e:
            logger.error(
                "Failed to connect to Redis. Caching will be disabled.",
                redis_url=CacheConfig.REDIS_URL,
                error=str(e),
                exc_info=True,
            )
            self._connected = False
        except Exception as e:
            logger.error(
                "An unexpected error occurred during Redis initialization.",
                error=str(e),
                exc_info=True,
            )
            self._connected = False

    def _generate_cache_key(self, cache_type: str = 'general', **kwargs) -> str:
        """Generate consistent cache key from parameters (SharedCacheManager compatibility)."""
        prefix = self.cache_types.get(cache_type, 'gen')
        key_parts = [CacheConfig.KEY_PREFIX, prefix]
        
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        
        return CacheConfig.KEY_SEPARATOR.join(key_parts)

    def is_connected(self) -> bool:
        """Check if Redis is connected."""
        return self._connected

    def _serialize(self, value: Any) -> bytes:
        """Serialize value for storage."""
        if CacheConfig.USE_PICKLE:
            return pickle.dumps(value)
        return json.dumps(value).encode("utf-8")

    def _deserialize(self, data: Optional[bytes]) -> Any:
        """Deserialize value from storage."""
        if data is None:
            return None
        if CacheConfig.USE_PICKLE:
            return pickle.loads(data)
        return json.loads(data.decode("utf-8"))

    @handle_errors(operation="cache_get")
    def get(self, key: str = None, cache_type: str = 'general', **kwargs) -> Optional[Any]:
        """Get value from cache with SharedCacheManager-style interface."""
        if not self._connected:
            self.cache_stats['errors'] += 1
            return None

        # Support both direct key and cache_type + kwargs interface
        if key is None:
            key = self._generate_cache_key(cache_type, **kwargs)

        logger.debug("Getting key from cache", cache_key=key)
        try:
            data = self._client.get(key)
            if data is not None:
                self.cache_stats['hits'] += 1
                return self._deserialize(data)
            else:
                self.cache_stats['misses'] += 1
                return None
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error("Cache get error", cache_key=key, error=str(e))
            return None

    @handle_errors(operation="cache_set")
    def set(self, key: str = None, value: Any = None, ttl: Optional[int] = None, 
            cache_type: str = 'general', data: Any = None, **kwargs) -> bool:
        """Set value in cache with SharedCacheManager-style interface."""
        if not self._connected:
            self.cache_stats['errors'] += 1
            return False

        # Support both direct key/value and cache_type + data interface
        if key is None:
            key = self._generate_cache_key(cache_type, **kwargs)
        if value is None and data is not None:
            value = data

        ttl = ttl or CacheConfig.CACHE_TTL_DEFAULT
        serialized_data = self._serialize(value)
        
        logger.debug("Setting key in cache", cache_key=key, ttl=ttl)
        try:
            result = bool(self._client.setex(key, ttl, serialized_data))
            if result:
                self.cache_stats['sets'] += 1
            else:
                self.cache_stats['errors'] += 1
            return result
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error("Cache set error", cache_key=key, error=str(e))
            return False

    @handle_errors(operation="cache_delete")
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if not self._connected:
            self.cache_stats['errors'] += 1
            return False

        logger.debug("Deleting key from cache", cache_key=key)
        try:
            result = bool(self._client.delete(key))
            if result:
                self.cache_stats['deletes'] += 1
            return result
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error("Cache delete error", cache_key=key, error=str(e))
            return False

    @handle_errors(operation="cache_invalidate")
    def invalidate(self, cache_type: Optional[str] = None, **kwargs) -> int:
        """Invalidate cache entries (SharedCacheManager compatibility)."""
        if not self._connected:
            return 0

        if cache_type is None:
            # Clear all cache types
            pattern = f"{CacheConfig.KEY_PREFIX}:*"
        else:
            # Clear specific cache type
            prefix = self.cache_types.get(cache_type, cache_type)
            if kwargs:
                # Build pattern with specific parameters
                key_pattern = self._generate_cache_key(cache_type, **kwargs)
                pattern = f"{key_pattern}*"
            else:
                # Clear all entries of this type
                pattern = f"{CacheConfig.KEY_PREFIX}:{prefix}:*"

        return self.clear_pattern(pattern)

    @handle_errors(operation="cache_batch_get")
    def batch_get(self, cache_type: str, keys_params: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Batch get operation for multiple cache keys (SharedCacheManager compatibility)."""
        if not self._connected:
            return {}

        self.cache_stats['batch_operations'] += 1
        results = {}
        
        # Generate all keys
        keys = [self._generate_cache_key(cache_type, **params) for params in keys_params]
        
        try:
            # Use Redis MGET for efficient batch retrieval
            values = self._client.mget(keys)
            
            for i, (key, value) in enumerate(zip(keys, values)):
                if value is not None:
                    results[key] = self._deserialize(value)
                    self.cache_stats['hits'] += 1
                else:
                    self.cache_stats['misses'] += 1
                    
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error("Batch get error", cache_type=cache_type, error=str(e))
            
        return results

    @handle_errors(operation="cache_batch_set")
    def batch_set(self, cache_type: str, data_params: List[Dict[str, Any]], ttl: Optional[int] = None) -> bool:
        """Batch set operation for multiple cache entries (SharedCacheManager compatibility)."""
        if not self._connected:
            return False

        self.cache_stats['batch_operations'] += 1
        ttl = ttl or CacheConfig.CACHE_TTL_DEFAULT
        
        try:
            pipe = self._client.pipeline()
            
            for item in data_params:
                data = item.pop('data', None)
                if data is not None:
                    key = self._generate_cache_key(cache_type, **item)
                    serialized_data = self._serialize(data)
                    pipe.setex(key, ttl, serialized_data)
            
            results = pipe.execute()
            success_count = sum(1 for r in results if r)
            self.cache_stats['sets'] += success_count
            
            return len(results) > 0 and all(results)
            
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error("Batch set error", cache_type=cache_type, error=str(e))
            return False

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics (SharedCacheManager compatibility)."""
        if not self._connected:
            return {
                'connected': False,
                'error': 'Redis not connected'
            }

        try:
            redis_info = self._client.info()
            return {
                'connected': True,
                'hits': self.cache_stats['hits'],
                'misses': self.cache_stats['misses'],
                'sets': self.cache_stats['sets'],
                'deletes': self.cache_stats['deletes'],
                'errors': self.cache_stats['errors'],
                'batch_operations': self.cache_stats['batch_operations'],
                'hit_rate': self.cache_stats['hits'] / max(1, self.cache_stats['hits'] + self.cache_stats['misses']),
                'redis_memory_used': redis_info.get('used_memory_human', 'unknown'),
                'redis_connected_clients': redis_info.get('connected_clients', 0),
                'redis_keyspace_hits': redis_info.get('keyspace_hits', 0),
                'redis_keyspace_misses': redis_info.get('keyspace_misses', 0)
            }
        except Exception as e:
            logger.error("Error getting cache statistics", error=str(e))
            return {
                'connected': self._connected,
                'error': str(e),
                **self.cache_stats
            }

    @handle_errors(operation="cache_exists")
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        if not self._connected:
            return False

        return bool(self._client.exists(key))

    @handle_errors(operation="cache_clear_pattern")
    def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern."""
        if not self._connected:
            return 0

        keys = [key.decode("utf-8") for key in self._client.scan_iter(pattern)]
        if keys:
            logger.info("Clearing keys by pattern", pattern=pattern, count=len(keys))
            deleted = self._client.delete(*keys)
            self.cache_stats['deletes'] += deleted
            return deleted
        return 0

    def clear_all_caches(self) -> None:
        """Clear all caches (SharedCacheManager compatibility)."""
        if self._connected:
            self.clear_pattern(f"{CacheConfig.KEY_PREFIX}:*")
            # Reset statistics
            self.cache_stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0,
                'errors': 0,
                'batch_operations': 0
            }

    @handle_errors(operation="cache_get_ttl")
    def get_ttl(self, key: str) -> Optional[int]:
        """Get TTL for a key in seconds."""
        if not self._connected:
            return None

        ttl = self._client.ttl(key)
        return ttl if ttl > 0 else None

    def close(self) -> None:
        """Close Redis connection."""
        if self._connected:
            if self._client:
                self._client.close()
            if self._pool:
                self._pool.disconnect()
            self._connected = False
            logger.info("Enhanced Redis cache connection closed.")


_cache_instance: Optional[RedisCache] = None


def get_cache() -> RedisCache:
    """Get global cache instance."""
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = RedisCache()
    return _cache_instance


def _generate_cache_key(func: Callable, *args, **kwargs) -> str:
    """Generate a consistent cache key for a function call."""
    key_parts = [
        CacheConfig.KEY_PREFIX,
        func.__module__,
        func.__name__,
    ]
    hasher = hashlib.md5()
    hasher.update(str(args).encode())
    hasher.update(str(sorted(kwargs.items())).encode())
    key_parts.append(hasher.hexdigest())

    return CacheConfig.KEY_SEPARATOR.join(key_parts)


def cached(ttl: Optional[int] = None):
    """
    Decorator for caching function results, with structured logging and
    graceful error handling.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache = get_cache()
            if not cache.is_connected():
                logger.warning(
                    "Cache is not connected. Bypassing cache.",
                    function=f"{func.__module__}.{func.__name__}",
                )
                return func(*args, **kwargs)

            key = _generate_cache_key(func, *args, **kwargs)
            cached_result = cache.get(key)

            if cached_result is not None:
                logger.debug("Cache hit.", cache_key=key)
                return cached_result

            logger.debug("Cache miss.", cache_key=key)
            result = func(*args, **kwargs)

            if result is not None:
                cache.set(key, result, ttl)

            return result

        return wrapper
    return decorator


def invalidate_cache(pattern: str) -> int:
    """Invalidate cache entries matching a pattern."""
    cache = get_cache()
    if not cache.is_connected():
        return 0
    count = cache.clear_pattern(f"{CacheConfig.KEY_PREFIX}{CacheConfig.KEY_SEPARATOR}{pattern}*")
    logger.info(
        "Invalidated cache entries.",
        pattern=pattern,
        count=count,
    )
    return count


def invalidate_org_cache(org_id: int) -> None:
    """Invalidate all cache entries for an organization."""
    org_pattern = f"*:{org_id}:*"
    invalidate_cache(org_pattern)


@handle_errors(operation="get_cache_stats")
def get_cache_stats() -> dict:
    """Get cache statistics."""
    cache = get_cache()
    if not cache.is_connected() or not cache._client:
        return {"connected": False, "error": "Redis not connected"}

    info = cache._client.info()
    hits = info.get("keyspace_hits", 0)
    misses = info.get("keyspace_misses", 0)
    total = hits + misses
    hit_rate = (hits / total * 100) if total > 0 else 0

    return {
        "connected": True,
        "redis_version": info.get("redis_version", "N/A"),
        "used_memory_human": info.get("used_memory_human", "N/A"),
        "connected_clients": info.get("connected_clients", 0),
        "total_commands_processed": info.get("total_commands_processed", 0),
        "keyspace_hits": hits,
        "keyspace_misses": misses,
        "hit_rate_percent": round(hit_rate, 2),
        "evicted_keys": info.get("evicted_keys", 0),
    }


# Compatibility layer for SharedCacheManager migration
class SharedCacheManager:
    """
    Compatibility wrapper around RedisCache to maintain backward compatibility
    during migration from the old SharedCacheManager to consolidated Redis caching.
    
    This class will be deprecated once all code is migrated to use RedisCache directly.
    """
    
    def __init__(self, cache_ttl_seconds: int = 300, max_cache_size: int = 1000):
        """Initialize with RedisCache backend."""
        self._redis_cache = get_cache()
        self.cache_ttl_seconds = cache_ttl_seconds
        self.max_cache_size = max_cache_size  # Not used with Redis, kept for compatibility
        
        logger.warning(
            "SharedCacheManager is deprecated. Please migrate to RedisCache directly.",
            migration_guide="Use get_cache() instead of get_shared_cache_manager()"
        )
    
    @property
    def circuit_breaker(self):
        """Compatibility property - circuit breaker functionality moved to RedisCache."""
        # For now, return None to maintain compatibility
        # Circuit breaker logic should be handled at the application level
        return None
    
    def _generate_cache_key(self, cache_type: str, **kwargs) -> str:
        """Generate cache key - delegates to RedisCache."""
        return self._redis_cache._generate_cache_key(cache_type, **kwargs)
    
    def get(self, cache_type: str, **kwargs) -> Optional[Any]:
        """Get item from cache - delegates to RedisCache."""
        return self._redis_cache.get(cache_type=cache_type, **kwargs)
    
    def set(self, cache_type: str, data: Any, **kwargs) -> None:
        """Set item in cache - delegates to RedisCache."""
        self._redis_cache.set(cache_type=cache_type, data=data, ttl=self.cache_ttl_seconds, **kwargs)
    
    def invalidate(self, cache_type: Optional[str] = None, **kwargs) -> None:
        """Invalidate cache entries - delegates to RedisCache."""
        self._redis_cache.invalidate(cache_type=cache_type, **kwargs)
    
    def batch_get(self, cache_type: str, keys_params: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Batch get operation - delegates to RedisCache."""
        return self._redis_cache.batch_get(cache_type, keys_params)
    
    def batch_set(self, cache_type: str, data_params: List[Dict[str, Any]]) -> None:
        """Batch set operation - delegates to RedisCache."""
        self._redis_cache.batch_set(cache_type, data_params, ttl=self.cache_ttl_seconds)
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache statistics - delegates to RedisCache."""
        return self._redis_cache.get_cache_statistics()
    
    def clear_all_caches(self) -> None:
        """Clear all caches - delegates to RedisCache."""
        self._redis_cache.clear_all_caches()
    
    # Async methods for compatibility (these were in the original SharedCacheManager)
    async def get_async(self, cache_type: str, **kwargs) -> Optional[Any]:
        """Async get - for now, just calls synchronous version."""
        return self.get(cache_type, **kwargs)
    
    async def set_async(self, cache_type: str, data: Any, **kwargs) -> None:
        """Async set - for now, just calls synchronous version."""
        self.set(cache_type, data, **kwargs)
    
    async def validation_timeout_context(self, timeout_seconds: float, operation_name: str = "validation"):
        """Async context manager for validation operations - simplified implementation."""
        import asyncio
        from contextlib import asynccontextmanager
        
        @asynccontextmanager
        async def timeout_context():
            try:
                yield
            except asyncio.TimeoutError:
                logger.warning(f"Operation '{operation_name}' timed out after {timeout_seconds}s")
                raise
        
        return timeout_context()


# Global instances for backward compatibility
_shared_cache_manager = None
_shared_executor = None


def get_shared_cache_manager() -> SharedCacheManager:
    """Get the global shared cache manager instance (compatibility function)."""
    global _shared_cache_manager
    if _shared_cache_manager is None:
        _shared_cache_manager = SharedCacheManager()
    return _shared_cache_manager


def get_shared_executor():
    """Get the global shared thread pool executor (compatibility function)."""
    global _shared_executor
    if _shared_executor is None:
        from concurrent.futures import ThreadPoolExecutor
        _shared_executor = ThreadPoolExecutor(max_workers=4)
    return _shared_executor


def shutdown_shared_executor():
    """Shutdown the shared executor (compatibility function)."""
    global _shared_executor
    if _shared_executor is not None:
        _shared_executor.shutdown(wait=True)
        _shared_executor = None
