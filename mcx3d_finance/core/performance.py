"""
Performance optimization configuration and utilities for MCX3D Financials,
refactored with structured logging and centralized error handling.
"""
import asyncio
import os
import time
from concurrent.futures import ThreadPoolExecutor
from functools import wraps
from typing import Any, Callable, Dict, List, Optional

from mcx3d_finance.exceptions.handlers import handle_errors
from mcx3d_finance.utils.logging_config import get_core_logger

logger = get_core_logger("performance")


class PerformanceConfig:
    """Performance optimization configuration."""

    MAX_WORKERS: int = int(os.getenv("MAX_WORKERS", "10"))
    THREAD_NAME_PREFIX: str = "mcx3d-worker"
    SLOW_API_THRESHOLD: float = float(os.getenv("SLOW_API_THRESHOLD", "2.0"))


_executor: Optional[ThreadPoolExecutor] = None


def get_executor() -> ThreadPoolExecutor:
    """Get the global thread pool executor, creating it if it doesn't exist."""
    global _executor
    if _executor is None:
        _executor = ThreadPoolExecutor(
            max_workers=PerformanceConfig.MAX_WORKERS,
            thread_name_prefix=PerformanceConfig.THREAD_NAME_PREFIX,
        )
        logger.info("ThreadPoolExecutor initialized.", max_workers=PerformanceConfig.MAX_WORKERS)
    return _executor


def measure_performance(operation_name: Optional[str] = None) -> Callable:
    """
    Decorator to measure and log the execution time of a function.
    This is similar to @log_performance but kept here for core logic.
    """
    def decorator(func: Callable) -> Callable:
        op_name = operation_name or f"{func.__module__}.{func.__name__}"

        @wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.perf_counter()
            try:
                return await func(*args, **kwargs)
            finally:
                execution_time = time.perf_counter() - start_time
                log_details = {"operation": op_name, "execution_time_ms": round(execution_time * 1000, 2)}
                if execution_time > PerformanceConfig.SLOW_API_THRESHOLD:
                    logger.warning("Slow operation detected.", **log_details)
                else:
                    logger.info("Operation performance.", **log_details)

        @wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.perf_counter()
            try:
                return func(*args, **kwargs)
            finally:
                execution_time = time.perf_counter() - start_time
                log_details = {"operation": op_name, "execution_time_ms": round(execution_time * 1000, 2)}
                if execution_time > PerformanceConfig.SLOW_API_THRESHOLD:
                    logger.warning("Slow operation detected.", **log_details)
                else:
                    logger.info("Operation performance.", **log_details)

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


class BatchProcessor:
    """Utility for processing large datasets in manageable batches."""

    def __init__(self, batch_size: int = 1000):
        self.batch_size = batch_size
        logger.info("BatchProcessor initialized.", batch_size=self.batch_size)

    @handle_errors(operation="process_in_batches")
    def process_in_batches(
        self, items: List[Any], processor: Callable[[List[Any]], Any]
    ) -> List[Any]:
        """Processes a list of items in batches."""
        if not items:
            return []

        results = []
        total_items = len(items)
        total_batches = (total_items + self.batch_size - 1) // self.batch_size

        logger.info(
            "Starting batch processing.",
            total_items=total_items,
            batch_size=self.batch_size,
            total_batches=total_batches,
        )

        for i in range(0, total_items, self.batch_size):
            batch = items[i : i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            logger.debug(f"Processing batch {batch_num}/{total_batches}.")
            batch_results = processor(batch)
            if isinstance(batch_results, list):
                results.extend(batch_results)
            elif batch_results is not None:
                results.append(batch_results)

        logger.info("Batch processing completed.", processed_items=len(results))
        return results

    @handle_errors(operation="process_in_batches_async")
    async def process_in_batches_async(
        self, items: List[Any], processor: Callable[[List[Any]], Any]
    ) -> List[Any]:
        """Processes a list of items in batches asynchronously."""
        if not items:
            return []

        results = []
        total_items = len(items)
        total_batches = (total_items + self.batch_size - 1) // self.batch_size

        logger.info(
            "Starting async batch processing.",
            total_items=total_items,
            batch_size=self.batch_size,
            total_batches=total_batches,
        )

        for i in range(0, total_items, self.batch_size):
            batch = items[i : i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            logger.debug(f"Processing async batch {batch_num}/{total_batches}.")
            batch_results = await processor(batch)
            if isinstance(batch_results, list):
                results.extend(batch_results)
            elif batch_results is not None:
                results.append(batch_results)

        logger.info("Async batch processing completed.", processed_items=len(results))
        return results


def cleanup_resources() -> None:
    """Cleans up performance-related resources like the global thread pool."""
    global _executor
    if _executor:
        logger.info("Shutting down ThreadPoolExecutor.")
        _executor.shutdown(wait=True)
        _executor = None
        logger.info("ThreadPoolExecutor shut down successfully.")
