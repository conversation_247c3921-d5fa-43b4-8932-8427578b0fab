"""
Currency conversion module with real-time exchange rates and multi-currency support.
Provides FRS 102-compliant currency conversion with robust error handling,
circuit breakers, and structured logging.
"""
from dataclasses import dataclass
from datetime import datetime
from decimal import ROUND_HALF_UP, Decimal
from enum import Enum
from functools import lru_cache
from typing import Any, Dict, List, Optional

import requests

from mcx3d_finance.exceptions.handlers import handle_errors
from mcx3d_finance.exceptions.recovery import with_circuit_breaker, with_retry
from mcx3d_finance.utils.logging_config import get_core_logger

logger = get_core_logger("currency_converter")


class CurrencyCode(Enum):
    """Supported currency codes (ISO 4217)."""

    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    CAD = "CAD"
    AUD = "AUD"
    JPY = "JPY"
    CHF = "CHF"
    # Add other currencies as needed


@dataclass
class ExchangeRate:
    """Exchange rate data structure."""

    from_currency: str
    to_currency: str
    rate: Decimal
    timestamp: datetime
    source: str

    def is_stale(self, max_age_hours: int = 24) -> bool:
        """Check if the exchange rate is older than the max age."""
        return (
            datetime.utcnow() - self.timestamp
        ).total_seconds() > max_age_hours * 3600


@dataclass
class CurrencyConversion:
    """Result of a currency conversion operation."""

    original_amount: Decimal
    converted_amount: Decimal
    from_currency: str
    to_currency: str
    exchange_rate: Decimal
    conversion_date: datetime


class CurrencyConverter:
    """
    Multi-currency converter with real-time exchange rates, caching,
    and fallback mechanisms.
    """

    def __init__(self, base_currency: str = "USD", api_key: Optional[str] = None):
        self.base_currency = base_currency
        self.api_key = api_key
        self.precision = Decimal("0.000001")  # 6 decimal places for rates
        self.amount_precision = Decimal("0.01")  # 2 decimal places for amounts
        self._rate_cache: Dict[str, ExchangeRate] = {}
        self._fallback_rates = self._load_fallback_rates()
        logger.info(
            "CurrencyConverter initialized", base_currency=self.base_currency
        )

    def _load_fallback_rates(self) -> Dict[str, Decimal]:
        """Load static fallback rates for when the API is unavailable."""
        return {
            "EUR": Decimal("0.92"),
            "GBP": Decimal("0.79"),
            "CAD": Decimal("1.36"),
            "AUD": Decimal("1.50"),
            "JPY": Decimal("157.00"),
            "CHF": Decimal("0.90"),
        }

    def _round_rate(self, rate: Decimal) -> Decimal:
        """Round an exchange rate to the defined precision."""
        return rate.quantize(self.precision, rounding=ROUND_HALF_UP)

    def _round_amount(self, amount: Decimal) -> Decimal:
        """Round a monetary amount to 2 decimal places."""
        return amount.quantize(self.amount_precision, rounding=ROUND_HALF_UP)

    @lru_cache(maxsize=128)
    @with_retry(attempts=3, delay=2, backoff=2)
    @with_circuit_breaker(name="exchange_rate_api", max_failures=5, reset_timeout=60)
    def _fetch_exchange_rate_from_api(
        self, from_currency: str, to_currency: str
    ) -> Optional[ExchangeRate]:
        """Fetch exchange rate from an external API, with caching and resilience."""
        # In a real app, you'd use a more reliable, paid API.
        # This uses exchangerate-api.com for demonstration.
        url = f"https://api.exchangerate-api.com/v4/latest/{from_currency}"
        logger.info(
            "Fetching exchange rate from API",
            from_currency=from_currency,
            to_currency=to_currency,
        )
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # Will trigger retry/circuit breaker on failure

        data = response.json()
        rates = data.get("rates", {})
        if to_currency in rates:
            rate = self._round_rate(Decimal(str(rates[to_currency])))
            return ExchangeRate(
                from_currency, to_currency, rate, datetime.utcnow(), "api"
            )
        logger.warning(
            "Target currency not found in API response",
            from_currency=from_currency,
            to_currency=to_currency,
        )
        return None

    @handle_errors(operation="_get_fallback_rate")
    def _get_fallback_rate(
        self, from_currency: str, to_currency: str
    ) -> Optional[ExchangeRate]:
        """Get a fallback exchange rate from static data."""
        if from_currency == self.base_currency and to_currency in self._fallback_rates:
            rate = self._fallback_rates[to_currency]
        elif to_currency == self.base_currency and from_currency in self._fallback_rates:
            rate = Decimal("1") / self._fallback_rates[from_currency]
        elif from_currency in self._fallback_rates and to_currency in self._fallback_rates:
            from_to_base = Decimal("1") / self._fallback_rates[from_currency]
            base_to_target = self._fallback_rates[to_currency]
            rate = from_to_base * base_to_target
        else:
            return None

        return ExchangeRate(
            from_currency,
            to_currency,
            self._round_rate(rate),
            datetime.utcnow(),
            "fallback",
        )

    @handle_errors(operation="get_exchange_rate")
    def get_exchange_rate(
        self, from_currency: str, to_currency: str, force_refresh: bool = False
    ) -> Optional[ExchangeRate]:
        """Get the exchange rate between two currencies, using cache, API, and fallbacks."""
        if from_currency == to_currency:
            return ExchangeRate(from_currency, to_currency, Decimal("1"), datetime.utcnow(), "identity")

        cache_key = f"{from_currency}_{to_currency}"
        if not force_refresh and cache_key in self._rate_cache:
            cached_rate = self._rate_cache[cache_key]
            if not cached_rate.is_stale():
                logger.debug("Exchange rate cache hit", cache_key=cache_key)
                return cached_rate

        rate = self._fetch_exchange_rate_from_api(from_currency, to_currency)
        if rate:
            self._rate_cache[cache_key] = rate
            return rate

        logger.warning(
            "API fetch failed, attempting to use fallback rate",
            from_currency=from_currency,
            to_currency=to_currency,
        )
        fallback_rate = self._get_fallback_rate(from_currency, to_currency)
        if fallback_rate:
            self._rate_cache[cache_key] = fallback_rate
            return fallback_rate

        logger.error(
            "Could not obtain any exchange rate",
            from_currency=from_currency,
            to_currency=to_currency,
        )
        return None

    @handle_errors(operation="convert_amount")
    def convert_amount(
        self,
        amount: Decimal,
        from_currency: str,
        to_currency: str,
        conversion_date: Optional[datetime] = None,
    ) -> Optional[CurrencyConversion]:
        """Convert an amount from one currency to another."""
        exchange_rate_data = self.get_exchange_rate(from_currency, to_currency)
        if not exchange_rate_data:
            return None

        converted_amount = self._round_amount(amount * exchange_rate_data.rate)
        return CurrencyConversion(
            original_amount=amount,
            converted_amount=converted_amount,
            from_currency=from_currency,
            to_currency=to_currency,
            exchange_rate=exchange_rate_data.rate,
            conversion_date=conversion_date or datetime.utcnow(),
        )

    def get_supported_currencies(self) -> List[str]:
        """Get the list of supported currency codes."""
        return [c.value for c in CurrencyCode]

    def clear_cache(self):
        """Clear the in-memory and LRU exchange rate caches."""
        self._rate_cache.clear()
        self._fetch_exchange_rate_from_api.cache_clear()
        logger.info("CurrencyConverter caches cleared.")
