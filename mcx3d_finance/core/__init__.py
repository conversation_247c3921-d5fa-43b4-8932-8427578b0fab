"""
This module serves as the primary entry point to the core business logic of the
mcx3d_finance application. It simplifies access to key components, such as
financial calculators and valuation models, by exposing them at the top level
of the 'core' package.

This approach allows other parts of the application (e.g., API endpoints, CLI commands)
to import these components with a cleaner, more stable path:

from mcx3d_finance.core import ProfitAndLossCalculator

instead of:

from mcx3d_finance.core.financials.profit_and_loss_calculator import ProfitAndLossCalculator
"""

from .financial_calculators import (
    DCFValuation,
    ProfitAndLossCalculator,
    SaasKpiCalculator,
)

__all__ = [
    "ProfitAndLossCalculator",
    "SaasKpiCalculator",
    "DCFValuation",
]
