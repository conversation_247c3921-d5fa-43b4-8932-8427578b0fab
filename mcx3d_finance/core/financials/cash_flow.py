"""
UK Companies House-compliant Cash Flow Statement generator with FRS 102 standards.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
from sqlalchemy.orm import Session
import json

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import BankTransaction
# from ...core.account_classifications import FRS102AccountClassification

logger = LoggerFactory.get_logger(__name__, domain="core")


class UKCashFlowGenerator:
    """Generate UK Companies House-compliant cash flow statements with FRS 102 standards."""

    def __init__(self, organization_id: int):
        self.organization_id = organization_id
        self.precision = Decimal("0.01")
        self.db: Session = SessionLocal()

    def generate_cash_flow_statement(
        self,
        from_date: datetime,
        to_date: datetime,
        comparative_from: Optional[datetime] = None,
        comparative_to: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """
        Generate UK-compliant Cash Flow Statement.

        Args:
            from_date: Period start date.
            to_date: Period end date.
            comparative_from: Comparative period start date.
            comparative_to: Comparative period end date.

        Returns:
            Complete cash flow statement with UK Companies House formatting.
        """
        try:
            logger.info(
                f"Generating UK-compliant cash flow statement for period {from_date.strftime('%d %B %Y')} to {to_date.strftime('%d %B %Y')}"
            )

            cash_flow_data = self._calculate_uk_cash_flow(from_date, to_date)

            comparative_data = None
            if comparative_from and comparative_to:
                comparative_data = self._calculate_uk_cash_flow(comparative_from, comparative_to)

            formatted_statement = self._format_for_uk_companies_house(
                cash_flow_data,
                from_date,
                to_date,
                comparative_data,
            )

            logger.info("UK format cash flow statement generation completed successfully.")
            return formatted_statement

        except Exception as e:
            logger.error(f"Error generating cash flow statement: {e}")
            raise
        finally:
            self.db.close()

    def _calculate_uk_cash_flow(self, from_date: datetime, to_date: datetime) -> Dict[str, Any]:
        """Calculate cash flow from transactions using FRS 102 classifications."""
        transactions = self.db.query(BankTransaction).filter(
            BankTransaction.organization_id == self.organization_id,
            BankTransaction.date >= from_date,
            BankTransaction.date <= to_date
        ).all()

        cash_flow = {
            "operating_activities": 0,
            "investing_activities": 0,
            "financing_activities": 0,
        }

        for trans in transactions:
            # Simplified classification based on transaction type
            if trans.type == "RECEIVE":
                cash_flow["operating_activities"] += trans.total
            elif trans.type == "SPEND":
                # Further classify spend transactions
                if self._is_investing_activity(trans):
                    cash_flow["investing_activities"] -= trans.total
                elif self._is_financing_activity(trans):
                    cash_flow["financing_activities"] -= trans.total
                else:
                    cash_flow["operating_activities"] -= trans.total

        return cash_flow

    def _is_investing_activity(self, transaction: BankTransaction) -> bool:
        """Determine if a transaction is an investing activity."""
        # Placeholder logic
        if transaction.line_items:
            # Handle double-encoded JSON strings
            line_items_raw = json.loads(transaction.line_items)
            # If the result is still a string, parse it again
            if isinstance(line_items_raw, str):
                line_items = json.loads(line_items_raw)
            else:
                line_items = line_items_raw

            for item in line_items:
                if "asset" in item.get("Description", "").lower():
                    return True
        return False

    def _is_financing_activity(self, transaction: BankTransaction) -> bool:
        """Determine if a transaction is a financing activity."""
        # Placeholder logic
        if transaction.line_items:
            # Handle double-encoded JSON strings
            line_items_raw = json.loads(transaction.line_items)
            # If the result is still a string, parse it again
            if isinstance(line_items_raw, str):
                line_items = json.loads(line_items_raw)
            else:
                line_items = line_items_raw

            for item in line_items:
                if "loan" in item.get("Description", "").lower() or "equity" in item.get("Description", "").lower():
                    return True
        return False

    def _format_for_uk_companies_house(
        self,
        cash_flow_data: Dict[str, Any],
        from_date: datetime,
        to_date: datetime,
        comparative_data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Format cash flow data for UK Companies House presentation."""

        net_increase_in_cash = sum(cash_flow_data.values())

        # These would be calculated properly in a real implementation
        cash_at_beginning = 100000
        cash_at_end = cash_at_beginning + net_increase_in_cash

        return {
            "header": {
                "company_name": "MCX3D LTD",
                "statement_title": "CASH FLOW STATEMENT",
                "period_description": f"for the year ended {to_date.strftime('%d %B %Y')}",
                "currency": "GBP",
            },
            "cash_flows_from_operating_activities": cash_flow_data["operating_activities"],
            "cash_flows_from_investing_activities": cash_flow_data["investing_activities"],
            "cash_flows_from_financing_activities": cash_flow_data["financing_activities"],
            "net_increase_in_cash": net_increase_in_cash,
            "cash_at_beginning_of_year": cash_at_beginning,
            "cash_at_end_of_year": cash_at_end,
            "comparative_data": comparative_data
        }
