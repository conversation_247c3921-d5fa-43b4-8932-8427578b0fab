"""
Financial Statement Generator wrapper for API compatibility.
"""

from typing import Dict, Any, Optional
from datetime import date, datetime
from sqlalchemy.orm import Session

from mcx3d_finance.core.financial_calculators import UKFinancialCalculator
from mcx3d_finance.api.schemas import StatementType


class FinancialStatementGenerator:
    """
    Wrapper class for generating financial statements.
    
    Provides compatibility layer for the API endpoints.
    """
    
    def __init__(self, db: Session) -> None:
        """Initialize the generator with database session."""
        self.db = db
        self.calculator = UKFinancialCalculator(db)
    
    async def generate(
        self,
        organization_id: str,
        statement_type: StatementType,
        end_date: date,
        periods: int = 1,
        comparison_periods: int = 0,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Generate financial statement based on type.
        
        Args:
            organization_id: Organization ID
            statement_type: Type of statement to generate
            end_date: End date for the statement
            periods: Number of periods to include
            comparison_periods: Number of comparison periods
            **kwargs: Additional options
            
        Returns:
            Generated financial statement data
        """
        # Convert async to sync call since UKFinancialCalculator is synchronous
        if statement_type == StatementType.INCOME_STATEMENT:
            return self._generate_income_statement(
                organization_id, end_date, periods, comparison_periods, **kwargs
            )
        elif statement_type == StatementType.BALANCE_SHEET:
            return self._generate_balance_sheet(
                organization_id, end_date, **kwargs
            )
        elif statement_type == StatementType.CASH_FLOW:
            return self._generate_cash_flow(
                organization_id, end_date, periods, comparison_periods, **kwargs
            )
        else:
            raise ValueError(f"Unsupported statement type: {statement_type}")
    
    def _generate_income_statement(
        self,
        organization_id: str,
        end_date: date,
        periods: int,
        comparison_periods: int,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate income statement."""
        # Calculate the start date based on periods
        # Assuming monthly periods for now
        from dateutil.relativedelta import relativedelta
        start_date = end_date - relativedelta(months=periods)
        
        # Use the financial calculator to generate P&L
        result = self.calculator.generate_profit_and_loss(
            organization_id=organization_id,
            start_date=start_date,
            end_date=end_date,
            include_comparisons=comparison_periods > 0,
            comparison_periods=comparison_periods
        )
        
        return {
            "statement_type": "income_statement",
            "organization_id": organization_id,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "data": result,
            "metadata": {
                "generated_at": datetime.utcnow().isoformat(),
                "periods": periods,
                "comparison_periods": comparison_periods
            }
        }
    
    def _generate_balance_sheet(
        self,
        organization_id: str,
        as_of_date: date,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate balance sheet."""
        # Use the financial calculator to generate balance sheet
        result = self.calculator.generate_balance_sheet(
            organization_id=organization_id,
            as_of_date=as_of_date,
            include_changes=kwargs.get('include_changes', False),
            comparison_date=kwargs.get('comparison_date')
        )
        
        return {
            "statement_type": "balance_sheet",
            "organization_id": organization_id,
            "as_of_date": as_of_date.isoformat(),
            "data": result,
            "metadata": {
                "generated_at": datetime.utcnow().isoformat(),
                "include_changes": kwargs.get('include_changes', False)
            }
        }
    
    def _generate_cash_flow(
        self,
        organization_id: str,
        end_date: date,
        periods: int,
        comparison_periods: int,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate cash flow statement."""
        # Calculate the start date based on periods
        from dateutil.relativedelta import relativedelta
        start_date = end_date - relativedelta(months=periods)
        
        # Use the financial calculator to generate cash flow
        result = self.calculator.generate_cash_flow_statement(
            organization_id=organization_id,
            start_date=start_date,
            end_date=end_date,
            method=kwargs.get('method', 'indirect')
        )
        
        return {
            "statement_type": "cash_flow",
            "organization_id": organization_id,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "data": result,
            "metadata": {
                "generated_at": datetime.utcnow().isoformat(),
                "periods": periods,
                "comparison_periods": comparison_periods,
                "method": kwargs.get('method', 'indirect')
            }
        }