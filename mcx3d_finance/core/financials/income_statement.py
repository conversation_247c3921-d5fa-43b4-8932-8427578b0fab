"""
UK Companies House-compliant Profit & Loss Account generator with FRS 102 standards.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from decimal import Decimal
from sqlalchemy.orm import Session
import json


from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.core.account_classifications import FRS102AccountClassification
from mcx3d_finance.db.query_optimizers import QueryOptimizer
from mcx3d_finance.integrations.xero_client import XeroClient

logger = LoggerFactory.get_logger(__name__, domain='core')


def calculate_income_statement(transactions: list) -> Dict[str, Any]:
    """
    Calculate income statement from a list of transactions.

    Args:
        transactions: List of transaction dictionaries with account_type and amount

    Returns:
        Income statement dictionary with calculated totals
    """
    revenue = sum(t["amount"] for t in transactions if t["account_type"] == "revenue")
    cost_of_goods_sold = sum(t["amount"] for t in transactions if t["account_type"] == "cost_of_goods_sold")
    expenses = sum(t["amount"] for t in transactions if t["account_type"] == "expense")

    gross_profit = revenue - cost_of_goods_sold
    net_profit = gross_profit - expenses

    return {
        "revenue": revenue,
        "cost_of_goods_sold": cost_of_goods_sold,
        "expenses": expenses,
        "gross_profit": gross_profit,
        "net_profit": net_profit
    }


class UKProfitAndLossGenerator:
    """Generate UK Companies House-compliant P&L accounts with FRS 102 standards."""

    def __init__(self):
        """Initializes the P&L generator."""
        self.precision = Decimal("0.01")

    def generate_profit_and_loss(
        self,
        from_date: datetime,
        to_date: datetime,
        accounts: List[Dict[str, Any]],
        transactions: List[Dict[str, Any]],
        invoices: List[Dict[str, Any]],
        start_balance: Optional[Dict[str, Any]] = None,
        end_balance: Optional[Dict[str, Any]] = None,
        comparative_from: Optional[datetime] = None,
        comparative_to: Optional[datetime] = None,
        comparative_accounts: Optional[List[Dict[str, Any]]] = None,
        comparative_transactions: Optional[List[Dict[str, Any]]] = None,
        comparative_invoices: Optional[List[Dict[str, Any]]] = None,
        comparative_start_balance: Optional[Dict[str, Any]] = None,
        comparative_end_balance: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Generate UK-compliant Profit & Loss account from pre-fetched data.
        """
        try:
            logger.info(
                f"Generating UK-compliant P&L for period {from_date.strftime('%d %B %Y')} to {to_date.strftime('%d %B %Y')}"
            )

            pnl_data = self._calculate_uk_pnl(
                from_date, to_date, accounts, transactions, invoices, start_balance, end_balance
            )

            comparative_data = None
            if comparative_from and comparative_to:
                comparative_data = self._calculate_uk_pnl(
                    comparative_from,
                    comparative_to,
                    comparative_accounts or accounts,
                    comparative_transactions or [],
                    comparative_invoices or [],
                    comparative_start_balance,
                    comparative_end_balance,
                )

            formatted_pnl = self._format_for_uk_companies_house(
                pnl_data,
                from_date,
                to_date,
                comparative_data,
                comparative_from,
                comparative_to,
            )

            formatted_pnl["financial_analysis"] = self._generate_pnl_analysis(formatted_pnl)
            formatted_pnl["compliance"] = self._add_uk_compliance_certifications()

            logger.info("UK format P&L generation completed successfully.")
            return formatted_pnl

        except Exception as e:
            logger.error(f"Error generating P&L: {e}")
            raise

    def _calculate_uk_pnl(
        self,
        from_date: datetime,
        to_date: datetime,
        accounts: List[Dict[str, Any]],
        transactions: List[Dict[str, Any]],
        invoices: List[Dict[str, Any]],
        start_balance: Optional[Dict[str, Any]] = None,
        end_balance: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Calculate P&L from transactions or trial balance using FRS 102 classifications."""
        if end_balance and end_balance.get("accounts"):
            logger.info(f"Using Trial Balance data for P&L from {from_date} to {to_date}")
            return self._calculate_uk_pnl_from_trial_balance(start_balance, end_balance, accounts)

        logger.warning(f"Falling back to transaction-based calculation for P&L from {from_date} to {to_date}")

        pnl = {
            "turnover": 0,
            "cost_of_sales": 0,
            "distribution_costs": 0,
            "administrative_expenses": 0,
            "interest_receivable": 0,
            "interest_payable": 0,
            "taxation": 0,
        }

        # Process bank transactions (already filtered by date by the caller)

        for trans in transactions:
            # First try to process line items if they exist
            line_items_processed = False

            if trans.get("line_items"):
                try:
                    # Handle double-encoded JSON strings
                    line_items_raw = json.loads(trans.get("line_items"))
                    # If the result is still a string, parse it again
                    if isinstance(line_items_raw, str):
                        line_items = json.loads(line_items_raw)
                    else:
                        line_items = line_items_raw

                    # Process line items if they're not empty
                    if line_items and isinstance(line_items, list) and len(line_items) > 0:
                        for item in line_items:
                            account_code = item.get("AccountCode")
                            account = next((acc for acc in accounts if acc.get("code") == account_code), None)
                            if not account:
                                continue

                            classification = self._get_frs102_classification(account)
                            amount = Decimal(item.get("LineAmount", 0))

                            if classification == FRS102AccountClassification.TURNOVER:
                                pnl["turnover"] += amount
                            elif classification == FRS102AccountClassification.COST_OF_SALES:
                                pnl["cost_of_sales"] += amount
                            elif classification == FRS102AccountClassification.DISTRIBUTION_COSTS:
                                pnl["distribution_costs"] += amount
                            elif classification == FRS102AccountClassification.ADMINISTRATIVE_EXPENSES:
                                pnl["administrative_expenses"] += amount
                            elif classification == FRS102AccountClassification.INTEREST_RECEIVABLE:
                                pnl["interest_receivable"] += amount
                            elif classification == FRS102AccountClassification.INTEREST_PAYABLE:
                                pnl["interest_payable"] += amount
                            elif classification == FRS102AccountClassification.TAXATION:
                                pnl["taxation"] += amount

                        line_items_processed = True
                except Exception as e:
                    logger.warning(f"Error processing line items for transaction {trans.get('id')}: {e}")

            # If no line items were processed, use transaction type to categorize
            if not line_items_processed and trans.get("total"):
                amount = Decimal(str(trans.get("total")))

                if trans.get("type") == "RECEIVE":
                    # Bank deposits without invoices go to revenue
                    pnl["turnover"] += amount
                elif trans.type == "SPEND":
                    # Bank payments without proper categorization go to admin expenses
                    pnl["administrative_expenses"] += amount

        # Process invoices (ACCREC = revenue, ACCPAY = expenses) - assumed to be pre-filtered

        for invoice in invoices:
            if not invoice.get("line_items"):
                continue

            # Handle double-encoded JSON strings
            line_items_raw = json.loads(invoice.get("line_items"))
            if isinstance(line_items_raw, str):
                line_items = json.loads(line_items_raw)
            else:
                line_items = line_items_raw

            for item in line_items:
                account_code = item.get("AccountCode")
                account = next((acc for acc in accounts if acc.get("code") == account_code), None)
                if not account:
                    continue

                classification = self._get_frs102_classification(account)
                amount = Decimal(item.get("LineAmount", 0))

                # For ACCREC (sales invoices), amounts are positive revenue
                # For ACCPAY (purchase invoices), amounts are positive expenses
                if invoice.get("type") == "ACCREC":
                    if classification == FRS102AccountClassification.TURNOVER:
                        pnl["turnover"] += amount
                    elif classification == FRS102AccountClassification.INTEREST_RECEIVABLE:
                        pnl["interest_receivable"] += amount
                elif invoice.get("type") == "ACCPAY":
                    if classification == FRS102AccountClassification.COST_OF_SALES:
                        pnl["cost_of_sales"] += amount
                    elif classification == FRS102AccountClassification.DISTRIBUTION_COSTS:
                        pnl["distribution_costs"] += amount
                    elif classification == FRS102AccountClassification.ADMINISTRATIVE_EXPENSES:
                        pnl["administrative_expenses"] += amount
                    elif classification == FRS102AccountClassification.INTEREST_PAYABLE:
                        pnl["interest_payable"] += amount
                    elif classification == FRS102AccountClassification.TAXATION:
                        pnl["taxation"] += amount

        return pnl

    def _calculate_uk_pnl_from_trial_balance(
        self,
        start_balance: Optional[Dict[str, Any]],
        end_balance: Dict[str, Any],
        accounts: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Calculate P&L from Trial Balance data using FRS 102 classifications.
        This method correctly calculates P&L movements and handles revenue/expense recognition.
        """
        pnl = {
            "turnover": Decimal(0),
            "cost_of_sales": Decimal(0),
            "distribution_costs": Decimal(0),
            "administrative_expenses": Decimal(0),
            "interest_receivable": Decimal(0),
            "interest_payable": Decimal(0),
            "taxation": Decimal(0),
        }

        try:
            account_map = {acc.get("code"): acc for acc in accounts if acc.get("code")}

            if not end_balance or "accounts" not in end_balance:
                logger.error("End balance data is missing or invalid.")
                return pnl

            start_accounts = {acc["account_code"]: acc for acc in start_balance["accounts"]} if start_balance and "accounts" in start_balance else {}

            for tb_account in end_balance.get("accounts", []):
                account_code = tb_account.get("account_code")
                if not account_code:
                    logger.warning(f"Skipping account with no code: {tb_account.get('account_name')}")
                    continue

                try:
                    end_debit = Decimal(tb_account.get("debit", 0))
                    end_credit = Decimal(tb_account.get("credit", 0))
                except Exception as e:
                    logger.error(f"Could not parse debit/credit for account {account_code}. Error: {e}")
                    continue

                start_account = start_accounts.get(account_code, {})
                start_debit = Decimal(start_account.get("debit", 0))
                start_credit = Decimal(start_account.get("credit", 0))

                account = account_map.get(account_code)
                if not account:
                    logger.warning(f"Account {account_code} - {tb_account.get('account_name')} not in DB. Classifying by name.")
                    classification = self._classify_by_name_pnl(tb_account.get("account_name", ""))
                else:
                    classification = self._get_frs102_classification(account)

                if not classification:
                    continue

                movement = Decimal(0)
                if classification in [FRS102AccountClassification.TURNOVER, FRS102AccountClassification.INTEREST_RECEIVABLE]:
                    # For revenue accounts, credit balances increase revenue.
                    movement = (end_credit - start_credit) - (end_debit - start_debit)
                elif classification in [
                    FRS102AccountClassification.COST_OF_SALES,
                    FRS102AccountClassification.DISTRIBUTION_COSTS,
                    FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
                    FRS102AccountClassification.INTEREST_PAYABLE,
                    FRS102AccountClassification.TAXATION,
                ]:
                    # For expense accounts, debit balances increase expenses.
                    movement = (end_debit - start_debit) - (end_credit - start_credit)

                if movement == 0:
                    continue

                logger.debug(f"Account: {account_code}, Classification: {classification}, Movement: {movement}")

                if classification == FRS102AccountClassification.TURNOVER:
                    pnl["turnover"] += movement
                elif classification == FRS102AccountClassification.COST_OF_SALES:
                    pnl["cost_of_sales"] += movement
                elif classification == FRS102AccountClassification.DISTRIBUTION_COSTS:
                    pnl["distribution_costs"] += movement
                elif classification == FRS102AccountClassification.ADMINISTRATIVE_EXPENSES:
                    pnl["administrative_expenses"] += movement
                elif classification == FRS102AccountClassification.INTEREST_RECEIVABLE:
                    pnl["interest_receivable"] += movement
                elif classification == FRS102AccountClassification.INTEREST_PAYABLE:
                    pnl["interest_payable"] += movement
                elif classification == FRS102AccountClassification.TAXATION:
                    pnl["taxation"] += movement

        except Exception as e:
            logger.exception(f"An unexpected error occurred during P&L calculation from Trial Balance: {e}")
            # Return empty P&L on failure
            return {k: Decimal(0) for k in pnl}

        return {k: float(v.quantize(self.precision)) for k, v in pnl.items()}

    def _classify_by_name_pnl(self, account_name: str) -> Optional[FRS102AccountClassification]:
        """Classify P&L account based on name when not found in database."""
        name_lower = account_name.lower()

        # Revenue classifications
        if any(term in name_lower for term in ["sales", "revenue", "income", "turnover"]) and not "interest" in name_lower:
            return FRS102AccountClassification.TURNOVER
        elif "interest" in name_lower and any(term in name_lower for term in ["income", "received", "receivable"]):
            return FRS102AccountClassification.INTEREST_RECEIVABLE

        # Expense classifications
        elif any(term in name_lower for term in ["cost of sales", "cost of goods", "direct cost", "purchases"]):
            return FRS102AccountClassification.COST_OF_SALES
        elif any(term in name_lower for term in ["distribution", "shipping", "delivery", "freight"]):
            return FRS102AccountClassification.DISTRIBUTION_COSTS
        elif any(term in name_lower for term in ["admin", "office", "salary", "wage", "rent", "insurance", "professional", "legal", "accounting"]):
            return FRS102AccountClassification.ADMINISTRATIVE_EXPENSES
        elif "interest" in name_lower and any(term in name_lower for term in ["expense", "paid", "payable"]):
            return FRS102AccountClassification.INTEREST_PAYABLE
        elif any(term in name_lower for term in ["tax", "corporation tax", "vat expense"]):
            return FRS102AccountClassification.TAXATION

        # Default to admin expenses for unclassified expenses
        elif any(term in name_lower for term in ["expense", "cost"]):
            return FRS102AccountClassification.ADMINISTRATIVE_EXPENSES

        return None

    def _get_frs102_classification(self, account: Dict[str, Any]) -> Optional[FRS102AccountClassification]:
        """Determine FRS 102 classification for an account."""
        # This is a placeholder. In a real system, this would be a robust mapping.
        acc_type = account.get("type", "")
        name = account.get("name", "").lower()

        if acc_type in ["REVENUE", "SALES", "OTHERINCOME"]: return FRS102AccountClassification.TURNOVER
        if acc_type == "DIRECTCOSTS": return FRS102AccountClassification.COST_OF_SALES
        if "distribution" in name: return FRS102AccountClassification.DISTRIBUTION_COSTS
        if acc_type in ["EXPENSE", "OVERHEADS"]: return FRS102AccountClassification.ADMINISTRATIVE_EXPENSES
        if "interest" in name and acc_type == "REVENUE": return FRS102AccountClassification.INTEREST_RECEIVABLE
        if "interest" in name and acc_type == "EXPENSE": return FRS102AccountClassification.INTEREST_PAYABLE
        if "tax" in name: return FRS102AccountClassification.TAXATION

        return None

    def _format_for_uk_companies_house(
        self,
        pnl_data: Dict[str, Any],
        from_date: datetime,
        to_date: datetime,
        comparative_data: Optional[Dict[str, Any]] = None,
        comparative_from: Optional[datetime] = None,
        comparative_to: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """Format P&L data for UK Companies House presentation."""

        gross_profit = pnl_data["turnover"] - pnl_data["cost_of_sales"]
        operating_profit = gross_profit - pnl_data["distribution_costs"] - pnl_data["administrative_expenses"]
        profit_before_tax = operating_profit + pnl_data["interest_receivable"] - pnl_data["interest_payable"]
        profit_after_tax = profit_before_tax - pnl_data["taxation"]

        return {
            "header": {
                "company_name": "MCX3D LTD",
                "statement_title": "PROFIT AND LOSS ACCOUNT",
                "period_description": f"for the year ended {to_date.strftime('%d %B %Y')}",
                "currency": "GBP",
            },
            "turnover": pnl_data["turnover"],
            "cost_of_sales": pnl_data["cost_of_sales"],
            "gross_profit": gross_profit,
            "distribution_costs": pnl_data["distribution_costs"],
            "administrative_expenses": pnl_data["administrative_expenses"],
            "operating_profit": operating_profit,
            "interest_receivable": pnl_data["interest_receivable"],
            "interest_payable": pnl_data["interest_payable"],
            "profit_before_tax": profit_before_tax,
            "taxation": pnl_data["taxation"],
            "profit_after_tax": profit_after_tax,
            "comparative_data": comparative_data
        }

    def _generate_pnl_analysis(self, pnl_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate financial analysis for the UK P&L."""
        turnover = pnl_data["turnover"]
        gross_profit = pnl_data["gross_profit"]
        operating_profit = pnl_data["operating_profit"]
        profit_after_tax = pnl_data["profit_after_tax"]

        return {
            "profitability_margins": {
                "gross_margin": round((float(gross_profit) / float(turnover)) * 100, 1) if turnover > 0 else 0,
                "operating_margin": round((float(operating_profit) / float(turnover)) * 100, 1) if turnover > 0 else 0,
                "net_margin": round((float(profit_after_tax) / float(turnover)) * 100, 1) if turnover > 0 else 0,
            }
        }

    def _add_uk_compliance_certifications(self) -> Dict[str, Any]:
        """Add UK Companies House compliance certifications."""
        return {
            "uk_gaap_compliance": True,
            "companies_house_requirements": True,
            "frs_102_compliance": True,
            "preparation_date": datetime.utcnow().isoformat(),
            "certifications": [
                "This Profit and Loss account has been prepared in accordance with UK Generally Accepted Accounting Practice (UK GAAP) and FRS 102.",
                "The financial statements comply with the Companies Act 2006.",
            ],
        }


def calculate_income_statement(transactions: list) -> Dict[str, Any]:
    """
    Simple income statement calculation function for basic transaction processing.

    This function provides a simplified interface for calculating income statements
    from a list of transaction dictionaries. It's primarily used for testing and
    simple calculations without requiring database access.

    Args:
        transactions: List of transaction dictionaries with 'account_type' and 'amount' keys

    Returns:
        Dictionary containing income statement components
    """
    # Initialize totals
    revenue = Decimal("0")
    cost_of_goods_sold = Decimal("0")
    operating_expenses = Decimal("0")

    # Process transactions
    for transaction in transactions:
        # Skip transactions without required fields
        if not isinstance(transaction, dict):
            continue

        account_type = transaction.get("account_type", "").lower()
        amount = transaction.get("amount", 0)

        # Convert amount to Decimal for accurate calculations
        try:
            amount = Decimal(str(amount))
        except (ValueError, TypeError):
            amount = Decimal("0")

        # Categorize based on account type
        if account_type == "revenue":
            revenue += amount
        elif account_type == "cost_of_goods_sold":
            cost_of_goods_sold += amount
        elif account_type in ["expense", "expenses"]:
            operating_expenses += amount

    # Calculate derived values
    gross_profit = revenue - cost_of_goods_sold
    operating_income = gross_profit - operating_expenses
    net_income = operating_income  # Simplified - no tax or interest for basic calculation

    # Return results as floats for compatibility
    return {
        "revenue": float(revenue),
        "cost_of_goods_sold": float(cost_of_goods_sold),
        "gross_profit": float(gross_profit),
        "operating_expenses": float(operating_expenses),
        "operating_income": float(operating_income),
        "net_income": float(net_income)
    }
