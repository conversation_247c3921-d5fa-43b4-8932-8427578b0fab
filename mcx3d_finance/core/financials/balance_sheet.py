"""
UK Companies House-compliant Balance Sheet generator with FRS 102 standards.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
from sqlalchemy.orm import Session
import json

from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.core.financials.income_statement import UKProfitAndLossGenerator
from mcx3d_finance.db.session import <PERSON><PERSON>oc<PERSON>
from mcx3d_finance.db.models import Account, BankTransaction, Invoice
from mcx3d_finance.core.account_classifications import FRS102AccountClassification
from mcx3d_finance.db.query_optimizers import QueryOptimizer

logger = LoggerFactory.get_logger(__name__, domain="core")


class UKBalanceSheetGenerator:
    """Generate UK Companies House-compliant balance sheets with FRS 102 standards."""

    def __init__(self, organization_id: int):
        self.organization_id = organization_id
        self.precision = Decimal("0.01")
        self.db: Session = SessionLocal()
        self.query_optimizer = QueryOptimizer()

    def generate_balance_sheet(
        self,
        as_of_date: datetime,
        comparative_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """
        Generate UK-compliant balance sheet.

        Args:
            as_of_date: Balance sheet date.
            comparative_date: Comparative period date.

        Returns:
            Complete balance sheet with UK Companies House formatting.
        """
        try:
            logger.info(
                f"Generating UK-compliant balance sheet as of {as_of_date.strftime('%d %B %Y')}"
            )

            organization = self.query_optimizer.get_organization_with_all_relationships(
                self.db, self.organization_id
            )
            if not organization:
                raise ValueError(f"Organization {self.organization_id} not found")

            uk_balance_sheet = self._calculate_uk_balance_sheet(as_of_date)

            comparative_data = None
            if comparative_date:
                logger.info(
                    f"Generating comparative data as of {comparative_date.strftime('%d %B %Y')}"
                )
                comparative_data = self._calculate_uk_balance_sheet(comparative_date)

            formatted_sheet = self._format_for_uk_companies_house(
                uk_balance_sheet,
                as_of_date,
                comparative_data,
                comparative_date,
            )

            formatted_sheet["financial_analysis"] = self._generate_balance_sheet_analysis(
                formatted_sheet
            )
            formatted_sheet["compliance"] = self._add_uk_compliance_certifications()

            logger.info("UK format balance sheet generation completed successfully.")
            return formatted_sheet

        except Exception as e:
            logger.error(f"Error generating balance sheet: {e}")
            raise
        finally:
            self.db.close()

    def _calculate_uk_balance_sheet(self, as_of_date: datetime) -> Dict[str, Any]:
        """Calculate balance sheet from accounts using FRS 102 classifications."""
        # Try to use Trial Balance first, fall back to transaction-based calculation
        try:
            # Import XeroClient to get Trial Balance
            from mcx3d_finance.integrations.xero_client import XeroClient
            xero_client = XeroClient(self.organization_id)
            trial_balance_data = xero_client.get_trial_balance(as_of_date)

            # Use Trial Balance data if available
            if trial_balance_data and trial_balance_data.get("accounts"):
                logger.info(f"Using Trial Balance data for balance sheet as of {as_of_date}")
                return self._calculate_uk_balance_sheet_from_trial_balance(trial_balance_data, as_of_date)
        except Exception as e:
            logger.warning(f"Could not fetch Trial Balance, falling back to transaction-based calculation: {e}")

        # Original transaction-based calculation as fallback
        accounts = self.db.query(Account).filter(
            Account.organization_id == self.organization_id,
            Account.status == "ACTIVE"
        ).all()

        balance_sheet = {
            "fixed_assets": {"tangible": 0, "intangible": 0, "investments": 0, "total": 0},
            "current_assets": {"stocks": 0, "debtors": 0, "cash": 0, "total": 0},
            "creditors_due_within_one_year": {"total": 0},
            "creditors_due_after_one_year": {"total": 0},
            "provisions_for_liabilities": {"total": 0},
            "capital_and_reserves": {"share_capital": 0, "profit_and_loss": 0, "total": 0},
        }

        # Calculate retained earnings from P&L
        pnl_gen = UKProfitAndLossGenerator(self.organization_id)
        # Get P&L from beginning of time to as_of_date
        from datetime import datetime
        start_date = datetime(2000, 1, 1)  # Far enough back to capture all transactions
        pnl_data = pnl_gen._calculate_uk_pnl(start_date, as_of_date)

        # Calculate net profit/loss
        net_profit = (pnl_data["turnover"] - pnl_data["cost_of_sales"] -
                        pnl_data["distribution_costs"] - pnl_data["administrative_expenses"] +
                        pnl_data["interest_receivable"] - pnl_data["interest_payable"] -
                        pnl_data["taxation"])

        balance_sheet["capital_and_reserves"]["profit_and_loss"] = float(net_profit)

        for account in accounts:
            # For bank accounts, calculate balance directly from transactions
            if account.type == "BANK":
                balance = self._calculate_bank_account_balance(account, as_of_date)
                balance_sheet["current_assets"]["cash"] += float(balance)
            else:
                balance = self._calculate_account_balance(account, as_of_date)
                classification = self._get_frs102_classification(account)

                if classification == FRS102AccountClassification.TANGIBLE_ASSETS:
                    balance_sheet["fixed_assets"]["tangible"] += float(balance)
                elif classification == FRS102AccountClassification.INTANGIBLE_ASSETS:
                    balance_sheet["fixed_assets"]["intangible"] += float(balance)
                elif classification == FRS102AccountClassification.INVESTMENTS:
                    balance_sheet["fixed_assets"]["investments"] += float(balance)
                elif classification == FRS102AccountClassification.STOCKS:
                    balance_sheet["current_assets"]["stocks"] += float(balance)
                elif classification == FRS102AccountClassification.DEBTORS:
                    balance_sheet["current_assets"]["debtors"] += float(balance)
                elif classification == FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND:
                    balance_sheet["current_assets"]["cash"] += float(balance)
                elif classification == FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR:
                    balance_sheet["creditors_due_within_one_year"]["total"] += float(abs(balance))
                elif classification == FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR:
                    balance_sheet["creditors_due_after_one_year"]["total"] += float(abs(balance))
                elif classification == FRS102AccountClassification.PROVISIONS_FOR_LIABILITIES:
                    balance_sheet["provisions_for_liabilities"]["total"] += float(abs(balance))
                elif classification == FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL:
                    balance_sheet["capital_and_reserves"]["share_capital"] += float(abs(balance))
                elif classification == FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT:
                    balance_sheet["capital_and_reserves"]["profit_and_loss"] += float(abs(balance))

            # Calculate totals
            balance_sheet["fixed_assets"]["total"] = sum(balance_sheet["fixed_assets"].values())
            balance_sheet["current_assets"]["total"] = sum(balance_sheet["current_assets"].values())
            balance_sheet["capital_and_reserves"]["total"] = sum(balance_sheet["capital_and_reserves"].values())

        return balance_sheet

    def _calculate_uk_balance_sheet_from_trial_balance(self, trial_balance_data: Dict[str, Any], as_of_date: datetime) -> Dict[str, Any]:
        """Calculate balance sheet from Trial Balance data using FRS 102 classifications."""
        balance_sheet = {
            "fixed_assets": {"tangible": 0, "intangible": 0, "investments": 0, "total": 0},
            "current_assets": {"stocks": 0, "debtors": 0, "cash": 0, "total": 0},
            "creditors_due_within_one_year": {"total": 0},
            "creditors_due_after_one_year": {"total": 0},
            "provisions_for_liabilities": {"total": 0},
            "capital_and_reserves": {"share_capital": 0, "profit_and_loss": 0, "total": 0},
        }

        retained_earnings = 0

        # Get accounts from the database to match with Trial Balance
        accounts = self.db.query(Account).filter(
            Account.organization_id == self.organization_id,
            Account.status == "ACTIVE"
        ).all()

        account_map = {acc.xero_account_id: acc for acc in accounts if acc.xero_account_id}

        # Process each account from Trial Balance
        for tb_account in trial_balance_data.get("accounts", []):
            try:
                account_id = tb_account.get("account_id")
                debit = Decimal(str(tb_account.get("debit", "0")))
                credit = Decimal(str(tb_account.get("credit", "0")))

                account = account_map.get(account_id)
                if not account:
                    logger.warning(f"Account with ID {account_id} not found in database. Skipping.")
                    continue

                classification = FRS102AccountClassification.get_frs102_classification(account.type)
                if not classification:
                    logger.warning(f"Could not classify account {account.name} ({account.type}). Skipping.")
                    continue

                # Handle balance calculation based on account type
                balance = debit - credit if classification in [
                    FRS102AccountClassification.TANGIBLE_ASSETS,
                    FRS102AccountClassification.INTANGIBLE_ASSETS,
                    FRS102AccountClassification.INVESTMENTS,
                    FRS102AccountClassification.STOCKS,
                    FRS102AccountClassification.DEBTORS,
                    FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND
                ] else credit - debit

                # Accumulate revenue and expense accounts for retained earnings
                if classification in [FRS102AccountClassification.TURNOVER, FRS102AccountClassification.COST_OF_SALES, FRS102AccountClassification.DISTRIBUTION_COSTS, FRS102AccountClassification.ADMINISTRATIVE_EXPENSES]:
                    retained_earnings += balance
                    continue

                # Assign balance to the correct balance sheet category
                if classification == FRS102AccountClassification.TANGIBLE_ASSETS:
                    balance_sheet["fixed_assets"]["tangible"] += float(balance)
                elif classification == FRS102AccountClassification.INTANGIBLE_ASSETS:
                    balance_sheet["fixed_assets"]["intangible"] += float(balance)
                elif classification == FRS102AccountClassification.INVESTMENTS:
                    balance_sheet["fixed_assets"]["investments"] += float(balance)
                elif classification == FRS102AccountClassification.STOCKS:
                    balance_sheet["current_assets"]["stocks"] += float(balance)
                elif classification == FRS102AccountClassification.DEBTORS:
                    balance_sheet["current_assets"]["debtors"] += float(balance)
                elif classification == FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND:
                    balance_sheet["current_assets"]["cash"] += float(balance)
                elif classification == FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR:
                    balance_sheet["creditors_due_within_one_year"]["total"] += float(balance)
                elif classification == FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR:
                    balance_sheet["creditors_due_after_one_year"]["total"] += float(balance)
                elif classification == FRS102AccountClassification.PROVISIONS_FOR_LIABILITIES:
                    balance_sheet["provisions_for_liabilities"]["total"] += float(balance)
                elif classification == FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL:
                    balance_sheet["capital_and_reserves"]["share_capital"] += float(balance)

            except (ValueError, TypeError) as e:
                logger.error(f"Error processing account {tb_account.get('account_id')}: {e}")
                continue

        balance_sheet["capital_and_reserves"]["profit_and_loss"] = float(retained_earnings)

        # Calculate totals
        balance_sheet["fixed_assets"]["total"] = sum(balance_sheet["fixed_assets"].values())
        balance_sheet["current_assets"]["total"] = sum(balance_sheet["current_assets"].values())
        balance_sheet["capital_and_reserves"]["total"] = sum(balance_sheet["capital_and_reserves"].values())

        return balance_sheet

    def _classify_by_name(self, account_name: str) -> Optional[FRS102AccountClassification]:
        """Classify account based on name when not found in database."""
        name_lower = account_name.lower()

        # Asset classifications
        if any(term in name_lower for term in ["fixed asset", "tangible", "property", "equipment", "machinery"]):
            return FRS102AccountClassification.TANGIBLE_ASSETS
        elif any(term in name_lower for term in ["intangible", "goodwill", "patent", "trademark"]):
            return FRS102AccountClassification.INTANGIBLE_ASSETS
        elif any(term in name_lower for term in ["investment", "shares", "securities"]):
            return FRS102AccountClassification.INVESTMENTS
        elif any(term in name_lower for term in ["stock", "inventory", "work in progress"]):
            return FRS102AccountClassification.STOCKS
        elif any(term in name_lower for term in ["debtor", "receivable", "prepayment", "vat refund"]):
            return FRS102AccountClassification.DEBTORS
        elif any(term in name_lower for term in ["bank", "cash", "deposit"]):
            return FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND

        # Liability classifications
        elif any(term in name_lower for term in ["creditor", "payable", "accrual", "vat payable", "paye"]):
            if "long term" in name_lower or "term loan" in name_lower:
                return FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR
            else:
                return FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR
        elif any(term in name_lower for term in ["provision", "deferred"]):
            return FRS102AccountClassification.PROVISIONS_FOR_LIABILITIES

        # Equity classifications
        elif any(term in name_lower for term in ["share capital", "capital stock", "ordinary shares"]):
            return FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL
        elif any(term in name_lower for term in ["retained", "profit", "loss", "reserve"]):
            return FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT

        return None

    def _get_frs102_classification(self, account: Account) -> Optional[FRS102AccountClassification]:
        """Determine FRS 102 classification for an account."""
        # This is a placeholder. In a real system, this would be a robust mapping.
        name = account.name.lower()
        acc_type = account.type

        if acc_type == "FIXED": return FRS102AccountClassification.TANGIBLE_ASSETS
        if acc_type == "INVENTORY": return FRS102AccountClassification.STOCKS
        if acc_type == "CURRENT" and "receivable" in name: return FRS102AccountClassification.DEBTORS
        if acc_type == "BANK": return FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND
        if acc_type == "CURRLIAB": return FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR
        if acc_type == "TERMLIAB": return FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR
        if acc_type == "EQUITY":
            if "share capital" in name: return FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL
            return FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT

        # Revenue and expense accounts affect retained earnings (profit and loss account)
        if acc_type in ["REVENUE", "EXPENSE"]:
            return FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT

        # Default classification for other account types
        if "receivable" in name or "debtor" in name:
            return FRS102AccountClassification.DEBTORS
        if "payable" in name or "creditor" in name:
            return FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR

        return None

    def _format_for_uk_companies_house(
        self,
        data: Dict[str, Any],
        as_of_date: datetime,
        comparative_data: Optional[Dict[str, Any]] = None,
        comparative_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """Format balance sheet data for UK Companies House presentation."""

        net_current_assets = data["current_assets"]["total"] - data["creditors_due_within_one_year"]["total"]
        total_assets_less_current_liabilities = data["fixed_assets"]["total"] + net_current_assets
        net_assets = total_assets_less_current_liabilities - data["creditors_due_after_one_year"]["total"] - data["provisions_for_liabilities"]["total"]

        return {
            "header": {
                "company_name": "MCX3D LTD",
                "statement_title": "BALANCE SHEET",
                "reporting_date": as_of_date.strftime("%d %B %Y"),
                "currency": "GBP",
                "amounts_in": "pounds sterling",
            },
            "fixed_assets": data["fixed_assets"],
            "current_assets": data["current_assets"],
            "creditors_due_within_one_year": data["creditors_due_within_one_year"],
            "net_current_assets": net_current_assets,
            "total_assets_less_current_liabilities": total_assets_less_current_liabilities,
            "creditors_due_after_one_year": data["creditors_due_after_one_year"],
            "provisions_for_liabilities": data["provisions_for_liabilities"],
            "net_assets": net_assets,
            "capital_and_reserves": data["capital_and_reserves"],
            "comparative_data": comparative_data
        }

    def _generate_balance_sheet_analysis(self, balance_sheet: Dict[str, Any]) -> Dict[str, Any]:
        """Generate financial analysis for the UK balance sheet."""
        current_assets = balance_sheet["current_assets"]["total"]
        current_liabilities = balance_sheet["creditors_due_within_one_year"]["total"]
        total_assets = balance_sheet["total_assets_less_current_liabilities"] + balance_sheet["creditors_due_within_one_year"]["total"]
        total_liabilities = current_liabilities + balance_sheet["creditors_due_after_one_year"]["total"]
        equity = balance_sheet["capital_and_reserves"]["total"]

        return {
            "liquidity_ratios": {
                "current_ratio": round(float(current_assets) / float(current_liabilities), 2) if current_liabilities > 0 else 0,
            },
            "solvency_ratios": {
                "debt_to_equity": round(float(total_liabilities) / float(equity), 2) if equity > 0 else 0,
                "debt_to_assets": round(float(total_liabilities) / float(total_assets), 2) if total_assets > 0 else 0,
            },
            "working_capital": current_assets - current_liabilities,
        }

    def _add_uk_compliance_certifications(self) -> Dict[str, Any]:
        """Add UK Companies House compliance certifications."""
        return {
            "uk_gaap_compliance": True,
            "companies_house_requirements": True,
            "frs_102_compliance": True,
            "audit_standards": "UK ISAs (International Standards on Auditing)",
            "preparation_date": datetime.utcnow().isoformat(),
            "certifications": [
                "This balance sheet has been prepared in accordance with UK Generally Accepted Accounting Practice (UK GAAP) and FRS 102.",
                "The financial statements comply with the Companies Act 2006.",
            ],
        }

    def _calculate_account_balance(self, account: Account, as_of_date: datetime) -> Decimal:
        """Calculate actual account balance from transactions and invoices."""
        balance = Decimal("0")

        # Get all bank transactions for this account up to the as_of_date
        bank_transactions = self.db.query(BankTransaction).filter(
            BankTransaction.organization_id == self.organization_id,
            BankTransaction.date <= as_of_date
        ).all()

        for trans in bank_transactions:
            if not trans.line_items:
                continue

            try:
                # Handle double-encoded JSON strings
                line_items_raw = json.loads(trans.line_items)
                if isinstance(line_items_raw, str):
                    line_items = json.loads(line_items_raw)
                else:
                    line_items = line_items_raw

                for item in line_items:
                    if item.get("AccountCode") == account.code:
                        amount = Decimal(str(item.get("LineAmount", 0)))
                        # For bank transactions, RECEIVE adds to account, SPEND subtracts
                        if trans.type in ["RECEIVE", "RECEIVE-TRANSFER"]:
                            balance += amount
                        elif trans.type in ["SPEND", "SPEND-TRANSFER"]:
                            balance -= amount
            except (json.JSONDecodeError, ValueError, TypeError):
                continue

        # Get all invoices for this account up to the as_of_date
        invoices = self.db.query(Invoice).filter(
            Invoice.organization_id == self.organization_id,
            Invoice.date <= as_of_date
        ).all()

        for invoice in invoices:
            if not invoice.line_items:
                continue

            try:
                # Handle double-encoded JSON strings
                line_items_raw = json.loads(invoice.line_items)
                if isinstance(line_items_raw, str):
                    line_items = json.loads(line_items_raw)
                else:
                    line_items = line_items_raw

                for item in line_items:
                    if item.get("AccountCode") == account.code:
                        amount = Decimal(str(item.get("LineAmount", 0)))
                        # For ACCREC (sales), positive amounts increase asset/revenue accounts
                        # For ACCPAY (purchases), positive amounts increase expense/liability accounts
                        if invoice.type == "ACCREC":
                            # Sales invoice - increases revenue (credit) or receivables (debit)
                            if account.type in ["REVENUE"]:
                                balance += amount  # Credit increases revenue
                            elif account.type in ["CURRENT", "RECEIVABLES"]:
                                balance += amount  # Debit increases receivables
                        elif invoice.type == "ACCPAY":
                            # Purchase invoice - increases expenses (debit) or payables (credit)
                            if account.type in ["EXPENSE"]:
                                balance += amount  # Debit increases expenses
                            elif account.type in ["CURRLIAB", "PAYABLES"]:
                                balance += amount  # Credit increases payables
            except (json.JSONDecodeError, ValueError, TypeError):
                continue

        return balance

    def _calculate_bank_account_balance(self, account: Account, as_of_date: datetime) -> Decimal:
        """Calculate bank account balance from bank transactions."""
        balance = Decimal("0")

        # Get all bank transactions for this account up to the as_of_date
        bank_transactions = self.db.query(BankTransaction).filter(
            BankTransaction.organization_id == self.organization_id,
            BankTransaction.date <= as_of_date
        ).all()

        for trans in bank_transactions:
            # Check if this transaction is for the current bank account
            bank_account_data = trans.bank_account
            if isinstance(bank_account_data, str):
                try:
                    bank_account_data = json.loads(bank_account_data)
                except:
                    continue

            if isinstance(bank_account_data, dict):
                if bank_account_data.get("Code") == account.code or bank_account_data.get("AccountID") == account.xero_account_id:
                    amount = Decimal(str(trans.total or 0))
                    # For bank accounts: RECEIVE increases balance, SPEND decreases balance
                    if trans.type in ["RECEIVE", "RECEIVE-TRANSFER"]:
                        balance += amount
                    elif trans.type in ["SPEND", "SPEND-TRANSFER"]:
                        balance -= amount

        return balance
