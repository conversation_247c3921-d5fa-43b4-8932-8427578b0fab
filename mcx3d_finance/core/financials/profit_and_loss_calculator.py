from datetime import datetime
from decimal import ROUND_HALF_UP, Decimal
from typing import Any, Dict

import pandas as pd
from mcx3d_finance.exceptions.handlers import calculation_error, handle_errors
from mcx3d_finance.utils.logging_config import get_core_logger, log_performance

logger = get_core_logger("profit_and_loss_calculator")


class AccountType:
    """Defines standardized account types."""

    REVENUE = "REVENUE"
    EXPENSE = "EXPENSE"


class AccountClass:
    """Defines standardized account classifications for P&L."""

    COST_OF_SALES = "COST_OF_SALES"
    DISTRIBUTION_COSTS = "DISTRIBUTION_COSTS"
    ADMINISTRATIVE_EXPENSES = "ADMINISTRATIVE_EXPENSES"


class ProfitAndLossCalculator:
    """
    Calculates a UK-compliant Profit and Loss (P&L) statement.

    This class encapsulates the logic for generating a P&L account from
    transactional data, ensuring compliance with UK financial standards.
    """

    def __init__(self, precision: int = 2):
        """
        Initializes the calculator with a given precision.

        Args:
            precision: The number of decimal places for rounding currency values.
        """
        self.precision = Decimal(f"1e-{precision}")

    def _round_currency(self, value: float) -> Decimal:
        """
        Rounds a float to the configured decimal precision for currency values.
        """
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def _validate_transactions(self, transactions_df: pd.DataFrame) -> None:
        """
        Validates that the transaction DataFrame has the required structure.
        """
        if transactions_df.empty:
            raise ValueError("Transaction data cannot be empty.")

        required_columns = ["date", "account_type", "account_class", "amount"]
        missing_columns = [
            col for col in required_columns if col not in transactions_df.columns
        ]
        if missing_columns:
            raise ValueError(
                f"Transaction data is missing required columns: {missing_columns}"
            )

    def _filter_transactions_for_period(
        self,
        transactions_df: pd.DataFrame,
        period_start: datetime,
        period_end: datetime,
    ) -> pd.DataFrame:
        """Filters transactions to a specific date range."""
        return transactions_df[
            (transactions_df["date"] >= period_start)
            & (transactions_df["date"] <= period_end)
        ]

    def _calculate_financial_metric(
        self,
        df: pd.DataFrame,
        account_type: str,
        account_class: str = None,
    ) -> Decimal:
        """
        Calculates a financial metric from the DataFrame based on account type and class.
        """
        filtered_df = df[df["account_type"] == account_type]
        if account_class:
            filtered_df = filtered_df[filtered_df["account_class"] == account_class]

        total = filtered_df["amount"].sum()
        return self._round_currency(total)

    def _generate_zero_report(
        self, period_start: datetime, period_end: datetime
    ) -> Dict[str, Any]:
        """Generates a P&L statement with all values set to zero."""
        return {
            "period_start": period_start.isoformat(),
            "period_end": period_end.isoformat(),
            "turnover": {"total_turnover": 0.0},
            "cost_of_sales": 0.0,
            "gross_profit": 0.0,
            "distribution_costs": 0.0,
            "administrative_expenses": 0.0,
            "operating_profit": 0.0,
            "profit_for_financial_year": 0.0,
        }

    @handle_errors(operation="generate_profit_and_loss")
    @log_performance()
    def generate_profit_and_loss(
        self,
        transactions_df: pd.DataFrame,
        period_start: datetime,
        period_end: datetime,
    ) -> Dict[str, Any]:
        """
        Generates a UK-compliant Profit and Loss account from transaction data.
        """
        logger.info(
            "Starting P&L generation.",
            period_start=period_start.isoformat(),
            period_end=period_end.isoformat(),
            transaction_count=len(transactions_df),
        )

        try:
            self._validate_transactions(transactions_df)
        except ValueError as e:
            raise calculation_error(
                "generate_profit_and_loss",
                str(e),
                context={
                    "period_start": period_start.isoformat(),
                    "period_end": period_end.isoformat(),
                },
            )

        period_transactions = self._filter_transactions_for_period(
            transactions_df, period_start, period_end
        )

        if period_transactions.empty:
            logger.warning(
                "No transactions found for the specified period.",
                period_start=period_start.isoformat(),
                period_end=period_end.isoformat(),
            )
            return self._generate_zero_report(period_start, period_end)

        total_turnover = self._calculate_financial_metric(
            period_transactions, AccountType.REVENUE
        )
        cost_of_sales = self._calculate_financial_metric(
            period_transactions, AccountType.EXPENSE, AccountClass.COST_OF_SALES
        )
        distribution_costs = self._calculate_financial_metric(
            period_transactions, AccountType.EXPENSE, AccountClass.DISTRIBUTION_COSTS
        )
        admin_expenses = self._calculate_financial_metric(
            period_transactions,
            AccountType.EXPENSE,
            AccountClass.ADMINISTRATIVE_EXPENSES,
        )

        gross_profit = total_turnover - cost_of_sales
        operating_profit = gross_profit - distribution_costs - admin_expenses
        profit_for_year = operating_profit

        result = {
            "period_start": period_start.isoformat(),
            "period_end": period_end.isoformat(),
            "turnover": {"total_turnover": float(total_turnover)},
            "cost_of_sales": float(cost_of_sales),
            "gross_profit": float(gross_profit),
            "distribution_costs": float(distribution_costs),
            "administrative_expenses": float(admin_expenses),
            "operating_profit": float(operating_profit),
            "profit_for_financial_year": float(profit_for_year),
        }

        logger.info(
            "P&L generated successfully.",
            turnover=result["turnover"]["total_turnover"],
            operating_profit=result["operating_profit"],
        )
        return result
