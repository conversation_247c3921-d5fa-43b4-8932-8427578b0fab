from datetime import datetime
from decimal import ROUND_HALF_UP, Decimal
from typing import Any, Dict

import pandas as pd

from mcx3d_finance.exceptions.handlers import calculation_error, handle_errors
from mcx3d_finance.utils.logging_config import get_core_logger, log_performance

logger = get_core_logger("saas_kpi_calculator")


class SaasKpiCalculator:
    """
    Calculates key performance indicators (KPIs) for a SaaS business.

    This class provides methods to compute essential SaaS metrics like MRR, ARR,
    ARPU, and churn rate from subscription data.
    """

    def __init__(self, precision: int = 2):
        """
        Initializes the calculator.

        Args:
            precision: The number of decimal places for rounding currency values.
        """
        self.precision = Decimal(f"1e-{precision}")

    def _round_currency(self, value: float) -> Decimal:
        """
        Rounds a float to the configured decimal precision for currency values.
        """
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def _validate_subscription_data(self, subscription_df: pd.DataFrame) -> None:
        """
        Validates the subscription DataFrame.
        """
        if subscription_df.empty:
            raise ValueError("Subscription data cannot be empty.")

        required_columns = ["status", "monthly_value", "churn_date"]
        missing_columns = [
            col for col in required_columns if col not in subscription_df.columns
        ]
        if missing_columns:
            raise ValueError(
                f"Subscription data is missing required columns: {missing_columns}"
            )

    def _generate_zero_kpis(
        self, period_start: datetime, period_end: datetime
    ) -> Dict[str, Any]:
        """Generate zero-valued KPIs for a given period."""
        return {
            "period_start": period_start.isoformat(),
            "period_end": period_end.isoformat(),
            "revenue_metrics": {"mrr": 0.0, "arr": 0.0, "arpu": 0.0},
            "customer_metrics": {
                "total_customers": 0,
                "churned_customers": 0,
                "churn_rate": 0.0,
            },
        }

    @handle_errors(operation="calculate_saas_kpis")
    @log_performance()
    def calculate_saas_kpis(
        self,
        subscription_data: pd.DataFrame,
        period_start: datetime,
        period_end: datetime,
    ) -> Dict[str, Any]:
        """
        Calculate SaaS-specific KPIs from subscription data.
        """
        logger.info(
            "Starting SaaS KPI calculation.",
            period_start=period_start.isoformat(),
            period_end=period_end.isoformat(),
            subscription_count=len(subscription_data),
        )

        try:
            self._validate_subscription_data(subscription_data)
        except ValueError as e:
            raise calculation_error(
                "calculate_saas_kpis",
                str(e),
                context={
                    "period_start": period_start.isoformat(),
                    "period_end": period_end.isoformat(),
                },
            )

        active_subscriptions = subscription_data[
            subscription_data["status"] == "active"
        ]
        if active_subscriptions.empty:
            logger.warning("No active subscriptions found for KPI calculation.")
            return self._generate_zero_kpis(period_start, period_end)

        mrr = self._round_currency(active_subscriptions["monthly_value"].sum())
        arr = mrr * 12
        total_customers = len(active_subscriptions)
        arpu = mrr / total_customers if total_customers > 0 else Decimal("0")

        churned_customers_df = subscription_data[
            (subscription_data["churn_date"] >= period_start)
            & (subscription_data["churn_date"] <= period_end)
        ]
        churned_customers = len(churned_customers_df)
        churn_rate = (
            (Decimal(churned_customers) / Decimal(total_customers) * Decimal(100))
            if total_customers > 0
            else Decimal("0")
        )

        result = {
            "period_start": period_start.isoformat(),
            "period_end": period_end.isoformat(),
            "revenue_metrics": {
                "mrr": float(mrr),
                "arr": float(arr),
                "arpu": float(arpu),
            },
            "customer_metrics": {
                "total_customers": total_customers,
                "churned_customers": churned_customers,
                "churn_rate": float(churn_rate),
            },
        }

        logger.info(
            "SaaS KPIs calculated successfully.",
            mrr=result["revenue_metrics"]["mrr"],
            total_customers=result["customer_metrics"]["total_customers"],
        )
        return result
