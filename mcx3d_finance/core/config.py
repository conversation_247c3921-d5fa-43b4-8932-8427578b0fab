import logging
import os
import sys
from typing import Any, Dict, Optional

from pydantic import ConfigDict
from pydantic_settings import BaseSettings as _BaseSettings

# Use standard logging to break the circular import
logger = logging.getLogger("mcx3d_finance.core.config")


class Settings(_BaseSettings):
    """Application settings with environment variable support."""

    # Database
    database_url: Optional[str] = None  # Must be set via environment variable

    # Redis
    redis_url: str = "redis://localhost:6379/0"

    # Xero API
    xero_client_id: Optional[str] = None
    xero_client_secret: Optional[str] = None
    xero_webhook_key: Optional[str] = None
    xero_redirect_uri: str = "http://localhost:8000/api/auth/xero/callback"
    xero_scopes: str = "accounting.transactions accounting.contacts accounting.reports.read accounting.settings"

    # Security - Basic
    secret_key: Optional[str] = None
    encryption_key: Optional[str] = None
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 15  # Reduced for security

    # Security - Session Management
    refresh_token_expire_days: int = 30
    max_sessions_per_user: int = 5
    session_idle_timeout_minutes: int = 60

    # Security - Account Lockout
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30

    # Security - MFA
    mfa_code_validity_minutes: int = 5
    mfa_issuer_name: str = "MCX3D Finance"

    # Security - Rate Limiting
    rate_limit_default: int = 100  # requests per minute
    rate_limit_auth: int = 5  # login attempts per 5 minutes

    # Security - Audit & Monitoring
    audit_log_file: str = "logs/mcx3d_audit.log"
    audit_retention_days: int = 365
    enable_audit_encryption: bool = True

    # Security - Data Protection
    enable_field_encryption: bool = True
    key_rotation_days: int = 90
    data_retention_days: int = 2555  # 7 years

    # Application
    debug: bool = False
    log_level: str = "INFO"
    environment: str = "production"

    model_config = ConfigDict(
        env_file=".env.development", case_sensitive=False, extra="allow"
    )


def detect_environment() -> str:
    """Detects the current environment."""
    for env_var in ["ENVIRONMENT", "ENV", "APP_ENV"]:
        env = os.getenv(env_var)
        if env:
            logger.info(f"Environment detected from {env_var}: {env}")
            return env.lower()
    logger.warning("No environment variable found, defaulting to 'development'.")
    return "development"


def load_config() -> Settings:
    """Loads configuration based on the detected environment."""
    environment = detect_environment()
    env_file = f".env.{environment}"

    if not os.path.exists(env_file):
        logger.warning(
            f"Environment file not found for '{environment}' environment: {env_file}"
        )
        env_file = ".env"

    logger.info(f"Loading configuration from: {env_file}")

    try:
        settings = Settings(_env_file=env_file)
        settings.environment = environment
        return settings
    except Exception as e:
        logger.critical(f"Failed to load configuration: {e}", exc_info=True)
        sys.exit(1)


settings = load_config()


def get_database_url() -> str:
    """Constructs and returns the database URL."""
    db_url = settings.database_url
    if not db_url:
        logger.critical("DATABASE_URL is not set in the environment.")
        raise ValueError("DATABASE_URL must be set.")
    return db_url


def get_xero_config() -> Dict[str, Any]:
    """Returns Xero API configuration."""
    config = {
        "client_id": settings.xero_client_id,
        "client_secret": settings.xero_client_secret,
        "redirect_uri": settings.xero_redirect_uri,
        "scopes": settings.xero_scopes.split(),
    }
    if not all(config.values()):
        logger.warning("Xero configuration is incomplete.")
    return config


def get_xero_webhook_key() -> Optional[str]:
    """Returns the Xero webhook key."""
    return settings.xero_webhook_key


def get_security_config() -> Dict[str, Any]:
    """Returns security-related configuration."""
    if not settings.secret_key or not settings.encryption_key:
        logger.warning("SECRET_KEY or ENCRYPTION_KEY is not set. This is insecure.")

    return {
        "secret_key": settings.secret_key,
        "encryption_key": settings.encryption_key,
        "jwt_algorithm": settings.jwt_algorithm,
        "access_token_expire_minutes": settings.access_token_expire_minutes,
        "refresh_token_expire_days": settings.refresh_token_expire_days,
        "max_sessions_per_user": settings.max_sessions_per_user,
        "session_idle_timeout_minutes": settings.session_idle_timeout_minutes,
        "max_login_attempts": settings.max_login_attempts,
        "lockout_duration_minutes": settings.lockout_duration_minutes,
        "mfa_issuer_name": settings.mfa_issuer_name,
        "enable_field_encryption": settings.enable_field_encryption,
    }


def validate_startup_config():
    """Validates critical configurations on startup."""
    logger.info("Validating startup configuration...")
    get_database_url()  # This will raise an error if not set
    sec_conf = get_security_config()
    if not sec_conf["secret_key"]:
        logger.critical("SECRET_KEY must be set for the application to run.")
        raise ValueError("SECRET_KEY must be set.")
    logger.info("Startup configuration appears valid.")
