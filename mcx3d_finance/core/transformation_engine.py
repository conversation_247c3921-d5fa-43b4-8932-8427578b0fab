"""
Enhanced data transformation pipeline with advanced error handling, batch processing,
data quality scoring, and configurable transformation rules.
"""

from typing import Dict, List, Any, Optional, Callable, TYPE_CHECKING
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import json
import time
import traceback
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from datetime import datetime

from mcx3d_finance.exceptions.handlers import handle_errors
from mcx3d_finance.utils.logging_config import get_core_logger, log_performance

if TYPE_CHECKING:
    from mcx3d_finance.core.validation_integration import IntegratedValidationEngine

logger = get_core_logger("transformation_engine")


class TransformationStatus(Enum):
    """Status of transformation operations."""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILED = "failed"
    SKIPPED = "skipped"


class DataQualityLevel(Enum):
    """Data quality levels."""
    EXCELLENT = "excellent"    # >= 0.9
    GOOD = "good"             # 0.7-0.89
    FAIR = "fair"             # 0.5-0.69
    POOR = "poor"             # < 0.5


class TransformationRuleType(Enum):
    """Types of transformation rules."""
    FIELD_MAPPING = "field_mapping"
    VALUE_TRANSFORMATION = "value_transformation"
    DATA_VALIDATION = "data_validation"
    ENRICHMENT = "enrichment"
    NORMALIZATION = "normalization"
    AGGREGATION = "aggregation"


@dataclass
class TransformationRule:
    """Represents a single transformation rule."""
    rule_id: str
    rule_type: TransformationRuleType
    name: str
    description: str
    source_fields: List[str]
    target_fields: List[str]
    transformation_function: str  # Function name or lambda expression
    conditions: Dict[str, Any] = field(default_factory=dict)
    priority: int = 100  # Lower numbers = higher priority
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class TransformationResult:
    """Result of a transformation operation."""
    rule_id: str
    status: TransformationStatus
    input_count: int
    output_count: int
    success_count: int
    error_count: int
    processing_time: float
    errors: List[Dict[str, Any]] = field(default_factory=list)
    warnings: List[Dict[str, Any]] = field(default_factory=list)
    quality_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchProcessingResult:
    """Result of batch processing operation."""
    batch_id: str
    total_records: int
    processed_records: int
    successful_records: int
    failed_records: int
    processing_time: float
    batch_results: List[TransformationResult] = field(default_factory=list)
    overall_quality_score: float = 0.0
    status: TransformationStatus = TransformationStatus.PENDING


class DataQualityScorer:
    """Advanced data quality scoring system."""

    def __init__(self):
        self.quality_weights = {
            'completeness': 0.25,      # How complete is the data
            'accuracy': 0.25,          # How accurate is the data
            'consistency': 0.20,       # How consistent is the data
            'validity': 0.15,          # How valid is the data format
            'uniqueness': 0.15         # How unique is the data (no duplicates)
        }

    def calculate_quality_score(
        self,
        data: List[Dict[str, Any]],
        schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Calculate comprehensive data quality score."""
        try:
            if not data:
                return {
                    'overall_score': 0.0,
                    'quality_level': DataQualityLevel.POOR,
                    'dimension_scores': {},
                    'recommendations': ['No data to analyze']
                }

            # Calculate individual quality dimensions
            completeness_score = self._calculate_completeness(data, schema)
            accuracy_score = self._calculate_accuracy(data, schema)
            consistency_score = self._calculate_consistency(data)
            validity_score = self._calculate_validity(data, schema)
            uniqueness_score = self._calculate_uniqueness(data)

            dimension_scores = {
                'completeness': completeness_score,
                'accuracy': accuracy_score,
                'consistency': consistency_score,
                'validity': validity_score,
                'uniqueness': uniqueness_score
            }

            # Calculate weighted overall score
            overall_score = sum(
                score * self.quality_weights[dimension]
                for dimension, score in dimension_scores.items()
            )

            # Determine quality level
            if overall_score >= 0.9:
                quality_level = DataQualityLevel.EXCELLENT
            elif overall_score >= 0.7:
                quality_level = DataQualityLevel.GOOD
            elif overall_score >= 0.5:
                quality_level = DataQualityLevel.FAIR
            else:
                quality_level = DataQualityLevel.POOR

            # Generate recommendations
            recommendations = self._generate_quality_recommendations(dimension_scores)

            return {
                'overall_score': overall_score,
                'quality_level': quality_level,
                'dimension_scores': dimension_scores,
                'recommendations': recommendations,
                'record_count': len(data),
                'analysis_timestamp': datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calculating data quality score: {e}")
            return {
                'overall_score': 0.0,
                'quality_level': DataQualityLevel.POOR,
                'error': str(e)
            }

    def _calculate_completeness(
        self,
        data: List[Dict[str, Any]],
        schema: Optional[Dict[str, Any]] = None
    ) -> float:
        """Calculate data completeness score."""
        if not data:
            return 0.0

        total_fields = 0
        filled_fields = 0

        for record in data:
            for _field_name, value in record.items():
                total_fields += 1
                if value is not None and value != "" and value != []:
                    filled_fields += 1

        return filled_fields / total_fields if total_fields > 0 else 0.0

    def _calculate_accuracy(
        self,
        data: List[Dict[str, Any]],
        schema: Optional[Dict[str, Any]] = None
    ) -> float:
        """Calculate data accuracy score based on format validation."""
        if not data:
            return 0.0

        total_values = 0
        accurate_values = 0

        for record in data:
            for f, value in record.items():
                if value is not None and value != "":
                    total_values += 1

                    # Basic accuracy checks
                    if f.lower() in ['email', 'email_address']:
                        if '@' in str(value) and '.' in str(value):
                            accurate_values += 1
                    elif f.lower() in ['phone', 'phone_number']:
                        # Check if phone has reasonable length and digits
                        phone_str = str(value).replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
                        if phone_str.isdigit() and 7 <= len(phone_str) <= 15:
                            accurate_values += 1
                    elif f.lower() in ['amount', 'total', 'balance']:
                        try:
                            float(value)
                            accurate_values += 1
                        except (ValueError, TypeError):
                            pass
                    elif f.lower() in ['date', 'created_date', 'modified_date']:
                        try:
                            if isinstance(value, str):
                                datetime.fromisoformat(value.replace('Z', '+00:00'))
                            accurate_values += 1
                        except (ValueError, TypeError):
                            pass
                    else:
                        # For other fields, assume accurate if not empty
                        accurate_values += 1

        return accurate_values / total_values if total_values > 0 else 0.0

    def _calculate_consistency(self, data: List[Dict[str, Any]]) -> float:
        """Calculate data consistency score."""
        if len(data) < 2:
            return 1.0  # Single record is consistent by definition

        # Check format consistency across records
        field_formats: Dict[str, Dict[str, set]] = {}
        total_checks = 0
        consistent_checks = 0

        for record in data:
            for f, value in record.items():
                if value is not None and value != "":
                    value_type = type(value).__name__
                    value_format = self._get_value_format(value)

                    if f not in field_formats:
                        field_formats[f] = {'types': set(), 'formats': set()}

                    field_formats[f]['types'].add(value_type)
                    field_formats[f]['formats'].add(value_format)

        # Calculate consistency score
        for field, formats in field_formats.items():
            total_checks += 2  # Type and format consistency
            if len(formats['types']) == 1:
                consistent_checks += 1
            if len(formats['formats']) == 1:
                consistent_checks += 1

        return consistent_checks / total_checks if total_checks > 0 else 1.0

    def _calculate_validity(
        self,
        data: List[Dict[str, Any]],
        schema: Optional[Dict[str, Any]] = None
    ) -> float:
        """Calculate data validity score."""
        # For now, use accuracy score as validity proxy
        # In a full implementation, this would validate against schema
        return self._calculate_accuracy(data, schema)

    def _calculate_uniqueness(self, data: List[Dict[str, Any]]) -> float:
        """Calculate data uniqueness score."""
        if not data:
            return 1.0

        # Create hash of each record for duplicate detection
        record_hashes = []
        for record in data:
            # Create a consistent hash of the record
            record_str = json.dumps(record, sort_keys=True, default=str)
            record_hash = hashlib.md5(record_str.encode()).hexdigest()
            record_hashes.append(record_hash)

        unique_records = len(set(record_hashes))
        total_records = len(record_hashes)

        return unique_records / total_records if total_records > 0 else 1.0

    def _get_value_format(self, value: Any) -> str:
        """Get format pattern of a value."""
        if isinstance(value, str):
            if '@' in value and '.' in value:
                return 'email'
            elif value.replace('-', '').replace(' ', '').replace('(', '').replace(')', '').isdigit():
                return 'phone'
            elif len(value) == 10 and value.isdigit():
                return 'date_yyyymmdd'
            else:
                return 'string'
        elif isinstance(value, (int, float)):
            return 'numeric'
        elif isinstance(value, bool):
            return 'boolean'
        else:
            return 'other'

    def _generate_quality_recommendations(
        self,
        dimension_scores: Dict[str, float]
    ) -> List[str]:
        """Generate recommendations based on quality scores."""
        recommendations = []

        for dimension, score in dimension_scores.items():
            if score < 0.7:
                if dimension == 'completeness':
                    recommendations.append("Improve data completeness by filling missing fields")
                elif dimension == 'accuracy':
                    recommendations.append("Validate and correct data formats and values")
                elif dimension == 'consistency':
                    recommendations.append("Standardize data formats across all records")
                elif dimension == 'validity':
                    recommendations.append("Implement schema validation for data integrity")
                elif dimension == 'uniqueness':
                    recommendations.append("Remove duplicate records to improve uniqueness")

        if not recommendations:
            recommendations.append("Data quality is good - maintain current standards")

        return recommendations


class TransformationRuleEngine:
    """Rule engine for configurable data transformations."""

    def __init__(self) -> None:
        self.rules: Dict[str, TransformationRule] = {}
        self.transformation_functions = self._load_builtin_functions()

    def add_rule(self, rule: TransformationRule) -> None:
        """Add a transformation rule."""
        self.rules[rule.rule_id] = rule
        logger.info(f"Added transformation rule: {rule.name}")

    def remove_rule(self, rule_id: str) -> bool:
        """Remove a transformation rule."""
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"Removed transformation rule: {rule_id}")
            return True
        return False

    def get_applicable_rules(
        self,
        data_type: str,
        available_fields: List[str]
    ) -> List[TransformationRule]:
        """Get rules applicable to the given data type and fields."""
        applicable_rules = []

        for rule in self.rules.values():
            if not rule.enabled:
                continue

            # Check if rule conditions are met
            if self._rule_applies(rule, data_type, available_fields):
                applicable_rules.append(rule)

        # Sort by priority (lower number = higher priority)
        applicable_rules.sort(key=lambda r: r.priority)
        return applicable_rules

    def apply_rule(
        self,
        rule: TransformationRule,
        data: List[Dict[str, Any]]
    ) -> TransformationResult:
        """Apply a single transformation rule to data."""
        start_time = time.time()

        result = TransformationResult(
            rule_id=rule.rule_id,
            status=TransformationStatus.PROCESSING,
            input_count=len(data),
            output_count=0,
            success_count=0,
            error_count=0,
            processing_time=0.0
        )

        try:
            transformed_data = []

            for i, record in enumerate(data):
                try:
                    # Apply transformation function
                    transformed_record = self._apply_transformation_function(
                        rule, record
                    )

                    if transformed_record is not None:
                        transformed_data.append(transformed_record)
                        result.success_count += 1
                    else:
                        result.error_count += 1
                        result.errors.append({
                            'record_index': i,
                            'error': 'Transformation returned None',
                            'record_id': record.get('id', f'record_{i}')
                        })

                except Exception as e:
                    result.error_count += 1
                    result.errors.append({
                        'record_index': i,
                        'error': str(e),
                        'record_id': record.get('id', f'record_{i}'),
                        'traceback': traceback.format_exc()
                    })

            result.output_count = len(transformed_data)
            result.processing_time = time.time() - start_time

            # Determine final status
            if result.error_count == 0:
                result.status = TransformationStatus.SUCCESS
            elif result.success_count > 0:
                result.status = TransformationStatus.PARTIAL_SUCCESS
            else:
                result.status = TransformationStatus.FAILED

            # Calculate quality score
            if result.input_count > 0:
                result.quality_score = result.success_count / result.input_count

            return result

        except Exception as e:
            result.status = TransformationStatus.FAILED
            result.processing_time = time.time() - start_time
            result.errors.append({
                'error': f"Rule application failed: {str(e)}",
                'traceback': traceback.format_exc()
            })
            return result

    def _rule_applies(
        self,
        rule: TransformationRule,
        data_type: str,
        available_fields: List[str]
    ) -> bool:
        """Check if a rule applies to the given context."""
        # Check if required source fields are available
        for f in rule.source_fields:
            if f not in available_fields:
                return False

        # Check rule conditions
        conditions = rule.conditions
        if 'data_type' in conditions:
            if conditions['data_type'] != data_type:
                return False

        return True

    def _apply_transformation_function(
        self,
        rule: TransformationRule,
        record: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Apply the transformation function to a single record."""
        function_name = rule.transformation_function

        if function_name in self.transformation_functions:
            func = self.transformation_functions[function_name]
            return func(record, rule)
        else:
            # Try to evaluate as lambda expression
            try:
                # Create safe evaluation context
                safe_globals = {
                    '__builtins__': {},
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'len': len,
                    'max': max,
                    'min': min,
                    'sum': sum,
                    'abs': abs,
                    'round': round
                }

                # Evaluate the function
                func = eval(function_name, safe_globals)
                return func(record)

            except Exception as e:
                logger.error(f"Error evaluating transformation function {function_name}: {e}")
                return None

    def _load_builtin_functions(self) -> Dict[str, Callable]:
        """Load built-in transformation functions."""
        return {
            'normalize_phone': self._normalize_phone,
            'normalize_email': self._normalize_email,
            'standardize_currency': self._standardize_currency,
            'format_date': self._format_date,
            'clean_text': self._clean_text,
            'calculate_age': self._calculate_age,
            'extract_domain': self._extract_domain
        }

    def _normalize_phone(self, record: Dict[str, Any], rule: TransformationRule) -> Dict[str, Any]:
        """Normalize phone number format."""
        result = record.copy()

        for source_field in rule.source_fields:
            if source_field in record:
                phone = str(record[source_field])
                # Remove all non-digit characters
                digits = ''.join(filter(str.isdigit, phone))

                # Format based on length
                if len(digits) == 10:
                    formatted = f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
                elif len(digits) == 11 and digits.startswith('1'):
                    formatted = f"+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
                else:
                    formatted = digits

                # Set target field
                target_field = rule.target_fields[0] if rule.target_fields else source_field
                result[target_field] = formatted

        return result

    def _normalize_email(self, record: Dict[str, Any], rule: TransformationRule) -> Dict[str, Any]:
        """Normalize email address."""
        result = record.copy()

        for source_field in rule.source_fields:
            if source_field in record:
                email = str(record[source_field]).lower().strip()
                target_field = rule.target_fields[0] if rule.target_fields else source_field
                result[target_field] = email

        return result

    def _standardize_currency(self, record: Dict[str, Any], rule: TransformationRule) -> Dict[str, Any]:
        """Standardize currency format."""
        result = record.copy()

        for source_field in rule.source_fields:
            if source_field in record:
                try:
                    amount = float(str(record[source_field]).replace('$', '').replace(',', ''))
                    target_field = rule.target_fields[0] if rule.target_fields else source_field
                    result[target_field] = round(amount, 2)
                except (ValueError, TypeError):
                    pass

        return result

    def _format_date(self, record: Dict[str, Any], rule: TransformationRule) -> Dict[str, Any]:
        """Format date to ISO format."""
        result = record.copy()

        for source_field in rule.source_fields:
            if source_field in record:
                try:
                    date_str = str(record[source_field])
                    # Try to parse and format to ISO
                    if 'T' in date_str:
                        dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    else:
                        dt = datetime.strptime(date_str, '%Y-%m-%d')

                    target_field = rule.target_fields[0] if rule.target_fields else source_field
                    result[target_field] = dt.isoformat()
                except (ValueError, TypeError):
                    pass

        return result

    def _clean_text(self, record: Dict[str, Any], rule: TransformationRule) -> Dict[str, Any]:
        """Clean text fields."""
        result = record.copy()

        for source_field in rule.source_fields:
            if source_field in record:
                text = str(record[source_field])
                # Remove extra whitespace and normalize
                cleaned = ' '.join(text.split())
                target_field = rule.target_fields[0] if rule.target_fields else source_field
                result[target_field] = cleaned

        return result

    def _calculate_age(self, record: Dict[str, Any], rule: TransformationRule) -> Dict[str, Any]:
        """Calculate age from birth date."""
        result = record.copy()

        for source_field in rule.source_fields:
            if source_field in record:
                try:
                    birth_date = datetime.fromisoformat(str(record[source_field]).replace('Z', '+00:00'))
                    age = (datetime.utcnow() - birth_date).days // 365
                    target_field = rule.target_fields[0] if rule.target_fields else f"{source_field}_age"
                    result[target_field] = age
                except (ValueError, TypeError):
                    pass

        return result

    def _extract_domain(self, record: Dict[str, Any], rule: TransformationRule) -> Dict[str, Any]:
        """Extract domain from email address."""
        result = record.copy()

        for source_field in rule.source_fields:
            if source_field in record:
                email = str(record[source_field])
                if '@' in email:
                    domain = email.split('@')[-1].lower()
                    target_field = rule.target_fields[0] if rule.target_fields else f"{source_field}_domain"
                    result[target_field] = domain

        return result


class BatchTransformationEngine:
    """High-performance batch transformation engine with parallel processing and validation integration."""

    @log_performance()
    def __init__(self, max_workers: int = 4, validation_engine: Optional['IntegratedValidationEngine'] = None):
        self.rule_engine = TransformationRuleEngine()
        self.quality_scorer = DataQualityScorer()
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # For validation integration
        self.validation_engine = validation_engine
        self.validation_stats = {
            'pre_validation_runs': 0,
            'post_validation_runs': 0,
            'pre_validation_failures': 0,
            'post_validation_failures': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        logger.info("BatchTransformationEngine initialized", max_workers=max_workers)
        
        # Use consolidated Redis cache instead of SharedCacheManager
        from mcx3d_finance.core.cache import get_cache
        self.cache_manager = get_cache()
        logger.info("Using consolidated Redis cache for batch transformation")

    async def validate_pre_transformation(
        self,
        data: List[Dict[str, Any]],
        data_type: str,
        organization_id: str
    ) -> Optional[Dict[str, Any]]:
        """Validate data before transformation and route based on results."""
        if not self.validation_engine:
            return None

        try:
            from mcx3d_finance.core.validation_integration import ValidationContext, ValidationTrigger

            # Create validation context for transformation
            context = ValidationContext(
                organization_id=organization_id,
                data_type=data_type,
                trigger=ValidationTrigger.ON_TRANSFORMATION,
                source_system="transformation_engine"
            )

            # Validate data before transformation
            validation_data = {f"{data_type}s": data}
            routing_result = await self.validation_engine.validate_and_route_data(
                validation_data, context, enable_routing=True
            )

            self.validation_stats['pre_transformation_validations'] += 1

            # Check if data should proceed to transformation
            if routing_result.action.value in ['reject', 'quarantine']:
                self.validation_stats['validation_failures'] += 1
                logger.warning(f"Pre-transformation validation failed: {routing_result.action.value}")
                return {
                    'routing_result': routing_result,
                    'should_proceed': False,
                    'reason': f"Validation {routing_result.action.value}: data quality insufficient"
                }

            self.validation_stats['routing_decisions'] += 1
            return {
                'routing_result': routing_result,
                'should_proceed': True,
                'reason': "Pre-transformation validation passed"
            }

        except Exception as e:
            logger.error(f"Error in pre-transformation validation: {e}")
            self.validation_stats['validation_failures'] += 1
            return {
                'should_proceed': True,  # Continue on validation errors
                'reason': f"Validation error (proceeding): {str(e)}"
            }

    async def validate_post_transformation(
        self,
        original_data: List[Dict[str, Any]],
        transformed_data: List[Dict[str, Any]],
        data_type: str,
        organization_id: str
    ) -> Optional[Dict[str, Any]]:
        """Validate data after transformation and provide quality assessment."""
        if not self.validation_engine:
            return None

        try:
            from mcx3d_finance.core.validation_integration import ValidationContext, ValidationTrigger

            # Create validation context for post-transformation
            context = ValidationContext(
                organization_id=organization_id,
                data_type=data_type,
                trigger=ValidationTrigger.ON_TRANSFORMATION,
                source_system="transformation_engine",
                metadata={'stage': 'post_transformation'}
            )

            # Validate transformed data
            validation_data = {f"{data_type}s": transformed_data}
            routing_result = await self.validation_engine.validate_and_route_data(
                validation_data, context, enable_routing=True
            )

            self.validation_stats['post_transformation_validations'] += 1

            # Calculate quality improvement
            original_quality = self.quality_scorer.calculate_quality_score(original_data)
            transformed_quality = self.quality_scorer.calculate_quality_score(transformed_data)

            quality_improvement = transformed_quality['overall_score'] - original_quality['overall_score']

            return {
                'routing_result': routing_result,
                'original_quality': original_quality,
                'transformed_quality': transformed_quality,
                'quality_improvement': quality_improvement,
                'transformation_successful': quality_improvement >= 0
            }

        except Exception as e:
            logger.error(f"Error in post-transformation validation: {e}")
            self.validation_stats['validation_failures'] += 1
            return {
                'transformation_successful': True,  # Assume success on validation errors
                'reason': f"Post-validation error: {str(e)}"
            }

    def process_batch(
        self,
        data: List[Dict[str, Any]],
        data_type: str,
        batch_size: int = 1000,
        parallel: bool = True,
        organization_id: Optional[str] = None,
        enable_validation: bool = True
    ) -> BatchProcessingResult:
        """Process data in batches with parallel execution."""
        start_time = time.time()
        batch_id = f"batch_{int(time.time())}_{hash(str(data[:5]))}"

        result = BatchProcessingResult(
            batch_id=batch_id,
            total_records=len(data),
            processed_records=0,
            successful_records=0,
            failed_records=0,
            processing_time=0.0,
            status=TransformationStatus.PROCESSING
        )

        try:
            logger.info(f"Starting batch processing: {batch_id}, {len(data)} records")

            # Get applicable transformation rules
            available_fields = list(data[0].keys()) if data else []
            applicable_rules = self.rule_engine.get_applicable_rules(data_type, available_fields)

            if not applicable_rules:
                logger.warning(f"No applicable transformation rules found for data type: {data_type}")
                result.status = TransformationStatus.SKIPPED
                result.processing_time = time.time() - start_time
                return result

            # Split data into batches
            batches = [data[i:i + batch_size] for i in range(0, len(data), batch_size)]

            if parallel and len(batches) > 1:
                # Process batches in parallel
                result.batch_results = self._process_batches_parallel(
                    batches, applicable_rules, data_type
                )
            else:
                # Process batches sequentially
                result.batch_results = self._process_batches_sequential(
                    batches, applicable_rules, data_type
                )

            # Aggregate results
            for batch_result in result.batch_results:
                result.processed_records += batch_result.input_count
                result.successful_records += batch_result.success_count
                result.failed_records += batch_result.error_count

            # Calculate overall quality score
            if result.processed_records > 0:
                result.overall_quality_score = result.successful_records / result.processed_records

            # Determine final status
            if result.failed_records == 0:
                result.status = TransformationStatus.SUCCESS
            elif result.successful_records > 0:
                result.status = TransformationStatus.PARTIAL_SUCCESS
            else:
                result.status = TransformationStatus.FAILED

            result.processing_time = time.time() - start_time

            logger.info(f"Batch processing completed: {batch_id}, "
                       f"{result.successful_records}/{result.total_records} successful, "
                       f"{result.processing_time:.2f}s")

            return result

        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            result.status = TransformationStatus.FAILED
            result.processing_time = time.time() - start_time
            return result

    def _process_batches_parallel(
        self,
        batches: List[List[Dict[str, Any]]],
        rules: List[TransformationRule],
        data_type: str
    ) -> List[TransformationResult]:
        """Process batches in parallel using ThreadPoolExecutor."""
        results = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all batch processing tasks
            future_to_batch = {
                executor.submit(self._process_single_batch, batch, rules, data_type): i
                for i, batch in enumerate(batches)
            }

            # Collect results as they complete
            for future in as_completed(future_to_batch):
                batch_index = future_to_batch[future]
                try:
                    batch_results = future.result()
                    results.extend(batch_results)
                except Exception as e:
                    logger.error(f"Error processing batch {batch_index}: {e}")
                    # Create error result for failed batch
                    error_result = TransformationResult(
                        rule_id="batch_error",
                        status=TransformationStatus.FAILED,
                        input_count=len(batches[batch_index]),
                        output_count=0,
                        success_count=0,
                        error_count=len(batches[batch_index]),
                        processing_time=0.0,
                        errors=[{'error': str(e), 'batch_index': batch_index}]
                    )
                    results.append(error_result)

        return results

    def _process_batches_sequential(
        self,
        batches: List[List[Dict[str, Any]]],
        rules: List[TransformationRule],
        data_type: str
    ) -> List[TransformationResult]:
        """Process batches sequentially."""
        results = []

        for i, batch in enumerate(batches):
            try:
                batch_results = self._process_single_batch(batch, rules, data_type)
                results.extend(batch_results)
            except Exception as e:
                logger.error(f"Error processing batch {i}: {e}")
                error_result = TransformationResult(
                    rule_id="batch_error",
                    status=TransformationStatus.FAILED,
                    input_count=len(batch),
                    output_count=0,
                    success_count=0,
                    error_count=len(batch),
                    processing_time=0.0,
                    errors=[{'error': str(e), 'batch_index': i}]
                )
                results.append(error_result)

        return results

    def _process_single_batch(
        self,
        batch: List[Dict[str, Any]],
        rules: List[TransformationRule],
        data_type: str
    ) -> List[TransformationResult]:
        """Process a single batch through all applicable rules."""
        results = []
        current_data = batch.copy()

        for rule in rules:
            try:
                # Apply rule to current data
                rule_result = self.rule_engine.apply_rule(rule, current_data)
                results.append(rule_result)

                # If rule was successful, update current_data for next rule
                if rule_result.status in [TransformationStatus.SUCCESS, TransformationStatus.PARTIAL_SUCCESS]:
                    # For now, keep original data structure
                    # In a full implementation, you'd apply the actual transformations
                    pass

            except Exception as e:
                logger.error(f"Error applying rule {rule.rule_id}: {e}")
                error_result = TransformationResult(
                    rule_id=rule.rule_id,
                    status=TransformationStatus.FAILED,
                    input_count=len(current_data),
                    output_count=0,
                    success_count=0,
                    error_count=len(current_data),
                    processing_time=0.0,
                    errors=[{'error': str(e)}]
                )
                results.append(error_result)

        return results

    def add_transformation_rule(self, rule: TransformationRule) -> None:
        """Add a transformation rule to the engine."""
        self.rule_engine.add_rule(rule)

    def remove_transformation_rule(self, rule_id: str) -> bool:
        """Remove a transformation rule from the engine."""
        return self.rule_engine.remove_rule(rule_id)

    def get_quality_report(
        self,
        data: List[Dict[str, Any]],
        schema: Optional[Dict[str, Any]] = None,
        organization_id: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """Generate comprehensive data quality report with caching."""
        # Try to get from cache first if enabled
        if use_cache and self.cache_manager and organization_id:
            data_hash = str(hash(json.dumps(data[:5], sort_keys=True, default=str)))  # Hash first 5 records
            cached_report = self.cache_manager.get(
                'quality_scores',
                organization_id=organization_id,
                data_hash=data_hash,
                record_count=len(data)
            )

            if cached_report:
                logger.debug(f"Using cached quality report for {len(data)} records")
                return cached_report

        # Calculate quality score
        quality_report = self.quality_scorer.calculate_quality_score(data, schema)

        # Cache the result if enabled
        if use_cache and self.cache_manager and organization_id:
            data_hash = str(hash(json.dumps(data[:5], sort_keys=True, default=str)))
            self.cache_manager.set(
                'quality_scores',
                quality_report,
                organization_id=organization_id,
                data_hash=data_hash,
                record_count=len(data)
            )

        return quality_report

    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation integration statistics including cache coordination."""
        total_validations = (
            self.validation_stats['pre_transformation_validations'] +
            self.validation_stats['post_transformation_validations']
        )

        failure_rate = (
            self.validation_stats['validation_failures'] / total_validations
            if total_validations > 0 else 0.0
        )

        # Get cache statistics if available
        cache_stats = {}
        if self.cache_manager:
            overall_cache_stats = self.cache_manager.get_cache_statistics()
            cache_stats = {
                'cache_coordination_enabled': True,
                'quality_scores_cached': overall_cache_stats['cache_sizes'].get('quality_scores', 0),
                'transformation_results_cached': overall_cache_stats['cache_sizes'].get('transformation_results', 0),
                'cache_hit_rate': overall_cache_stats['hit_rate']
            }
        else:
            cache_stats = {'cache_coordination_enabled': False}

        return {
            'validation_enabled': self.validation_engine is not None,
            'total_validations': total_validations,
            'pre_transformation_validations': self.validation_stats['pre_transformation_validations'],
            'post_transformation_validations': self.validation_stats['post_transformation_validations'],
            'validation_failures': self.validation_stats['validation_failures'],
            'routing_decisions': self.validation_stats['routing_decisions'],
            'failure_rate': failure_rate,
            'success_rate': 1.0 - failure_rate,
            'cache_stats': cache_stats
        }

    def reset_validation_statistics(self) -> None:
        """Reset validation statistics counters."""
        self.validation_stats = {
            'pre_transformation_validations': 0,
            'post_transformation_validations': 0,
            'validation_failures': 0,
            'routing_decisions': 0
        }
