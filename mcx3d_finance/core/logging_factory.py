"""
Centralized logging factory for the MCX3D Financial Platform.

This module provides a unified interface for creating loggers throughout
the application, ensuring consistent structured logging, security filtering,
and correlation ID tracking.
"""

import contextvars
import functools
from typing import Optional, Dict, Any, Set, Callable
from datetime import datetime
import uuid
import threading
import logging
import os

from mcx3d_finance.monitoring.structured_logger import (
    StructuredLogger,
    correlation_id_context,
    get_correlation_id as get_structured_correlation_id,
    set_correlation_id as set_structured_correlation_id
)

# Context variable for correlation ID that works across async and sync contexts
_correlation_id: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar(
    'mcx3d_correlation_id',
    default=None
)

# Thread-local storage for sync contexts
_thread_local = threading.local()

# Security-sensitive fields that should never be logged
REDACTED_FIELDS: Set[str] = {
    'password', 'secret', 'token', 'api_key', 'private_key',
    'access_token', 'refresh_token', 'authorization', 'x-api-key',
    'ssn', 'social_security_number', 'credit_card', 'card_number',
    'cvv', 'pin', 'account_number', 'routing_number', 'xero_token',
    'jwt_token', 'session_id', 'cookie', 'bearer_token'
}


from mcx3d_finance.utils.logging_config import SecurityFilter as BaseSecurityFilter
class SecurityFilter(BaseSecurityFilter):
    """Security filter with dictionary filtering capabilities."""

    def filter_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively redact sensitive fields in a dictionary."""
        if not isinstance(data, dict):
            return data

        filtered = {}
        for key, value in data.items():
            if self._is_sensitive_key(key):
                filtered[key] = '[REDACTED]'
            elif isinstance(value, dict):
                filtered[key] = self.filter_dict(value)
            elif isinstance(value, list):
                filtered[key] = self._filter_list(value)
            elif isinstance(value, str) and self._is_sensitive_value(value):
                filtered[key] = '[REDACTED]'
            else:
                filtered[key] = value
        return filtered

    def _filter_list(self, items: list) -> list:
        """Filter sensitive data from a list."""
        filtered = []
        for item in items:
            if isinstance(item, dict):
                filtered.append(self.filter_dict(item))
            elif isinstance(item, str) and self._is_sensitive_value(item):
                filtered.append('[REDACTED]')
            else:
                filtered.append(item)
        return filtered

    def _is_sensitive_key(self, key: str) -> bool:
        """Check if a key name indicates sensitive data."""
        key_lower = key.lower()
        return any(field in key_lower for field in self.redacted_fields)

    def _is_sensitive_value(self, value: str) -> bool:
        """Check if a value looks like sensitive data."""
        # Check for common patterns in sensitive values
        if len(value) > 20 and not ' ' in value:  # Long strings without spaces
            # Might be a token or key
            if any(prefix in value.lower() for prefix in ['bearer', 'basic', 'token']):
                return True
        return False


class CorrelationContextManager:
    """Manages correlation ID context across async and sync execution contexts."""

    @staticmethod
    def get_correlation_id() -> Optional[str]:
        """
        Get the current correlation ID from context.

        Checks in order:
        1. Async context (contextvars)
        2. Thread-local storage
        3. Structured logger context (fallback)
        """
        # Try async context first
        correlation_id = _correlation_id.get()
        if correlation_id:
            return correlation_id

        # Try thread-local storage
        if hasattr(_thread_local, 'correlation_id'):
            return _thread_local.correlation_id

        # Fallback to structured logger
        return get_structured_correlation_id()

    @staticmethod
    def set_correlation_id(correlation_id: Optional[str]) -> None:
        """
        Set correlation ID in all available contexts.

        Args:
            correlation_id: The correlation ID to set (or None to clear)
        """
        # Set in async context
        _correlation_id.set(correlation_id)

        # Set in thread-local storage
        _thread_local.correlation_id = correlation_id

        # Set in structured logger context
        if correlation_id:
            set_structured_correlation_id(correlation_id)

    @staticmethod
    def generate_correlation_id() -> str:
        """Generate a new correlation ID if none exists."""
        return f"mcx3d-{uuid.uuid4()}"

    @staticmethod
    def ensure_correlation_id() -> str:
        """Ensure a correlation ID exists, creating one if necessary."""
        correlation_id = CorrelationContextManager.get_correlation_id()
        if not correlation_id:
            correlation_id = CorrelationContextManager.generate_correlation_id()
            CorrelationContextManager.set_correlation_id(correlation_id)
        return correlation_id


class LoggerFactory:
    """
    Factory for creating domain-specific structured loggers.

    This factory ensures all loggers in the system:
    - Use structured logging format
    - Include correlation ID tracking
    - Apply security filtering for sensitive data
    - Add performance monitoring for slow operations
    """

    _instances: Dict[str, StructuredLogger] = {}
    _security_filter = SecurityFilter()
    _lock = threading.Lock()

    # Domain-specific configurations
    _domain_configs = {
        'api': {
            'include_request_info': True,
            'include_user_context': True,
            'performance_threshold_ms': 1000
        },
        'core': {
            'include_business_context': True,
            'performance_threshold_ms': 500
        },
        'integration': {
            'include_external_context': True,
            'include_retry_info': True,
            'performance_threshold_ms': 3000
        },
        'tasks': {
            'include_task_context': True,
            'include_queue_info': True,
            'performance_threshold_ms': 5000
        },
        'security': {
            'include_security_context': True,
            'audit_all_events': True
        },
        'monitoring': {
            'include_metrics': True,
            'include_health_status': True
        }
    }

    @classmethod
    def get_logger(
        cls,
        name: str,
        domain: Optional[str] = None,
        **extra_context
    ) -> StructuredLogger:
        """
        Get or create a logger instance with correlation ID support.

        Args:
            name: Logger name (typically __name__)
            domain: Optional domain for specialized logging (e.g., 'api', 'core', 'integration')
            **extra_context: Additional context to include in all log messages

        Returns:
            Configured StructuredLogger instance with correlation ID tracking
        """
        # Determine the domain from the module name if not provided
        if not domain:
            domain = cls._infer_domain(name)

        # Create a unique key for this logger instance
        logger_key = f"{name}:{domain}" if domain else name

        # Check if we already have this logger (thread-safe)
        with cls._lock:
            if logger_key in cls._instances:
                logger = cls._instances[logger_key]
            else:
                # Create new logger instance
                logger = StructuredLogger(name, domain=domain)

                # Apply domain-specific configuration
                if domain and domain in cls._domain_configs:
                    cls._apply_domain_config(logger, domain)

                # Add correlation ID injector
                logger = cls._wrap_with_correlation_id(logger)

                # Add security filter wrapper
                logger = cls._wrap_with_security_filter(logger)

                # Store the instance
                cls._instances[logger_key] = logger

        # Add any extra context provided
        if extra_context:
            logger = cls._add_context(logger, extra_context)

        return logger

    @classmethod
    def _infer_domain(cls, module_name: str) -> Optional[str]:
        """Infer the domain from the module name."""
        if 'api' in module_name:
            return 'api'
        elif 'core' in module_name:
            return 'core'
        elif 'integration' in module_name or 'xero' in module_name:
            return 'integration'
        elif 'task' in module_name or 'celery' in module_name:
            return 'tasks'
        elif 'security' in module_name or 'auth' in module_name:
            return 'security'
        elif 'monitoring' in module_name or 'metric' in module_name:
            return 'monitoring'
        return None

    @classmethod
    def _apply_domain_config(cls, logger: StructuredLogger, domain: str) -> None:
        """Apply domain-specific configuration to the logger."""
        config = cls._domain_configs.get(domain, {})

        # Set performance threshold
        if 'performance_threshold_ms' in config:
            logger.performance_threshold_ms = config['performance_threshold_ms']

        # Add domain-specific context flags
        for key, value in config.items():
            if key.startswith('include_') or key.startswith('audit_'):
                setattr(logger, key, value)

    @classmethod
    def _wrap_with_correlation_id(cls, logger: StructuredLogger) -> StructuredLogger:
        """Wrap logger methods to automatically include correlation ID."""
        original_log = logger._log if hasattr(logger, '_log') else logger.log

        @functools.wraps(original_log)
        def log_with_correlation_id(level, message, **kwargs):
            # Always ensure correlation ID is included
            correlation_id = CorrelationContextManager.get_correlation_id()
            if correlation_id:
                kwargs['correlation_id'] = correlation_id

            # Add timestamp if not present
            if 'timestamp' not in kwargs:
                kwargs['timestamp'] = datetime.utcnow().isoformat()

            return original_log(level, message, **kwargs)

        if hasattr(logger, '_log'):
            logger._log = log_with_correlation_id
        else:
            logger.log = log_with_correlation_id
        return logger

    @classmethod
    def _wrap_with_security_filter(cls, logger: StructuredLogger) -> StructuredLogger:
        """Wrap logger methods to apply security filtering."""
        original_log = logger._log if hasattr(logger, '_log') else logger.log

        @functools.wraps(original_log)
        def log_with_security_filter(level, message, **kwargs):
            # Apply security filtering to kwargs
            filtered_kwargs = cls._security_filter.filter_dict(kwargs)

            # Also filter the message if it's a dict
            if isinstance(message, dict):
                message = cls._security_filter.filter_dict(message)

            return original_log(level, message, **filtered_kwargs)

        if hasattr(logger, '_log'):
            logger._log = log_with_security_filter
        else:
            logger.log = log_with_security_filter
        return logger

    @classmethod
    def _add_context(cls, logger: StructuredLogger, context: Dict[str, Any]) -> StructuredLogger:
        """Create a logger variant with additional context."""
        # Create a wrapper that adds context to all log calls
        class ContextualLogger(StructuredLogger):
            def __init__(self, base_logger: StructuredLogger, extra_context: Dict[str, Any]):
                self._base_logger = base_logger
                self._extra_context = extra_context
                # Copy attributes from base logger
                for attr in ['name', 'domain', 'performance_threshold_ms']:
                    if hasattr(base_logger, attr):
                        setattr(self, attr, getattr(base_logger, attr))

            def _log(self, level, message, **kwargs):
                # Merge extra context with provided kwargs
                merged_kwargs = {**self._extra_context, **kwargs}
                return self._base_logger._log(level, message, **merged_kwargs)

            def log(self, level, message, **kwargs):
                # Merge extra context with provided kwargs
                merged_kwargs = {**self._extra_context, **kwargs}
                return self._base_logger.log(level, message, **merged_kwargs)

            # Delegate all other methods to base logger
            def __getattr__(self, name):
                return getattr(self._base_logger, name)

        return ContextualLogger(logger, context)

    @classmethod
    def create_child_logger(
        cls,
        parent_logger: StructuredLogger,
        child_name: str,
        **extra_context
    ) -> StructuredLogger:
        """
        Create a child logger that inherits context from parent.

        Args:
            parent_logger: The parent logger
            child_name: Name for the child logger
            **extra_context: Additional context for the child

        Returns:
            Child logger with inherited and additional context
        """
        # Get parent context if available
        parent_context = {}
        if hasattr(parent_logger, '_extra_context'):
            parent_context = parent_logger._extra_context.copy()

        # Merge with new context
        child_context = {**parent_context, **extra_context}

        # Create child logger with full name
        full_name = f"{parent_logger.name}.{child_name}"
        return cls.get_logger(full_name, parent_logger.domain, **child_context)

    @classmethod
    def clear_cache(cls) -> None:
        """Clear the logger instance cache (mainly for testing)."""
        with cls._lock:
            cls._instances.clear()


# Correlation ID context manager for use in with statements
class correlation_context:
    """Context manager for correlation ID scope."""

    def __init__(self, correlation_id: Optional[str] = None):
        self.correlation_id = correlation_id or CorrelationContextManager.generate_correlation_id()
        self.previous_id = None

    def __enter__(self):
        self.previous_id = CorrelationContextManager.get_correlation_id()
        CorrelationContextManager.set_correlation_id(self.correlation_id)
        return self.correlation_id

    def __exit__(self, exc_type, exc_val, exc_tb):
        CorrelationContextManager.set_correlation_id(self.previous_id)


# Decorator for adding correlation ID to functions
def with_correlation_id(func: Callable) -> Callable:
    """Decorator that ensures a correlation ID exists for the function execution."""

    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        correlation_id = CorrelationContextManager.ensure_correlation_id()
        kwargs['correlation_id'] = correlation_id
        return await func(*args, **kwargs)

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        correlation_id = CorrelationContextManager.ensure_correlation_id()
        kwargs['correlation_id'] = correlation_id
        return func(*args, **kwargs)

    # Return appropriate wrapper based on function type
    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


# Export key components
__all__ = [
    'LoggerFactory',
    'CorrelationContextManager',
    'correlation_context',
    'with_correlation_id',
    'SecurityFilter',
    'REDACTED_FIELDS'
]
