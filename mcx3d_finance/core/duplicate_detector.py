"""
Sophisticated duplicate detection and merging engine for financial data,
refactored with structured logging and centralized error handling.
"""
import hashlib
import re
from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

from mcx3d_finance.exceptions.handlers import handle_errors, processing_error
from mcx3d_finance.utils.logging_config import get_core_logger, log_performance

logger = get_core_logger("duplicate_detector")


# --- Enums and Data Models ---


class MatchType(Enum):
    """Type of duplicate match found."""

    EXACT = "exact"
    FUZZY = "fuzzy"


class ConfidenceLevel(Enum):
    """Confidence level of a duplicate match."""

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    NONE = "none"


@dataclass
class DuplicateMatch:
    """Represents a potential duplicate pair."""

    entity1_id: str
    entity2_id: str
    entity_type: str
    confidence_score: float
    confidence_level: ConfidenceLevel
    matching_fields: List[str]
    match_type: MatchType = MatchType.FUZZY
    field_scores: Dict[str, float] = field(default_factory=dict)
    merge_recommendation: str = "no_action"
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class MergeResult:
    """Result of a merge operation."""

    success: bool
    merged_entity_id: Optional[str] = None
    source_entity_ids: List[str] = field(default_factory=list)
    merged_fields: Dict[str, Any] = field(default_factory=dict)
    conflicts_resolved: List[Dict[str, Any]] = field(default_factory=list)
    error_message: Optional[str] = None


# --- Core Components ---


class FuzzyMatcher:
    """Provides fuzzy string matching algorithms."""

    def normalize_string(self, text: str) -> str:
        """Normalizes a string for comparison."""
        if not text:
            return ""
        text = text.lower().strip()
        text = re.sub(r"[^\w\s]", "", text)  # Remove punctuation
        text = re.sub(r"\s+", " ", text)  # Collapse whitespace
        return text

    def jaro_winkler_similarity(self, s1: str, s2: str) -> float:
        """Calculates Jaro-Winkler similarity. A robust fuzzy matching algorithm."""
        # This is a simplified implementation for demonstration.
        # In a real-world scenario, a library like `jellyfish` would be used.
        if not s1 or not s2:
            return 0.0
        len_s1, len_s2 = len(s1), len(s2)
        match_distance = max(len_s1, len_s2) // 2 - 1
        matches1, matches2 = [False] * len_s1, [False] * len_s2
        matches = 0
        transpositions = 0

        for i in range(len_s1):
            start = max(0, i - match_distance)
            end = min(i + match_distance + 1, len_s2)
            for j in range(start, end):
                if not matches2[j] and s1[i] == s2[j]:
                    matches1[i] = True
                    matches2[j] = True
                    matches += 1
                    break
        if matches == 0:
            return 0.0

        k = 0
        for i in range(len_s1):
            if matches1[i]:
                while not matches2[k]:
                    k += 1
                if s1[i] != s2[k]:
                    transpositions += 1
                k += 1

        jaro_similarity = (
            matches / len_s1
            + matches / len_s2
            + (matches - transpositions // 2) / matches
        ) / 3

        # Winkler prefix bonus
        prefix = 0
        for i in range(min(len(s1), len(s2), 4)):
            if s1[i] == s2[i]:
                prefix += 1
            else:
                break
        return jaro_similarity + prefix * 0.1 * (1 - jaro_similarity)


class ConfidenceScorer:
    """Calculates confidence scores for duplicate matches."""

    def __init__(self):
        self.weights = {
            "transaction": {"amount": 0.4, "date": 0.3, "description": 0.2},
            "contact": {"email": 0.5, "name": 0.3, "phone": 0.2},
            "account": {"code": 0.6, "name": 0.3, "type": 0.1},
        }
        self.thresholds = {
            "high": 0.9,
            "medium": 0.75,
            "low": 0.6,
        }

    def calculate_confidence(
        self, entity_type: str, field_scores: Dict[str, float]
    ) -> Tuple[float, ConfidenceLevel]:
        """Calculates the overall confidence score and level."""
        score = sum(
            field_scores.get(field, 0.0) * weight
            for field, weight in self.weights.get(entity_type, {}).items()
        )
        if score >= self.thresholds["high"]:
            level = ConfidenceLevel.HIGH
        elif score >= self.thresholds["medium"]:
            level = ConfidenceLevel.MEDIUM
        elif score >= self.thresholds["low"]:
            level = ConfidenceLevel.LOW
        else:
            level = ConfidenceLevel.NONE
        return score, level


class DuplicateDetector:
    """Engine for detecting duplicate entities in financial data."""

    def __init__(self):
        self.fuzzy_matcher = FuzzyMatcher()
        self.confidence_scorer = ConfidenceScorer()
        self.processed_pairs: Set[Tuple[str, str]] = set()

    @handle_errors(operation="detect_duplicates")
    @log_performance()
    def detect_duplicates(
        self, entities: List[Dict[str, Any]], entity_type: str
    ) -> List[DuplicateMatch]:
        """Detects duplicates in a list of entities using blocking and fuzzy matching."""
        if not entities:
            return []

        logger.info(
            "Starting duplicate detection.",
            entity_type=entity_type,
            record_count=len(entities),
        )
        self.processed_pairs.clear()
        duplicates = []
        blocks = self._create_blocking_index(entities, entity_type)

        for block_key, block_entities in blocks.items():
            if len(block_entities) < 2:
                continue

            for i in range(len(block_entities)):
                for j in range(i + 1, len(block_entities)):
                    entity1, entity2 = block_entities[i], block_entities[j]
                    pair_key = tuple(
                        sorted([str(entity1.get("id")), str(entity2.get("id"))])
                    )
                    if pair_key in self.processed_pairs:
                        continue
                    self.processed_pairs.add(pair_key)

                    match = self._compare_entities(entity1, entity2, entity_type)
                    if match:
                        duplicates.append(match)

        logger.info(
            "Duplicate detection finished.",
            entity_type=entity_type,
            duplicates_found=len(duplicates),
        )
        return duplicates

    def _create_blocking_index(
        self, entities: List[Dict[str, Any]], entity_type: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Creates an index (blocking) to reduce pairwise comparisons."""
        blocks: Dict[str, List[Dict[str, Any]]] = {}
        for entity in entities:
            key = "default"
            if entity_type == "transaction":
                date_str = entity.get("date", "")
                if date_str and isinstance(date_str, str):
                    key = date_str[:10]  # Block by day
            elif entity_type == "contact":
                name = entity.get("name", "")
                if name:
                    key = self.fuzzy_matcher.normalize_string(name).split()[0]
            elif entity_type == "account":
                acc_type = entity.get("type", "unknown")
                key = str(acc_type)

            if key not in blocks:
                blocks[key] = []
            blocks[key].append(entity)
        return blocks

    def _compare_entities(
        self, entity1: Dict[str, Any], entity2: Dict[str, Any], entity_type: str
    ) -> Optional[DuplicateMatch]:
        """Compares two entities and returns a match object if they are potential duplicates."""
        field_scores: Dict[str, float] = {}
        # Comparison logic is simplified for this example
        if entity_type == "transaction":
            amount1 = Decimal(str(entity1.get("amount", 0)))
            amount2 = Decimal(str(entity2.get("amount", 0)))
            field_scores["amount"] = 1.0 if amount1 == amount2 else 0.0
            field_scores["date"] = (
                1.0 if entity1.get("date") == entity2.get("date") else 0.0
            )
            field_scores["description"] = self.fuzzy_matcher.jaro_winkler_similarity(
                entity1.get("description", ""), entity2.get("description", "")
            )

        elif entity_type == "contact":
            field_scores["email"] = (
                1.0 if entity1.get("email") == entity2.get("email") else 0.0
            )
            field_scores["name"] = self.fuzzy_matcher.jaro_winkler_similarity(
                entity1.get("name", ""), entity2.get("name", "")
            )

        elif entity_type == "account":
            field_scores["code"] = (
                1.0 if entity1.get("code") == entity2.get("code") else 0.0
            )
            field_scores["name"] = self.fuzzy_matcher.jaro_winkler_similarity(
                entity1.get("name", ""), entity2.get("name", "")
            )

        confidence, level = self.confidence_scorer.calculate_confidence(
            entity_type, field_scores
        )
        if level == ConfidenceLevel.NONE:
            return None

        matching_fields = [
            field for field, score in field_scores.items() if score > 0.8
        ]
        return DuplicateMatch(
            entity1_id=str(entity1.get("id")),
            entity2_id=str(entity2.get("id")),
            entity_type=entity_type,
            confidence_score=confidence,
            confidence_level=level,
            matching_fields=matching_fields,
            field_scores=field_scores,
        )


class MergeEngine:
    """Engine for merging duplicate entities with conflict resolution."""

    @handle_errors(operation="merge_duplicates")
    @log_performance()
    def merge_duplicates(
        self,
        base_entities: List[Dict[str, Any]],
        duplicate_matches: List[DuplicateMatch],
    ) -> List[Dict[str, Any]]:
        """Merges high-confidence duplicates within a list of entities."""
        merged_ids = set()
        final_entities = []
        entity_map = {str(e.get("id")): e for e in base_entities}

        high_confidence_matches = [
            m for m in duplicate_matches if m.confidence_level == ConfidenceLevel.HIGH
        ]

        logger.info(
            "Starting merge process.",
            total_entities=len(base_entities),
            high_confidence_matches=len(high_confidence_matches),
        )

        for match in high_confidence_matches:
            if (
                match.entity1_id in merged_ids
                or match.entity2_id in merged_ids
            ):
                continue

            entity1 = entity_map.get(match.entity1_id)
            entity2 = entity_map.get(match.entity2_id)

            if not entity1 or not entity2:
                continue

            merged_entity = self._merge_pair(entity1, entity2)
            final_entities.append(merged_entity)
            merged_ids.add(match.entity1_id)
            merged_ids.add(match.entity2_id)
            logger.debug(
                "Merged pair.", entity1_id=match.entity1_id, entity2_id=match.entity2_id
            )

        # Add non-merged entities
        for entity in base_entities:
            if str(entity.get("id")) not in merged_ids:
                final_entities.append(entity)

        logger.info(
            "Merge process completed.",
            initial_count=len(base_entities),
            final_count=len(final_entities),
            merged_count=len(merged_ids),
        )
        return final_entities

    def _merge_pair(
        self, entity1: Dict[str, Any], entity2: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Merges two entities, prioritizing the most complete record."""
        # Determine primary and secondary based on completeness
        primary, secondary = (
            (entity1, entity2)
            if len(entity1) >= len(entity2)
            else (entity2, entity1)
        )
        merged = primary.copy()

        for key, value in secondary.items():
            if key not in merged or not merged[key]:
                merged[key] = value

        merged["merged_from"] = [
            str(entity1.get("id")),
            str(entity2.get("id")),
        ]
        merged["id"] = self._generate_merged_id(merged["merged_from"])
        return merged

    def _generate_merged_id(self, source_ids: List[str]) -> str:
        """Generates a consistent ID for a merged entity."""
        id_string = "".join(sorted(source_ids))
        return "merged_" + hashlib.md5(id_string.encode()).hexdigest()
