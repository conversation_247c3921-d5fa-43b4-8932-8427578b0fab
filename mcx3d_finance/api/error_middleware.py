"""
Centralized error handling middleware for consistent API error responses.

This middleware ensures all API errors are returned in a standardized format
with proper logging, correlation IDs, and user-friendly messages.
"""
import traceback
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from fastapi import Request, Response
from fastapi.exceptions import HTT<PERSON>Exception, RequestValidationError
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from mcx3d_finance.api.schemas import ErrorResponse
from mcx3d_finance.exceptions.base import MCX3DException
from mcx3d_finance.exceptions.auth import (
    InvalidCredentialsError,
    AccountLockedError,
    MFARequiredError,
    MCX3DAuthenticationError,
)
from mcx3d_finance.exceptions.integration import (
    XeroIntegrationError,
)
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.monitoring.structured_logger import set_correlation_id
from mcx3d_finance.core.config import detect_environment, settings

logger = LoggerFactory.get_logger(__name__, domain="api")


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Centralized error handling middleware that catches all exceptions
    and returns standardized error responses with enhanced context.
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """Process the request and handle any exceptions."""
        # Generate correlation ID for this request
        correlation_id = request.headers.get("X-Correlation-ID", str(uuid.uuid4()))
        request.state.correlation_id = correlation_id
        
        # Set correlation ID in structured logger context
        set_correlation_id(correlation_id)
        
        # Set error context for this request
        from mcx3d_finance.exceptions.handlers import error_context
        error_context.set_correlation_id(correlation_id)
        
        # Extract user context if available
        if hasattr(request.state, "user"):
            error_context.user_id = getattr(request.state.user, "id", None)
            error_context.organization_id = getattr(
                request.state.user, "organization_id", None
            )

        try:
            response = await call_next(request)
            
            # Add correlation ID to response headers
            response.headers["X-Correlation-ID"] = correlation_id
            response.headers["X-Request-ID"] = correlation_id
            
            return response
        except Exception as exc:
            return await self._handle_exception(request, exc, correlation_id)

    async def _handle_exception(
        self, request: Request, exc: Exception, correlation_id: str
    ) -> JSONResponse:
        """Handle different types of exceptions and return appropriate responses."""
        
        # Create error response and determine status code
        error_response, status_code = self._create_error_response(exc, correlation_id, request)
        
        # Extract user context if available
        user_id = None
        organization_id = None
        if hasattr(request.state, "user"):
            user_id = getattr(request.state.user, "id", None)
            organization_id = getattr(request.state.user, "organization_id", None)
        
        # Log based on severity using structured logging
        if status_code >= 500:
            logger.error(
                "Server error occurred",
                correlation_id=correlation_id,
                method=request.method,
                url=str(request.url),
                path=request.url.path,
                user_agent=request.headers.get("user-agent"),
                client_host=request.client.host if request.client else None,
                status_code=status_code,
                error_type=type(exc).__name__,
                error_message=str(exc),
                user_id=user_id,
                organization_id=organization_id,
                error_code=error_response.error_code
            )
        elif status_code >= 400:
            logger.warning(
                "Client error occurred",
                correlation_id=correlation_id,
                method=request.method,
                url=str(request.url),
                path=request.url.path,
                status_code=status_code,
                error_type=type(exc).__name__,
                error_message=str(exc),
                user_id=user_id,
                organization_id=organization_id,
                error_code=error_response.error_code
            )
        else:
            logger.info(
                "Request error occurred",
                correlation_id=correlation_id,
                method=request.method,
                path=request.url.path,
                status_code=status_code,
                error_type=type(exc).__name__,
                error_message=str(exc)
            )
        
        return JSONResponse(
            status_code=status_code,
            content=error_response.dict(),
            headers={
                "X-Correlation-ID": correlation_id,
                "X-Request-ID": correlation_id,
                "X-Error-Code": error_response.error_code or "UNKNOWN",
            }
        )

    def _create_error_response(
        self, exc: Exception, correlation_id: str, request: Request
    ) -> Tuple[ErrorResponse, int]:
        """Create standardized error response based on exception type."""
        
        timestamp = datetime.utcnow()
        environment = detect_environment()
        
        # Handle MCX3D base exceptions with full context
        if isinstance(exc, MCX3DException):
            error_detail = exc.get_user_message() if hasattr(exc, 'get_user_message') else str(exc)
            
            # Add debug information in non-production environments
            debug_info = None
            if environment != "production":
                debug_info = {
                    "technical_message": getattr(exc, 'message', str(exc)),
                    "context": getattr(exc, 'context', {}),
                    "traceback": getattr(exc, 'traceback_info', traceback.format_exc()),
                }
            
            return (
                ErrorResponse(
                    error=self._get_error_category(exc),
                    detail=error_detail,
                    error_code=getattr(exc, 'error_code', 'MCX3D_ERROR'),
                    timestamp=timestamp,
                    request_id=correlation_id,
                    debug_info=debug_info,
                    validation_errors=None,
                    help_url=None,
                    support_contact=None,
                ),
                self._get_status_code_for_exception(exc),
            )

        # Handle HTTPException (FastAPI's standard HTTP errors)
        if isinstance(exc, HTTPException):
            return (
                ErrorResponse(
                    error=self._get_error_type(exc.status_code),
                    detail=exc.detail,
                    error_code=f"HTTP_{exc.status_code}",
                    timestamp=timestamp,
                    request_id=correlation_id,
                    validation_errors=None,
                    debug_info=None,
                    help_url=None,
                    support_contact=None,
                ),
                exc.status_code,
            )

        # Handle Pydantic validation errors
        if isinstance(exc, RequestValidationError):
            error_detail = self._format_validation_error(exc)
            validation_errors = self._extract_validation_errors(exc)
            
            return (
                ErrorResponse(
                    error="Validation Error",
                    detail=error_detail,
                    error_code="VALIDATION_ERROR",
                    timestamp=timestamp,
                    request_id=correlation_id,
                    validation_errors=validation_errors,
                    debug_info=None,
                    help_url=None,
                    support_contact=None,
                ),
                422,
            )

        # Handle any other unexpected errors - convert to MCX3DException
        from mcx3d_finance.exceptions.handlers import convert_exception
        
        mcx3d_exc = convert_exception(
            exc,
            operation=f"{request.method} {request.url.path}",
            correlation_id=correlation_id,
        )
        
        # In production, hide internal error details
        if environment == "production":
            error_detail = "An unexpected error occurred. Please try again later."
            debug_info = None
        else:
            error_detail = str(exc)
            debug_info = {
                "exception_type": type(exc).__name__,
                "traceback": traceback.format_exc(),
            }
        
        return (
            ErrorResponse(
                error="Internal Server Error",
                detail=error_detail,
                error_code=getattr(mcx3d_exc, 'error_code', 'INTERNAL_ERROR'),
                timestamp=timestamp,
                request_id=correlation_id,
                debug_info=debug_info,
                validation_errors=None,
                help_url=None,
                support_contact=None,
            ),
            HTTP_500_INTERNAL_SERVER_ERROR,
        )

    def _get_error_category(self, exc: MCX3DException) -> str:
        """Get error category from MCX3D exception type."""
        exception_type = type(exc).__name__
        
        category_map = {
            # Authentication errors
            "MCX3DAuthenticationError": "Authentication Error",
            "InvalidCredentialsError": "Invalid Credentials",
            "AccountLockedError": "Account Locked",
            "MFARequiredError": "MFA Required",
            "TokenExpiredError": "Token Expired",
            "SessionExpiredError": "Session Expired",
            
            # Financial errors
            "FinancialCalculationError": "Calculation Error",
            "ValuationError": "Valuation Error",
            "ProjectionError": "Projection Error",
            
            # Integration errors
            "XeroIntegrationError": "Integration Error",
            "APIConnectionError": "Connection Error",
            "RateLimitError": "Rate Limit Exceeded",
            "ServiceUnavailableError": "Service Unavailable",
            
            # Validation errors
            "ValidationError": "Validation Error",
            "DataIntegrityError": "Data Integrity Error",
            "BusinessRuleValidationError": "Business Rule Violation",
            
            # Reporting errors
            "ReportGenerationError": "Report Generation Error",
            "ReportDataValidationError": "Report Data Error",
            
            # Default
            "MCX3DException": "Application Error",
        }
        
        return category_map.get(exception_type, "Error")

    def _get_status_code_for_exception(self, exc: MCX3DException) -> int:
        """Map MCX3D exceptions to HTTP status codes."""
        exception_type = type(exc).__name__
        
        status_map = {
            # 400 Bad Request
            "ValidationError": 400,
            "DataIntegrityError": 400,
            "BusinessRuleValidationError": 400,
            "ReportDataValidationError": 400,
            
            # 401 Unauthorized
            "MCX3DAuthenticationError": 401,
            "InvalidCredentialsError": 401,
            "TokenExpiredError": 401,
            "SessionExpiredError": 401,
            
            # 403 Forbidden
            "MFARequiredError": 403,
            
            # 404 Not Found
            "SessionNotFoundError": 404,
            
            # 409 Conflict
            "DataIntegrityError": 409,
            
            # 422 Unprocessable Entity
            "FinancialCalculationError": 422,
            "ValuationError": 422,
            "ProjectionError": 422,
            
            # 423 Locked
            "AccountLockedError": 423,
            
            # 429 Too Many Requests
            "RateLimitError": 429,
            
            # 500 Internal Server Error
            "MCX3DSystemError": 500,
            "ReportGenerationError": 500,
            
            # 502 Bad Gateway
            "XeroIntegrationError": 502,
            "APIConnectionError": 502,
            
            # 503 Service Unavailable
            "ServiceUnavailableError": 503,
            
            # 504 Gateway Timeout
            "MCX3DTimeoutError": 504,
        }
        
        return status_map.get(exception_type, 500)

    def _get_error_type(self, status_code: int) -> str:
        """Get human-readable error type from HTTP status code."""
        error_types = {
            400: "Bad Request",
            401: "Unauthorized",
            403: "Forbidden",
            404: "Not Found",
            405: "Method Not Allowed",
            409: "Conflict",
            422: "Validation Error",
            423: "Locked",
            429: "Too Many Requests",
            500: "Internal Server Error",
            502: "Bad Gateway",
            503: "Service Unavailable",
            504: "Gateway Timeout",
        }
        return error_types.get(status_code, "Error")

    def _format_validation_error(self, exc: RequestValidationError) -> str:
        """Format Pydantic validation errors into user-friendly messages."""
        errors = []
        for error in exc.errors():
            field = " -> ".join(str(loc) for loc in error["loc"])
            message = error["msg"]
            errors.append(f"{field}: {message}")
        
        if len(errors) == 1:
            return f"Validation failed: {errors[0]}"
        else:
            return f"Validation failed for {len(errors)} fields: {'; '.join(errors)}"
    
    def _extract_validation_errors(
        self, exc: RequestValidationError
    ) -> List[Dict[str, Any]]:
        """Extract structured validation errors for API response."""
        validation_errors = []
        
        for error in exc.errors():
            validation_errors.append({
                "field": " -> ".join(str(loc) for loc in error["loc"]),
                "message": error["msg"],
                "type": error["type"],
                "context": error.get("ctx", {}),
            })
        
        return validation_errors


def get_correlation_id(request: Request) -> Optional[str]:
    """Get the correlation ID for the current request."""
    return getattr(request.state, 'correlation_id', None)