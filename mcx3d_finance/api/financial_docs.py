from datetime import date, datetime
from typing import Any, Dict

from celery.result import AsyncR<PERSON>ult
from fastapi import (
    APIRouter,
    Depends,
    Query,
    Request,
    status,
)
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from mcx3d_finance.api.auth_middleware import get_current_user
from mcx3d_finance.api.schemas import (
    DocumentGenerationStatus,
    ExecutiveSummaryResponse,
    FinancialKPIResponse,
    FinancialPackageRequest,
    FinancialPackageResponse,
    FinancialStatementResponse,
    RecurringGenerationRequest,
    RecurringGenerationResponse,
    ReportFormat,
    StatementType,
)
from mcx3d_finance.reporting.executive_summary import ExecutiveSummaryGenerator
from mcx3d_finance.core.financials.financial_statements import (
    FinancialStatementGenerator,
)
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator as FinancialKPICalculator
from mcx3d_finance.db.models import User
from mcx3d_finance.db.session import get_db
from mcx3d_finance.exceptions.handlers import (
    error_boundary,
    handle_errors,
    persistence_error,
)
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.tasks.report_tasks import (
    generate_financial_package_task,
    get_report_path,
    schedule_recurring_report,
)
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_performance

logger = LoggerFactory.get_logger(__name__, domain="api")
router = APIRouter(prefix="/api/financial-docs", tags=["financial-documentation"])


@router.post(
    "/generate-package",
    response_model=FinancialPackageResponse,
    status_code=status.HTTP_202_ACCEPTED,
)
@handle_errors(operation="generate_financial_package")
async def generate_financial_package(
    request: Request,
    package_request: FinancialPackageRequest,
    current_user: User = Depends(get_current_user),
) -> FinancialPackageResponse:
    """
    Triggers an asynchronous task to generate a financial documentation package.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "generate_financial_package",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=package_request.organization_id,
    ):
        logger.info(
            "Financial package generation requested",
            correlation_id=correlation_id,
            user_id=current_user.id,
            organization_id=package_request.organization_id,
            formats=[f.value for f in package_request.formats],
        )

        task = generate_financial_package_task.delay(
            organization_id=package_request.organization_id,
            user_id=current_user.id,
            report_config=package_request.dict(),
            correlation_id=correlation_id,
        )

        logger.info(
            "Financial package generation task queued",
            correlation_id=correlation_id,
            task_id=task.id,
        )

        return FinancialPackageResponse(
            task_id=task.id,
            status="queued",
            message="Financial documentation generation has been queued.",
            estimated_completion_time=300,
        )


@router.get("/generation-status/{task_id}", response_model=DocumentGenerationStatus)
@handle_errors(operation="get_generation_status")
async def get_generation_status(
    request: Request,
    task_id: str,
    current_user: User = Depends(get_current_user),
) -> DocumentGenerationStatus:
    """
    Retrieves the status of a financial document generation task.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_generation_status",
        correlation_id=correlation_id,
        user_id=current_user.id,
        task_id=task_id,
    ):
        logger.info(
            "Checking generation status for task",
            correlation_id=correlation_id,
            task_id=task_id,
        )

        task_result = AsyncResult(task_id)
        task_status = task_result.state
        result = task_result.result if task_status == "SUCCESS" else None

        if task_status == "FAILURE":
            logger.error(
                "Task failed during execution",
                correlation_id=correlation_id,
                task_id=task_id,
                error_info=str(task_result.info),
            )

        logger.info(
            "Generation status retrieved",
            correlation_id=correlation_id,
            task_id=task_id,
            status=task_status,
        )

        return DocumentGenerationStatus(
            task_id=task_id,
            status=task_status,
            result=result,
            timestamp=datetime.utcnow(),
        )


@router.get("/download-package/{task_id}")
@handle_errors(operation="download_financial_package")
async def download_financial_package(
    request: Request,
    task_id: str,
    format: ReportFormat,
    current_user: User = Depends(get_current_user),
) -> FileResponse:
    """
    Downloads the generated financial package in the specified format.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "download_financial_package",
        correlation_id=correlation_id,
        user_id=current_user.id,
        task_id=task_id,
        format=format.value,
    ):
        logger.info(
            "Download requested for financial package",
            correlation_id=correlation_id,
            task_id=task_id,
            format=format.value,
        )

        file_path = get_report_path(task_id, format)

        if not file_path or not file_path.exists():
            raise persistence_error(
                message="Generated document not found or generation is not complete.",
                entity="FinancialPackage",
                identifier=task_id,
            )

        logger.info(
            "Financial package found, initiating download",
            correlation_id=correlation_id,
            file_path=str(file_path),
        )

        media_type = (
            "application/pdf"
            if format == ReportFormat.PDF
            else "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        return FileResponse(
            path=file_path,
            filename=f"financial_package_{task_id}.{format.value}",
            media_type=media_type,
        )


@router.get(
    "/{organization_id}/executive-summary",
    response_model=ExecutiveSummaryResponse,
)
@handle_errors(operation="get_executive_summary")
@log_performance()
async def get_executive_summary(
    request: Request,
    organization_id: int,
    report_date: date = Depends(Query(default_factory=date.today)),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> ExecutiveSummaryResponse:
    """
    Generates and retrieves an executive summary for a given organization.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_executive_summary",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request for executive summary received",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )

        generator = ExecutiveSummaryGenerator(db)
        summary = await generator.generate(organization_id, report_date)

        logger.info(
            "Executive summary generated successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )
        return summary


@router.get(
    "/{organization_id}/financial-statement",
    response_model=FinancialStatementResponse,
)
@handle_errors(operation="get_financial_statement")
@log_performance()
async def get_financial_statement(
    request: Request,
    organization_id: int,
    statement_type: StatementType,
    end_date: date = Depends(Query(default_factory=date.today)),
    periods: int = Query(12, ge=1, le=36),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> FinancialStatementResponse:
    """
    Generates and retrieves a specific financial statement.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_financial_statement",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
        statement_type=statement_type.value,
    ):
        logger.info(
            "Request for financial statement received",
            correlation_id=correlation_id,
            organization_id=organization_id,
            statement_type=statement_type.value,
        )

        generator = FinancialStatementGenerator(db)
        statement = await generator.generate(
            organization_id=organization_id,
            statement_type=statement_type,
            end_date=end_date,
            periods=periods,
        )

        logger.info(
            "Financial statement generated successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )
        return statement


@router.get("/{organization_id}/financial-kpis", response_model=FinancialKPIResponse)
@handle_errors(operation="get_financial_kpis")
@log_performance()
async def get_financial_kpis(
    request: Request,
    organization_id: int,
    end_date: date = Depends(Query(default_factory=date.today)),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> FinancialKPIResponse:
    """
    Calculates and retrieves key financial KPIs.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_financial_kpis",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request for financial KPIs received",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )

        calculator = FinancialKPICalculator(db)
        kpis = await calculator.calculate(organization_id, end_date)

        logger.info(
            "Financial KPIs calculated successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )
        return kpis


@router.post("/schedule-recurring", response_model=RecurringGenerationResponse)
@handle_errors(operation="schedule_recurring_generation")
async def schedule_recurring_generation(
    request: Request,
    schedule_request: RecurringGenerationRequest,
    current_user: User = Depends(get_current_user),
) -> RecurringGenerationResponse:
    """
    Schedules recurring generation of a financial package.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "schedule_recurring_generation",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=schedule_request.organization_id,
    ):
        logger.info(
            "Request to schedule recurring report received",
            correlation_id=correlation_id,
            organization_id=schedule_request.organization_id,
            cron_schedule=schedule_request.cron_schedule,
        )

        schedule_id = await schedule_recurring_report(
            schedule_request, current_user.id, correlation_id
        )

        logger.info(
            "Recurring report scheduled successfully",
            correlation_id=correlation_id,
            schedule_id=schedule_id,
        )

        return RecurringGenerationResponse(
            schedule_id=schedule_id,
            organization_id=schedule_request.organization_id,
            status="active",
            next_run_time=datetime.utcnow(),
        )
