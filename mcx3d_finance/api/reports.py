"""
API endpoints for generating financial reports and valuations.

This module provides secure, reliable, and performant endpoints for generating
key financial statements and business valuations, leveraging structured logging,
centralized error handling, and performance monitoring.
"""

# Standard library imports
from datetime import date

# Third-party imports
from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.orm import Session

# Application imports
from mcx3d_finance.api.auth_middleware import get_current_user
from mcx3d_finance.api.schemas import (
    BalanceSheetResponse,
    CashFlowResponse,
    DCFValuationRequest,
    DCFValuationResponse,
    IncomeStatementResponse,
    MultiplesValuationRequest,
    MultiplesValuationResponse,
)
from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.core.financials.cash_flow import CashFlowStatementGenerator
from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator
from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.multiples import MultiplesValuation
from mcx3d_finance.db.models import User
from mcx3d_finance.db.session import get_db
from mcx3d_finance.exceptions.handlers import error_boundary, handle_errors
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_performance

# --- Logger and Router Setup ---
logger = LoggerFactory.get_logger(__name__, domain="api")
router = APIRouter(prefix="/api/reports", tags=["Financial Reports"])


# --- Endpoints ---


@router.get(
    "/{organization_id}/income-statement",
    response_model=IncomeStatementResponse,
    summary="Generate Income Statement",
)
@handle_errors(operation="get_income_statement")
@log_performance()
async def get_income_statement(
    request: Request,
    organization_id: int,
    end_date: date = Depends(Query(default_factory=date.today)),
    periods: int = Query(12, description="Number of months to include.", ge=1, le=36),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> IncomeStatementResponse:
    """
    Generates an Income Statement (Profit & Loss) for a specified organization.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_income_statement",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request for Income Statement received",
            correlation_id=correlation_id,
            organization_id=organization_id,
            end_date=str(end_date),
            periods=periods,
        )

        generator = IncomeStatementGenerator(db)
        statement = await generator.generate(organization_id, end_date, periods)

        logger.info(
            "Income Statement generated successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )
        return statement


@router.get(
    "/{organization_id}/balance-sheet",
    response_model=BalanceSheetResponse,
    summary="Generate Balance Sheet",
)
@handle_errors(operation="get_balance_sheet")
@log_performance()
async def get_balance_sheet(
    request: Request,
    organization_id: int,
    as_of_date: date = Depends(Query(default_factory=date.today)),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> BalanceSheetResponse:
    """
    Generates a Balance Sheet for a specified organization as of a given date.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_balance_sheet",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request for Balance Sheet received",
            correlation_id=correlation_id,
            organization_id=organization_id,
            as_of_date=str(as_of_date),
        )

        generator = BalanceSheetGenerator(db)
        statement = await generator.generate(organization_id, as_of_date)

        logger.info(
            "Balance Sheet generated successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )
        return statement


@router.get(
    "/{organization_id}/cash-flow",
    response_model=CashFlowResponse,
    summary="Generate Cash Flow Statement",
)
@handle_errors(operation="get_cash_flow_statement")
@log_performance()
async def get_cash_flow_statement(
    request: Request,
    organization_id: int,
    end_date: date = Depends(Query(default_factory=date.today)),
    periods: int = Query(12, description="Number of months to include.", ge=1, le=36),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> CashFlowResponse:
    """
    Generates a Cash Flow Statement for a specified organization.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_cash_flow_statement",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request for Cash Flow Statement received",
            correlation_id=correlation_id,
            organization_id=organization_id,
            end_date=str(end_date),
            periods=periods,
        )

        generator = CashFlowStatementGenerator(db)
        statement = await generator.generate(organization_id, end_date, periods)

        logger.info(
            "Cash Flow Statement generated successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )
        return statement


@router.post(
    "/{organization_id}/valuation/dcf",
    response_model=DCFValuationResponse,
    summary="Perform DCF Valuation",
)
@handle_errors(operation="perform_dcf_valuation")
@log_performance()
async def perform_dcf_valuation(
    request: Request,
    organization_id: int,
    valuation_request: DCFValuationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> DCFValuationResponse:
    """
    Performs a Discounted Cash Flow (DCF) valuation for an organization.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "perform_dcf_valuation",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request for DCF valuation received",
            correlation_id=correlation_id,
            organization_id=organization_id,
            config=valuation_request.dict(),
        )

        dcf_valuation = DCFValuation(db, organization_id)
        result = await dcf_valuation.evaluate(valuation_request)

        logger.info(
            "DCF valuation completed successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
            valuation_result=result.valuation,
        )
        return result


@router.post(
    "/{organization_id}/valuation/multiples",
    response_model=MultiplesValuationResponse,
    summary="Perform Multiples Valuation",
)
@handle_errors(operation="perform_multiples_valuation")
@log_performance()
async def perform_multiples_valuation(
    request: Request,
    organization_id: int,
    valuation_request: MultiplesValuationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> MultiplesValuationResponse:
    """
    Performs a valuation based on comparable company analysis (multiples).
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "perform_multiples_valuation",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request for Multiples valuation received",
            correlation_id=correlation_id,
            organization_id=organization_id,
            config=valuation_request.dict(),
        )

        multiples_valuation = MultiplesValuation(db, organization_id)
        result = await multiples_valuation.evaluate(valuation_request)

        logger.info(
            "Multiples valuation completed successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
            valuation_range=f"{result.low_valuation} - {result.high_valuation}",
        )
        return result
