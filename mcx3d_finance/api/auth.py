"""
OAuth2 API endpoints for Xero authentication.
Handles the complete OAuth2 flow with proper security.
"""

# Standard library imports
import base64
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone

# FastAPI imports
from fastapi import APIRouter, Depends, HTTPException, Query, Request, Response
from fastapi.responses import RedirectResponse
from pydantic import BaseModel
from sqlalchemy.orm import Session

# Structured logging and error handling imports
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_security_event, log_performance
from mcx3d_finance.exceptions.handlers import handle_errors, error_boundary
from mcx3d_finance.monitoring.structured_logger import get_correlation_id

# Database and model imports
from mcx3d_finance.db.session import get_db
from mcx3d_finance.db.models import User, Organization, UserOrganization

# Authentication and security imports
from mcx3d_finance.auth.oauth_security import (
    require_auth, get_current_user, hash_password, verify_password,
    is_mfa_enabled, verify_mfa_token, create_mfa_challenge, verify_mfa_challenge,
    generate_mfa_secret, get_mfa_qr_uri, generate_backup_codes, verify_backup_code,
    clear_failed_logins, record_failed_login, check_account_lockout
)
from mcx3d_finance.auth.xero_oauth import XeroAuthManager
from mcx3d_finance.exceptions.auth import (
    InvalidCredentialsError, AccountLockedError, MFARequiredError,
    InvalidMFATokenError, SessionError, TokenExpiredError
)
from mcx3d_finance.exceptions.integration import XeroAuthError
from mcx3d_finance.utils.session_manager import session_manager
from mcx3d_finance.utils.rate_limiter import auth_limiter
from mcx3d_finance.utils.input_validator import input_validator
from mcx3d_finance.utils.audit_logger import audit_logger, AuditEventType
import jwt

logger = LoggerFactory.get_logger(__name__, domain="api")

router = APIRouter(prefix="/api/auth", tags=["authentication"])


class LoginRequest(BaseModel):
    """User login request."""

    email: str
    password: str
    mfa_token: Optional[str] = None


class LoginResponse(BaseModel):
    """User login response."""

    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    user: Dict[str, Any]
    mfa_required: bool = False
    mfa_challenge: Optional[str] = None


class RefreshTokenRequest(BaseModel):
    """Refresh token request."""

    refresh_token: str


class MFASetupResponse(BaseModel):
    """MFA setup response."""

    secret: str
    qr_uri: str
    backup_codes: List[str]


class MFAVerifyRequest(BaseModel):
    """MFA verification request."""

    challenge: str
    token: str


class PasswordChangeRequest(BaseModel):
    """Password change request."""

    current_password: str
    new_password: str


class AuthStatusResponse(BaseModel):
    """Authentication status response."""

    authenticated: bool
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    token_expires_at: Optional[datetime] = None


class UserRegistrationRequest(BaseModel):
    """User registration request."""

    email: str
    password: str
    full_name: str
    organization_name: Optional[str] = None
    role: Optional[str] = "user"  # user, admin, viewer


class UserRegistrationResponse(BaseModel):
    """User registration response."""

    user_id: int
    email: str
    full_name: str
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    role: Optional[str] = None
    message: str


@router.post("/register", response_model=UserRegistrationResponse)
@handle_errors(operation="user_registration")
@log_performance("user_registration")
@log_security_event("user_registration", severity="medium")
async def register_user(
    request: UserRegistrationRequest, 
    req: Request, 
    db: Session = Depends(get_db)
) -> UserRegistrationResponse:
    """
    User registration endpoint.
    
    Creates new user account with optional organization creation.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "user_registration",
        correlation_id=correlation_id,
        email=request.email,
        organization_name=request.organization_name
    ):
        # Get client IP
        ip_address = auth_limiter.get_client_ip(req)

        # Check rate limit for registration endpoint
        await auth_limiter.check_and_raise(ip_address, "auth_register", cost=1)

        # Validate input
        validated_data = input_validator.validate_and_sanitize(
            {
                "email": request.email,
                "password": request.password,
                "full_name": request.full_name,
                "organization_name": request.organization_name,
                "role": request.role,
            },
            {
                "email": {"type": "email", "required": True},
                "password": {"type": "string", "required": True, "min_length": 8},
                "full_name": {"type": "string", "required": True, "min_length": 2},
                "organization_name": {"type": "string", "required": False, "min_length": 2},
                "role": {"type": "string", "required": False, "allowed_values": ["user", "admin", "viewer"]},
            },
        )

        logger.info("Starting user registration",
                   email=request.email,
                   organization_name=request.organization_name,
                   correlation_id=correlation_id)

        # Check if user already exists
        existing_user = db.query(User).filter(User.email == validated_data["email"]).first()
        if existing_user:
            logger.warning("Registration failed - email already exists",
                         email=validated_data["email"],
                         correlation_id=correlation_id)
            await audit_logger.log_event(
                event_type=AuditEventType.USER_REGISTRATION_FAILURE,
                action="register_user",
                outcome="failure",
                ip_address=ip_address,
                details={"reason": "email_already_exists", "email": validated_data["email"]},
            )
            raise HTTPException(status_code=409, detail="User with this email already exists")

        # Hash password
        hashed_password = hash_password(validated_data["password"])

        # Create user
        new_user = User(
            email=validated_data["email"],
            hashed_password=hashed_password,
            full_name=validated_data["full_name"],
            is_active=True,
            is_superuser=False,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        db.add(new_user)
        db.flush()  # Get user ID

        organization_id = None
        organization_name = None
        role = validated_data.get("role", "user")

        # Create organization if provided
        if validated_data.get("organization_name"):
            organization = Organization(
                name=validated_data["organization_name"],
                base_currency="USD",
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )
            db.add(organization)
            db.flush()  # Get organization ID

            # Create user-organization association
            user_org = UserOrganization(
                user_id=new_user.id,
                organization_id=organization.id,
                role=role,
                created_at=datetime.now(timezone.utc),
            )
            db.add(user_org)

            organization_id = organization.id
            organization_name = organization.name

        db.commit()

        # Log successful registration
        await audit_logger.log_event(
            event_type=AuditEventType.USER_REGISTRATION,
            action="register_user",
            outcome="success",
            user_id=str(new_user.id),
            ip_address=ip_address,
            details={
                "email": new_user.email,
                "organization_created": bool(organization_id),
                "role": role,
            },
        )

        logger.info("User registered successfully",
                   user_id=str(new_user.id),
                   email=new_user.email,
                   organization_id=organization_id,
                   correlation_id=correlation_id,
                   method="password")

        return UserRegistrationResponse(
            user_id=new_user.id,
            email=new_user.email,
            full_name=new_user.full_name,
            organization_id=organization_id,
            organization_name=organization_name,
            role=role if organization_id else None,
            message="User registered successfully",
        )



@router.post("/login", response_model=LoginResponse)
@handle_errors(operation="user_login")
@log_performance("user_login")
@log_security_event("user_login", severity="high")
async def login(request: LoginRequest, req: Request, db: Session = Depends(get_db)) -> LoginResponse:
    """
    User login endpoint with MFA support and account lockout.

    Authenticates user and returns JWT token.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "user_login",
        correlation_id=correlation_id,
        email=request.email
    ):
        # Get client IP
        ip_address = auth_limiter.get_client_ip(req)

        # Check rate limit for login endpoint
        await auth_limiter.check_and_raise(ip_address, "auth_login", cost=1)

        # Validate input
        validated_data = input_validator.validate_and_sanitize(
            {"email": request.email, "password": request.password},
            {
                "email": {"type": "email", "required": True},
                "password": {"type": "string", "required": True, "min_length": 8},
            },
        )

        # Find user by email
        user = db.query(User).filter(User.email == validated_data["email"]).first()

        if not user or not user.is_active:
            # Still record failed attempt for IP-based tracking
            await auth_limiter.check_and_raise(ip_address, "auth_login", cost=1)

            await audit_logger.log_event(
                event_type=AuditEventType.LOGIN_FAILURE,
                action="login_attempt",
                outcome="failure",
                ip_address=ip_address,
                details={"reason": "user_not_found"},
            )

            raise InvalidCredentialsError("Invalid email or password")

        # Check account lockout
        is_locked, remaining_minutes = check_account_lockout(str(user.id))
        if is_locked:
            await audit_logger.log_event(
                event_type=AuditEventType.LOGIN_FAILURE,
                action="login_attempt",
                outcome="failure",
                user_id=str(user.id),
                ip_address=ip_address,
                details={
                    "reason": "account_locked",
                    "remaining_minutes": remaining_minutes,
                },
            )

            raise AccountLockedError(f"Account locked. Try again in {remaining_minutes} minutes.")

        # Verify password using bcrypt

        if not user.hashed_password:
            logger.error("Login attempt with no password hash",
                        user_email=user.email,
                        correlation_id=correlation_id,
                        security_event="login_no_password")
            raise InvalidCredentialsError("Invalid email or password")

        if not verify_password(validated_data["password"], user.hashed_password):
            # Record failed login
            record_failed_login(str(user.id), ip_address)

            await audit_logger.log_event(
                event_type=AuditEventType.LOGIN_FAILURE,
                action="login_attempt",
                outcome="failure",
                user_id=str(user.id),
                ip_address=ip_address,
                details={"reason": "invalid_password"},
            )

            logger.warning("Login failed - invalid password",
                          user_id=str(user.id),
                          email=user.email,
                          correlation_id=correlation_id,
                          method="password")
            raise InvalidCredentialsError("Invalid email or password")

        # Check if MFA is enabled
        if is_mfa_enabled(str(user.id)):
            # If MFA token provided, verify it
            if request.mfa_token:
                if not verify_mfa_token(str(user.id), request.mfa_token):
                    await audit_logger.log_event(
                        event_type=AuditEventType.MFA_CHALLENGE_FAILURE,
                        action="mfa_verify",
                        outcome="failure",
                        user_id=str(user.id),
                        ip_address=ip_address,
                    )

                    raise MFARequiredError("Invalid MFA token")

                # MFA successful
                await audit_logger.log_event(
                    event_type=AuditEventType.MFA_CHALLENGE_SUCCESS,
                    action="mfa_verify",
                    outcome="success",
                    user_id=str(user.id),
                    ip_address=ip_address,
                )
            else:
                # Create MFA challenge
                challenge = create_mfa_challenge(str(user.id))

                return LoginResponse(
                    access_token="",  # No token yet
                    mfa_required=True,
                    mfa_challenge=challenge,
                    user={"id": user.id, "email": user.email},
                )

        # Clear failed login attempts
        clear_failed_logins(str(user.id))

        # Get user's organizations
        user_orgs = (
            db.query(UserOrganization).filter(UserOrganization.user_id == user.id).all()
        )

        org_ids = [uo.organization_id for uo in user_orgs]

        # Create session with refresh token
        user_data = {"email": user.email, "organizations": org_ids}

        session_data = session_manager.create_session(
            user_id=str(user.id),
            user_data=user_data,
            device_info={
                "ip_address": ip_address,
                "user_agent": req.headers.get("User-Agent", "Unknown"),
            },
        )

        # Log successful login
        await audit_logger.log_event(
            event_type=AuditEventType.LOGIN_SUCCESS,
            action="login",
            outcome="success",
            user_id=str(user.id),
            session_id=session_data["session_id"],
            ip_address=ip_address,
        )

        logger.info("Login successful",
                   user_id=str(user.id),
                   email=user.email,
                   session_id=session_data["session_id"],
                   correlation_id=correlation_id,
                   method="password",
                   organization_count=len(org_ids))

        return LoginResponse(
            access_token=session_data["access_token"],
            refresh_token=session_data["refresh_token"],
            user={
                "id": user.id,
                "email": user.email,
                "full_name": user.full_name,
                "organizations": org_ids,
            },
        )



@router.post("/refresh", response_model=LoginResponse)
@handle_errors(operation="token_refresh")
@log_performance("token_refresh")
@log_security_event("token_refresh", severity="medium")
async def refresh_token(request: RefreshTokenRequest, req: Request) -> LoginResponse:
    """
    Refresh access token using refresh token.

    Implements token rotation for security.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "token_refresh",
        correlation_id=correlation_id
    ):
        # Get client IP
        ip_address = auth_limiter.get_client_ip(req)

        logger.info("Starting token refresh",
                   correlation_id=correlation_id,
                   ip_address=ip_address)

        # Refresh session
        new_tokens = session_manager.refresh_session(request.refresh_token)

        # Get user info from token
        payload = jwt.decode(
            new_tokens["access_token"],
            session_manager.secret_key,
            algorithms=[session_manager.algorithm],
        )

        await audit_logger.log_event(
            event_type=AuditEventType.SESSION_CREATED,
            action="token_refresh",
            outcome="success",
            user_id=payload["sub"],
            ip_address=ip_address,
        )

        logger.info("Token refresh successful",
                   user_id=payload["sub"],
                   correlation_id=correlation_id,
                   ip_address=ip_address)

        return LoginResponse(
            access_token=new_tokens["access_token"],
            refresh_token=new_tokens["refresh_token"],
            user={"id": payload["sub"], "email": payload.get("email", "")},
        )


@router.post("/mfa/setup", response_model=MFASetupResponse)
@require_auth
@handle_errors(operation="mfa_setup")
@log_performance("mfa_setup")
@log_security_event("mfa_setup", severity="high")
async def setup_mfa(current_user: Dict[str, Any] = Depends(get_current_user)) -> MFASetupResponse:
    """
    Setup MFA for user account.

    Returns QR code URI and backup codes.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "mfa_setup",
        correlation_id=correlation_id,
        user_id=current_user["user_id"]
    ):
        user_id = current_user["user_id"]
        email = current_user["email"]

        logger.info("Starting MFA setup",
                   user_id=user_id,
                   email=email,
                   correlation_id=correlation_id)

        # Generate MFA secret
        secret = generate_mfa_secret(user_id)

        # Get QR code URI
        qr_uri = get_mfa_qr_uri(user_id, email, secret)

        # Generate backup codes
        backup_codes = generate_backup_codes(user_id)

        await audit_logger.log_event(
            event_type=AuditEventType.MFA_ENABLED,
            action="mfa_setup",
            outcome="success",
            user_id=user_id,
        )

        logger.info("MFA setup completed successfully",
                   user_id=user_id,
                   correlation_id=correlation_id,
                   backup_codes_count=len(backup_codes))

        return MFASetupResponse(secret=secret, qr_uri=qr_uri, backup_codes=backup_codes)


@router.post("/mfa/verify", response_model=LoginResponse)
@handle_errors(operation="mfa_verify")
@log_performance("mfa_verify")
@log_security_event("mfa_verify", severity="high")
async def verify_mfa(request: MFAVerifyRequest, req: Request) -> LoginResponse:
    """
    Verify MFA challenge with token.

    Completes login after MFA verification.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "mfa_verify",
        correlation_id=correlation_id,
        challenge=request.challenge
    ):
        # Get client IP
        ip_address = auth_limiter.get_client_ip(req)

        logger.info("Starting MFA verification",
                   challenge=request.challenge,
                   correlation_id=correlation_id,
                   ip_address=ip_address)

        # Verify MFA challenge
        user_id = verify_mfa_challenge(request.challenge, request.token)

        if not user_id:
            # Try backup code
            challenge_key = f"mfa_challenge:{request.challenge}"
            user_id_from_challenge = session_manager.redis.get(challenge_key)

            if user_id_from_challenge and verify_backup_code(
                user_id_from_challenge, request.token
            ):
                user_id = user_id_from_challenge
                session_manager.redis.delete(challenge_key)

                await audit_logger.log_event(
                    event_type=AuditEventType.MFA_CHALLENGE_SUCCESS,
                    action="mfa_verify_backup",
                    outcome="success",
                    user_id=user_id,
                    ip_address=ip_address,
                )
            else:
                await audit_logger.log_event(
                    event_type=AuditEventType.MFA_CHALLENGE_FAILURE,
                    action="mfa_verify",
                    outcome="failure",
                    ip_address=ip_address,
                )

                raise HTTPException(
                    status_code=401, detail="Invalid MFA token or backup code"
                )
        else:
            await audit_logger.log_event(
                event_type=AuditEventType.MFA_CHALLENGE_SUCCESS,
                action="mfa_verify",
                outcome="success",
                user_id=user_id,
                ip_address=ip_address,
            )

        # Clear failed login attempts
        clear_failed_logins(user_id)

        # Create session (simplified - in production, get full user data)
        session_data = session_manager.create_session(
            user_id=user_id,
            user_data={"mfa_verified": True},
            device_info={
                "ip_address": ip_address,
                "user_agent": req.headers.get("User-Agent", "Unknown"),
            },
        )

        return LoginResponse(
            access_token=session_data["access_token"],
            refresh_token=session_data["refresh_token"],
            user={"id": user_id},
        )

        logger.info("MFA verification completed successfully",
                   user_id=user_id,
                   correlation_id=correlation_id,
                   ip_address=ip_address)


@router.post("/password/change")
@require_auth
@handle_errors(operation="password_change")
@log_performance("password_change")
@log_security_event("password_change", severity="high")
async def change_password(
    request: PasswordChangeRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Dict[str, Union[bool, str]]:
    """
    Change user password with policy enforcement.

    Requires current password verification.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "password_change",
        correlation_id=correlation_id,
        user_id=current_user["user_id"]
    ):
        user_id = current_user["user_id"]

        logger.info("Starting password change",
                   user_id=user_id,
                   correlation_id=correlation_id)

        # Get user
        user = db.query(User).get(int(user_id))
        if not user:
            logger.error("User not found for password change",
                        user_id=user_id,
                        correlation_id=correlation_id)
            raise HTTPException(status_code=404, detail="User not found")

        # Verify current password

        if not verify_password(request.current_password, user.hashed_password):
            await audit_logger.log_event(
                event_type=AuditEventType.PASSWORD_CHANGED,
                action="password_change",
                outcome="failure",
                user_id=user_id,
                details={"reason": "invalid_current_password"},
            )

            raise HTTPException(status_code=401, detail="Current password is incorrect")

        # Validate new password
        password_validator = input_validator.create_password_validator(
            min_length=12,
            require_uppercase=True,
            require_lowercase=True,
            require_numbers=True,
            require_special=True,
        )

        try:
            password_validator(request.new_password)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))

        # Check password not same as current
        if request.current_password == request.new_password:
            raise HTTPException(
                status_code=400, detail="New password must be different"
            )

        # Update password
        user.hashed_password = hash_password(request.new_password)
        db.commit()

        # Invalidate all sessions for security
        session_manager.invalidate_all_sessions(user_id)

        await audit_logger.log_event(
            event_type=AuditEventType.PASSWORD_CHANGED,
            action="password_change",
            outcome="success",
            user_id=user_id,
        )

        logger.info("Password changed successfully",
                   user_id=user_id,
                   correlation_id=correlation_id)

        return {
            "success": True,
            "message": "Password changed successfully. Please login again.",
        }


@router.get("/xero/authorize")
@require_auth
@handle_errors(operation="xero_authorize")
@log_performance("xero_authorize")
@log_security_event("xero_authorize", severity="medium")
async def xero_authorize(
    organization_id: Optional[int] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, str]:
    """
    Initiate Xero OAuth2 authorization flow.

    Requires authentication. Generates authorization URL with CSRF protection.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "xero_authorize",
        correlation_id=correlation_id,
        user_id=current_user["user_id"],
        organization_id=organization_id
    ):
        auth_manager = XeroAuthManager()

        # Generate state with user and org info for callback
        state_data = {
            "user_id": current_user["user_id"],
            "organization_id": organization_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        state = (
            base64.urlsafe_b64encode(json.dumps(state_data).encode())
            .decode()
            .rstrip("=")
        )

        # Generate authorization URL
        auth_url = auth_manager.generate_auth_url(state)

        if not auth_url:
            raise XeroAuthError("Failed to generate authorization URL")

        logger.info("Xero authorization initiated",
                   user_id=current_user["user_id"],
                   organization_id=organization_id,
                   correlation_id=correlation_id)

        return {
            "auth_url": auth_url,
            "message": "Redirect user to the authorization URL",
        }


@router.get("/xero/callback")
@handle_errors(operation="xero_callback")
@log_performance("xero_callback")
@log_security_event("xero_callback", severity="medium")
async def xero_callback(
    request: Request,
    code: str = Query(...),
    state: str = Query(...),
    db: Session = Depends(get_db),
) -> RedirectResponse:
    """
    Handle Xero OAuth2 callback.

    Exchanges authorization code for access token and stores it securely.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "xero_callback",
        correlation_id=correlation_id,
        code=code[:10] + "...",  # Log partial code for debugging
        state=state
    ):
        # Build full callback URL
        callback_url = f"{request.url.scheme}://{request.url.netloc}{request.url.path}?code={code}&state={state}"

        logger.info("Processing Xero callback",
                   correlation_id=correlation_id,
                   callback_url=callback_url[:100] + "...")

        # Handle callback using XeroAuthManager
        auth_manager = XeroAuthManager()
        result = auth_manager.handle_callback(callback_url)

        if not result["success"]:
            error_message = result.get("error", "Authentication failed")
            logger.error("Xero callback failed",
                        error_message=error_message,
                        correlation_id=correlation_id)
            return RedirectResponse(
                url=f"/api/auth/error?message={error_message.replace(' ', '+')}", status_code=302
            )

        # Try to decode state for user linking (optional, for backward compatibility)
        user_id = None
        try:
            state_data = json.loads(base64.urlsafe_b64decode(state + "==").decode())
            user_id = state_data.get("user_id")
        except Exception:
            # State is not base64 JSON, that's fine - continue without user linking
            pass

        # Link user to organization if user_id was provided in state
        if user_id and result.get("organization_id"):
            user_org = (
                db.query(UserOrganization)
                .filter(
                    UserOrganization.user_id == user_id,
                    UserOrganization.organization_id == result["organization_id"],
                )
                .first()
            )

            if not user_org:
                user_org = UserOrganization(
                    user_id=user_id,
                    organization_id=result["organization_id"],
                    role="admin",
                    created_at=datetime.now(timezone.utc),
                )
                db.add(user_org)
                db.commit()

        logger.info("Xero callback successful",
                   organization_id=result["organization_id"],
                   correlation_id=correlation_id,
                   user_linked=bool(user_id))

        # Redirect to success page
        return RedirectResponse(
            url=f"/api/auth/success?organization_id={result['organization_id']}", status_code=302
        )


@router.post("/xero/refresh/{organization_id}")
@require_auth
@handle_errors(operation="xero_refresh_token")
@log_performance("xero_refresh_token")
@log_security_event("xero_refresh_token", severity="medium")
async def xero_refresh_token(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Dict[str, Union[bool, str]]:
    """
    Manually refresh Xero OAuth2 token.

    Requires authentication and organization access.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "xero_refresh_token",
        correlation_id=correlation_id,
        user_id=current_user["user_id"],
        organization_id=organization_id
    ):
        # Verify user has access to organization
        if organization_id not in current_user.get("organizations", []):
            raise HTTPException(
                status_code=403, detail="You do not have access to this organization"
            )

        auth_manager = XeroAuthManager()
        token = auth_manager.get_valid_token(organization_id)

        if not token:
            raise HTTPException(
                status_code=404, detail="No token found for organization"
            )

        logger.info("Xero token refreshed",
                   user_id=current_user["user_id"],
                   organization_id=organization_id,
                   correlation_id=correlation_id)

        return {"success": True, "message": "Token refreshed successfully"}



@router.delete("/xero/revoke/{organization_id}")
@require_auth
@handle_errors(operation="xero_revoke_token")
@log_performance("xero_revoke_token")
@log_security_event("xero_revoke_token", severity="high")
async def xero_revoke_token(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Dict[str, Union[bool, str]]:
    """
    Revoke Xero OAuth2 token for organization.

    Requires authentication and organization admin access.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "xero_revoke_token",
        correlation_id=correlation_id,
        user_id=current_user["user_id"],
        organization_id=organization_id
    ):
        # Verify user has admin access to organization
        user_org = (
            db.query(UserOrganization)
            .filter(
                UserOrganization.user_id == current_user["user_id"],
                UserOrganization.organization_id == organization_id,
            )
            .first()
        )

        if not user_org or user_org.role != "admin":
            raise HTTPException(
                status_code=403, detail="Admin access required to revoke token"
            )

        auth_manager = XeroAuthManager()
        success = auth_manager.revoke_token(organization_id)

        if not success:
            raise HTTPException(
                status_code=404, detail="No token found for organization"
            )

        logger.info("Xero token revoked",
                   user_id=current_user["user_id"],
                   organization_id=organization_id,
                   correlation_id=correlation_id)

        return {"success": True, "message": "Token revoked successfully"}



@router.get("/xero/status/{organization_id}", response_model=AuthStatusResponse)
@require_auth
@handle_errors(operation="xero_auth_status")
@log_performance("xero_auth_status")
async def xero_auth_status(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> AuthStatusResponse:
    """
    Check Xero authentication status for organization.

    Requires authentication and organization access.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "xero_auth_status",
        correlation_id=correlation_id,
        user_id=current_user["user_id"],
        organization_id=organization_id
    ):
        # Verify user has access to organization
        if organization_id not in current_user.get("organizations", []):
            raise HTTPException(
                status_code=403, detail="You do not have access to this organization"
            )

        # Get organization
        org = db.query(Organization).get(organization_id)
        if not org:
            raise HTTPException(status_code=404, detail="Organization not found")

        # Check if authenticated
        auth_manager = XeroAuthManager()
        token = auth_manager.get_valid_token(organization_id)

        return AuthStatusResponse(
            authenticated=token is not None,
            organization_id=organization_id,
            organization_name=org.name,
            token_expires_at=org.token_expires_at,
        )



@router.post("/logout")
@require_auth
@handle_errors(operation="user_logout")
@log_performance("user_logout")
@log_security_event("user_logout", severity="medium")
async def logout(
    response: Response,
    req: Request,
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Union[bool, str]]:
    """
    User logout endpoint with session invalidation.

    Invalidates the current session and logs security event.
    """
    correlation_id = get_correlation_id()
    
    with error_boundary(
        "user_logout",
        correlation_id=correlation_id,
        user_id=current_user["user_id"]
    ):
        user_id = current_user["user_id"]
        ip_address = auth_limiter.get_client_ip(req)

        # Get session ID from token if available
        session_id = current_user.get("session_id")

        # Invalidate session if session ID available
        if session_id:
            session_manager.invalidate_session(user_id, session_id)

        # Log logout event
        await audit_logger.log_event(
            event_type=AuditEventType.LOGOUT,
            action="logout",
            outcome="success",
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
        )

        logger.info("User logout successful",
                   user_id=user_id,
                   session_id=session_id,
                   correlation_id=correlation_id,
                   ip_address=ip_address)

        return {"success": True, "message": "Logged out successfully"}


@router.get("/error")
async def oauth_error(message: str = Query("Unknown error")) -> Dict[str, Union[bool, str]]:
    """
    OAuth error page for handling callback errors.
    
    Displays user-friendly error messages for OAuth flow failures.
    """
    return {
        "error": True,
        "message": message.replace("+", " "),
        "details": "OAuth authorization failed. Please try again or contact support.",
        "action": "Please close this window and retry the authorization process."
    }


@router.get("/success")
async def oauth_success(organization_id: int = Query(...)) -> Dict[str, Union[bool, str, int]]:
    """
    OAuth success page for successful authorization.
    
    Confirms successful connection to Xero organization.
    """
    return {
        "success": True,
        "message": "Successfully connected to Xero!",
        "organization_id": organization_id,
        "details": "Your organization has been connected to Xero. You can now sync financial data.",
        "action": "You can close this window and continue with data synchronization."
    }
