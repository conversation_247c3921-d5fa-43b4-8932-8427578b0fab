"""
Authentication middleware for protecting API endpoints.
Implements JWT-based authentication with organization access control, MFA, and account lockout.
"""
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_performance
from mcx3d_finance.exceptions.handlers import handle_errors, error_boundary
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from typing import Optional, Dict, Any, Tuple, List, Callable
from datetime import datetime, timedelta, timezone
from functools import wraps
import pyotp
import redis
import secrets

from fastapi import HTTPException, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.orm import Session

from mcx3d_finance.core.config import get_security_config
from mcx3d_finance.db.session import get_db
from mcx3d_finance.db.models import User, UserOrganization
from mcx3d_finance.utils.audit_logger import audit_logger, AuditEventType, AuditSeverity

logger = LoggerFactory.get_logger(__name__, domain="api")

# Security configuration
security_config = get_security_config()
SECRET_KEY = security_config["secret_key"]  # No default - will raise error if not configured
ALGORITHM = security_config.get("algorithm", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = security_config.get("access_token_expire_minutes", 30)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Bearer token security
security = HTTPBearer()

# Redis connection for account lockout and MFA
redis_url = security_config.get("redis_url", "redis://localhost:6379/0")
redis_client = redis.from_url(redis_url, decode_responses=True)

# Account lockout settings
MAX_LOGIN_ATTEMPTS = security_config.get("max_login_attempts", 5)
LOCKOUT_DURATION = security_config.get("lockout_duration_minutes", 30)
MFA_CODE_VALIDITY = security_config.get("mfa_code_validity_minutes", 5)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against a hashed password.
    
    Args:
        plain_password: Plain text password to verify
        hashed_password: Hashed password to compare against
        
    Returns:
        True if password matches, False otherwise
    """
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error("Password verification error", error=str(e))
        return False


def hash_password(password: str) -> str:
    """
    Hash a plain password using bcrypt.
    
    Args:
        password: Plain text password to hash
        
    Returns:
        Hashed password string
    """
    return pwd_context.hash(password)


def check_account_lockout(user_id: str) -> Tuple[bool, Optional[int]]:
    """
    Check if account is locked out due to failed login attempts.
    
    Args:
        user_id: User identifier
        
    Returns:
        Tuple of (is_locked, remaining_minutes)
    """
    lockout_key = f"lockout:{user_id}"
    lockout_time = redis_client.get(lockout_key)
    
    if lockout_time:
        remaining = int(float(lockout_time) - datetime.now(timezone.utc).timestamp())
        if remaining > 0:
            return True, int(remaining / 60)
    
    return False, None


def record_failed_login(user_id: str, ip_address: Optional[str] = None) -> None:
    """
    Record failed login attempt and lock account if threshold exceeded.
    
    Args:
        user_id: User identifier
        ip_address: Client IP address
    """
    # Increment failure count
    fail_key = f"login_failures:{user_id}"
    failures = redis_client.incr(fail_key)
    redis_client.expire(fail_key, 3600)  # Reset after 1 hour
    
    # Check if should lock account
    if failures >= MAX_LOGIN_ATTEMPTS:
        lockout_until = datetime.now(timezone.utc) + timedelta(minutes=LOCKOUT_DURATION)
        lockout_key = f"lockout:{user_id}"
        redis_client.setex(
            lockout_key,
            LOCKOUT_DURATION * 60,
            lockout_until.timestamp()
        )
        
        # Log security event
        audit_logger.log_event(
            event_type=AuditEventType.ACCOUNT_LOCKED,
            action="account_locked",
            outcome="success",
            user_id=user_id,
            ip_address=ip_address,
            details={
                "reason": "Exceeded maximum login attempts",
                "failures": failures,
                "lockout_duration_minutes": LOCKOUT_DURATION
            },
            severity=AuditSeverity.WARNING
        )
        
        logger.warning("Account locked due to failed attempts", user_id=user_id, failures=failures)


def clear_failed_logins(user_id: str) -> None:
    """Clear failed login attempts after successful login."""
    fail_key = f"login_failures:{user_id}"
    redis_client.delete(fail_key)


def generate_mfa_secret(user_id: str) -> str:
    """
    Generate MFA secret for user.
    
    Args:
        user_id: User identifier
        
    Returns:
        Base32 encoded secret
    """
    secret = pyotp.random_base32()
    
    # Store in Redis (in production, store encrypted in database)
    mfa_key = f"mfa_secret:{user_id}"
    redis_client.setex(mfa_key, 86400 * 365, secret)  # 1 year expiry
    
    return secret


def get_mfa_qr_uri(user_id: str, email: str, secret: str) -> str:
    """
    Generate QR code URI for MFA setup.
    
    Args:
        user_id: User identifier
        email: User email
        secret: MFA secret
        
    Returns:
        URI for QR code generation
    """
    totp = pyotp.TOTP(secret)
    return totp.provisioning_uri(
        name=email,
        issuer_name="MCX3D Finance"
    )


def verify_mfa_token(user_id: str, token: str) -> bool:
    """
    Verify MFA token.
    
    Args:
        user_id: User identifier
        token: MFA token from user
        
    Returns:
        True if valid
    """
    # Get secret from Redis (in production, get from database)
    mfa_key = f"mfa_secret:{user_id}"
    secret = redis_client.get(mfa_key)
    
    if not secret:
        return False
    
    # Verify token
    totp = pyotp.TOTP(secret)
    return totp.verify(token, valid_window=1)  # Allow 30 second window


def generate_backup_codes(user_id: str, count: int = 10) -> List[str]:
    """
    Generate backup codes for MFA.
    
    Args:
        user_id: User identifier
        count: Number of codes to generate
        
    Returns:
        List of backup codes
    """
    codes = []
    for _ in range(count):
        code = f"{secrets.randbelow(1000000):06d}"
        codes.append(code)
    
    # Store hashed codes in Redis
    backup_key = f"mfa_backup:{user_id}"
    for code in codes:
        hashed_code = hash_password(code)
        redis_client.sadd(backup_key, hashed_code)
    
    redis_client.expire(backup_key, 86400 * 365)  # 1 year expiry
    
    return codes


def verify_backup_code(user_id: str, code: str) -> bool:
    """
    Verify and consume backup code.
    
    Args:
        user_id: User identifier
        code: Backup code
        
    Returns:
        True if valid
    """
    backup_key = f"mfa_backup:{user_id}"
    stored_codes = redis_client.smembers(backup_key)
    
    # Check each stored code
    for stored_code in stored_codes:
        if verify_password(code, stored_code):
            # Remove used code
            redis_client.srem(backup_key, stored_code)
            return True
    
    return False


def is_mfa_enabled(user_id: str) -> bool:
    """Check if MFA is enabled for user."""
    mfa_key = f"mfa_secret:{user_id}"
    return bool(redis_client.exists(mfa_key))


def create_mfa_challenge(user_id: str) -> str:
    """
    Create MFA challenge token.
    
    Args:
        user_id: User identifier
        
    Returns:
        Challenge token
    """
    challenge = secrets.token_urlsafe(32)
    challenge_key = f"mfa_challenge:{challenge}"
    
    redis_client.setex(
        challenge_key,
        MFA_CODE_VALIDITY * 60,
        user_id
    )
    
    return challenge


def verify_mfa_challenge(challenge: str, token: str) -> Optional[str]:
    """
    Verify MFA challenge with token.
    
    Args:
        challenge: Challenge token
        token: MFA token
        
    Returns:
        User ID if valid, None otherwise
    """
    challenge_key = f"mfa_challenge:{challenge}"
    user_id = redis_client.get(challenge_key)
    
    if not user_id:
        return None
    
    # Verify MFA token
    if verify_mfa_token(user_id, token):
        # Delete challenge
        redis_client.delete(challenge_key)
        return user_id
    
    return None


class AuthenticationError(HTTPException):
    """Custom authentication error."""
    def __init__(self, detail: str):
        super().__init__(status_code=401, detail=detail)


class AuthorizationError(HTTPException):
    """Custom authorization error."""
    def __init__(self, detail: str):
        super().__init__(status_code=403, detail=detail)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token.
    
    Args:
        data: Token payload data
        expires_delta: Token expiration time
        
    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """
    Verify and decode JWT token.
    
    Args:
        token: JWT token string
        
    Returns:
        Decoded token payload
        
    Raises:
        AuthenticationError: If token is invalid
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError as e:
        logger.error("JWT verification failed", error=str(e))
        raise AuthenticationError("Invalid authentication token")


@handle_errors(operation="get_current_user")
@log_performance("get_current_user")
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get current authenticated user from JWT token.
    
    Args:
        credentials: Bearer token from request
        db: Database session
        
    Returns:
        User information dict
        
    Raises:
        AuthenticationError: If authentication fails
    """
    correlation_id = get_correlation_id()
    token = credentials.credentials

    with error_boundary("get_current_user", correlation_id=correlation_id):
        payload = verify_token(token)
        user_id = payload.get("sub")

        if user_id is None:
            logger.warning("Invalid token payload: 'sub' missing", correlation_id=correlation_id)
            raise AuthenticationError("Invalid token payload")

        logger.info("Fetching user from DB", user_id=user_id, correlation_id=correlation_id)
        user = db.query(User).filter(User.id == int(user_id)).first()

        if not user:
            logger.warning("User not found in DB", user_id=user_id, correlation_id=correlation_id)
            raise AuthenticationError("User not found")

        if not user.is_active:
            logger.warning("User is inactive", user_id=user_id, correlation_id=correlation_id)
            raise AuthenticationError("User is inactive")

        # Add organization info to user dict
        user_org = db.query(UserOrganization).filter(UserOrganization.user_id == user.id).first()

        logger.info("User authenticated successfully", user_id=user.id, correlation_id=correlation_id)
        return {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "is_superuser": user.is_superuser,
            "organization_id": user_org.organization_id if user_org else None,
            "role": user_org.role if user_org else None,
        }


async def verify_organization_access(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> bool:
    """
    Verify user has access to the specified organization.
    
    Args:
        organization_id: Organization to check access for
        current_user: Current authenticated user
        
    Returns:
        True if user has access
        
    Raises:
        HTTPException: 403 Forbidden if user lacks access
    """
    user_organizations = current_user.get("organizations", [])
    
    if organization_id not in user_organizations:
        logger.warning(
            f"User {current_user['user_id']} attempted to access "
            f"organization {organization_id} without permission"
        )
        raise HTTPException(
            status_code=403,
            detail=f"You do not have access to organization {organization_id}"
        )
    
    return True


def require_auth(func) -> Callable:
    """
    Decorator to require authentication for an endpoint.
    
    Usage:
        @router.get("/protected")
        @require_auth
        async def protected_endpoint(current_user: Dict = Depends(get_current_user)):
            return {"user": current_user}
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)
    return wrapper


def require_organization_access(func) -> Callable:
    """
    Decorator to require organization access for an endpoint.
    
    The endpoint must have an 'organization_id' parameter.
    
    Usage:
        @router.get("/reports/{organization_id}")
        @require_organization_access
        async def get_report(
            organization_id: int,
            has_access: bool = Depends(verify_organization_access)
        ):
            return {"organization_id": organization_id}
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)
    return wrapper


# Note: Redis-based rate limiting is implemented in utils/rate_limiter.py
# Import and use rate_limiter, api_limiter, or auth_limiter from there instead


# Middleware for adding security headers
async def add_security_headers(request: Request, call_next) -> Any:
    """Add security headers to all responses."""
    response = await call_next(request)
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    
    return response