"""
Dashboard API endpoints for web interface.
"""

from fastapi import APIRouter, Depends, Query, Request, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
from datetime import datetime, timedelta

from mcx3d_finance.db.session import get_db
from mcx3d_finance.api.auth_middleware import get_current_user
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator
from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_performance
from mcx3d_finance.exceptions.handlers import handle_errors, error_boundary
from mcx3d_finance.monitoring.structured_logger import get_correlation_id

logger = LoggerFactory.get_logger(__name__, domain="api")

router = APIRouter(prefix="/api/dashboard", tags=["Dashboard"])


@router.get("/{organization_id}")
@handle_errors(operation="get_dashboard_data")
@log_performance()
async def get_dashboard_data(
    request: Request,
    organization_id: int,
    period_months: int = Query(12, description="Number of months to include"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """Get comprehensive dashboard data for authenticated users."""
    correlation_id = get_correlation_id()
    user_id = current_user["user_id"]

    with error_boundary(
        "get_dashboard_data",
        correlation_id=correlation_id,
        user_id=user_id,
        organization_id=organization_id,
    ):
        # Verify user has access to the organization
        user_organizations = current_user.get("organizations", [])
        if organization_id not in user_organizations:
            logger.warning(
                "User attempted to access organization without permission",
                user_id=user_id,
                organization_id=organization_id,
                correlation_id=correlation_id,
            )
            raise HTTPException(
                status_code=403, 
                detail="You do not have access to this organization"
            )

        logger.info(
            "Processing request for dashboard data",
            endpoint=f"/dashboard/{organization_id}",
            method="GET",
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )

        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=period_months * 30)

        # Get KPIs
        kpi_calculator = SaaSKPICalculator()
        kpis = kpi_calculator.calculate_comprehensive_kpis(
            organization_id, start_date, end_date
        )

        # Get financial summary
        balance_sheet_gen = BalanceSheetGenerator(db)
        income_stmt_gen = IncomeStatementGenerator(db)

        latest_balance_sheet = balance_sheet_gen.generate_balance_sheet(
            organization_id, end_date
        )

        latest_income_stmt = income_stmt_gen.generate_income_statement(
            organization_id, start_date, end_date
        )

        result = {
            "organization_id": organization_id,
            "period": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat(),
                "months": period_months,
            },
            "kpis": kpis,
            "financial_summary": {
                "balance_sheet": latest_balance_sheet,
                "income_statement": latest_income_stmt,
            },
            "generated_at": datetime.utcnow().isoformat(),
        }

        logger.info(
            "Dashboard data request processed successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
        )

        return result
