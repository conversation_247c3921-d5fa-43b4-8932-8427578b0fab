"""
API endpoints for retrieving financial metrics and KPIs.

This module provides endpoints to access key business and financial metrics,
such as SaaS KPIs, with structured logging and robust error handling.
"""

from datetime import date

from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.orm import Session

from mcx3d_finance.api.schemas import SaasKpisResponse
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator
from mcx3d_finance.db.session import get_db
from mcx3d_finance.exceptions.handlers import error_boundary, handle_errors
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_performance

# --- Logger and Router Setup ---
logger = LoggerFactory.get_logger(__name__, domain="api")
router = APIRouter(prefix="/api/metrics", tags=["Metrics"])


# --- Endpoints ---


@router.get(
    "/{organization_id}/saas-kpis",
    response_model=SaasKpisResponse,
    summary="Get SaaS KPIs",
)
@handle_errors(operation="get_saas_kpis")
@log_performance()
async def get_saas_kpis(
    request: Request,
    organization_id: int,
    end_date: date = Depends(Query(default_factory=date.today)),
    db: Session = Depends(get_db),
) -> SaasKpisResponse:
    """
    Calculates and retrieves key SaaS (Software as a Service) KPIs for a
    given organization up to a specified end date.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_saas_kpis",
        correlation_id=correlation_id,
        organization_id=organization_id,
    ):
        logger.info(
            "Request received for SaaS KPIs",
            correlation_id=correlation_id,
            organization_id=organization_id,
            end_date=str(end_date),
        )

        calculator = SaaSKPICalculator(db)
        # The `period` parameter from the original function is interpreted as the `end_date`.
        # This aligns with other reporting functions for consistency.
        kpis = calculator.calculate_comprehensive_kpis(
            organization_id=organization_id, end_date=end_date
        )

        response = SaasKpisResponse(
            organization_id=organization_id,
            calculation_date=date.today(),
            kpis=kpis,
        )

        logger.info(
            "SaaS KPIs calculated successfully",
            correlation_id=correlation_id,
            organization_id=organization_id,
            kpi_count=len(kpis) if kpis else 0,
        )
        return response
