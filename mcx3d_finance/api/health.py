"""
Health check endpoints for the financial system.

This module provides a variety of health check endpoints to monitor the status
and performance of the application, from basic liveness checks to comprehensive
business-level diagnostics, all integrated with structured logging and error
handling.
"""

from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from mcx3d_finance.api.schemas import (
    BusinessHealthResponse,
    ComprehensiveHealthResponse,
    HealthCheckResponse,
)
from mcx3d_finance.db.session import get_db
from mcx3d_finance.exceptions.handlers import error_boundary, handle_errors
from mcx3d_finance.monitoring.health_checker import HealthChecker
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_performance

# --- Logger and Router Setup ---
logger = LoggerFactory.get_logger(__name__, domain="api")
router = APIRouter(prefix="/health", tags=["Health"])


# --- Endpoints ---


@router.get(
    "/",
    response_model=HealthCheckResponse,
    summary="Basic Liveness Check",
)
@handle_errors(operation="basic_health_check")
async def basic_health_check(request: Request) -> HealthCheckResponse:
    """
    Provides a basic liveness check to confirm the API is running and responsive.
    """
    correlation_id = get_correlation_id()
    with error_boundary("basic_health_check", correlation_id=correlation_id):
        logger.info(
            "Performing basic health check", correlation_id=correlation_id
        )
        response = HealthCheckResponse(
            status="healthy",
            service="MCX3D Financials API",
            version="2.0.0",
        )
        logger.info(
            "Basic health check successful", correlation_id=correlation_id
        )
        return response


@router.get(
    "/comprehensive",
    response_model=ComprehensiveHealthResponse,
    summary="Comprehensive System Health Check",
)
@handle_errors(operation="comprehensive_health_check")
@log_performance()
async def comprehensive_health_check(
    request: Request, db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Runs a comprehensive health check across all system components, including
    database, cache, and external integrations.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "comprehensive_health_check", correlation_id=correlation_id
    ):
        logger.info(
            "Performing comprehensive health check",
            correlation_id=correlation_id,
        )
        health_checker = HealthChecker(db)
        health_report = await health_checker.get_comprehensive_health()

        status_code = status.HTTP_200_OK
        if health_report.overall_status == "unhealthy":
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
            logger.error(
                "Comprehensive health check failed",
                correlation_id=correlation_id,
                report=health_report.dict(),
            )
        else:
            logger.info(
                "Comprehensive health check completed",
                correlation_id=correlation_id,
                status=health_report.overall_status,
            )

        return JSONResponse(
            content=health_report.dict(), status_code=status_code
        )


@router.get(
    "/business",
    response_model=BusinessHealthResponse,
    summary="Business Logic Health Check",
)
@handle_errors(operation="business_health_check")
@log_performance()
async def business_health_check(
    request: Request, db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Performs a health check on critical business processes, such as report
    generation and data processing pipelines.
    """
    correlation_id = get_correlation_id()
    with error_boundary("business_health_check", correlation_id=correlation_id):
        logger.info(
            "Performing business health check", correlation_id=correlation_id
        )
        health_checker = HealthChecker(db)
        health_report = await health_checker.get_business_health()

        status_code = status.HTTP_200_OK
        if health_report.overall_status == "unhealthy":
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
            logger.error(
                "Business health check failed",
                correlation_id=correlation_id,
                report=health_report.dict(),
            )
        else:
            logger.info(
                "Business health check completed",
                correlation_id=correlation_id,
                status=health_report.overall_status,
            )

        return JSONResponse(
            content=health_report.dict(), status_code=status_code
        )


@router.get(
    "/readiness",
    response_model=HealthCheckResponse,
    summary="Readiness Probe",
)
@handle_errors(operation="readiness_check")
async def readiness_check(
    request: Request, db: Session = Depends(get_db)
) -> JSONResponse:
    """
    A readiness probe to indicate if the service is ready to accept traffic.
    Checks essential dependencies like the database connection.
    """
    correlation_id = get_correlation_id()
    with error_boundary("readiness_check", correlation_id=correlation_id):
        logger.info("Performing readiness check", correlation_id=correlation_id)
        health_checker = HealthChecker(db)
        db_status = await health_checker.check_database_connection()

        is_ready = db_status["status"] == "healthy"
        status_code = (
            status.HTTP_200_OK
            if is_ready
            else status.HTTP_503_SERVICE_UNAVAILABLE
        )

        response = {
            "status": "ready" if is_ready else "not_ready",
            "timestamp": datetime.utcnow().isoformat(),
            "details": [db_status],
        }

        if is_ready:
            logger.info(
                "Readiness check passed", correlation_id=correlation_id
            )
        else:
            logger.warning(
                "Readiness check failed",
                correlation_id=correlation_id,
                details=response["details"],
            )

        return JSONResponse(content=response, status_code=status_code)


@router.get(
    "/liveness",
    response_model=HealthCheckResponse,
    summary="Liveness Probe",
)
@handle_errors(operation="liveness_check")
async def liveness_check(request: Request) -> HealthCheckResponse:
    """
    A simple liveness probe for orchestration platforms to verify the container
    is still alive. This is an alias for the basic health check.
    """
    correlation_id = get_correlation_id()
    with error_boundary("liveness_check", correlation_id=correlation_id):
        logger.debug("Performing liveness check", correlation_id=correlation_id)
        return HealthCheckResponse(
            status="alive",
            service="MCX3D Financials API",
            version="2.0.0",
        )
