"""
Webhook endpoints for receiving real-time updates from external services.

This module handles incoming webhooks, verifies their authenticity, and
triggers appropriate asynchronous tasks, with integrated structured logging
and robust error handling.
"""
import base64
import hashlib
import hmac
from typing import Dict, Optional

from fastapi import APIRouter, Depends, Header, HTTPException, Request
from sqlalchemy.orm import Session

from mcx3d_finance.core.config import get_xero_webhook_key
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.db.models import Organization
from mcx3d_finance.db.session import get_db
from mcx3d_finance.exceptions.handlers import error_boundary, handle_errors
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.tasks.sync_tasks import sync_xero_data

logger = LoggerFactory.get_logger(__name__, domain="api")
router = APIRouter(prefix="/webhooks", tags=["Webhooks"])


def verify_signature(payload: bytes, signature: str, secret: str) -> bool:
    """
    Verify the Xero webhook signature using HMAC-SHA256.
    """
    computed_hash = hmac.new(secret.encode(), payload, hashlib.sha256).digest()
    computed_signature = base64.b64encode(computed_hash).decode()
    return hmac.compare_digest(computed_signature, signature)


@router.post("/xero")
@handle_errors(operation="xero_webhook")
async def xero_webhook(
    request: Request,
    xero_signature: Optional[str] = Header(None),
    db: Session = Depends(get_db),
) -> Dict[str, str]:
    """
    Receives and processes webhooks from Xero.

    It verifies the signature, parses events, and enqueues background
    tasks to synchronize data for the relevant organization.
    """
    correlation_id = get_correlation_id()
    with error_boundary("xero_webhook", correlation_id=correlation_id):
        logger.info("Xero webhook received.", correlation_id=correlation_id)
        payload = await request.body()
        webhook_key = get_xero_webhook_key()

        if not webhook_key:
            logger.critical(
                "Xero webhook key is not configured on the server.",
                correlation_id=correlation_id,
            )
            # Let the error handler create a standard 500 response
            raise ValueError("Webhook key not configured.")

        if not xero_signature or not verify_signature(
            payload, xero_signature, webhook_key
        ):
            logger.security(
                "Invalid Xero webhook signature.",
                severity="high",
                correlation_id=correlation_id,
                request_ip=request.client.host if request.client else "unknown",
            )
            raise HTTPException(status_code=401, detail="Invalid signature")

        logger.security(
            "Xero webhook signature verified successfully.",
            severity="info",
            correlation_id=correlation_id,
        )

        data = await request.json()
        events = data.get("events", [])
        logger.info(
            f"Processing {len(events)} event(s) from Xero.",
            num_events=len(events),
            correlation_id=correlation_id,
        )

        for event in events:
            tenant_id = event.get("tenantId")
            if tenant_id:
                organization = (
                    db.query(Organization)
                    .filter(Organization.xero_tenant_id == tenant_id)
                    .first()
                )
                if organization:
                    # Pass correlation_id to the background task
                    sync_xero_data.delay(organization.id, correlation_id)
                    logger.info(
                        "Queued Xero data sync task for organization.",
                        organization_id=organization.id,
                        xero_tenant_id=tenant_id,
                        correlation_id=correlation_id,
                    )
                else:
                    logger.warning(
                        "Received webhook for an unknown Xero tenant ID.",
                        xero_tenant_id=tenant_id,
                        correlation_id=correlation_id,
                    )

        return {"status": "ok"}
