"""
API endpoints for managing Xero data import and synchronization tasks.

This module provides routes to trigger, monitor, and check the status of
Xero data synchronization processes, with integrated structured logging and
robust error handling.
"""

from typing import Any, Dict

from celery import current_app as celery_app
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from mcx3d_finance.api.auth_middleware import get_current_user
from mcx3d_finance.api.schemas import (
    SyncStatusResponse,
    SyncTaskResponse,
    XeroAuthStatusResponse,
)
from mcx3d_finance.db.models import Organization, SyncStatus, User
from mcx3d_finance.db.session import get_db
from mcx3d_finance.exceptions.handlers import error_boundary, handle_errors
from mcx3d_finance.integrations.xero_auth_service import XeroAuthService
from mcx3d_finance.monitoring.structured_logger import get_correlation_id
from mcx3d_finance.tasks.sync_tasks import sync_xero_data
from mcx3d_finance.core.logging_factory import LoggerFactory
from mcx3d_finance.utils.logging_config import log_performance

logger = LoggerFactory.get_logger(__name__, domain="api")
router = APIRouter(prefix="/api/xero", tags=["Xero Integration"])


@router.post(
    "/sync/{organization_id}",
    response_model=SyncTaskResponse,
    summary="Trigger Xero Data Synchronization",
)
@handle_errors(operation="trigger_xero_sync")
async def trigger_sync(
    request: Request,
    organization_id: int,
    force_full: bool = False,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> SyncTaskResponse:
    """
    Queues an asynchronous task to synchronize data from Xero.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "trigger_xero_sync",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Xero sync triggered.",
            organization_id=organization_id,
            user_id=current_user.id,
            force_full=force_full,
            correlation_id=correlation_id,
        )

        organization = db.query(Organization).get(organization_id)
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found.")

        # Pass the correlation ID to the Celery task for distributed tracing
        task = sync_xero_data.delay(
            organization_id=organization_id,
            incremental=not force_full,
            correlation_id=correlation_id,
        )

        logger.info(
            "Xero sync task queued successfully.",
            organization_id=organization_id,
            task_id=task.id,
            correlation_id=correlation_id,
        )
        return SyncTaskResponse(
            task_id=task.id,
            organization_id=organization_id,
            status="queued",
            message="Xero synchronization task has been queued.",
        )


@router.get(
    "/sync-status/{organization_id}",
    response_model=SyncStatusResponse,
    summary="Get Xero Sync Status",
)
@handle_errors(operation="get_xero_sync_status")
@log_performance()
async def get_sync_status(
    request: Request,
    organization_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> SyncStatusResponse:
    """
    Retrieves the latest synchronization status for an organization.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_xero_sync_status",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Fetching Xero sync status.",
            organization_id=organization_id,
            correlation_id=correlation_id,
        )
        sync_status = (
            db.query(SyncStatus)
            .filter(SyncStatus.organization_id == organization_id)
            .first()
        )

        if not sync_status:
            return SyncStatusResponse(
                organization_id=organization_id, sync_status="never_synced"
            )

        return SyncStatusResponse.from_orm(sync_status)


@router.get(
    "/auth-status/{organization_id}",
    response_model=XeroAuthStatusResponse,
    summary="Check Xero Authentication Status",
)
@handle_errors(operation="get_xero_auth_status")
async def get_xero_auth_status(
    request: Request,
    organization_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> XeroAuthStatusResponse:
    """
    Checks the validity and status of the Xero API authentication for an organization.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_xero_auth_status",
        correlation_id=correlation_id,
        user_id=current_user.id,
        organization_id=organization_id,
    ):
        logger.info(
            "Checking Xero auth status.",
            organization_id=organization_id,
            correlation_id=correlation_id,
        )
        auth_service = XeroAuthService(db, organization_id)
        auth_status = await auth_service.get_auth_status()
        logger.info(
            "Xero auth status retrieved.",
            organization_id=organization_id,
            is_authenticated=auth_status.is_authenticated,
            correlation_id=correlation_id,
        )
        return auth_status


@router.get(
    "/task-status/{task_id}",
    response_model=Dict[str, Any],
    summary="Get Asynchronous Task Status",
)
@handle_errors(operation="get_task_status")
async def get_task_status(
    request: Request, task_id: str, current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Retrieves the status of a background task (e.g., a Xero sync) by its ID.
    """
    correlation_id = get_correlation_id()
    with error_boundary(
        "get_task_status",
        correlation_id=correlation_id,
        user_id=current_user.id,
        task_id=task_id,
    ):
        logger.info(
            "Fetching task status.", task_id=task_id, correlation_id=correlation_id
        )
        result = celery_app.AsyncResult(task_id)

        response = {
            "task_id": task_id,
            "status": result.status,
            "result": result.result if result.ready() else None,
        }

        logger.info(
            "Task status retrieved.",
            task_id=task_id,
            task_status=result.status,
            correlation_id=correlation_id,
        )
        return response
