"""
Security audit logging system for tracking sensitive operations.
Provides comprehensive logging for compliance and security monitoring.
"""
from mcx3d_finance.core.logging_factory import LoggerFactory
import json
import sys
import time
import os
from typing import Dict, Any, Optional, List, Iterator
from datetime import datetime, timezone
from enum import Enum
import hashlib
from redis.asyncio import Redis as AIORedis
from dataclasses import dataclass, asdict
import asyncio
from contextlib import contextmanager

from mcx3d_finance.core.config import get_security_config

logger = LoggerFactory.get_logger(__name__, domain='utils')


class AuditEventType(Enum):
    """Types of audit events."""
    # Authentication events
    LOGIN_SUCCESS = "auth.login.success"
    LOGIN_FAILURE = "auth.login.failure"
    LOGOUT = "auth.logout"
    SESSION_CREATED = "auth.session.created"
    SESSION_EXPIRED = "auth.session.expired"
    SESSION_INVALIDATED = "auth.session.invalidated"
    USER_REGISTRATION = "auth.user.registration.success"
    USER_REGISTRATION_FAILURE = "auth.user.registration.failure"
    
    # Authorization events
    ACCESS_GRANTED = "authz.access.granted"
    ACCESS_DENIED = "authz.access.denied"
    PERMISSION_CHANGED = "authz.permission.changed"
    ROLE_ASSIGNED = "authz.role.assigned"
    ROLE_REVOKED = "authz.role.revoked"
    
    # Data access events
    DATA_READ = "data.read"
    DATA_CREATED = "data.created"
    DATA_UPDATED = "data.updated"
    DATA_DELETED = "data.deleted"
    DATA_EXPORTED = "data.exported"
    DATA_IMPORTED = "data.imported"
    
    # Security events
    PASSWORD_CHANGED = "security.password.changed"
    PASSWORD_RESET = "security.password.reset"
    MFA_ENABLED = "security.mfa.enabled"
    MFA_DISABLED = "security.mfa.disabled"
    MFA_CHALLENGE_SUCCESS = "security.mfa.success"
    MFA_CHALLENGE_FAILURE = "security.mfa.failure"
    ACCOUNT_LOCKED = "security.account.locked"
    ACCOUNT_UNLOCKED = "security.account.unlocked"
    
    # System events
    CONFIG_CHANGED = "system.config.changed"
    SERVICE_STARTED = "system.service.started"
    SERVICE_STOPPED = "system.service.stopped"
    ERROR_OCCURRED = "system.error"
    
    # Compliance events
    CONSENT_GRANTED = "compliance.consent.granted"
    CONSENT_REVOKED = "compliance.consent.revoked"
    DATA_RETENTION_APPLIED = "compliance.retention.applied"
    DATA_PURGED = "compliance.data.purged"
    AUDIT_ACCESSED = "compliance.audit.accessed"


class AuditSeverity(Enum):
    """Severity levels for audit events."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Structure of an audit event."""
    event_id: str
    timestamp: str
    event_type: str
    severity: str
    user_id: Optional[str]
    session_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource_type: Optional[str]
    resource_id: Optional[str]
    action: str
    outcome: str
    details: Dict[str, Any]
    metadata: Dict[str, Any]


class AuditLogger:
    """
    Comprehensive audit logging system.
    
    Features:
    - Structured audit events
    - Event correlation
    - Tamper detection
    - Retention policies
    - SIEM integration ready
    - Search and analytics
    """
    
    def __init__(self, redis_client: Optional[AIORedis] = None) -> None:
        """
        Initialize audit logger.
        
        Args:
            redis_client: Redis client for temporary storage and analytics
        """
        self.security_config = get_security_config()
        
        # Redis connection
        self.redis: AIORedis
        if redis_client:
            self.redis = redis_client
        else:
            redis_url = self.security_config.get("redis_url", "redis://localhost:6379/0")
            self.redis = AIORedis.from_url(redis_url, decode_responses=True)
        
        # Audit configuration
        self.retention_days = self.security_config.get("audit_retention_days", 365)
        self.enable_tamper_detection = self.security_config.get("audit_tamper_detection", True)
        self.enable_event_correlation = self.security_config.get("audit_correlation", True)
        
        # Initialize structured logger
        self.audit_handler = self._setup_audit_handler()
    
    def _setup_audit_handler(self) -> logging.Handler:
        """Set up dedicated audit log handler."""
        log_file_path = self.security_config.get("audit_log_file", "logs/mcx3d_audit.log")
        
        # Create logs directory if it doesn't exist
        log_dir = os.path.dirname(log_file_path)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        handler = logging.FileHandler(log_file_path)
        
        # Use JSON formatter for structured logging
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        
        # Create dedicated audit logger
        audit_logger = logging.getLogger("audit")
        audit_logger.setLevel(logging.INFO)
        audit_logger.addHandler(handler)
        audit_logger.propagate = False
        
        return handler
    
    async def log_event(
        self,
        event_type: AuditEventType,
        action: str,
        outcome: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.INFO
    ) -> str:
        """
        Log an audit event.
        
        Args:
            event_type: Type of audit event
            action: Action performed
            outcome: Outcome (success/failure)
            user_id: User performing action
            session_id: Session ID
            ip_address: Client IP address
            user_agent: Client user agent
            resource_type: Type of resource accessed
            resource_id: ID of resource accessed
            details: Additional event details
            severity: Event severity
            
        Returns:
            Event ID
        """
        # Generate event ID
        event_id = self._generate_event_id()
        
        # Create audit event
        event = AuditEvent(
            event_id=event_id,
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type=event_type.value,
            severity=severity.value,
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            outcome=outcome,
            details=details or {},
            metadata={
                "service": "mcx3d_finance",
                "version": "2.0",
                "environment": self.security_config.get("environment", "production")
            }
        )
        
        # Add tamper detection hash if enabled
        if self.enable_tamper_detection:
            event.metadata["integrity_hash"] = self._calculate_integrity_hash(event)
        
        # Log the event
        await self._write_audit_log(event)
        
        # Store in Redis for analytics
        await self._store_in_redis(event)
        
        # Correlate with other events
        if self.enable_event_correlation:
            await self._correlate_event(event)
        
        # Check for suspicious patterns
        await self._check_suspicious_patterns(event)
        
        return event_id
    
    def _generate_event_id(self) -> str:
        """Generate unique event ID."""
        timestamp = int(time.time() * 1000000)
        random_part = hashlib.sha256(
            f"{timestamp}{id(self)}".encode()
        ).hexdigest()[:8]
        
        return f"AUD-{timestamp}-{random_part}"
    
    def _calculate_integrity_hash(self, event: AuditEvent) -> str:
        """Calculate integrity hash for tamper detection."""
        # Create canonical representation
        canonical = json.dumps({
            "event_id": event.event_id,
            "timestamp": event.timestamp,
            "event_type": event.event_type,
            "user_id": event.user_id,
            "action": event.action,
            "outcome": event.outcome,
            "details": event.details
        }, sort_keys=True)
        
        # Add secret for HMAC
        secret = self.security_config.get("audit_hash_secret", "default_secret")
        
        return hashlib.sha256(
            f"{canonical}{secret}".encode()
        ).hexdigest()
    
    async def _write_audit_log(self, event: AuditEvent) -> None:
        """Write event to audit log file."""
        audit_logger = logging.getLogger("audit")
        
        # Convert to dict and log as JSON
        event_dict = asdict(event)
        audit_logger.info(json.dumps(event_dict))
    
    async def _store_in_redis(self, event: AuditEvent) -> None:
        """Store event in Redis for analytics."""
        # Store event details
        event_key = f"audit:event:{event.event_id}"
        event_dict = {k: v for k, v in asdict(event).items() if v is not None}
        
        # Serialize dicts to JSON strings
        if "details" in event_dict:
            event_dict["details"] = json.dumps(event_dict["details"])
        if "metadata" in event_dict:
            event_dict["metadata"] = json.dumps(event_dict["metadata"])
            
        await self.redis.hset(  # type: ignore
            event_key,
            mapping=event_dict
        )
        await self.redis.expire(event_key, 86400)  # 24 hours
        
        # Update indices
        if event.user_id:
            user_key = f"audit:user:{event.user_id}"
            await self.redis.zadd(
                user_key,
                {event.event_id: time.time()}
            )
            await self.redis.expire(user_key, 86400)
        
        # Update event type index
        type_key = f"audit:type:{event.event_type}"
        await self.redis.zadd(
            type_key,
            {event.event_id: time.time()}
        )
        await self.redis.expire(type_key, 86400)
    
    async def _correlate_event(self, event: AuditEvent) -> None:
        """Correlate event with related events."""
        correlation_rules = {
            AuditEventType.LOGIN_FAILURE.value: self._correlate_login_failures,
            AuditEventType.ACCESS_DENIED.value: self._correlate_access_denied,
            AuditEventType.DATA_EXPORTED.value: self._correlate_data_exports,
        }
        
        correlator = correlation_rules.get(event.event_type)
        if correlator:
            await correlator(event)
    
    async def _correlate_login_failures(self, event: AuditEvent) -> None:
        """Correlate login failures for brute force detection."""
        if not event.user_id:
            return
        
        # Count recent failures
        failure_key = f"audit:failures:login:{event.user_id}"
        failures = await self.redis.incr(failure_key)
        await self.redis.expire(failure_key, 300)  # 5 minutes
        
        if failures >= 5:
            # Log security alert
            await self.log_event(
                event_type=AuditEventType.ACCOUNT_LOCKED,
                action="account_locked_due_to_failures",
                outcome="success",
                user_id=event.user_id,
                details={
                    "reason": "Multiple login failures",
                    "failure_count": failures,
                    "correlated_event": event.event_id
                },
                severity=AuditSeverity.WARNING
            )
    
    async def _correlate_access_denied(self, event: AuditEvent) -> None:
        """Correlate access denied events for privilege escalation attempts."""
        if not event.user_id:
            return
        
        # Count access denials
        denial_key = f"audit:denials:{event.user_id}"
        denials = await self.redis.incr(denial_key)
        await self.redis.expire(denial_key, 3600)  # 1 hour
        
        if denials >= 10:
            # Log security alert
            logger.warning(
                f"Multiple access denials for user {event.user_id}: {denials} in 1 hour"
            )
    
    async def _correlate_data_exports(self, event: AuditEvent) -> None:
        """Correlate data exports for data exfiltration detection."""
        if not event.user_id:
            return
        
        # Track export volume
        export_key = f"audit:exports:{event.user_id}"
        export_count = await self.redis.incr(export_key)
        await self.redis.expire(export_key, 3600)  # 1 hour
        
        if export_count >= 20:
            # Log security alert
            logger.warning(
                f"High volume of data exports by user {event.user_id}: "
                f"{export_count} in 1 hour"
            )
    
    async def _check_suspicious_patterns(self, event: AuditEvent) -> None:
        """Check for suspicious patterns in audit events."""
        suspicious_patterns = [
            # After hours access
            self._check_after_hours_access,
            # Rapid privilege changes
            self._check_rapid_privilege_changes,
            # Unusual IP locations
            self._check_unusual_locations,
        ]
        
        for pattern_checker in suspicious_patterns:
            await pattern_checker(event)
    
    async def _check_after_hours_access(self, event: AuditEvent) -> None:
        """Check for after-hours access patterns."""
        # Skip non-data access events
        if not event.event_type.startswith("data."):
            return
        
        # Check time
        event_time = datetime.fromisoformat(event.timestamp)
        hour = event_time.hour
        
        # Define business hours (9 AM - 6 PM)
        if hour < 9 or hour > 18:
            logger.info(
                f"After-hours access detected: {event.user_id} at {hour}:00"
            )
    
    async def _check_rapid_privilege_changes(self, event: AuditEvent) -> None:
        """Check for rapid privilege changes."""
        if event.event_type not in [
            AuditEventType.ROLE_ASSIGNED.value,
            AuditEventType.PERMISSION_CHANGED.value
        ]:
            return
        
        if not event.user_id:
            return
        
        # Count privilege changes
        priv_key = f"audit:privchanges:{event.user_id}"
        changes = await self.redis.incr(priv_key)
        await self.redis.expire(priv_key, 600)  # 10 minutes
        
        if changes >= 3:
            logger.warning(
                f"Rapid privilege changes for user {event.user_id}: "
                f"{changes} in 10 minutes"
            )
    
    async def _check_unusual_locations(self, event: AuditEvent) -> None:
        """Check for access from unusual locations."""
        if not event.ip_address or not event.user_id:
            return
        
        # Store user locations
        location_key = f"audit:locations:{event.user_id}"
        await self.redis.sadd(location_key, event.ip_address)  # type: ignore
        await self.redis.expire(location_key, 86400 * 30)  # 30 days
        
        # Check if this is a new location
        location_count = await self.redis.scard(location_key)  # type: ignore
        if location_count > 5:
            logger.info(
                f"User {event.user_id} has accessed from {location_count} "
                f"different IPs"
            )
    
class AuditContext:
    def __init__(self, logger: 'AuditLogger', event_type: AuditEventType, action: str, **kwargs: Any) -> None:
        self.logger = logger
        self.event_type = event_type
        self.action = action
        self.kwargs = kwargs
        self.details: Dict[str, Any] = {}
        self.outcome = "success"

    def add_detail(self, key: str, value: Any) -> None:
        self.details[key] = value

    def set_outcome(self, outcome: str) -> None:
        self.outcome = outcome


@contextmanager
def audit_context(
    self: 'AuditLogger',
    event_type: AuditEventType,
    action: str,
    **kwargs: Any
) -> Iterator[AuditContext]:
    """
    Context manager for automatic audit logging.
    
    Usage:
        with audit_logger.audit_context(
            AuditEventType.DATA_READ,
            "read_report",
            user_id=user_id,
            resource_id=report_id
        ) as audit:
            # Perform operation
            report = read_report(report_id)
            audit.add_detail("rows_read", len(report.data))
    """
    context = AuditContext(self, event_type, action, **kwargs)
    
    try:
        yield context
    except Exception as e:
        context.outcome = "failure"
        context.details["error"] = str(e)
        raise
    finally:
        # Log the audit event
        asyncio.create_task(
            self.log_event(
                event_type=context.event_type,
                action=context.action,
                outcome=context.outcome,
                details=context.details,
                **context.kwargs
            )
        )
    
    async def search_events(
        self,
        event_type: Optional[str] = None,
        user_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        outcome: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Search audit events.
        
        Args:
            event_type: Filter by event type
            user_id: Filter by user ID
            start_time: Start time filter
            end_time: End time filter
            outcome: Filter by outcome
            limit: Maximum results
            
        Returns:
            List of matching events
        """
        # This is a simplified search implementation
        # In production, use Elasticsearch or similar
        
        events = []
        
        # Search by event type
        if event_type:
            type_key = f"audit:type:{event_type}"
            event_ids = await self.redis.zrevrange(type_key, 0, limit - 1)
            
            for event_id in event_ids:
                event_key = f"audit:event:{event_id}"
                event_data = await self.redis.hgetall(event_key)  # type: ignore
                
                if event_data:
                    # Apply filters
                    if user_id and event_data.get("user_id") != user_id:
                        continue
                    
                    if outcome and event_data.get("outcome") != outcome:
                        continue
                    
                    events.append(event_data)
        
        return events
    
    async def generate_compliance_report(
        self,
        start_date: datetime,
        end_date: datetime,
        report_type: str = "general"
    ) -> Dict[str, Any]:
        """
        Generate compliance report from audit logs.
        
        Args:
            start_date: Report start date
            end_date: Report end date
            report_type: Type of report
            
        Returns:
            Compliance report data
        """
        # Log access to audit logs
        await self.log_event(
            event_type=AuditEventType.AUDIT_ACCESSED,
            action="generate_compliance_report",
            outcome="success",
            details={
                "report_type": report_type,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )
        
        # Generate report (simplified)
        report = {
            "period": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "summary": {
                "total_events": 0,
                "login_attempts": 0,
                "failed_logins": 0,
                "data_access": 0,
                "configuration_changes": 0,
                "security_events": 0
            },
            "compliance_status": "compliant",
            "recommendations": []
        }
        
        return report


# Global audit logger instance
if "pytest" in sys.modules:
    audit_logger = None
else:
    audit_logger = AuditLogger()
