"""
Comprehensive input validation and sanitization for security.
Prevents injection attacks and ensures data integrity.
"""
import re
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Any, Dict, List, Optional, Union, Callable, cast
from datetime import datetime
from decimal import Decimal
import bleach  # type: ignore
from email_validator import validate_email, EmailNotValidError
from urllib.parse import urlparse
import ipaddress

logger = LoggerFactory.get_logger(__name__, domain='utils')


class ValidationError(Exception):
    """Custom validation error with detailed information."""
    def __init__(self, field: str, message: str, value: Any = None) -> None:
        self.field = field
        self.message = message
        self.value = value
        super().__init__(f"{field}: {message}")


class InputValidator:
    """
    Comprehensive input validation for security and data integrity.
    
    Features:
    - SQL injection prevention
    - XSS protection
    - Path traversal prevention
    - Command injection prevention
    - Data type validation
    - Format validation
    - Business logic validation
    """
    
    # SQL injection patterns
    SQL_INJECTION_PATTERNS = [
        r"(\bunion\b.*\bselect\b|\bselect\b.*\bfrom\b|\binsert\b.*\binto\b)",
        r"(\bdrop\b.*\btable\b|\bdelete\b.*\bfrom\b|\bupdate\b.*\bset\b)",
        r"(\bexec\b|\bexecute\b)\s*\(",
        r"(\bscript\b|\balert\b)\s*\(",
        r"(;|--|\*|\/\*|\*\/|xp_|sp_)",
        r"(\bor\b|\band\b)\s*['\"]?\s*['\"]?\s*=",
    ]
    
    # XSS patterns
    XSS_PATTERNS = [
        r"<\s*script[^>]*>.*?<\s*/\s*script\s*>",
        r"javascript\s*:",
        r"on\w+\s*=",
        r"<\s*iframe[^>]*>",
        r"<\s*object[^>]*>",
        r"<\s*embed[^>]*>",
        r"<\s*link[^>]*>",
    ]
    
    # Path traversal patterns
    PATH_TRAVERSAL_PATTERNS = [
        r"\.\./",
        r"\.\.\\"
        r"%2e%2e/",
        r"%2e%2e\\",
        r"\.\.%2f",
        r"\.\.%5c",
    ]
    
    # Command injection patterns
    COMMAND_INJECTION_PATTERNS = [
        r"[;&|`$]",
        r"\$\(",
        r"\beval\b",
        r"\bexec\b",
        r"\bsystem\b",
        r"\bpassthru\b",
    ]
    
    # File upload settings
    ALLOWED_FILE_EXTENSIONS = {
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
        'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.csv'],
        'archive': ['.zip', '.tar', '.gz', '.7z'],
    }
    
    MAX_FILE_SIZES = {
        'image': 10 * 1024 * 1024,  # 10MB
        'document': 50 * 1024 * 1024,  # 50MB
        'archive': 100 * 1024 * 1024,  # 100MB
    }
    
    def __init__(self) -> None:
        """Initialize validator with compiled regex patterns."""
        self.sql_patterns = [re.compile(p, re.IGNORECASE) for p in self.SQL_INJECTION_PATTERNS]
        self.xss_patterns = [re.compile(p, re.IGNORECASE) for p in self.XSS_PATTERNS]
        self.path_patterns = [re.compile(p, re.IGNORECASE) for p in self.PATH_TRAVERSAL_PATTERNS]
        self.cmd_patterns = [re.compile(p) for p in self.COMMAND_INJECTION_PATTERNS]
    
    def validate_and_sanitize(
        self,
        data: Dict[str, Any],
        schema: Dict[str, Dict[str, Any]],
        strict: bool = True
    ) -> Dict[str, Any]:
        """
        Validate and sanitize input data according to schema.
        
        Args:
            data: Input data to validate
            schema: Validation schema
            strict: If True, reject unknown fields
            
        Returns:
            Sanitized and validated data
            
        Raises:
            ValidationError: If validation fails
        """
        validated = {}
        errors = []
        
        # Check for unknown fields
        if strict:
            unknown_fields = set(data.keys()) - set(schema.keys())
            if unknown_fields:
                errors.append(ValidationError(
                    "unknown_fields",
                    f"Unknown fields: {', '.join(unknown_fields)}"
                ))
        
        # Validate each field
        for field_name, field_schema in schema.items():
            try:
                value = data.get(field_name)
                validated_value = self._validate_field(field_name, value, field_schema)
                
                if validated_value is not None or not field_schema.get('optional', False):
                    validated[field_name] = validated_value
                    
            except ValidationError as e:
                errors.append(e)
        
        if errors:
            raise ValidationError(
                "validation_failed",
                f"Validation failed for {len(errors)} fields",
                errors
            )
        
        return validated
    
    def _validate_field(self, name: str, value: Any, schema: Dict[str, Any]) -> Any:
        """Validate a single field according to its schema."""
        # Check required
        if value is None:
            if not schema.get('optional', False):
                raise ValidationError(name, "Field is required")
            return None
        
        # Type validation
        field_type = schema.get('type', 'string')
        value = self._validate_type(name, value, field_type)
        
        # Additional validations
        validators: Dict[str, Callable[..., Any]] = {
            'min_length': self._validate_min_length,
            'max_length': self._validate_max_length,
            'min_value': self._validate_min_value,
            'max_value': self._validate_max_value,
            'pattern': self._validate_pattern,
            'enum': self._validate_enum,
            'custom': self._validate_custom,
        }
        
        for validator_name, validator_func in validators.items():
            if validator_name in schema:
                value = validator_func(name, value, schema[validator_name])
        
        # Security validations
        if schema.get('no_sql_injection', True) and isinstance(value, str):
            self._check_sql_injection(name, value)
        
        if schema.get('no_xss', True) and isinstance(value, str):
            value = self._sanitize_xss(value)
        
        if schema.get('no_path_traversal', True) and isinstance(value, str):
            self._check_path_traversal(name, value)
        
        if schema.get('no_command_injection', True) and isinstance(value, str):
            self._check_command_injection(name, value)
        
        return value
    
    def _validate_type(self, name: str, value: Any, expected_type: str) -> Any:
        """Validate and convert value to expected type."""
        type_validators = {
            'string': self._validate_string,
            'integer': self._validate_integer,
            'float': self._validate_float,
            'decimal': self._validate_decimal,
            'boolean': self._validate_boolean,
            'date': self._validate_date,
            'datetime': self._validate_datetime,
            'email': self._validate_email,
            'url': self._validate_url,
            'ip': self._validate_ip,
            'list': self._validate_list,
            'dict': self._validate_dict,
        }
        
        validator = type_validators.get(expected_type)
        if not validator:
            raise ValidationError(name, f"Unknown type: {expected_type}")
        
        return validator(name, value)
    
    def _validate_string(self, name: str, value: Any) -> str:
        """Validate string type."""
        if not isinstance(value, str):
            try:
                return str(value)
            except:
                raise ValidationError(name, "Invalid string value")
        return value
    
    def _validate_integer(self, name: str, value: Any) -> int:
        """Validate integer type."""
        if isinstance(value, bool):
            raise ValidationError(name, "Boolean is not a valid integer")
        
        try:
            return int(value)
        except (ValueError, TypeError):
            raise ValidationError(name, "Invalid integer value")
    
    def _validate_float(self, name: str, value: Any) -> float:
        """Validate float type."""
        try:
            return float(value)
        except (ValueError, TypeError):
            raise ValidationError(name, "Invalid float value")
    
    def _validate_decimal(self, name: str, value: Any) -> Decimal:
        """Validate decimal type."""
        try:
            return Decimal(str(value))
        except:
            raise ValidationError(name, "Invalid decimal value")
    
    def _validate_boolean(self, name: str, value: Any) -> bool:
        """Validate boolean type."""
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            if value.lower() in ('true', '1', 'yes', 'on'):
                return True
            elif value.lower() in ('false', '0', 'no', 'off'):
                return False
        
        raise ValidationError(name, "Invalid boolean value")
    
    def _validate_date(self, name: str, value: Any) -> str:
        """Validate date format (YYYY-MM-DD)."""
        if isinstance(value, datetime):
            return value.date().isoformat()
        
        if isinstance(value, str):
            try:
                datetime.strptime(value, '%Y-%m-%d')
                return value
            except ValueError:
                pass
        
        raise ValidationError(name, "Invalid date format (expected YYYY-MM-DD)")
    
    def _validate_datetime(self, name: str, value: Any) -> str:
        """Validate datetime format (ISO 8601)."""
        if isinstance(value, datetime):
            return value.isoformat()
        
        if isinstance(value, str):
            try:
                datetime.fromisoformat(value.replace('Z', '+00:00'))
                return value
            except ValueError:
                pass
        
        raise ValidationError(name, "Invalid datetime format (expected ISO 8601)")
    
    def _validate_email(self, name: str, value: Any) -> str:
        """Validate email address."""
        if not isinstance(value, str):
            raise ValidationError(name, "Email must be a string")
        
        try:
            validation = validate_email(value, check_deliverability=False)
            return validation.email
        except EmailNotValidError as e:
            raise ValidationError(name, str(e))
    
    def _validate_url(self, name: str, value: Any) -> str:
        """Validate URL format."""
        if not isinstance(value, str):
            raise ValidationError(name, "URL must be a string")
        
        try:
            result = urlparse(value)
            if not all([result.scheme, result.netloc]):
                raise ValidationError(name, "Invalid URL format")
            
            if result.scheme not in ('http', 'https', 'ftp', 'ftps'):
                raise ValidationError(name, f"Unsupported URL scheme: {result.scheme}")
            
            return value
        except Exception:
            raise ValidationError(name, "Invalid URL format")
    
    def _validate_ip(self, name: str, value: Any) -> str:
        """Validate IP address (v4 or v6)."""
        if not isinstance(value, str):
            raise ValidationError(name, "IP address must be a string")
        
        try:
            ipaddress.ip_address(value)
            return value
        except ValueError:
            raise ValidationError(name, "Invalid IP address")
    
    def _validate_list(self, name: str, value: Any) -> List[Any]:
        """Validate list type."""
        if not isinstance(value, list):
            raise ValidationError(name, "Value must be a list")
        return value
    
    def _validate_dict(self, name: str, value: Any) -> Dict[str, Any]:
        """Validate dict type."""
        if not isinstance(value, dict):
            raise ValidationError(name, "Value must be a dictionary")
        return value
    
    def _validate_min_length(self, name: str, value: Any, min_length: int) -> Any:
        """Validate minimum length."""
        if len(value) < min_length:
            raise ValidationError(name, f"Minimum length is {min_length}")
        return value
    
    def _validate_max_length(self, name: str, value: Any, max_length: int) -> Any:
        """Validate maximum length."""
        if len(value) > max_length:
            raise ValidationError(name, f"Maximum length is {max_length}")
        return value
    
    def _validate_min_value(self, name: str, value: Any, min_value: Union[int, float]) -> Any:
        """Validate minimum value."""
        if value < min_value:
            raise ValidationError(name, f"Minimum value is {min_value}")
        return value
    
    def _validate_max_value(self, name: str, value: Any, max_value: Union[int, float]) -> Any:
        """Validate maximum value."""
        if value > max_value:
            raise ValidationError(name, f"Maximum value is {max_value}")
        return value
    
    def _validate_pattern(self, name: str, value: str, pattern: str) -> str:
        """Validate against regex pattern."""
        if not re.match(pattern, value):
            raise ValidationError(name, f"Value does not match required pattern")
        return value
    
    def _validate_enum(self, name: str, value: Any, allowed_values: List[Any]) -> Any:
        """Validate value is in allowed list."""
        if value not in allowed_values:
            raise ValidationError(
                name,
                f"Value must be one of: {', '.join(map(str, allowed_values))}"
            )
        return value
    
    def _validate_custom(self, name: str, value: Any, validator: Callable) -> Any:
        """Apply custom validation function."""
        try:
            return validator(value)
        except Exception as e:
            raise ValidationError(name, f"Custom validation failed: {str(e)}")
    
    def _check_sql_injection(self, name: str, value: str) -> None:
        """Check for SQL injection patterns."""
        for pattern in self.sql_patterns:
            if pattern.search(value):
                logger.warning(f"Potential SQL injection in field '{name}': {value[:50]}...")
                raise ValidationError(name, "Input contains potentially dangerous SQL patterns")
    
    def _sanitize_xss(self, value: str) -> str:
        """Sanitize input to prevent XSS attacks."""
        # Use bleach to clean HTML
        cleaned = bleach.clean(
            value,
            tags=[],  # No HTML tags allowed by default
            attributes={},
            strip=True
        )
        
        # Additional XSS pattern check
        for pattern in self.xss_patterns:
            if pattern.search(cleaned):
                logger.warning(f"XSS pattern detected and removed: {value[:50]}...")
                cleaned = pattern.sub('', cleaned)
        
        return cast(str, cleaned)
    
    def _check_path_traversal(self, name: str, value: str) -> None:
        """Check for path traversal attempts."""
        for pattern in self.path_patterns:
            if pattern.search(value):
                logger.warning(f"Path traversal attempt in field '{name}': {value}")
                raise ValidationError(name, "Input contains path traversal patterns")
    
    def _check_command_injection(self, name: str, value: str) -> None:
        """Check for command injection patterns."""
        for pattern in self.cmd_patterns:
            if pattern.search(value):
                logger.warning(f"Command injection attempt in field '{name}': {value[:50]}...")
                raise ValidationError(name, "Input contains potentially dangerous characters")
    
    def validate_file_upload(
        self,
        filename: str,
        file_size: int,
        file_type: str = 'document',
        content: Optional[bytes] = None
    ) -> Dict[str, Any]:
        """
        Validate file upload for security.
        
        Args:
            filename: Name of the file
            file_size: Size of the file in bytes
            file_type: Type category of file
            content: Optional file content for deeper inspection
            
        Returns:
            Validation result with sanitized filename
        """
        # Sanitize filename
        safe_filename = self._sanitize_filename(filename)
        
        # Check extension
        ext = safe_filename.lower().split('.')[-1] if '.' in safe_filename else ''
        allowed_extensions = self.ALLOWED_FILE_EXTENSIONS.get(file_type, [])
        
        if f'.{ext}' not in allowed_extensions:
            raise ValidationError(
                'file_extension',
                f"File type not allowed. Allowed: {', '.join(allowed_extensions)}"
            )
        
        # Check file size
        max_size = self.MAX_FILE_SIZES.get(file_type, 10 * 1024 * 1024)
        if file_size > max_size:
            raise ValidationError(
                'file_size',
                f"File too large. Maximum size: {max_size / 1024 / 1024:.1f}MB"
            )
        
        # Content validation if provided
        if content:
            # Check for malicious content patterns
            if self._check_file_content(content, ext):
                raise ValidationError('file_content', "File contains suspicious content")
        
        return {
            'original_filename': filename,
            'safe_filename': safe_filename,
            'extension': ext,
            'size': file_size,
            'type': file_type
        }
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename to prevent directory traversal and other attacks."""
        # Remove path components
        filename = filename.split('/')[-1].split('\\')[-1]
        
        # Remove dangerous characters
        filename = re.sub(r'[^\w\s.-]', '', filename)
        
        # Limit length
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        if len(name) > 100:
            name = name[:100]
        
        return f"{name}.{ext}" if ext else name
    
    def _check_file_content(self, content: bytes, extension: str) -> bool:
        """Check file content for malicious patterns."""
        # Check file magic numbers
        magic_numbers = {
            'jpg': b'\xff\xd8\xff',
            'png': b'\x89PNG',
            'pdf': b'%PDF',
            'zip': b'PK\x03\x04',
        }
        
        expected_magic = magic_numbers.get(extension)
        if expected_magic and not content.startswith(expected_magic):
            logger.warning(f"File content doesn't match extension: {extension}")
            return True
        
        # Check for embedded scripts in images
        if extension in ('jpg', 'png', 'gif'):
            suspicious_patterns = [
                b'<script',
                b'javascript:',
                b'<?php',
                b'<%',
            ]
            
            for pattern in suspicious_patterns:
                if pattern in content.lower():
                    logger.warning(f"Suspicious pattern found in {extension} file")
                    return True
        
        return False
    
    def create_password_validator(
        self,
        min_length: int = 12,
        require_uppercase: bool = True,
        require_lowercase: bool = True,
        require_numbers: bool = True,
        require_special: bool = True,
        check_common: bool = True
    ) -> Callable[[str], str]:
        """
        Create a password validator with specified requirements.
        
        Returns:
            Validator function for passwords
        """
        def validate_password(password: str) -> str:
            if len(password) < min_length:
                raise ValueError(f"Password must be at least {min_length} characters")
            
            if require_uppercase and not re.search(r'[A-Z]', password):
                raise ValueError("Password must contain uppercase letters")
            
            if require_lowercase and not re.search(r'[a-z]', password):
                raise ValueError("Password must contain lowercase letters")
            
            if require_numbers and not re.search(r'\d', password):
                raise ValueError("Password must contain numbers")
            
            if require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                raise ValueError("Password must contain special characters")
            
            if check_common:
                # Check against common passwords
                common_passwords = [
                    'password123', 'admin123', 'letmein', 'welcome123',
                    'password', 'admin', 'root', 'toor', '123456'
                ]
                
                if password.lower() in common_passwords:
                    raise ValueError("Password is too common")
            
            return password
        
        return validate_password


# Global validator instance
input_validator = InputValidator()