"""
Retry mechanisms with exponential backoff for external API calls.
"""
import time
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Any, Callable, Type, Tuple, Optional, cast
from functools import wraps
import random

logger = LoggerFactory.get_logger(__name__, domain='utils')


class RetryError(Exception):
    """Exception raised when all retry attempts fail."""
    pass


def exponential_backoff_retry(
    max_attempts: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    exceptions: Tuple[Type[Exception], ...] = (Exception,)
) -> Callable:
    """
    Decorator that implements retry logic with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        initial_delay: Initial delay in seconds before first retry
        max_delay: Maximum delay between retries
        backoff_factor: Factor by which delay increases after each attempt
        jitter: Whether to add random jitter to delays
        exceptions: Tuple of exception types to catch and retry
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            last_exception = None
            delay: float = initial_delay
            
            for attempt in range(max_attempts):
                try:
                    # Log attempt
                    if attempt > 0:
                        logger.info(
                            f"Retry attempt {attempt}/{max_attempts - 1} for {func.__name__}"
                        )
                    
                    # Execute function
                    result = func(*args, **kwargs)
                    
                    # Success - reset any circuit breaker state
                    if hasattr(func, '_circuit_breaker'):
                        func._circuit_breaker.record_success()
                    
                    return result
                    
                except exceptions as e:
                    last_exception = e
                    
                    # Log the error
                    logger.warning(
                        f"Error in {func.__name__} (attempt {attempt + 1}/{max_attempts}): {e}"
                    )
                    
                    # Record failure if circuit breaker is attached
                    if hasattr(func, '_circuit_breaker'):
                        func._circuit_breaker.record_failure()
                    
                    # Check if we've exhausted retries
                    if attempt >= max_attempts - 1:
                        logger.error(
                            f"All retry attempts failed for {func.__name__}"
                        )
                        raise RetryError(
                            f"Failed after {max_attempts} attempts: {last_exception}"
                        ) from last_exception
                    
                    # Calculate next delay
                    if jitter:
                        # Add random jitter (0.5 to 1.5 times the delay)
                        actual_delay = delay * (0.5 + random.random())
                    else:
                        actual_delay = delay
                    
                    # Cap at max delay
                    actual_delay = min(actual_delay, max_delay)
                    
                    logger.info(
                        f"Waiting {actual_delay:.2f} seconds before retry..."
                    )
                    time.sleep(actual_delay)
                    
                    # Increase delay for next attempt
                    delay *= backoff_factor
            
            # This should not be reached, but just in case
            raise RetryError(
                f"Unexpected retry failure for {func.__name__}"
            )
        
        return wrapper
    return decorator


class CircuitBreaker:
    """
    Circuit breaker pattern implementation to prevent cascading failures.
    """
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = Exception
    ) -> None:
        """
        Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds before attempting to close circuit
            expected_exception: Exception type to catch
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = "closed"  # closed, open, half-open
    
    def record_success(self) -> None:
        """Record a successful call."""
        self.failure_count = 0
        self.state = "closed"
        self.last_failure_time = None
    
    def record_failure(self) -> None:
        """Record a failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            logger.warning(
                f"Circuit breaker opened after {self.failure_count} failures"
            )
    
    def call(self, func: Callable, *args: Any, **kwargs: Any) -> Any:
        """
        Execute function with circuit breaker protection.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: If circuit is open or function fails
        """
        # Check circuit state
        if self.state == "open":
            # Check if we should try half-open
            if (self.last_failure_time and 
                time.time() - self.last_failure_time > self.recovery_timeout):
                self.state = "half-open"
                logger.info("Circuit breaker attempting half-open state")
            else:
                raise Exception("Circuit breaker is open")
        
        # Try to execute function
        try:
            result = func(*args, **kwargs)
            self.record_success()
            return result
        except self.expected_exception:
            self.record_failure()
            raise
    
    def __call__(self, func: Callable) -> Callable:
        """
        Decorator to apply circuit breaker to a function.
        """
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            return self.call(func, *args, **kwargs)
        
        # Attach circuit breaker instance to function for retry decorator
        wrapper._circuit_breaker = self  # type: ignore
        return wrapper


def with_circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    expected_exception: Type[Exception] = Exception
) -> Callable:
    """
    Decorator factory for circuit breaker.
    
    Args:
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Seconds before attempting to close circuit
        expected_exception: Exception type to catch
        
    Returns:
        Circuit breaker decorator
    """
    def decorator(func: Callable) -> Callable:
        breaker = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            expected_exception=expected_exception
        )
        return breaker(func)
    
    return decorator


# Convenience decorators with common configurations
def retry_on_network_error(func: Callable) -> Callable:
    """Retry decorator for network-related errors."""
    return cast(
        Callable,
        exponential_backoff_retry(
            max_attempts=3,
            initial_delay=1.0,
            max_delay=30.0,
            exceptions=(ConnectionError, TimeoutError),
        )(func),
    )


def retry_on_api_error(func: Callable) -> Callable:
    """Retry decorator for API errors."""
    return cast(
        Callable,
        exponential_backoff_retry(
            max_attempts=5,
            initial_delay=2.0,
            max_delay=60.0,
            backoff_factor=2.0,
            exceptions=(Exception,),  # Catch broader exceptions for APIs
        )(func),
    )


# Combined decorator for external API calls
def external_api_call(
    max_retries: int = 3,
    circuit_breaker_threshold: int = 5
) -> Callable:
    """
    Combined decorator for external API calls with retry and circuit breaker.
    
    Args:
        max_retries: Maximum retry attempts
        circuit_breaker_threshold: Failures before opening circuit
        
    Returns:
        Combined decorator
    """
    def decorator(func: Callable) -> Callable:
        # Apply circuit breaker first
        func = with_circuit_breaker(
            failure_threshold=circuit_breaker_threshold,
            recovery_timeout=60.0
        )(func)
        
        # Then apply retry logic
        func = exponential_backoff_retry(
            max_attempts=max_retries,
            initial_delay=1.0,
            max_delay=30.0
        )(func)
        
        return func
    
    return decorator