"""
Encryption utilities for secure data storage.
Implements AES-256 encryption for sensitive data like OAuth tokens.
"""
import json
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Dict, Any
from cryptography.fernet import Fernet
import base64

from mcx3d_finance.core.config import get_security_config

logger = LoggerFactory.get_logger(__name__, domain='utils')


class EncryptionError(Exception):
    """Custom exception for encryption errors."""
    pass


class TokenEncryption:
    """
    Secure token encryption using Fernet (AES-256).
    """
    
    def __init__(self) -> None:
        """Initialize encryption with security configuration."""
        self.config = get_security_config()
        self._fernet = self._initialize_fernet()
    
    def _initialize_fernet(self) -> Fernet:
        """
        Initialize Fernet encryption with derived key.
        
        Returns:
            Fernet instance for encryption/decryption
        """
        # Get encryption key from config or generate one
        encryption_key = self.config.get("encryption_key")
        
        if not encryption_key:
            # Generate a new key if not configured
            logger.warning(
                "No encryption key configured. Generating new key. "
                "This should be stored securely in production!"
            )
            encryption_key = Fernet.generate_key().decode()
            logger.info(f"Generated encryption key: {encryption_key}")
        
        # Convert string key to bytes if needed
        if isinstance(encryption_key, str):
            encryption_key = encryption_key.encode()
        
        try:
            return Fernet(encryption_key)
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            raise EncryptionError(f"Encryption initialization failed: {e}")
    
    def encrypt_token(self, token: Dict[str, Any]) -> str:
        """
        Encrypt OAuth token for secure storage.
        
        Args:
            token: Token dictionary to encrypt
            
        Returns:
            Encrypted token string
            
        Raises:
            EncryptionError: If encryption fails
        """
        try:
            # Convert token to JSON string
            token_json = json.dumps(token, separators=(',', ':'))
            
            # Encrypt the JSON string
            encrypted_bytes = self._fernet.encrypt(token_json.encode())
            
            # Return base64 encoded string for database storage
            return base64.urlsafe_b64encode(encrypted_bytes).decode()
            
        except Exception as e:
            logger.error(f"Token encryption failed: {e}")
            raise EncryptionError(f"Failed to encrypt token: {e}")
    
    def decrypt_token(self, encrypted_token: str) -> Dict[str, Any]:
        """
        Decrypt stored OAuth token.
        
        Args:
            encrypted_token: Encrypted token string
            
        Returns:
            Decrypted token dictionary
            
        Raises:
            EncryptionError: If decryption fails
        """
        try:
            # Decode from base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_token.encode())
            
            # Decrypt the bytes
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            
            # Parse JSON back to dictionary
            decrypted_token = json.loads(decrypted_bytes.decode())
            assert isinstance(decrypted_token, dict)
            return decrypted_token
            
        except Exception as e:
            logger.error(f"Token decryption failed: {e}")
            raise EncryptionError(f"Failed to decrypt token: {e}")
    
    def rotate_encryption_key(
        self,
        old_key: str,
        new_key: str,
        encrypted_data: str
    ) -> str:
        """
        Rotate encryption key by re-encrypting data with new key.
        
        Args:
            old_key: Current encryption key
            new_key: New encryption key to use
            encrypted_data: Data encrypted with old key
            
        Returns:
            Data encrypted with new key
        """
        try:
            # Create Fernet instances for both keys
            old_fernet = Fernet(old_key.encode())
            new_fernet = Fernet(new_key.encode())
            
            # Decrypt with old key
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = old_fernet.decrypt(encrypted_bytes)
            
            # Encrypt with new key
            new_encrypted = new_fernet.encrypt(decrypted_data)
            
            return base64.urlsafe_b64encode(new_encrypted).decode()
            
        except Exception as e:
            logger.error(f"Key rotation failed: {e}")
            raise EncryptionError(f"Failed to rotate encryption key: {e}")


class SensitiveDataEncryption:
    """
    General-purpose encryption for sensitive data fields.
    """
    
    def __init__(self) -> None:
        """Initialize with token encryption instance."""
        self._encryptor = TokenEncryption()
    
    def encrypt_field(self, value: str) -> str:
        """
        Encrypt a sensitive field value.
        
        Args:
            value: Plain text value to encrypt
            
        Returns:
            Encrypted value
        """
        if not value:
            return value
        
        try:
            # Wrap in dict for consistent handling
            data = {"value": value}
            encrypted = self._encryptor.encrypt_token(data)
            return f"ENC:{encrypted}"  # Prefix to identify encrypted values
        except Exception as e:
            logger.error(f"Field encryption failed: {e}")
            raise
    
    def decrypt_field(self, encrypted_value: str) -> str:
        """
        Decrypt a sensitive field value.
        
        Args:
            encrypted_value: Encrypted value with ENC: prefix
            
        Returns:
            Decrypted plain text value
        """
        if not encrypted_value or not encrypted_value.startswith("ENC:"):
            return encrypted_value
        
        try:
            # Remove prefix and decrypt
            encrypted_data = encrypted_value[4:]
            decrypted = self._encryptor.decrypt_token(encrypted_data)
            decrypted_value = decrypted.get("value", "")
            assert isinstance(decrypted_value, str)
            return decrypted_value
        except Exception as e:
            logger.error(f"Field decryption failed: {e}")
            raise
    
    def encrypt_pii(self, data: Dict[str, Any], fields: list) -> Dict[str, Any]:
        """
        Encrypt specified PII fields in a data dictionary.
        
        Args:
            data: Dictionary containing data
            fields: List of field names to encrypt
            
        Returns:
            Dictionary with specified fields encrypted
        """
        encrypted_data = data.copy()
        
        for field in fields:
            if field in encrypted_data and encrypted_data[field]:
                encrypted_data[field] = self.encrypt_field(str(encrypted_data[field]))
        
        return encrypted_data
    
    def decrypt_pii(self, data: Dict[str, Any], fields: list) -> Dict[str, Any]:
        """
        Decrypt specified PII fields in a data dictionary.
        
        Args:
            data: Dictionary containing encrypted data
            fields: List of field names to decrypt
            
        Returns:
            Dictionary with specified fields decrypted
        """
        decrypted_data = data.copy()
        
        for field in fields:
            if field in decrypted_data and decrypted_data[field]:
                decrypted_data[field] = self.decrypt_field(decrypted_data[field])
        
        return decrypted_data


# Singleton instances
_token_encryption = None
_data_encryption = None


def get_token_encryption() -> TokenEncryption:
    """Get singleton token encryption instance."""
    global _token_encryption
    if _token_encryption is None:
        _token_encryption = TokenEncryption()
    return _token_encryption


def get_data_encryption() -> SensitiveDataEncryption:
    """Get singleton data encryption instance."""
    global _data_encryption
    if _data_encryption is None:
        _data_encryption = SensitiveDataEncryption()
    return _data_encryption


# Utility functions for backward compatibility
def encrypt_token(token: Dict[str, Any]) -> str:
    """Encrypt OAuth token."""
    return get_token_encryption().encrypt_token(token)


def decrypt_token(encrypted_token: str) -> Dict[str, Any]:
    """Decrypt OAuth token."""
    return get_token_encryption().decrypt_token(encrypted_token)
