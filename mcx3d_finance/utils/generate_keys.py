#!/usr/bin/env python3
"""
Utility script to generate secure cryptographic keys for MCX3D Finance.
Generates JWT secret keys and Fernet encryption keys.
"""
import secrets
import string
from cryptography.fernet import Fernet
import argparse
import sys


def generate_secret_key(length: int = 64) -> str:
    """
    Generate a cryptographically secure secret key.
    
    Args:
        length: Length of the key (default 64 characters)
        
    Returns:
        Secure random string suitable for JWT secret
    """
    # Use a combination of letters, digits, and safe special characters
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_fernet_key() -> str:
    """
    Generate a Fernet encryption key for AES-256 encryption.
    
    Returns:
        Base64-encoded Fernet key
    """
    return Fernet.generate_key().decode()


def validate_secret_key(key: str) -> bool:
    """
    Validate that a secret key meets security requirements.
    
    Args:
        key: Secret key to validate
        
    Returns:
        True if key is valid, False otherwise
    """
    if len(key) < 32:
        return False
    
    # Check for at least one uppercase, lowercase, digit, and special character
    has_upper = any(c.isupper() for c in key)
    has_lower = any(c.islower() for c in key)
    has_digit = any(c.isdigit() for c in key)
    has_special = any(c in "!@#$%^&*" for c in key)
    
    return all([has_upper, has_lower, has_digit, has_special])


def main() -> None:
    """Main function to generate keys based on command line arguments."""
    parser = argparse.ArgumentParser(
        description='Generate secure cryptographic keys for MCX3D Finance'
    )
    parser.add_argument(
        '--type',
        choices=['jwt', 'fernet', 'both'],
        default='both',
        help='Type of key to generate'
    )
    parser.add_argument(
        '--length',
        type=int,
        default=64,
        help='Length of JWT secret key (minimum 32)'
    )
    parser.add_argument(
        '--validate',
        type=str,
        help='Validate an existing secret key'
    )
    
    args = parser.parse_args()
    
    # Validate mode
    if args.validate:
        if validate_secret_key(args.validate):
            print("✅ Key is valid and meets security requirements")
            sys.exit(0)
        else:
            print("❌ Key does not meet security requirements:")
            print("   - Must be at least 32 characters long")
            print("   - Must contain uppercase, lowercase, digits, and special characters")
            sys.exit(1)
    
    # Generation mode
    if args.length < 32:
        print("Warning: Key length should be at least 32 characters for security")
        args.length = 32
    
    print("🔐 MCX3D Finance Security Key Generator")
    print("=" * 50)
    
    if args.type in ['jwt', 'both']:
        jwt_key = generate_secret_key(args.length)
        print("\n📋 JWT Secret Key:")
        print(f"SECRET_KEY={jwt_key}")
        print(f"\nLength: {len(jwt_key)} characters")
        print(f"Valid: {'✅' if validate_secret_key(jwt_key) else '❌'}")
    
    if args.type in ['fernet', 'both']:
        fernet_key = generate_fernet_key()
        print("\n🔒 Fernet Encryption Key:")
        print(f"ENCRYPTION_KEY={fernet_key}")
        print("\nThis key is used for AES-256 encryption of sensitive data")
    
    print("\n⚠️  Security Instructions:")
    print("1. Copy these keys to your .env file")
    print("2. NEVER commit these keys to version control")
    print("3. Use different keys for each environment (dev, staging, prod)")
    print("4. Store production keys in a secure key management system")
    print("5. Rotate keys periodically (recommended: every 90 days)")


if __name__ == "__main__":
    main()