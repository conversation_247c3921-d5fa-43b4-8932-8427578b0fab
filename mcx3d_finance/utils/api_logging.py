"""
API-specific Logging Utilities

Provides logging utilities for API operations including request/response logging,
authentication events, and API performance monitoring.
"""

import time
import json
import functools
from typing import Any, Callable, Optional, Dict, Union
from datetime import datetime
from fastapi import Request, Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware

from mcx3d_finance.utils.logging_config import get_api_logger, get_security_logger
from mcx3d_finance.monitoring.structured_logger import (
    get_correlation_id, 
    set_correlation_id,
    correlation_id_context
)

# API logger instances
api_logger = get_api_logger("request_handler")
auth_logger = get_security_logger("authentication")

class LoggingRoute(APIRoute):
    """Custom route class that logs API requests and responses."""
    
    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()
        
        async def logging_route_handler(request: Request) -> Response:
            # Generate or extract correlation ID
            correlation_id = request.headers.get('X-Correlation-ID', str(uuid.uuid4()))
            
            # Set correlation ID in context
            with api_logger.correlation_context(correlation_id=correlation_id):
                # Log request
                await log_api_request(request)
                
                # Time the request
                start_time = time.time()
                
                try:
                    # Call the actual route handler
                    response = await original_route_handler(request)
                    
                    # Calculate duration
                    duration_ms = (time.time() - start_time) * 1000
                    
                    # Log response
                    await log_api_response(request, response, duration_ms)
                    
                    # Add correlation ID to response headers
                    response.headers['X-Correlation-ID'] = correlation_id
                    
                    return response
                    
                except Exception as e:
                    # Calculate duration
                    duration_ms = (time.time() - start_time) * 1000
                    
                    # Log error
                    await log_api_error(request, e, duration_ms)
                    
                    raise
        
        return logging_route_handler

async def log_api_request(request: Request):
    """Log incoming API request."""
    # Extract user info if available
    user_id = None
    user_email = None
    if hasattr(request.state, 'user'):
        user_id = getattr(request.state.user, 'id', None)
        user_email = getattr(request.state.user, 'email', None)
    
    # Extract request details
    log_data = {
        'method': request.method,
        'path': request.url.path,
        'query_params': dict(request.query_params),
        'client_host': request.client.host if request.client else None,
        'user_agent': request.headers.get('user-agent'),
        'user_id': user_id,
        'user_email': user_email,
        'correlation_id': get_correlation_id()
    }
    
    # Log content type and size for POST/PUT requests
    if request.method in ['POST', 'PUT', 'PATCH']:
        log_data['content_type'] = request.headers.get('content-type')
        log_data['content_length'] = request.headers.get('content-length')
    
    api_logger.log_business_event('api_request', **log_data)

async def log_api_response(request: Request, response: Response, duration_ms: float):
    """Log API response."""
    # Determine if response is successful
    is_success = 200 <= response.status_code < 400
    
    log_data = {
        'method': request.method,
        'path': request.url.path,
        'status_code': response.status_code,
        'duration_ms': duration_ms,
        'success': is_success,
        'correlation_id': get_correlation_id()
    }
    
    # Add response size if available
    if 'content-length' in response.headers:
        log_data['response_size'] = int(response.headers['content-length'])
    
    # Log as performance metric
    api_logger.log_performance_metric(
        operation=f"api_{request.method.lower()}_{request.url.path}",
        duration_ms=duration_ms,
        success=is_success,
        **log_data
    )

async def log_api_error(request: Request, error: Exception, duration_ms: float):
    """Log API error."""
    api_logger.log_error(
        error,
        operation=f"api_{request.method.lower()}_{request.url.path}",
        method=request.method,
        path=request.url.path,
        duration_ms=duration_ms,
        correlation_id=get_correlation_id()
    )

def log_api_endpoint(operation_name: Optional[str] = None):
    """
    Decorator to log API endpoint execution.
    
    Args:
        operation_name: Optional operation name
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            op_name = operation_name or func.__name__
            
            try:
                result = await func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                api_logger.log_performance_metric(
                    operation=op_name,
                    duration_ms=duration_ms,
                    success=True,
                    function=func.__name__,
                    module=func.__module__
                )
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                api_logger.log_error(
                    e,
                    operation=op_name,
                    duration_ms=duration_ms,
                    function=func.__name__,
                    module=func.__module__
                )
                
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            op_name = operation_name or func.__name__
            
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                api_logger.log_performance_metric(
                    operation=op_name,
                    duration_ms=duration_ms,
                    success=True,
                    function=func.__name__,
                    module=func.__module__
                )
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                api_logger.log_error(
                    e,
                    operation=op_name,
                    duration_ms=duration_ms,
                    function=func.__name__,
                    module=func.__module__
                )
                
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def log_authentication_event(
    event_type: str,
    user_id: Optional[str] = None,
    success: bool = True,
    method: Optional[str] = None,
    **metadata
):
    """
    Log authentication-related events.
    
    Args:
        event_type: Type of auth event (login, logout, token_refresh, etc.)
        user_id: User identifier
        success: Whether the operation succeeded
        method: Authentication method used
        **metadata: Additional event metadata
    """
    severity = "low" if success else "medium"
    
    auth_logger.log_security_event(
        event_type=event_type,
        severity=severity,
        user_id=user_id,
        success=success,
        method=method,
        **metadata
    )

def log_authorization_event(
    resource: str,
    action: str,
    user_id: str,
    allowed: bool,
    **metadata
):
    """
    Log authorization decisions.
    
    Args:
        resource: Resource being accessed
        action: Action being attempted
        user_id: User attempting the action
        allowed: Whether access was granted
        **metadata: Additional context
    """
    event_type = "authorization_granted" if allowed else "authorization_denied"
    severity = "low" if allowed else "medium"
    
    auth_logger.log_security_event(
        event_type=event_type,
        severity=severity,
        resource=resource,
        action=action,
        user_id=user_id,
        allowed=allowed,
        **metadata
    )

def log_rate_limit_event(
    user_id: Optional[str],
    endpoint: str,
    limit: int,
    window: str,
    **metadata
):
    """
    Log rate limiting events.
    
    Args:
        user_id: User who hit the rate limit
        endpoint: Endpoint that was rate limited
        limit: Rate limit threshold
        window: Time window for the limit
        **metadata: Additional context
    """
    auth_logger.log_security_event(
        event_type="rate_limit_exceeded",
        severity="medium",
        user_id=user_id,
        endpoint=endpoint,
        limit=limit,
        window=window,
        **metadata
    )

class APILoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for comprehensive API logging.
    
    This middleware logs all HTTP requests and responses with
    correlation IDs and performance metrics.
    """
    
    def __init__(self, app, service_name: str = "mcx3d-finance"):
        super().__init__(app)
        self.service_name = service_name
        self.logger = get_api_logger("middleware")
    
    async def dispatch(self, request: Request, call_next):
        # Generate or extract correlation ID
        correlation_id = request.headers.get('X-Correlation-ID')
        if not correlation_id:
            import uuid
            correlation_id = str(uuid.uuid4())
        
        # Set correlation ID in context
        token = set_correlation_id(correlation_id)
        
        # Start timing
        start_time = time.time()
        
        try:
            # Log request
            self.logger.log_business_event(
                'http_request',
                method=request.method,
                path=str(request.url.path),
                query=str(request.url.query),
                correlation_id=correlation_id
            )
            
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000
            
            # Log response
            self.logger.log_performance_metric(
                operation='http_request',
                duration_ms=duration_ms,
                success=response.status_code < 400,
                method=request.method,
                path=str(request.url.path),
                status_code=response.status_code,
                correlation_id=correlation_id
            )
            
            # Add correlation ID to response
            response.headers['X-Correlation-ID'] = correlation_id
            
            return response
            
        except Exception as e:
            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000
            
            # Log error
            self.logger.log_error(
                e,
                operation='http_request',
                method=request.method,
                path=str(request.url.path),
                duration_ms=duration_ms,
                correlation_id=correlation_id
            )
            
            raise
        finally:
            # Reset correlation ID
            correlation_id_context.reset(token)

# Convenience functions for common API logging scenarios
def log_api_validation_error(
    endpoint: str,
    validation_errors: Dict[str, Any],
    request_data: Optional[Dict[str, Any]] = None
):
    """Log API validation errors."""
    api_logger.log_business_event(
        'api_validation_error',
        endpoint=endpoint,
        validation_errors=validation_errors,
        request_data=request_data,
        correlation_id=get_correlation_id()
    )

def log_api_business_error(
    endpoint: str,
    error_code: str,
    error_message: str,
    **context
):
    """Log business logic errors in API."""
    api_logger.log_business_event(
        'api_business_error',
        endpoint=endpoint,
        error_code=error_code,
        error_message=error_message,
        correlation_id=get_correlation_id(),
        **context
    )

import asyncio
import uuid