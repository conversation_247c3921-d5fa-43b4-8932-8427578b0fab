"""
Field-level encryption and data protection utilities.
Provides encryption for sensitive data at rest and in transit.
"""
from mcx3d_finance.core.logging_factory import LoggerFactory
from typing import Any, Dict, List, Optional, Union, Type
from dataclasses import dataclass, field
from enum import Enum
import json
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
import secrets

from mcx3d_finance.core.config import get_security_config

logger = LoggerFactory.get_logger(__name__, domain='utils')


class EncryptionLevel(Enum):
    """Encryption levels for different data sensitivity."""
    NONE = "none"
    STANDARD = "standard"  # Fernet (AES-128)
    HIGH = "high"  # AES-256-GCM
    MAXIMUM = "maximum"  # AES-256-GCM with key derivation


class DataClassification(Enum):
    """Data classification levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"  # PII, financial data
    TOP_SECRET = "top_secret"  # Encryption keys, passwords


@dataclass
class EncryptedField:
    """Container for encrypted field data."""
    ciphertext: str
    encryption_level: str
    algorithm: str
    key_version: int
    nonce: Optional[str] = None
    tag: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class EncryptionContext:
    def __init__(self, protection: 'DataProtection', classification: DataClassification) -> None:
        self.protection = protection
        self.classification = classification
        self.encrypted_fields: List[str] = []

    def encrypt(self, value: Any, field_name: Optional[str] = None) -> Any:
        encrypted = self.protection.encrypt_field(
            value,
            classification=self.classification
        )
        
        if field_name:
            self.encrypted_fields.append(field_name)
        
        return encrypted

    def __enter__(self) -> 'EncryptionContext':
        return self

    def __exit__(self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[Any]) -> None:
        if self.encrypted_fields:
            logger.info(
                f"Encrypted {len(self.encrypted_fields)} fields "
                f"with {self.classification.value} classification"
            )


class DataProtection:
    """
    Field-level encryption and data protection.
    
    Features:
    - Multiple encryption algorithms
    - Key rotation support
    - Format-preserving encryption
    - Tokenization
    - Data masking
    - Secure deletion
    """
    
    def __init__(self, custom_key: Optional[str] = None) -> None:
        """
        Initialize data protection.
        
        Args:
            custom_key: Optional custom encryption key
        """
        self.security_config = get_security_config()
        
        # Load encryption keys
        self.keys = self._load_encryption_keys(custom_key)
        self.current_key_version = len(self.keys) - 1
        
        # Encryption settings
        self.default_level = EncryptionLevel.STANDARD
        self.enable_key_rotation = self.security_config.get("enable_key_rotation", True)
        self.rotation_interval_days = self.security_config.get("key_rotation_days", 90)
        
        # Initialize encryptors
        self._init_encryptors()
    
    def _load_encryption_keys(self, custom_key: Optional[str]) -> List[bytes]:
        """Load encryption keys with support for key rotation."""
        keys = []
        
        # Primary key
        if custom_key:
            keys.append(custom_key.encode())
        else:
            primary_key = self.security_config.get("encryption_key")
            if not primary_key:
                raise ValueError("No encryption key configured")
            keys.append(primary_key.encode())
        
        # Load rotated keys for decryption
        rotated_keys = self.security_config.get("rotated_encryption_keys", [])
        for key in rotated_keys:
            keys.append(key.encode())
        
        return keys
    
    def _init_encryptors(self) -> None:
        """Initialize encryption handlers."""
        # Fernet encryptors for each key version
        self.fernet_encryptors = {}
        for version, key in enumerate(self.keys):
            try:
                self.fernet_encryptors[version] = Fernet(key)
            except Exception:
                # Key might not be Fernet format, skip
                pass
        
        # AES-GCM encryptors
        self.aesgcm_encryptors = {}
        for version, key in enumerate(self.keys):
            # Derive 256-bit key from input
            key_256 = self._derive_key(key, salt=b"aesgcm", length=32)
            self.aesgcm_encryptors[version] = AESGCM(key_256)
    
    def _derive_key(self, key: bytes, salt: bytes, length: int = 32) -> bytes:
        """Derive encryption key using PBKDF2."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=length,
            salt=salt,
            iterations=100000,
        )
        return kdf.derive(key)
    
    def encrypt_field(
        self,
        value: Any,
        level: Optional[EncryptionLevel] = None,
        classification: Optional[DataClassification] = None
    ) -> Union[EncryptedField, str, None]:
        """
        Encrypt a field value.
        
        Args:
            value: Value to encrypt
            level: Encryption level (auto-determined if not specified)
            classification: Data classification
            
        Returns:
            Encrypted field data
        """
        if value is None:
            return None
        
        # Determine encryption level
        if level is None:
            level = self._determine_encryption_level(value, classification)
        
        if level == EncryptionLevel.NONE:
            return str(value)
        
        # Convert to string
        if not isinstance(value, str):
            value_str = json.dumps(value)
        else:
            value_str = value
        
        # Encrypt based on level
        if level == EncryptionLevel.STANDARD:
            return self._encrypt_standard(value_str)
        elif level in (EncryptionLevel.HIGH, EncryptionLevel.MAXIMUM):
            return self._encrypt_high(value_str, level)
        
        return value_str
    
    def decrypt_field(
        self,
        encrypted_data: Union[EncryptedField, str, Dict[str, Any]]
    ) -> Any:
        """
        Decrypt a field value.
        
        Args:
            encrypted_data: Encrypted field data
            
        Returns:
            Decrypted value
        """
        if encrypted_data is None:
            return None
        
        # Handle plain strings
        if isinstance(encrypted_data, str):
            return encrypted_data
        
        # Convert dict to EncryptedField
        if isinstance(encrypted_data, dict):
            encrypted_data = EncryptedField(**encrypted_data)
        
        # Decrypt based on algorithm
        if encrypted_data.algorithm == "fernet":
            return self._decrypt_fernet(encrypted_data)
        elif encrypted_data.algorithm in ("aesgcm", "aesgcm-pbkdf2"):
            return self._decrypt_aesgcm(encrypted_data)
        
        raise ValueError(f"Unknown encryption algorithm: {encrypted_data.algorithm}")
    
    def _determine_encryption_level(
        self,
        value: Any,
        classification: Optional[DataClassification]
    ) -> EncryptionLevel:
        """Determine appropriate encryption level."""
        if classification:
            if classification == DataClassification.TOP_SECRET:
                return EncryptionLevel.MAXIMUM
            elif classification == DataClassification.RESTRICTED:
                return EncryptionLevel.HIGH
            elif classification == DataClassification.CONFIDENTIAL:
                return EncryptionLevel.STANDARD
        
        # Auto-detect based on content
        value_str = str(value).lower()
        
        # High sensitivity patterns
        high_sensitivity = [
            "password", "secret", "key", "token",
            "ssn", "social_security", "tax_id"
        ]
        
        for pattern in high_sensitivity:
            if pattern in value_str:
                return EncryptionLevel.HIGH
        
        # Standard sensitivity patterns
        standard_sensitivity = [
            "email", "phone", "address", "salary",
            "account", "balance"
        ]
        
        for pattern in standard_sensitivity:
            if pattern in value_str:
                return EncryptionLevel.STANDARD
        
        return self.default_level
    
    def _encrypt_standard(self, value: str) -> EncryptedField:
        """Encrypt using Fernet (AES-128 in CBC mode)."""
        encryptor = self.fernet_encryptors.get(self.current_key_version)
        if not encryptor:
            raise ValueError("No Fernet encryptor available")
        
        ciphertext = encryptor.encrypt(value.encode())
        
        return EncryptedField(
            ciphertext=base64.urlsafe_b64encode(ciphertext).decode(),
            encryption_level=EncryptionLevel.STANDARD.value,
            algorithm="fernet",
            key_version=self.current_key_version
        )
    
    def _encrypt_high(self, value: str, level: EncryptionLevel) -> EncryptedField:
        """Encrypt using AES-256-GCM."""
        encryptor = self.aesgcm_encryptors[self.current_key_version]
        
        # Generate nonce
        nonce = secrets.token_bytes(12)
        
        # Additional authenticated data
        aad = json.dumps({
            "level": level.value,
            "version": self.current_key_version,
            "algorithm": "aesgcm"
        }).encode()
        
        # Encrypt
        ciphertext = encryptor.encrypt(nonce, value.encode(), aad)
        
        return EncryptedField(
            ciphertext=base64.urlsafe_b64encode(ciphertext).decode(),
            encryption_level=level.value,
            algorithm="aesgcm" if level == EncryptionLevel.HIGH else "aesgcm-pbkdf2",
            key_version=self.current_key_version,
            nonce=base64.urlsafe_b64encode(nonce).decode()
        )
    
    def _decrypt_fernet(self, encrypted: EncryptedField) -> str:
        """Decrypt Fernet encrypted data."""
        encryptor = self.fernet_encryptors.get(encrypted.key_version)
        if not encryptor:
            raise ValueError(f"No encryptor for key version {encrypted.key_version}")
        
        ciphertext = base64.urlsafe_b64decode(encrypted.ciphertext)
        plaintext = encryptor.decrypt(ciphertext)
        
        return plaintext.decode()
    
    def _decrypt_aesgcm(self, encrypted: EncryptedField) -> str:
        """Decrypt AES-GCM encrypted data."""
        encryptor = self.aesgcm_encryptors.get(encrypted.key_version)
        if not encryptor:
            raise ValueError(f"No encryptor for key version {encrypted.key_version}")
        
        # Decode components
        ciphertext = base64.urlsafe_b64decode(encrypted.ciphertext)
        if not encrypted.nonce:
            raise ValueError("Nonce is required for AES-GCM decryption")
        nonce = base64.urlsafe_b64decode(encrypted.nonce)
        
        # Reconstruct AAD
        aad = json.dumps({
            "level": encrypted.encryption_level,
            "version": encrypted.key_version,
            "algorithm": encrypted.algorithm
        }).encode()
        
        # Decrypt
        plaintext = encryptor.decrypt(nonce, ciphertext, aad)
        
        return plaintext.decode()
    
    def mask_data(
        self,
        value: str,
        mask_type: str = "partial",
        show_first: int = 4,
        show_last: int = 4
    ) -> str:
        """
        Mask sensitive data for display.
        
        Args:
            value: Value to mask
            mask_type: Type of masking (partial, full, email, phone)
            show_first: Number of characters to show at start
            show_last: Number of characters to show at end
            
        Returns:
            Masked value
        """
        if not value:
            return value
        
        if mask_type == "full":
            return "*" * len(value)
        
        elif mask_type == "partial":
            if len(value) <= show_first + show_last:
                return "*" * len(value)
            
            masked_middle = "*" * (len(value) - show_first - show_last)
            return value[:show_first] + masked_middle + value[-show_last:]
        
        elif mask_type == "email":
            if "@" in value:
                local, domain = value.split("@", 1)
                if len(local) > 2:
                    masked_local = local[0] + "*" * (len(local) - 2) + local[-1]
                else:
                    masked_local = "*" * len(local)
                return f"{masked_local}@{domain}"
            
        elif mask_type == "phone":
            # Keep country code and last 4 digits
            if len(value) > 10:
                return value[:3] + "*" * (len(value) - 7) + value[-4:]
            elif len(value) > 4:
                return "*" * (len(value) - 4) + value[-4:]
        
        return mask_type
    
    def tokenize(self, value: str, token_type: str = "random") -> str:
        """
        Tokenize sensitive data.
        
        Args:
            value: Value to tokenize
            token_type: Type of token (random, format_preserving)
            
        Returns:
            Token that can be used to retrieve original value
        """
        # Generate token
        if token_type == "random":
            token = f"tok_{secrets.token_urlsafe(24)}"
        else:  # format_preserving
            # Simple format preservation (in production, use FF3-1)
            token = "".join(
                secrets.choice("0123456789") if c.isdigit() else
                secrets.choice("abcdefghijklmnopqrstuvwxyz") if c.islower() else
                secrets.choice("ABCDEFGHIJKLMNOPQRSTUVWXYZ") if c.isupper() else c
                for c in value
            )
        
        # Store mapping (in production, use secure token vault)
        # This is simplified - do not use in production
        token_key = f"token:{token}"
        # Store encrypted value with token mapping
        
        logger.info(f"Generated token for value: {token}")
        
        return token
    
    def secure_delete(self, data: Any) -> bool:
        """
        Securely delete sensitive data.
        
        Args:
            data: Data to securely delete
            
        Returns:
            Success status
        """
        try:
            if isinstance(data, str):
                # Overwrite string in memory (Python specific)
                data_len = len(data)
                data = secrets.token_urlsafe(data_len)
                
            elif isinstance(data, bytearray):
                # Overwrite bytearray
                for i in range(len(data)):
                    data[i] = secrets.randbits(8)
            elif isinstance(data, bytes):
                # bytes are immutable, can't overwrite in place
                # best effort is to hope it gets garbage collected
                pass
            
            elif isinstance(data, dict):
                # Recursively secure delete dict values
                for key in list(data.keys()):
                    self.secure_delete(data[key])
                    del data[key]
            
            elif isinstance(data, list):
                # Recursively secure delete list items
                for i in range(len(data)):
                    self.secure_delete(data[i])
                data.clear()
            
            # Force garbage collection
            import gc
            gc.collect()
            
            return True
            
        except Exception as e:
            logger.error(f"Secure delete failed: {e}")
            return False
    
    def create_encryption_context(
        self,
        classification: DataClassification = DataClassification.INTERNAL
    ):
        """
        Create context manager for automatic encryption.
        
        Usage:
            with data_protection.create_encryption_context(DataClassification.RESTRICTED) as ctx:
                encrypted_ssn = ctx.encrypt(ssn)
                encrypted_salary = ctx.encrypt(salary)
        """
        class EncryptionContext:
            def __init__(self, protection: 'DataProtection', classification: DataClassification) -> None:
                self.protection = protection
                self.classification = classification
                self.encrypted_fields: List[str] = []
            
            def encrypt(self, value: Any, field_name: Optional[str] = None) -> Any:
                encrypted = self.protection.encrypt_field(
                    value,
                    classification=self.classification
                )
                
                if field_name:
                    self.encrypted_fields.append(field_name)
                
                return encrypted
            
            def __enter__(self) -> 'EncryptionContext':
                return self
            
            def __exit__(self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[Any]) -> None:
                if self.encrypted_fields:
                    logger.info(
                        f"Encrypted {len(self.encrypted_fields)} fields "
                        f"with {self.classification.value} classification"
                    )
        
        return EncryptionContext(self, classification)
    
    def rotate_encryption_key(self, new_key: str) -> bool:
        """
        Rotate encryption keys.
        
        Args:
            new_key: New encryption key
            
        Returns:
            Success status
        """
        try:
            # Add new key
            self.keys.append(new_key.encode())
            self.current_key_version = len(self.keys) - 1
            
            # Reinitialize encryptors
            self._init_encryptors()
            
            logger.info(f"Rotated to encryption key version {self.current_key_version}")
            
            return True
            
        except Exception as e:
            logger.error(f"Key rotation failed: {e}")
            return False
    
    def re_encrypt_field(
        self,
        encrypted_data: Union[EncryptedField, Dict[str, Any]],
        target_version: Optional[int] = None
    ) -> EncryptedField:
        """
        Re-encrypt field with new key version.
        
        Args:
            encrypted_data: Currently encrypted data
            target_version: Target key version (current if not specified)
            
        Returns:
            Re-encrypted field data
        """
        # Decrypt with old key
        plaintext = self.decrypt_field(encrypted_data)
        
        # Temporarily set key version
        original_version = self.current_key_version
        if target_version is not None:
            self.current_key_version = target_version
        
        try:
            # Re-encrypt with new key
            if isinstance(encrypted_data, dict):
                level = EncryptionLevel(encrypted_data.get("encryption_level"))
            else:
                level = EncryptionLevel(encrypted_data.encryption_level)
            
            re_encrypted = self.encrypt_field(plaintext, level=level)
            if not isinstance(re_encrypted, EncryptedField):
                raise TypeError("Re-encryption failed to return EncryptedField")
            return re_encrypted
            
        finally:
            self.current_key_version = original_version


# Global data protection instance
data_protection = DataProtection()