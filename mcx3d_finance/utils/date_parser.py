"""
Date parsing utilities for handling various datetime formats from external APIs.
"""

from datetime import datetime, timezone
from typing import Optional
import re
from mcx3d_finance.core.logging_factory import LoggerFactory

logger = LoggerFactory.get_logger(__name__, domain='utils')


def parse_date_safe(date_string: Optional[str]) -> Optional[datetime]:
    """
    Parse a date string safely, handling various formats from Xero and other APIs.
    
    Args:
        date_string: Date string in various formats
        
    Returns:
        Parsed datetime object or None if parsing fails
    """
    if not date_string or not isinstance(date_string, str):
        return None
    
    # Remove any extra whitespace
    date_string = date_string.strip()
    
    # Handle empty strings
    if not date_string:
        return None
    
    # List of common date formats to try
    formats = [
        # ISO formats
        "%Y-%m-%dT%H:%M:%S.%fZ",  # 2023-12-01T10:30:45.123Z
        "%Y-%m-%dT%H:%M:%SZ",     # 2023-12-01T10:30:45Z
        "%Y-%m-%dT%H:%M:%S.%f",   # 2023-12-01T10:30:45.123
        "%Y-%m-%dT%H:%M:%S",      # 2023-12-01T10:30:45
        "%Y-%m-%d %H:%M:%S.%f",   # 2023-12-01 10:30:45.123
        "%Y-%m-%d %H:%M:%S",      # 2023-12-01 10:30:45
        "%Y-%m-%d",               # 2023-12-01
        
        # Alternative formats
        "%d/%m/%Y %H:%M:%S",      # 01/12/2023 10:30:45
        "%d/%m/%Y",               # 01/12/2023
        "%m/%d/%Y %H:%M:%S",      # 12/01/2023 10:30:45
        "%m/%d/%Y",               # 12/01/2023
        
        # Xero specific formats
        "/Date(%s)/",             # /Date(1640995200000)/
    ]
    
    # Handle Xero's .NET DateTime format: /Date(timestamp)/ or /Date(timestamp+0000)/
    if date_string.startswith("/Date(") and date_string.endswith(")/"):
        try:
            # Extract timestamp from /Date(1640995200000)/ or /Date(1640995200000+0000)/
            timestamp_match = re.search(r'/Date\((\d+)(?:[+-]\d{4})?\)/', date_string)
            if timestamp_match:
                timestamp = int(timestamp_match.group(1)) / 1000  # Convert to seconds
                return datetime.fromtimestamp(timestamp, tz=timezone.utc)
        except (ValueError, TypeError) as e:
            logger.warning(f"Could not parse Xero timestamp '{date_string}': {e}")
    
    # Try each format
    for fmt in formats:
        try:
            # Parse the datetime
            dt = datetime.strptime(date_string, fmt)
            
            # If no timezone info, assume UTC
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            
            return dt
            
        except ValueError:
            continue
    
    # Try to handle ISO format with timezone offset
    try:
        # Handle formats like: 2023-12-01T10:30:45+00:00 or 2023-12-01T10:30:45-05:00
        iso_pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})([+-]\d{2}:\d{2}|Z)?'
        match = re.match(iso_pattern, date_string)
        
        if match:
            base_date = match.group(1)
            timezone_part = match.group(2)
            
            # Parse base datetime
            dt = datetime.strptime(base_date, "%Y-%m-%dT%H:%M:%S")
            
            # Handle timezone
            if timezone_part == 'Z' or not timezone_part:
                dt = dt.replace(tzinfo=timezone.utc)
            else:
                # For now, just treat as UTC (can be enhanced later for proper timezone parsing)
                dt = dt.replace(tzinfo=timezone.utc)
            
            return dt
            
    except Exception as e:
        logger.debug(f"ISO pattern matching failed for '{date_string}': {e}")
    
    # If all else fails, try python-dateutil if available
    try:
        from dateutil import parser  # type: ignore
        dt = parser.parse(date_string)
        
        # If no timezone info, assume UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
            
        return dt
        
    except ImportError:
        logger.debug("python-dateutil not available for advanced date parsing")
    except Exception as e:
        logger.debug(f"dateutil parsing failed for '{date_string}': {e}")
    
    logger.warning(f"Could not parse date string: '{date_string}'")
    return None


def format_date_for_xero(dt: datetime) -> str:
    """
    Format a datetime object for Xero API calls.
    
    Args:
        dt: Datetime object to format
        
    Returns:
        ISO formatted date string
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


def format_date_for_display(dt: datetime, format_type: str = "short") -> str:
    """
    Format a datetime object for display purposes.
    
    Args:
        dt: Datetime object to format
        format_type: Type of format ("short", "medium", "long")
        
    Returns:
        Formatted date string
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    if format_type == "short":
        return dt.strftime("%Y-%m-%d")
    elif format_type == "medium":
        return dt.strftime("%Y-%m-%d %H:%M")
    elif format_type == "long":
        return dt.strftime("%Y-%m-%d %H:%M:%S %Z")
    else:
        return dt.isoformat()