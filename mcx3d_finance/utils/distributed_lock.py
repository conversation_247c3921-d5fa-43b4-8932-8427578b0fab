"""
Distributed locking implementation using Redis.

Provides a Redis-based distributed lock implementation to prevent
race conditions in concurrent operations like token refresh.
"""

from mcx3d_finance.core.logging_factory import LoggerFactory
import time
import uuid
from typing import Optional, Union, Iterator
from contextlib import contextmanager
import redis
from redis.exceptions import RedisError

logger = LoggerFactory.get_logger(__name__, domain='utils')


class DistributedLock:
    """
    Redis-based distributed lock implementation using single-instance Redlock pattern.
    
    This implementation provides:
    - Atomic lock acquisition and release
    - Automatic lock expiration to prevent deadlocks
    - Lock extension for long-running operations
    - Context manager support for easy usage
    """
    
    def __init__(
        self,
        redis_client: redis.Redis,
        key: str,
        timeout: int = 10,
        blocking: bool = True,
        blocking_timeout: Optional[int] = None,
        auto_extend: bool = False,
        extend_interval: int = 5
    ) -> None:
        """
        Initialize distributed lock.
        
        Args:
            redis_client: Redis client instance
            key: Lock key name
            timeout: Lock expiration time in seconds (default: 10)
            blocking: Whether to block waiting for lock (default: True)
            blocking_timeout: Max time to wait for lock in seconds (default: None - wait forever)
            auto_extend: Whether to automatically extend lock (default: False)
            extend_interval: Interval to extend lock in seconds (default: 5)
        """
        self.redis = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.blocking = blocking
        self.blocking_timeout = blocking_timeout
        self.auto_extend = auto_extend
        self.extend_interval = extend_interval
        
        # Generate unique identifier for this lock instance
        self.identifier = str(uuid.uuid4())
        self._lock_acquired = False
        self._extension_thread = None
    
    def acquire(self, retry_interval: float = 0.1) -> bool:
        """
        Acquire the distributed lock.
        
        Args:
            retry_interval: Time to wait between retries in seconds
            
        Returns:
            True if lock acquired, False otherwise
        """
        start_time = time.time()
        
        while True:
            try:
                # Try to set lock with NX (only if not exists) and EX (expiration)
                acquired = self.redis.set(
                    self.key,
                    self.identifier,
                    nx=True,
                    ex=self.timeout
                )
                
                if acquired:
                    self._lock_acquired = True
                    logger.debug(f"Acquired lock {self.key} with identifier {self.identifier}")
                    
                    # Start auto-extension thread if enabled
                    if self.auto_extend:
                        self._start_extension_thread()
                    
                    return True
                
                # If not blocking, return immediately
                if not self.blocking:
                    return False
                
                # Check if we've exceeded blocking timeout
                if self.blocking_timeout is not None:
                    elapsed = time.time() - start_time
                    if elapsed >= self.blocking_timeout:
                        logger.warning(
                            f"Failed to acquire lock {self.key} after {self.blocking_timeout}s"
                        )
                        return False
                
                # Wait before retrying
                time.sleep(retry_interval)
                
            except RedisError as e:
                logger.error(f"Redis error acquiring lock {self.key}: {e}")
                return False
    
    def release(self) -> bool:
        """
        Release the distributed lock.
        
        Uses Lua script to ensure atomic release - only releases if we own the lock.
        
        Returns:
            True if lock was released, False otherwise
        """
        if not self._lock_acquired:
            return False
        
        # Stop auto-extension if running
        if self._extension_thread:
            self._stop_extension_thread()
        
        # Lua script to atomically check and delete lock
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        
        try:
            released = self.redis.eval(lua_script, 1, self.key, self.identifier)
            
            if released:
                self._lock_acquired = False
                logger.debug(f"Released lock {self.key}")
                return True
            else:
                logger.warning(
                    f"Failed to release lock {self.key} - lock expired or owned by another process"
                )
                return False
                
        except RedisError as e:
            logger.error(f"Redis error releasing lock {self.key}: {e}")
            return False
    
    def extend(self, additional_time: Optional[int] = None) -> bool:
        """
        Extend the lock expiration time.
        
        Args:
            additional_time: Additional seconds to extend (default: self.timeout)
            
        Returns:
            True if extended successfully, False otherwise
        """
        if not self._lock_acquired:
            return False
        
        extend_time = additional_time or self.timeout
        
        # Lua script to atomically check ownership and extend
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("expire", KEYS[1], ARGV[2])
        else
            return 0
        end
        """
        
        try:
            extended = self.redis.eval(
                lua_script,
                1,
                self.key,
                self.identifier,
                str(extend_time)
            )
            
            if extended:
                logger.debug(f"Extended lock {self.key} by {extend_time}s")
                return True
            else:
                logger.warning(f"Failed to extend lock {self.key} - not owner")
                return False
                
        except RedisError as e:
            logger.error(f"Redis error extending lock {self.key}: {e}")
            return False
    
    def is_locked(self) -> bool:
        """
        Check if the lock is currently held by any process.
        
        Returns:
            True if locked, False otherwise
        """
        try:
            return bool(self.redis.exists(self.key))
        except RedisError as e:
            logger.error(f"Redis error checking lock {self.key}: {e}")
            return False
    
    def is_owned(self) -> bool:
        """
        Check if this instance owns the lock.
        
        Returns:
            True if this instance owns the lock, False otherwise
        """
        if not self._lock_acquired:
            return False
        
        try:
            current_value = self.redis.get(self.key)
            if current_value:
                return current_value == self.identifier.encode('utf-8')
            return False
        except RedisError as e:
            logger.error(f"Redis error checking lock ownership {self.key}: {e}")
            return False
    
    def _start_extension_thread(self) -> None:
        """Start background thread to automatically extend lock."""
        import threading
        
        def extend_loop() -> None:
            while self._lock_acquired:
                time.sleep(self.extend_interval)
                if self._lock_acquired:
                    self.extend()
        
        self._extension_thread = threading.Thread(
            target=extend_loop,
            daemon=True,
            name=f"lock-extension-{self.key}"
        )
        self._extension_thread.start()
    
    def _stop_extension_thread(self) -> None:
        """Stop the auto-extension thread."""
        self._lock_acquired = False
        if self._extension_thread:
            self._extension_thread.join(timeout=1)
            self._extension_thread = None
    
    def __enter__(self) -> "DistributedLock":
        """Context manager entry - acquire lock."""
        acquired = self.acquire()
        if not acquired:
            raise RuntimeError(f"Failed to acquire lock {self.key}")
        return self
    
    def __exit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[object]) -> None:
        """Context manager exit - release lock."""
        self.release()


@contextmanager
def distributed_lock(
    redis_client: redis.Redis,
    key: str,
    timeout: int = 10,
    blocking: bool = True,
    blocking_timeout: Optional[int] = None,
    auto_extend: bool = False
) -> Iterator["DistributedLock"]:
    """
    Context manager for distributed locking.
    
    Usage:
        with distributed_lock(redis_client, "my_resource", timeout=30):
            # Critical section
            perform_exclusive_operation()
    
    Args:
        redis_client: Redis client instance
        key: Lock key name
        timeout: Lock expiration time in seconds
        blocking: Whether to block waiting for lock
        blocking_timeout: Max time to wait for lock
        auto_extend: Whether to automatically extend lock
        
    Yields:
        DistributedLock instance
        
    Raises:
        RuntimeError: If lock cannot be acquired
    """
    lock = DistributedLock(
        redis_client,
        key,
        timeout=timeout,
        blocking=blocking,
        blocking_timeout=blocking_timeout,
        auto_extend=auto_extend
    )
    
    acquired = lock.acquire()
    if not acquired:
        raise RuntimeError(f"Failed to acquire distributed lock for {key}")
    
    try:
        yield lock
    finally:
        lock.release()


class TokenRefreshLock:
    """
    Specialized distributed lock for token refresh operations.
    
    Provides additional safety features specific to OAuth token refresh:
    - Longer default timeout for API calls
    - Automatic extension for long operations
    - Logging of lock contention
    """
    
    def __init__(self, redis_client: redis.Redis, organization_id: Union[str, int]) -> None:
        """
        Initialize token refresh lock.
        
        Args:
            redis_client: Redis client instance
            organization_id: Organization ID to lock
        """
        self.lock = DistributedLock(
            redis_client,
            f"xero_token_refresh:{organization_id}",
            timeout=30,  # 30 seconds for token refresh
            blocking=True,
            blocking_timeout=60,  # Wait up to 60 seconds
            auto_extend=True,  # Auto-extend for long operations
            extend_interval=10  # Extend every 10 seconds
        )
        self.organization_id = organization_id
    
    def __enter__(self) -> "TokenRefreshLock":
        """Acquire token refresh lock."""
        start_time = time.time()
        acquired = self.lock.acquire()
        
        if acquired:
            wait_time = time.time() - start_time
            if wait_time > 1.0:
                logger.info(
                    f"Token refresh lock acquired for org {self.organization_id} "
                    f"after waiting {wait_time:.2f}s"
                )
        else:
            raise RuntimeError(
                f"Failed to acquire token refresh lock for organization {self.organization_id}"
            )
        
        return self
    
    def __exit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[object]) -> None:
        """Release token refresh lock."""
        self.lock.release()