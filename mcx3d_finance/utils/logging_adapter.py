"""
Logging Adapter for Legacy Code Migration

This module provides a drop-in replacement for standard Python logging
that automatically uses our structured logging system.
"""

import logging
from typing import Any, Dict, Optional
import traceback
from datetime import datetime

from mcx3d_finance.utils.logging_config import (
    get_api_logger,
    get_core_logger,
    get_integration_logger,
    get_db_logger,
    get_task_logger,
    get_security_logger,
    get_domain_logger
)
from mcx3d_finance.monitoring.structured_logger import get_correlation_id

class StructuredLoggingAdapter:
    """
    Adapter that mimics Python's logging interface but uses structured logging.
    
    This allows gradual migration from standard logging to structured logging
    without breaking existing code.
    """
    
    def __init__(self, name: str):
        self.name = name
        self._determine_domain()
        self._structured_logger = self._get_structured_logger()
    
    def _determine_domain(self):
        """Determine the domain based on the logger name."""
        if 'mcx3d_finance.api' in self.name:
            self.domain = 'api'
            self.component = self.name.replace('mcx3d_finance.api.', '')
        elif 'mcx3d_finance.core' in self.name:
            self.domain = 'core'
            self.component = self.name.replace('mcx3d_finance.core.', '')
        elif 'mcx3d_finance.integrations' in self.name:
            self.domain = 'integrations'
            self.component = self.name.replace('mcx3d_finance.integrations.', '')
        elif 'mcx3d_finance.db' in self.name:
            self.domain = 'db'
            self.component = self.name.replace('mcx3d_finance.db.', '')
        elif 'mcx3d_finance.tasks' in self.name:
            self.domain = 'tasks'
            self.component = self.name.replace('mcx3d_finance.tasks.', '')
        elif 'mcx3d_finance.security' in self.name:
            self.domain = 'security'
            self.component = self.name.replace('mcx3d_finance.security.', '')
        else:
            self.domain = 'general'
            self.component = self.name
    
    def _get_structured_logger(self):
        """Get the appropriate structured logger based on domain."""
        if self.domain == 'api':
            return get_api_logger(self.component)
        elif self.domain == 'core':
            return get_core_logger(self.component)
        elif self.domain == 'integrations':
            return get_integration_logger(self.component)
        elif self.domain == 'db':
            return get_db_logger(self.component)
        elif self.domain == 'tasks':
            return get_task_logger(self.component)
        elif self.domain == 'security':
            return get_security_logger(self.component)
        else:
            return get_domain_logger(self.domain, self.component)
    
    def _log(self, level: str, msg: str, *args, **kwargs):
        """Internal logging method that converts to structured format."""
        # Format message with args if provided
        if args:
            try:
                msg = msg % args
            except Exception:
                # If formatting fails, just concatenate
                msg = f"{msg} {args}"
        
        # Extract extra context
        extra = kwargs.get('extra', {})
        exc_info = kwargs.get('exc_info', False)
        
        # Build structured log entry
        log_data = {
            'message': msg,
            'logger_name': self.name,
            'timestamp': datetime.utcnow().isoformat(),
            'correlation_id': get_correlation_id(),
            **extra
        }
        
        # Add exception info if requested
        if exc_info:
            if isinstance(exc_info, Exception):
                log_data['error_type'] = type(exc_info).__name__
                log_data['error_message'] = str(exc_info)
                log_data['stack_trace'] = traceback.format_exc()
            elif exc_info is True:
                log_data['stack_trace'] = traceback.format_exc()
        
        # Log at appropriate level
        logger = self._structured_logger.logger
        if level == 'debug':
            logger.debug("legacy_log", **log_data)
        elif level == 'info':
            logger.info("legacy_log", **log_data)
        elif level == 'warning':
            logger.warning("legacy_log", **log_data)
        elif level == 'error':
            logger.error("legacy_log", **log_data)
        elif level == 'critical':
            logger.critical("legacy_log", **log_data)
    
    def debug(self, msg: str, *args, **kwargs):
        """Log a debug message."""
        self._log('debug', msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        """Log an info message."""
        self._log('info', msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """Log a warning message."""
        self._log('warning', msg, *args, **kwargs)
    
    def warn(self, msg: str, *args, **kwargs):
        """Alias for warning (deprecated in Python logging)."""
        self.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """Log an error message."""
        self._log('error', msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """Log a critical message."""
        self._log('critical', msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        """Log an exception with traceback."""
        kwargs['exc_info'] = True
        self.error(msg, *args, **kwargs)
    
    def log(self, level: int, msg: str, *args, **kwargs):
        """Log at a specific numeric level."""
        if level <= logging.DEBUG:
            self.debug(msg, *args, **kwargs)
        elif level <= logging.INFO:
            self.info(msg, *args, **kwargs)
        elif level <= logging.WARNING:
            self.warning(msg, *args, **kwargs)
        elif level <= logging.ERROR:
            self.error(msg, *args, **kwargs)
        else:
            self.critical(msg, *args, **kwargs)
    
    def setLevel(self, level):
        """Set logging level (no-op for compatibility)."""
        pass
    
    def addHandler(self, handler):
        """Add handler (no-op for compatibility)."""
        pass
    
    def removeHandler(self, handler):
        """Remove handler (no-op for compatibility)."""
        pass

class LoggerFactory:
    """Factory for creating structured logging adapters."""
    
    _loggers: Dict[str, StructuredLoggingAdapter] = {}
    
    @classmethod
    def get_logger(cls, name: str) -> StructuredLoggingAdapter:
        """
        Get or create a logger with the given name.
        
        Args:
            name: Logger name (usually __name__)
        
        Returns:
            StructuredLoggingAdapter instance
        """
        if name not in cls._loggers:
            cls._loggers[name] = StructuredLoggingAdapter(name)
        return cls._loggers[name]

def patch_logging():
    """
    Monkey-patch the standard logging module to use our structured logging.
    
    This should be called early in the application lifecycle to ensure
    all logging goes through our structured system.
    """
    # Store original getLogger
    _original_getLogger = logging.getLogger
    
    def getLogger(name: Optional[str] = None) -> StructuredLoggingAdapter:
        """Replacement for logging.getLogger that returns our adapter."""
        if name is None:
            name = 'root'
        
        # Only intercept our application loggers
        if name.startswith('mcx3d_finance'):
            return LoggerFactory.get_logger(name)
        else:
            # Use original logger for third-party libraries
            return _original_getLogger(name)
    
    # Replace logging.getLogger
    logging.getLogger = getLogger

# Convenience function for migration
def get_logger(name: str) -> StructuredLoggingAdapter:
    """
    Get a structured logging adapter.
    
    This is the preferred way to get a logger in new code.
    
    Args:
        name: Logger name (usually __name__)
    
    Returns:
        StructuredLoggingAdapter instance
    """
    return LoggerFactory.get_logger(name)