import os
import redis
from urllib.parse import urlparse
from typing import cast

# Create a connection pool
pool = None


def get_redis_client() -> redis.Redis:
    """
    Creates and returns a Redis client instance configured with environment variables.
    Uses a connection pool for efficient connection management.
    """
    global pool
    if pool is None:
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        
        # Parse the Redis URL
        parsed_url = urlparse(redis_url)
        redis_host = parsed_url.hostname or "localhost"
        redis_port = parsed_url.port or 6379
        redis_db = int(parsed_url.path.lstrip('/')) if parsed_url.path else 0
        
        pool = redis.ConnectionPool(host=redis_host, port=redis_port, db=redis_db)

    return cast(redis.Redis, redis.Redis(connection_pool=pool))
