"""
Robust file operations with retry logic and graceful fallback strategies.

Provides enhanced file I/O operations with exponential backoff, disk space validation,
and automatic fallback mechanisms for the MCX3D financial system.
"""

import os
import shutil
import tempfile
import time
from mcx3d_finance.core.logging_factory import LoggerFactory
from contextlib import contextmanager
from pathlib import Path
from typing import Dict, Any, Optional, Union, List, Callable, Generator, IO
from dataclasses import dataclass, field
import json

from mcx3d_finance.exceptions import (
    ReportOutputError,
    MCX3DResourceError,
    MCX3DTimeoutError,
    ReportMemoryError
)
from mcx3d_finance.utils.resource_monitor import ResourceMonitor, get_resource_monitor

logger = LoggerFactory.get_logger(__name__, domain='utils')


@dataclass
class RetryConfig:
    """Configuration for retry operations."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 30.0
    backoff_factor: float = 2.0
    retry_on_disk_full: bool = True
    retry_on_permission_error: bool = True


@dataclass
class FallbackConfig:
    """Configuration for file operation fallback strategies."""
    enable_format_fallback: bool = True
    enable_size_reduction: bool = True
    enable_temp_directory_fallback: bool = True
    format_priority: List[str] = field(default_factory=list)
    
    def __post_init__(self) -> None:
        if self.format_priority is None:
            self.format_priority = ['pdf', 'excel', 'csv', 'json']


class RobustFileOperations:
    """
    Enhanced file operations with retry logic and resource monitoring.
    
    Provides robust file I/O with automatic retry, resource checking,
    and graceful degradation strategies.
    """
    
    def __init__(
        self,
        retry_config: Optional[RetryConfig] = None,
        fallback_config: Optional[FallbackConfig] = None,
        resource_monitor: Optional[ResourceMonitor] = None
    ) -> None:
        """
        Initialize robust file operations.
        
        Args:
            retry_config: Configuration for retry behavior
            fallback_config: Configuration for fallback strategies
            resource_monitor: Resource monitoring instance
        """
        self.retry_config = retry_config or RetryConfig()
        self.fallback_config = fallback_config or FallbackConfig()
        self.resource_monitor = resource_monitor or get_resource_monitor()
        self._temp_files: List[str] = []  # Track temp files for cleanup
    
    def safe_write_file(
        self,
        file_path: str,
        content: Union[bytes, str],
        mode: str = 'wb',
        estimated_size: Optional[int] = None
    ) -> str:
        """
        Safely write file with retry logic and resource validation.
        
        Args:
            file_path: Target file path
            content: Content to write
            mode: File opening mode ('wb', 'w', etc.)
            estimated_size: Estimated file size for disk space check
            
        Returns:
            Actual file path written (may differ due to fallbacks)
            
        Raises:
            ReportOutputError: If all retry attempts fail
            MCX3DResourceError: If insufficient resources
        """
        try:
            # Estimate content size if not provided
            if estimated_size is None:
                if isinstance(content, str):
                    estimated_size = len(content.encode('utf-8'))
                else:
                    estimated_size = len(content)
            
            # Check disk space before attempting write
            self.resource_monitor.check_disk_space(file_path, estimated_size)
            
            # Ensure parent directory exists
            parent_dir = os.path.dirname(os.path.abspath(file_path))
            os.makedirs(parent_dir, exist_ok=True)
            
            # Attempt write with retry logic
            return self._write_with_retry(file_path, content, mode)
            
        except Exception as e:
            logger.error(f"Safe file write failed for {file_path}: {e}")
            
            # Try fallback strategies
            if self.fallback_config.enable_temp_directory_fallback:
                try:
                    return self._write_to_temp_directory(file_path, content, mode)
                except Exception as temp_e:
                    logger.error(f"Temp directory fallback failed: {temp_e}")
            
            raise ReportOutputError(
                f"Failed to write file after all fallback attempts: {e}",
                output_path=file_path,
                original_error=str(e)
            )
    
    def safe_create_directory(self, dir_path: str, mode: int = 0o755) -> str:
        """
        Safely create directory with retry logic.
        
        Args:
            dir_path: Directory path to create
            mode: Directory permissions
            
        Returns:
            Created directory path
            
        Raises:
            ReportOutputError: If directory creation fails
        """
        try:
            return self._create_directory_with_retry(dir_path, mode)
        except Exception as e:
            raise ReportOutputError(
                f"Failed to create directory: {e}",
                output_path=dir_path,
                original_error=str(e)
            )
    
    def safe_copy_file(
        self,
        source_path: str,
        dest_path: str,
        preserve_metadata: bool = True
    ) -> str:
        """
        Safely copy file with retry logic and validation.
        
        Args:
            source_path: Source file path
            dest_path: Destination file path
            preserve_metadata: Whether to preserve file metadata
            
        Returns:
            Destination file path
            
        Raises:
            ReportOutputError: If copy operation fails
        """
        try:
            # Check source file exists
            if not os.path.exists(source_path):
                raise ReportOutputError(
                    f"Source file does not exist: {source_path}",
                    output_path=source_path
                )
            
            # Get source file size for disk space check
            source_size = os.path.getsize(source_path)
            self.resource_monitor.check_disk_space(dest_path, source_size)
            
            # Ensure destination directory exists
            dest_dir = os.path.dirname(os.path.abspath(dest_path))
            os.makedirs(dest_dir, exist_ok=True)
            
            # Perform copy with retry
            return self._copy_with_retry(source_path, dest_path, preserve_metadata)
            
        except Exception as e:
            raise ReportOutputError(
                f"Failed to copy file: {e}",
                output_path=dest_path,
                original_error=str(e)
            )
    
    @contextmanager
    def atomic_write(self, file_path: str, mode: str = 'wb') -> Generator[IO[Any], None, None]:
        """
        Context manager for atomic file writes using temporary files.
        
        Args:
            file_path: Target file path
            mode: File opening mode
            
        Yields:
            Temporary file handle for writing
        """
        # Create temporary file in same directory as target
        target_dir = os.path.dirname(os.path.abspath(file_path))
        temp_fd = None
        temp_path = None
        
        try:
            os.makedirs(target_dir, exist_ok=True)
            temp_fd, temp_path = tempfile.mkstemp(
                dir=target_dir,
                prefix=f".tmp_{os.path.basename(file_path)}_"
            )
            self._temp_files.append(temp_path)
            
            with open(temp_fd, mode) as temp_file:
                yield temp_file
            
            # Atomic move to final location
            if os.name == 'nt':  # Windows
                if os.path.exists(file_path):
                    os.remove(file_path)
            os.rename(temp_path, file_path)
            
            # Remove from temp files list since it's now permanent
            if temp_path in self._temp_files:
                self._temp_files.remove(temp_path)
                
            logger.debug(f"Atomic write completed: {file_path}")
            
        except Exception as e:
            # Clean up temp file on error
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                    if temp_path in self._temp_files:
                        self._temp_files.remove(temp_path)
                except:
                    pass
            
            raise ReportOutputError(
                f"Atomic write failed: {e}",
                output_path=file_path,
                original_error=str(e)
            )
    
    def get_available_formats_by_priority(
        self,
        requested_format: str,
        available_formats: List[str]
    ) -> List[str]:
        """
        Get list of formats in fallback priority order.
        
        Args:
            requested_format: Originally requested format
            available_formats: List of available format generators
            
        Returns:
            Ordered list of formats to try
        """
        if not self.fallback_config.enable_format_fallback:
            return [requested_format] if requested_format in available_formats else []
        
        # Start with requested format
        ordered_formats = []
        if requested_format in available_formats:
            ordered_formats.append(requested_format)
        
        # Add other formats in priority order
        for fmt in self.fallback_config.format_priority:
            if fmt != requested_format and fmt in available_formats:
                ordered_formats.append(fmt)
        
        return ordered_formats
    
    def cleanup_temp_files(self) -> None:
        """Clean up any remaining temporary files."""
        for temp_path in self._temp_files[:]:  # Copy list to avoid modification during iteration
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                self._temp_files.remove(temp_path)
                logger.debug(f"Cleaned up temp file: {temp_path}")
            except Exception as e:
                logger.warning(f"Failed to clean up temp file {temp_path}: {e}")
    
    def _write_with_retry(self, file_path: str, content: Union[bytes, str], mode: str) -> str:
        """Write file with exponential backoff retry logic."""
        last_exception = None
        
        for attempt in range(self.retry_config.max_attempts):
            try:
                with self.atomic_write(file_path, mode) as f:
                    f.write(content)
                
                # Verify file was written correctly
                if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                    logger.debug(f"File written successfully on attempt {attempt + 1}: {file_path}")
                    return file_path
                else:
                    raise ReportOutputError(
                        "File write verification failed - file is empty or missing",
                        output_path=file_path
                    )
                    
            except (OSError, IOError) as e:
                last_exception = e
                error_code = getattr(e, 'errno', 0)
                
                # Check if we should retry based on error type
                should_retry = (
                    attempt < self.retry_config.max_attempts - 1 and
                    (
                        (error_code == 28 and self.retry_config.retry_on_disk_full) or  # No space left
                        (error_code == 13 and self.retry_config.retry_on_permission_error) or  # Permission denied
                        error_code in [5, 11, 12, 16, 24, 32]  # Other retryable errors
                    )
                )
                
                if should_retry:
                    delay = min(
                        self.retry_config.base_delay * (self.retry_config.backoff_factor ** attempt),
                        self.retry_config.max_delay
                    )
                    logger.warning(f"File write attempt {attempt + 1} failed: {e}. Retrying in {delay:.1f}s")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"File write failed permanently on attempt {attempt + 1}: {e}")
                    break
        
        raise ReportOutputError(
            f"File write failed after {self.retry_config.max_attempts} attempts",
            output_path=file_path,
            original_error=str(last_exception)
        )
    
    def _write_to_temp_directory(self, original_path: str, content: Union[bytes, str], mode: str) -> str:
        """Write file to temporary directory as fallback."""
        try:
            # Use system temp directory
            temp_dir = tempfile.gettempdir()
            file_name = os.path.basename(original_path)
            
            # Add timestamp to avoid conflicts
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            name_parts = os.path.splitext(file_name)
            temp_file_name = f"{name_parts[0]}_{timestamp}{name_parts[1]}"
            
            temp_path = os.path.join(temp_dir, temp_file_name)
            
            # Write to temp location
            with open(temp_path, mode) as f:
                f.write(content)
            
            logger.warning(f"File written to temporary location: {temp_path}")
            return temp_path
            
        except Exception as e:
            raise ReportOutputError(
                f"Temp directory fallback failed: {e}",
                output_path=original_path,
                original_error=str(e)
            )
    
    def _create_directory_with_retry(self, dir_path: str, mode: int) -> str:
        """Create directory with retry logic."""
        last_exception = None
        
        for attempt in range(self.retry_config.max_attempts):
            try:
                os.makedirs(dir_path, mode=mode, exist_ok=True)
                return dir_path
                
            except (OSError, IOError) as e:
                last_exception = e
                
                if attempt < self.retry_config.max_attempts - 1:
                    delay = min(
                        self.retry_config.base_delay * (self.retry_config.backoff_factor ** attempt),
                        self.retry_config.max_delay
                    )
                    logger.warning(f"Directory creation attempt {attempt + 1} failed: {e}. Retrying in {delay:.1f}s")
                    time.sleep(delay)
                    continue
                else:
                    break
        
        raise ReportOutputError(
            f"Directory creation failed after {self.retry_config.max_attempts} attempts",
            output_path=dir_path,
            original_error=str(last_exception)
        )
    
    def _copy_with_retry(self, source_path: str, dest_path: str, preserve_metadata: bool) -> str:
        """Copy file with retry logic."""
        last_exception = None
        
        for attempt in range(self.retry_config.max_attempts):
            try:
                if preserve_metadata:
                    shutil.copy2(source_path, dest_path)
                else:
                    shutil.copy(source_path, dest_path)
                
                # Verify copy
                if os.path.exists(dest_path):
                    source_size = os.path.getsize(source_path)
                    dest_size = os.path.getsize(dest_path)
                    if source_size == dest_size:
                        return dest_path
                    else:
                        raise ReportOutputError(
                            f"Copy verification failed - size mismatch: {source_size} != {dest_size}",
                            output_path=dest_path
                        )
                
            except (OSError, IOError) as e:
                last_exception = e
                
                if attempt < self.retry_config.max_attempts - 1:
                    delay = min(
                        self.retry_config.base_delay * (self.retry_config.backoff_factor ** attempt),
                        self.retry_config.max_delay
                    )
                    logger.warning(f"File copy attempt {attempt + 1} failed: {e}. Retrying in {delay:.1f}s")
                    time.sleep(delay)
                    continue
                else:
                    break
        
        raise ReportOutputError(
            f"File copy failed after {self.retry_config.max_attempts} attempts",
            output_path=dest_path,
            original_error=str(last_exception)
        )


# Global instance for convenience
_global_robust_file_ops: Optional[RobustFileOperations] = None


def get_robust_file_ops(
    retry_config: Optional[RetryConfig] = None,
    fallback_config: Optional[FallbackConfig] = None
) -> RobustFileOperations:
    """
    Get or create global robust file operations instance.
    
    Args:
        retry_config: Retry configuration (only used on first call)
        fallback_config: Fallback configuration (only used on first call)
        
    Returns:
        RobustFileOperations instance
    """
    global _global_robust_file_ops
    
    if _global_robust_file_ops is None:
        _global_robust_file_ops = RobustFileOperations(
            retry_config=retry_config,
            fallback_config=fallback_config
        )
    
    return _global_robust_file_ops


def reset_robust_file_ops() -> None:
    """Reset the global robust file operations instance."""
    global _global_robust_file_ops
    
    if _global_robust_file_ops:
        _global_robust_file_ops.cleanup_temp_files()
    
    _global_robust_file_ops = None


# Convenience functions
def safe_write_json(file_path: str, data: Dict[str, Any], **kwargs) -> str:
    """Safely write JSON data to file."""
    file_ops = get_robust_file_ops()
    json_content = json.dumps(data, indent=2, ensure_ascii=False)
    return file_ops.safe_write_file(file_path, json_content, mode='w', **kwargs)


def safe_write_csv(file_path: str, data: str, **kwargs) -> str:
    """Safely write CSV data to file."""
    file_ops = get_robust_file_ops()
    return file_ops.safe_write_file(file_path, data, mode='w', **kwargs)