"""
Database-specific Logging Utilities

Provides logging utilities for database operations including query logging,
performance monitoring, and connection pool tracking.
"""

import time
import functools
from typing import Any, Callable, Optional, Dict
from datetime import datetime
import sqlalchemy
from sqlalchemy import event
from sqlalchemy.engine import Engine
from sqlalchemy.pool import Pool

from mcx3d_finance.utils.logging_config import get_db_logger
from mcx3d_finance.monitoring.structured_logger import get_correlation_id

# Database logger instance
db_logger = get_db_logger("query_monitor")

class DatabaseQueryLogger:
    """Logs database queries with performance metrics."""
    
    def __init__(self, slow_query_threshold_ms: float = 100):
        """
        Initialize the query logger.
        
        Args:
            slow_query_threshold_ms: Threshold for slow query warnings
        """
        self.slow_query_threshold_ms = slow_query_threshold_ms
        self._query_context = {}
    
    def before_execute(self, conn, clause, multiparams, params, execution_options):
        """Called before query execution."""
        conn.info['query_start_time'] = time.time()
        conn.info['correlation_id'] = get_correlation_id()
    
    def after_execute(self, conn, clause, multiparams, params, execution_options, result):
        """Called after query execution."""
        duration_ms = (time.time() - conn.info.get('query_start_time', time.time())) * 1000
        
        # Determine query type
        query_str = str(clause)
        if query_str.upper().startswith('SELECT'):
            query_type = 'SELECT'
        elif query_str.upper().startswith('INSERT'):
            query_type = 'INSERT'
        elif query_str.upper().startswith('UPDATE'):
            query_type = 'UPDATE'
        elif query_str.upper().startswith('DELETE'):
            query_type = 'DELETE'
        else:
            query_type = 'OTHER'
        
        # Log query metrics
        log_data = {
            'query_type': query_type,
            'duration_ms': duration_ms,
            'row_count': result.rowcount if hasattr(result, 'rowcount') else 0,
            'correlation_id': conn.info.get('correlation_id'),
            'slow_query': duration_ms > self.slow_query_threshold_ms
        }
        
        # Add query details for slow queries
        if duration_ms > self.slow_query_threshold_ms:
            log_data['query'] = query_str[:500]  # Truncate long queries
            log_data['params'] = self._sanitize_params(params)
        
        db_logger.log_performance_metric(
            operation='database_query',
            duration_ms=duration_ms,
            success=True,
            **log_data
        )
    
    def _sanitize_params(self, params: Any) -> Any:
        """Sanitize query parameters to remove sensitive data."""
        if isinstance(params, dict):
            return {k: '[REDACTED]' if self._is_sensitive_param(k) else v 
                    for k, v in params.items()}
        return params
    
    def _is_sensitive_param(self, param_name: str) -> bool:
        """Check if a parameter name indicates sensitive data."""
        sensitive_keywords = ['password', 'token', 'secret', 'key', 'ssn', 'credit_card']
        return any(keyword in param_name.lower() for keyword in sensitive_keywords)

class ConnectionPoolLogger:
    """Logs database connection pool events."""
    
    def __init__(self):
        self.logger = get_db_logger("connection_pool")
    
    def on_connect(self, dbapi_conn, connection_record):
        """Called when a new connection is created."""
        self.logger.log_business_event(
            'db_connection_created',
            pool_size=connection_record.pool.size() if hasattr(connection_record, 'pool') else 0,
            overflow=connection_record.pool.overflow() if hasattr(connection_record, 'pool') else 0
        )
    
    def on_checkout(self, dbapi_conn, connection_record, connection_proxy):
        """Called when a connection is checked out from the pool."""
        connection_record.info['checkout_time'] = time.time()
    
    def on_checkin(self, dbapi_conn, connection_record):
        """Called when a connection is returned to the pool."""
        checkout_time = connection_record.info.get('checkout_time')
        if checkout_time:
            duration_ms = (time.time() - checkout_time) * 1000
            self.logger.log_performance_metric(
                operation='db_connection_usage',
                duration_ms=duration_ms,
                success=True,
                pool_size=connection_record.pool.size() if hasattr(connection_record, 'pool') else 0
            )

def log_db_transaction(operation_name: Optional[str] = None):
    """
    Decorator to log database transactions.
    
    Args:
        operation_name: Optional operation name
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            op_name = operation_name or func.__name__
            logger = get_db_logger("transaction")
            
            try:
                # Log transaction start
                logger.log_business_event(
                    'db_transaction_start',
                    operation=op_name,
                    function=func.__name__,
                    module=func.__module__
                )
                
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                # Log successful transaction
                logger.log_business_event(
                    'db_transaction_complete',
                    operation=op_name,
                    duration_ms=duration_ms,
                    success=True,
                    function=func.__name__,
                    module=func.__module__
                )
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                # Log failed transaction
                logger.log_error(
                    e,
                    operation=f"db_transaction_{op_name}",
                    duration_ms=duration_ms,
                    function=func.__name__,
                    module=func.__module__
                )
                
                raise
        
        return wrapper
    return decorator

def log_bulk_operation(operation_name: str, record_count: int):
    """
    Context manager for logging bulk database operations.
    
    Args:
        operation_name: Name of the bulk operation
        record_count: Number of records being processed
    """
    class BulkOperationLogger:
        def __init__(self, operation_name: str, record_count: int):
            self.operation_name = operation_name
            self.record_count = record_count
            self.logger = get_db_logger("bulk_operations")
            self.start_time = None
        
        def __enter__(self):
            self.start_time = time.time()
            self.logger.log_business_event(
                'bulk_operation_start',
                operation=self.operation_name,
                record_count=self.record_count
            )
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            duration_ms = (time.time() - self.start_time) * 1000
            
            if exc_type is None:
                # Successful operation
                records_per_second = (self.record_count / duration_ms) * 1000 if duration_ms > 0 else 0
                
                self.logger.log_business_event(
                    'bulk_operation_complete',
                    operation=self.operation_name,
                    record_count=self.record_count,
                    duration_ms=duration_ms,
                    records_per_second=records_per_second,
                    success=True
                )
            else:
                # Failed operation
                self.logger.log_error(
                    exc_val,
                    operation=f"bulk_operation_{self.operation_name}",
                    record_count=self.record_count,
                    duration_ms=duration_ms
                )
    
    return BulkOperationLogger(operation_name, record_count)

def setup_database_logging(engine: Engine):
    """
    Set up comprehensive database logging for a SQLAlchemy engine.
    
    Args:
        engine: SQLAlchemy engine instance
    """
    # Initialize loggers
    query_logger = DatabaseQueryLogger()
    pool_logger = ConnectionPoolLogger()
    
    # Set up query logging
    event.listen(engine, "before_execute", query_logger.before_execute)
    event.listen(engine, "after_execute", query_logger.after_execute)
    
    # Set up connection pool logging
    event.listen(engine.pool, "connect", pool_logger.on_connect)
    event.listen(engine.pool, "checkout", pool_logger.on_checkout)
    event.listen(engine.pool, "checkin", pool_logger.on_checkin)
    
    db_logger.log_business_event(
        'database_logging_configured',
        engine_name=engine.name,
        pool_size=engine.pool.size() if hasattr(engine.pool, 'size') else 0
    )