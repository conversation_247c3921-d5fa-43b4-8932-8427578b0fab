"""
Enhanced error logging with correlation ID support and structured logging.

This module provides comprehensive error logging capabilities with correlation
tracking, structured logging formats, and integration with monitoring systems.
"""

import json
from mcx3d_finance.core.logging_factory import LoggerFactory
import sys
import traceback
from contextvars import ContextVar
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from mcx3d_finance.exceptions.base import MCX3DEx<PERSON>

# Context variable for correlation ID
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class StructuredFormatter(logging.Formatter):
    """
    Custom formatter that outputs structured JSON logs.
    
    This formatter ensures all log entries include correlation IDs,
    timestamps, and proper structure for log aggregation systems.
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        # Get correlation ID from context or generate new one
        correlation_id = correlation_id_var.get()
        if not correlation_id and hasattr(record, 'correlation_id'):
            correlation_id = record.correlation_id
        if not correlation_id:
            correlation_id = str(uuid4())
        
        # Build structured log entry
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "correlation_id": correlation_id,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info),
            }
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in [
                'name', 'msg', 'args', 'created', 'filename', 'funcName',
                'levelname', 'levelno', 'lineno', 'module', 'exc_info',
                'exc_text', 'stack_info', 'pathname', 'processName',
                'process', 'threadName', 'thread', 'getMessage', 'correlation_id'
            ]:
                log_entry[key] = value
        
        return json.dumps(log_entry)


class ErrorLogger:
    """
    Enhanced error logger with correlation tracking and structured output.
    """
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self._configure_if_needed()
    
    def _configure_if_needed(self):
        """Configure logger if not already configured."""
        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            handler.setFormatter(StructuredFormatter())
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_error(
        self,
        error: Union[Exception, MCX3DException],
        message: Optional[str] = None,
        extra_context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ):
        """
        Log an error with full context.
        
        Args:
            error: The exception to log
            message: Optional custom message
            extra_context: Additional context to include
            correlation_id: Correlation ID for tracking
        """
        # Set correlation ID in context if provided
        if correlation_id:
            correlation_id_var.set(correlation_id)
        
        # Build log context
        log_context = {
            "error_type": type(error).__name__,
            "error_module": type(error).__module__,
        }
        
        # Add MCX3D exception context
        if isinstance(error, MCX3DException):
            log_context.update({
                "error_code": error.error_code,
                "user_message": error.user_message,
                "severity": error.severity,
                "context": error.context,
                "timestamp": error.timestamp,
            })
            
            if error.original_exception:
                log_context["original_exception"] = {
                    "type": type(error.original_exception).__name__,
                    "message": str(error.original_exception),
                }
        
        # Add extra context
        if extra_context:
            log_context.update(extra_context)
        
        # Determine log level based on error type
        if isinstance(error, MCX3DException):
            log_level = getattr(logging, error.severity, logging.ERROR)
        else:
            log_level = logging.ERROR
        
        # Log the error
        self.logger.log(
            log_level,
            message or str(error),
            exc_info=error,
            extra=log_context
        )
    
    def log_operation(
        self,
        operation: str,
        status: str,
        duration_ms: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ):
        """
        Log an operation with metrics.
        
        Args:
            operation: Operation name
            status: Operation status (success, failure, etc.)
            duration_ms: Operation duration in milliseconds
            metadata: Additional metadata
            correlation_id: Correlation ID
        """
        if correlation_id:
            correlation_id_var.set(correlation_id)
        
        log_data = {
            "operation": operation,
            "status": status,
            "duration_ms": duration_ms,
        }
        
        if metadata:
            log_data.update(metadata)
        
        self.logger.info(
            f"Operation {operation} completed with status {status}",
            extra=log_data
        )
    
    def log_api_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration_ms: float,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None,
        user_id: Optional[str] = None,
        organization_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        """
        Log API request with detailed metrics.
        
        Args:
            method: HTTP method
            path: Request path
            status_code: Response status code
            duration_ms: Request duration in milliseconds
            request_size: Request body size in bytes
            response_size: Response body size in bytes
            user_id: User ID if authenticated
            organization_id: Organization ID if applicable
            correlation_id: Correlation ID
        """
        if correlation_id:
            correlation_id_var.set(correlation_id)
        
        log_data = {
            "http_method": method,
            "http_path": path,
            "http_status": status_code,
            "duration_ms": duration_ms,
            "request_size_bytes": request_size,
            "response_size_bytes": response_size,
            "user_id": user_id,
            "organization_id": organization_id,
        }
        
        # Determine log level based on status code
        if status_code >= 500:
            log_level = logging.ERROR
        elif status_code >= 400:
            log_level = logging.WARNING
        else:
            log_level = logging.INFO
        
        self.logger.log(
            log_level,
            f"{method} {path} - {status_code}",
            extra=log_data
        )


class AuditLogger:
    """
    Specialized logger for audit trails and compliance logging.
    """
    
    def __init__(self):
        self.logger = logging.getLogger("mcx3d.audit")
        self._configure_if_needed()
    
    def _configure_if_needed(self):
        """Configure audit logger with special handling."""
        if not self.logger.handlers:
            # Audit logs might go to a different destination
            handler = logging.StreamHandler(sys.stdout)
            handler.setFormatter(StructuredFormatter())
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
            # Ensure audit logs are always written
            self.logger.propagate = False
    
    def log_financial_operation(
        self,
        operation_type: str,
        entity_type: str,
        entity_id: str,
        user_id: str,
        organization_id: str,
        changes: Optional[Dict[str, Any]] = None,
        result: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        """
        Log financial operations for audit trail.
        
        Args:
            operation_type: Type of operation (create, update, delete, calculate)
            entity_type: Type of entity (transaction, report, valuation)
            entity_id: ID of the entity
            user_id: User performing the operation
            organization_id: Organization context
            changes: What was changed
            result: Operation result
            correlation_id: Correlation ID
        """
        if correlation_id:
            correlation_id_var.set(correlation_id)
        
        audit_entry = {
            "audit_type": "financial_operation",
            "operation_type": operation_type,
            "entity_type": entity_type,
            "entity_id": entity_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "changes": changes,
            "result": result,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        
        self.logger.info(
            f"Audit: {operation_type} {entity_type} {entity_id}",
            extra=audit_entry
        )
    
    def log_data_access(
        self,
        resource_type: str,
        resource_id: str,
        access_type: str,
        user_id: str,
        organization_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        """
        Log data access for compliance and security.
        
        Args:
            resource_type: Type of resource accessed
            resource_id: ID of the resource
            access_type: Type of access (read, write, delete)
            user_id: User accessing the data
            organization_id: Organization context
            ip_address: Client IP address
            user_agent: Client user agent
            correlation_id: Correlation ID
        """
        if correlation_id:
            correlation_id_var.set(correlation_id)
        
        access_entry = {
            "audit_type": "data_access",
            "resource_type": resource_type,
            "resource_id": resource_id,
            "access_type": access_type,
            "user_id": user_id,
            "organization_id": organization_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        
        self.logger.info(
            f"Access: {access_type} {resource_type} {resource_id}",
            extra=access_entry
        )


# Singleton instances
error_logger = ErrorLogger("mcx3d.errors")
audit_logger = AuditLogger()


def log_exception(
    error: Exception,
    message: Optional[str] = None,
    **kwargs
):
    """
    Convenience function to log exceptions.
    
    Args:
        error: Exception to log
        message: Optional message
        **kwargs: Additional context
    """
    correlation_id = correlation_id_var.get() or kwargs.pop('correlation_id', None)
    error_logger.log_error(
        error,
        message,
        extra_context=kwargs,
        correlation_id=correlation_id
    )


def set_correlation_id(correlation_id: str):
    """Set the correlation ID for the current context."""
    correlation_id_var.set(correlation_id)


def get_correlation_id() -> Optional[str]:
    """Get the current correlation ID."""
    return correlation_id_var.get()


def with_correlation_id(correlation_id: Optional[str] = None):
    """
    Decorator to ensure function runs with a correlation ID.
    
    Args:
        correlation_id: Optional correlation ID to use
    
    Example:
        @with_correlation_id()
        def process_request():
            # Function will have correlation ID in context
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Use provided ID or generate new one
            cid = correlation_id or str(uuid4())
            token = correlation_id_var.set(cid)
            try:
                return func(*args, **kwargs)
            finally:
                correlation_id_var.reset(token)
        return wrapper
    return decorator