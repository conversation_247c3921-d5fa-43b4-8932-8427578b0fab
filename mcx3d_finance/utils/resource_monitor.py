"""
Resource monitoring and management utilities for the MCX3D financial system.

Provides memory usage monitoring, disk space checking, and resource-aware
operation scaling to prevent system exhaustion during report generation.
"""

import gc
from mcx3d_finance.core.logging_factory import LoggerFactory
import os
import shutil
import time
import threading
import psutil
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, Callable, Union, List, Generator
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

from mcx3d_finance.exceptions import (
    MCX3DResourceError,
    MCX3DTimeoutError,
    ReportMemoryError,
    ReportOutputError
)

logger = LoggerFactory.get_logger(__name__, domain='utils')


@dataclass
class ResourceLimits:
    """Resource limit configuration."""
    memory_mb: int = 512
    disk_space_mb: int = 1000
    cpu_percent: float = 80.0
    timeout_seconds: int = 300
    max_file_size_mb: int = 100


@dataclass
class ResourceUsage:
    """Current resource usage snapshot."""
    memory_mb: float
    memory_percent: float
    disk_free_mb: float
    cpu_percent: float
    timestamp: datetime
    process_count: int
    
    def __post_init__(self) -> None:
        if not hasattr(self, 'timestamp'):
            self.timestamp = datetime.now()


class ResourceMonitor:
    """
    Resource monitoring and management for report generation operations.
    
    Monitors memory, disk space, and CPU usage to prevent resource exhaustion
    and provides context managers for safe resource-aware operations.
    """
    
    def __init__(self, limits: Optional[ResourceLimits] = None) -> None:
        """
        Initialize the resource monitor.
        
        Args:
            limits: Resource limit configuration
        """
        self.limits = limits or ResourceLimits()
        self.process = psutil.Process()
        self._monitoring_active = False
        self._monitoring_thread: Optional[threading.Thread] = None
        self._usage_history: List[ResourceUsage] = []
        self._max_history_size = 100
        self._lock = threading.RLock()
        
    def get_current_usage(self) -> ResourceUsage:
        """
        Get current resource usage snapshot.
        
        Returns:
            ResourceUsage object with current metrics
        """
        try:
            # Memory usage
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = self.process.memory_percent()
            
            # Disk usage for current working directory
            disk_usage = shutil.disk_usage(os.getcwd())
            disk_free_mb = disk_usage.free / 1024 / 1024
            
            # CPU usage
            cpu_percent = self.process.cpu_percent()
            
            # Process count
            try:
                process_count = len(psutil.pids())
            except:
                process_count = 0
            
            usage = ResourceUsage(
                memory_mb=memory_mb,
                memory_percent=memory_percent,
                disk_free_mb=disk_free_mb,
                cpu_percent=cpu_percent,
                timestamp=datetime.now(),
                process_count=process_count
            )
            
            # Update history
            with self._lock:
                self._usage_history.append(usage)
                if len(self._usage_history) > self._max_history_size:
                    self._usage_history.pop(0)
            
            return usage
            
        except Exception as e:
            logger.error(f"Failed to get resource usage: {e}")
            raise MCX3DResourceError(
                f"Resource monitoring failed: {e}",
                resource_type="system"
            )
    
    def check_resource_availability(self, operation_type: str = "general") -> bool:
        """
        Check if sufficient resources are available for operation.
        
        Args:
            operation_type: Type of operation being checked
            
        Returns:
            True if resources are available
            
        Raises:
            MCX3DResourceError: If resources are insufficient
        """
        try:
            usage = self.get_current_usage()
            
            # Memory check
            if usage.memory_mb > self.limits.memory_mb:
                raise MCX3DResourceError(
                    f"Memory limit exceeded for {operation_type}",
                    resource_type="memory",
                    current_usage=usage.memory_mb,
                    limit=self.limits.memory_mb
                )
            
            # Disk space check
            if usage.disk_free_mb < self.limits.disk_space_mb:
                raise MCX3DResourceError(
                    f"Insufficient disk space for {operation_type}",
                    resource_type="disk_space",
                    current_usage=usage.disk_free_mb,
                    limit=self.limits.disk_space_mb
                )
            
            # CPU usage check (warning only)
            if usage.cpu_percent > self.limits.cpu_percent:
                logger.warning(
                    f"High CPU usage ({usage.cpu_percent:.1f}%) during {operation_type}. "
                    "Performance may be degraded."
                )
            
            return True
            
        except Exception as e:
            if isinstance(e, MCX3DResourceError):
                raise
            raise MCX3DResourceError(
                f"Resource availability check failed: {e}",
                resource_type="system"
            )
    
    def check_disk_space(self, file_path: str, required_size_bytes: int) -> bool:
        """
        Check if sufficient disk space is available for file operation.
        
        Args:
            file_path: Path where file will be created
            required_size_bytes: Required space in bytes
            
        Returns:
            True if sufficient space available
            
        Raises:
            MCX3DResourceError: If insufficient disk space
        """
        try:
            # Get disk usage for the target directory
            target_dir = os.path.dirname(os.path.abspath(file_path))
            if not os.path.exists(target_dir):
                target_dir = os.getcwd()
            
            disk_usage = shutil.disk_usage(target_dir)
            available_bytes = disk_usage.free
            
            # Add buffer (10% or minimum 100MB)
            buffer_bytes = max(required_size_bytes * 0.1, 100 * 1024 * 1024)
            total_required = required_size_bytes + buffer_bytes
            
            if available_bytes < total_required:
                raise MCX3DResourceError(
                    f"Insufficient disk space for file creation",
                    resource_type="disk_space",
                    current_usage=available_bytes / 1024 / 1024,
                    limit=total_required / 1024 / 1024
                ).add_context("file_path", file_path).add_context("required_mb", required_size_bytes / 1024 / 1024)
            
            return True
            
        except Exception as e:
            if isinstance(e, MCX3DResourceError):
                raise
            raise MCX3DResourceError(
                f"Disk space check failed: {e}",
                resource_type="disk_space"
            )
    
    @contextmanager
    def monitor_operation(
        self,
        operation_name: str,
        memory_limit_override: Optional[int] = None,
        timeout_override: Optional[int] = None
    ) -> Generator[ResourceUsage, None, None]:
        """
        Context manager for monitoring resource usage during operations.
        
        Args:
            operation_name: Name of the operation being monitored
            memory_limit_override: Override memory limit for this operation (MB)
            timeout_override: Override timeout for this operation (seconds)
            
        Yields:
            ResourceUsage snapshot at operation start
            
        Raises:
            MCX3DResourceError: If resource limits are exceeded
            MCX3DTimeoutError: If operation times out
        """
        # Set up monitoring
        original_memory_limit = self.limits.memory_mb
        original_timeout = self.limits.timeout_seconds
        
        if memory_limit_override:
            self.limits.memory_mb = memory_limit_override
        if timeout_override:
            self.limits.timeout_seconds = timeout_override
        
        start_time = time.time()
        initial_usage = self.get_current_usage()
        peak_memory = initial_usage.memory_mb
        
        logger.info(f"Starting monitored operation: {operation_name}")
        logger.info(f"Initial resource usage: Memory={initial_usage.memory_mb:.1f}MB, "
                   f"Disk={initial_usage.disk_free_mb:.1f}MB free, "
                   f"CPU={initial_usage.cpu_percent:.1f}%")
        
        try:
            # Start monitoring thread if not already active
            if not self._monitoring_active:
                self._start_monitoring()
            
            yield initial_usage
            
        except MemoryError:
            gc.collect()  # Force garbage collection
            current_usage = self.get_current_usage()
            raise ReportMemoryError(
                f"Memory exhausted during {operation_name}",
                memory_usage_mb=current_usage.memory_mb,
                memory_limit_mb=self.limits.memory_mb
            )
            
        finally:
            # Calculate final metrics
            end_time = time.time()
            duration = end_time - start_time
            final_usage = self.get_current_usage()
            
            # Check for timeout
            if duration > self.limits.timeout_seconds:
                raise MCX3DTimeoutError(
                    f"Operation '{operation_name}' exceeded timeout",
                    operation=operation_name,
                    timeout_seconds=self.limits.timeout_seconds
                )
            
            # Track peak memory usage
            for usage in self._usage_history[-10:]:  # Check last 10 samples
                if usage.memory_mb > peak_memory:
                    peak_memory = usage.memory_mb
            
            # Log operation completion
            memory_change = final_usage.memory_mb - initial_usage.memory_mb
            logger.info(
                f"Completed operation: {operation_name} "
                f"(Duration: {duration:.2f}s, "
                f"Memory change: {memory_change:+.1f}MB, "
                f"Peak memory: {peak_memory:.1f}MB)"
            )
            
            # Restore original limits
            self.limits.memory_mb = original_memory_limit
            self.limits.timeout_seconds = original_timeout
            
            # Stop monitoring if we started it
            if self._monitoring_active and not timeout_override and not memory_limit_override:
                self._stop_monitoring()
    
    @contextmanager
    def limit_memory_usage(self, max_memory_mb: int) -> Generator[None, None, None]:
        """
        Context manager to limit memory usage for the current process.
        
        Args:
            max_memory_mb: Maximum memory usage in MB
            
        Raises:
            MCX3DResourceError: If memory limit is exceeded
        """
        original_limit = self.limits.memory_mb
        self.limits.memory_mb = max_memory_mb
        
        try:
            initial_usage = self.get_current_usage()
            logger.debug(f"Setting memory limit to {max_memory_mb}MB (current: {initial_usage.memory_mb:.1f}MB)")
            
            yield
            
            final_usage = self.get_current_usage()
            if final_usage.memory_mb > max_memory_mb:
                logger.warning(f"Memory usage ({final_usage.memory_mb:.1f}MB) exceeded limit ({max_memory_mb}MB)")
                
        finally:
            self.limits.memory_mb = original_limit
    
    def cleanup_resources(self) -> None:
        """Force cleanup of system resources."""
        try:
            # Force garbage collection
            collected = gc.collect()
            logger.debug(f"Garbage collection freed {collected} objects")
            
            # Get updated memory usage
            usage = self.get_current_usage()
            logger.debug(f"Memory usage after cleanup: {usage.memory_mb:.1f}MB")
            
        except Exception as e:
            logger.warning(f"Resource cleanup failed: {e}")
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive resource usage summary.
        
        Returns:
            Dictionary with current and historical resource information
        """
        current = self.get_current_usage()
        
        # Calculate averages from recent history
        recent_history = self._usage_history[-10:] if self._usage_history else [current]
        
        avg_memory = sum(u.memory_mb for u in recent_history) / len(recent_history)
        avg_cpu = sum(u.cpu_percent for u in recent_history) / len(recent_history)
        
        max_memory = max(u.memory_mb for u in recent_history)
        max_cpu = max(u.cpu_percent for u in recent_history)
        
        return {
            'current': {
                'memory_mb': current.memory_mb,
                'memory_percent': current.memory_percent,
                'disk_free_mb': current.disk_free_mb,
                'cpu_percent': current.cpu_percent,
                'process_count': current.process_count,
                'timestamp': current.timestamp.isoformat()
            },
            'recent_averages': {
                'memory_mb': avg_memory,
                'cpu_percent': avg_cpu,
                'samples': len(recent_history)
            },
            'recent_peaks': {
                'memory_mb': max_memory,
                'cpu_percent': max_cpu
            },
            'limits': {
                'memory_mb': self.limits.memory_mb,
                'disk_space_mb': self.limits.disk_space_mb,
                'cpu_percent': self.limits.cpu_percent,
                'timeout_seconds': self.limits.timeout_seconds
            },
            'status': {
                'memory_usage_pct': (current.memory_mb / self.limits.memory_mb) * 100,
                'disk_usage_pct': max(0, 100 - (current.disk_free_mb / self.limits.disk_space_mb) * 100),
                'monitoring_active': self._monitoring_active
            }
        }
    
    def _start_monitoring(self) -> None:
        """Start background resource monitoring."""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        
        def monitor_loop() -> None:
            while self._monitoring_active:
                try:
                    usage = self.get_current_usage()
                    
                    # Check for resource exhaustion
                    if usage.memory_mb > self.limits.memory_mb * 0.95:
                        logger.warning(f"Memory usage approaching limit: {usage.memory_mb:.1f}MB / {self.limits.memory_mb}MB")
                    
                    if usage.disk_free_mb < self.limits.disk_space_mb:
                        logger.warning(f"Disk space low: {usage.disk_free_mb:.1f}MB free")
                    
                    time.sleep(5)  # Check every 5 seconds
                    
                except Exception as e:
                    logger.error(f"Resource monitoring error: {e}")
                    time.sleep(10)  # Wait longer on error
        
        self._monitoring_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitoring_thread.start()
        logger.debug("Resource monitoring started")
    
    def _stop_monitoring(self) -> None:
        """Stop background resource monitoring."""
        if not self._monitoring_active:
            return
        
        self._monitoring_active = False
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=5)
        
        logger.debug("Resource monitoring stopped")


class ProcessPoolManager:
    """
    Manages process pools for CPU-intensive operations with resource awareness.
    """
    
    def __init__(self, resource_monitor: ResourceMonitor, max_workers: Optional[int] = None) -> None:
        """
        Initialize process pool manager.
        
        Args:
            resource_monitor: ResourceMonitor instance
            max_workers: Maximum number of worker processes
        """
        self.resource_monitor = resource_monitor
        self.max_workers = max_workers or min(4, os.cpu_count() or 1)
        self._executor: Optional[ProcessPoolExecutor] = None
    
    @contextmanager
    def get_executor(self) -> Generator[ProcessPoolExecutor, None, None]:
        """
        Get a managed process pool executor.
        
        Yields:
            ProcessPoolExecutor instance
        """
        try:
            # Check resources before creating pool
            self.resource_monitor.check_resource_availability("process_pool")
            
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                self._executor = executor
                logger.debug(f"Created process pool with {self.max_workers} workers")
                yield executor
                
        except Exception as e:
            logger.error(f"Process pool error: {e}")
            raise
        finally:
            self._executor = None


# Global resource monitor instance
_global_resource_monitor: Optional[ResourceMonitor] = None


def get_resource_monitor(limits: Optional[ResourceLimits] = None) -> ResourceMonitor:
    """
    Get or create global resource monitor instance.
    
    Args:
        limits: Resource limits (only used on first call)
        
    Returns:
        ResourceMonitor instance
    """
    global _global_resource_monitor
    
    if _global_resource_monitor is None:
        _global_resource_monitor = ResourceMonitor(limits)
    
    return _global_resource_monitor


def reset_resource_monitor() -> None:
    """Reset the global resource monitor instance."""
    global _global_resource_monitor
    
    if _global_resource_monitor:
        _global_resource_monitor._monitoring_active = False
    
    _global_resource_monitor = None