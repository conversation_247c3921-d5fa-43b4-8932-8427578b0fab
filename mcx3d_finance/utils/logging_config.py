"""
Centralized Logging Configuration for MCX3D Financial Platform

This module provides standardized logging configuration for all components
of the MCX3D financial system, ensuring consistent structured logging,
correlation ID tracking, and security compliance.
"""

import logging
import structlog
from typing import Dict, Any, Optional, List
from contextvars import ContextVar
from datetime import datetime
import os
import sys
from pathlib import Path

# Import our existing structured logger
from mcx3d_finance.monitoring.structured_logger import (
    StructuredLogger,
    correlation_id_context,
    get_correlation_id,
    set_correlation_id
)

# Import LoggerFactory for domain logger functions
from mcx3d_finance.core.logging_factory import LoggerFactory

# Domain-specific logger registry
_domain_loggers: Dict[str, StructuredLogger] = {}

# Security-sensitive fields that should never be logged
REDACTED_FIELDS = {
    'password', 'secret', 'token', 'api_key', 'private_key', 
    'access_token', 'refresh_token', 'authorization', 'x-api-key',
    'ssn', 'social_security_number', 'credit_card', 'card_number',
    'cvv', 'pin', 'account_number', 'routing_number'
}

# Log level configuration based on environment
LOG_LEVELS = {
    'development': 'DEBUG',
    'testing': 'INFO',
    'staging': 'INFO',
    'production': 'WARNING'
}

class SecurityFilter:
    """Filter to redact sensitive information from logs."""
    
    def __init__(self, redacted_fields: Optional[set] = None):
        self.redacted_fields = redacted_fields or REDACTED_FIELDS
    
    def __call__(self, logger, name, event_dict):
        """Redact sensitive fields from log events."""
        for key in list(event_dict.keys()):
            if any(field in key.lower() for field in self.redacted_fields):
                event_dict[key] = '[REDACTED]'
            elif isinstance(event_dict[key], dict):
                event_dict[key] = self._redact_dict(event_dict[key])
        return event_dict
    
    def _redact_dict(self, data: dict) -> dict:
        """Recursively redact sensitive fields in nested dictionaries."""
        redacted = {}
        for key, value in data.items():
            if any(field in key.lower() for field in self.redacted_fields):
                redacted[key] = '[REDACTED]'
            elif isinstance(value, dict):
                redacted[key] = self._redact_dict(value)
            else:
                redacted[key] = value
        return redacted

class PerformanceProcessor:
    """Processor to add performance context to slow operations."""
    
    def __init__(self, slow_threshold_ms: float = 1000):
        self.slow_threshold_ms = slow_threshold_ms
    
    def __call__(self, logger, name, event_dict):
        """Add performance warnings for slow operations."""
        duration_ms = event_dict.get('duration_ms', 0)
        if duration_ms > self.slow_threshold_ms:
            event_dict['performance_warning'] = 'SLOW_OPERATION'
            event_dict['threshold_exceeded_by_ms'] = duration_ms - self.slow_threshold_ms
        return event_dict

class ContextEnricher:
    """Processor to add standard context to all log entries."""
    
    def __call__(self, logger, name, event_dict):
        """Add standard context fields."""
        # Add timestamp if not present
        if 'timestamp' not in event_dict:
            event_dict['timestamp'] = datetime.utcnow().isoformat()
        
        # Add correlation ID if available
        correlation_id = get_correlation_id()
        if correlation_id and 'correlation_id' not in event_dict:
            event_dict['correlation_id'] = correlation_id
        
        # Add environment info
        event_dict['environment'] = os.getenv('ENVIRONMENT', 'development')
        event_dict['service'] = 'mcx3d-finance'
        
        # Add Python version for debugging
        event_dict['python_version'] = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        
        return event_dict

def configure_logging(
    service_name: str = "mcx3d-finance",
    log_level: Optional[str] = None,
    enable_console: bool = True,
    enable_file: bool = True,
    log_dir: Optional[Path] = None,
    additional_processors: Optional[List] = None
):
    """
    Configure structured logging for the application.
    
    Args:
        service_name: Name of the service for log identification
        log_level: Override log level (uses environment-based default if None)
        enable_console: Enable console output
        enable_file: Enable file output
        log_dir: Directory for log files (defaults to logs/)
        additional_processors: Additional structlog processors
    """
    # Determine log level
    environment = os.getenv('ENVIRONMENT', 'development')
    level = log_level or LOG_LEVELS.get(environment, 'INFO')
    
    # Configure processors
    processors = [
        structlog.contextvars.merge_contextvars,
        ContextEnricher(),
        SecurityFilter(),
        PerformanceProcessor(),
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
    ]
    
    # Add any additional processors
    if additional_processors:
        processors.extend(additional_processors)
    
    # Add renderer based on environment
    if environment == 'development' and enable_console:
        processors.append(structlog.dev.ConsoleRenderer())
    else:
        processors.append(structlog.processors.JSONRenderer())
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(getattr(logging, level)),
        context_class=dict,
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        level=getattr(logging, level),
        format='%(message)s',
        handlers=[]
    )
    
    # Add console handler if enabled
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, level))
        logging.root.addHandler(console_handler)
    
    # Add file handler if enabled
    if enable_file:
        log_dir = log_dir or Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / f'{service_name}.log',
            maxBytes=100 * 1024 * 1024,  # 100MB
            backupCount=10
        )
        file_handler.setLevel(getattr(logging, level))
        logging.root.addHandler(file_handler)

def get_domain_logger(domain: str, component: Optional[str] = None) -> StructuredLogger:
    """
    Get or create a domain-specific logger.
    
    Args:
        domain: Domain name (api, core, integrations, db, tasks, security)
        component: Optional component within the domain
    
    Returns:
        Configured StructuredLogger instance
    """
    logger_key = f"{domain}:{component}" if component else domain
    
    if logger_key not in _domain_loggers:
        _domain_loggers[logger_key] = StructuredLogger(
            service_name="mcx3d-finance",
            component=logger_key
        )
    
    return _domain_loggers[logger_key]

# Pre-configured domain loggers (using LoggerFactory for consistency)
def get_api_logger(component: Optional[str] = None) -> StructuredLogger:
    """Get logger for API layer."""
    name = f"mcx3d_finance.api.{component}" if component else "mcx3d_finance.api"
    return LoggerFactory.get_logger(name, domain="api")

def get_core_logger(component: Optional[str] = None) -> StructuredLogger:
    """Get logger for business logic layer."""
    name = f"mcx3d_finance.core.{component}" if component else "mcx3d_finance.core"
    return LoggerFactory.get_logger(name, domain="core")

def get_integration_logger(component: Optional[str] = None) -> StructuredLogger:
    """Get logger for integration layer."""
    name = f"mcx3d_finance.integrations.{component}" if component else "mcx3d_finance.integrations"
    return LoggerFactory.get_logger(name, domain="integration")

def get_db_logger(component: Optional[str] = None) -> StructuredLogger:
    """Get logger for database layer."""
    name = f"mcx3d_finance.db.{component}" if component else "mcx3d_finance.db"
    return LoggerFactory.get_logger(name, domain="core")

def get_task_logger(component: Optional[str] = None) -> StructuredLogger:
    """Get logger for background tasks."""
    name = f"mcx3d_finance.tasks.{component}" if component else "mcx3d_finance.tasks"
    return LoggerFactory.get_logger(name, domain="tasks")

def get_security_logger(component: Optional[str] = None) -> StructuredLogger:
    """Get logger for security events."""
    name = f"mcx3d_finance.security.{component}" if component else "mcx3d_finance.security"
    return LoggerFactory.get_logger(name, domain="security")

# Convenience decorators for logging
def log_performance(operation_name: Optional[str] = None):
    """
    Decorator to log performance metrics for functions.
    
    Args:
        operation_name: Optional operation name (uses function name if None)
    """
    def decorator(func):
        import functools
        import time
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            logger = get_core_logger(func.__module__)
            op_name = operation_name or func.__name__
            
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                logger.log_performance_metric(
                    operation=op_name,
                    duration_ms=duration_ms,
                    success=True,
                    function=func.__name__,
                    module=func.__module__
                )
                
                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                logger.log_performance_metric(
                    operation=op_name,
                    duration_ms=duration_ms,
                    success=False,
                    error=str(e),
                    function=func.__name__,
                    module=func.__module__
                )
                
                raise
        
        return wrapper
    return decorator

def log_security_event(event_type: str, severity: str = "medium"):
    """
    Decorator to log security-sensitive operations.
    
    Args:
        event_type: Type of security event
        severity: Event severity (low, medium, high, critical)
    """
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_security_logger(func.__module__)
            
            # Extract user context if available
            user_id = None
            if args and hasattr(args[0], 'current_user'):
                user_id = getattr(args[0].current_user, 'id', None)
            
            try:
                result = func(*args, **kwargs)
                
                logger.log_security_event(
                    event_type=event_type,
                    severity=severity,
                    success=True,
                    function=func.__name__,
                    module=func.__module__,
                    user_id=user_id
                )
                
                return result
            except Exception as e:
                logger.log_security_event(
                    event_type=f"{event_type}_failed",
                    severity="high" if severity in ["high", "critical"] else "medium",
                    success=False,
                    error=str(e),
                    function=func.__name__,
                    module=func.__module__,
                    user_id=user_id
                )
                
                raise
        
        return wrapper
    return decorator

# Initialize logging on module import
configure_logging()